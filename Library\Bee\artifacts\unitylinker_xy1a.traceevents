{ "pid": 35672, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON><PERSON><PERSON>" } },
{ "pid": 35672, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 35672, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 35672, "tid": 12884901888, "ts": 1752055794495888, "dur": 213429, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 35672, "tid": 1, "ts": 1752055809690858, "dur": 4601, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 35672, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 35672, "tid": 1, "ts": 1752055809695462, "dur": 1, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 35672, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 35672, "tid": 4294967296, "ts": 1752055793297719, "dur": 178407, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 35672, "tid": 1, "ts": 1752055809695464, "dur": 91, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 35672, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35672, "tid": 1, "ts": 1752055791140913, "dur": 18521693, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 35672, "tid": 1, "ts": 1752055791144064, "dur": 118330, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791153106, "dur": 67149, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791262396, "dur": 20314, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791323228, "dur": 58947, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791386007, "dur": 186194, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791572279, "dur": 52428, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791624731, "dur": 76065, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791700811, "dur": 286839, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055791987674, "dur": 102484, "ph": "X", "name": "ResolveForEngineModuleStrippingDisabledStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792090170, "dur": 23513, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792113695, "dur": 3202, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792116900, "dur": 5446, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792122350, "dur": 700, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792123060, "dur": 24319, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792147387, "dur": 572, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792147961, "dur": 856, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792148819, "dur": 366, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792149188, "dur": 414, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792149605, "dur": 510, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792150118, "dur": 381, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792150502, "dur": 407, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792150911, "dur": 1163, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792152077, "dur": 862, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792152941, "dur": 1021, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792153979, "dur": 11291, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792165286, "dur": 8779, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792174074, "dur": 5143, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792179223, "dur": 3582, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792182809, "dur": 8485, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792191299, "dur": 7671, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792198975, "dur": 3372, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792202350, "dur": 1905, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792204257, "dur": 2250, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792206509, "dur": 1870, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792208421, "dur": 3482, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792211914, "dur": 855, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792212785, "dur": 1757, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792214551, "dur": 7572, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792222127, "dur": 228, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792222357, "dur": 300, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792222659, "dur": 4791, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792227453, "dur": 3420, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792230875, "dur": 1688, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792232573, "dur": 38594, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792271174, "dur": 12432, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792283620, "dur": 220727, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792504383, "dur": 888, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792505281, "dur": 965, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792506257, "dur": 479, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792506746, "dur": 1796, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792508553, "dur": 1353, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792509916, "dur": 6034, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792515957, "dur": 234, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792516192, "dur": 283, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792516476, "dur": 115, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792516591, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792516613, "dur": 148875, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792665524, "dur": 286595, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792952149, "dur": 601, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055792952759, "dur": 236373, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055793189159, "dur": 52154, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055793241342, "dur": 12104, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055793253468, "dur": 226925, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055793480431, "dur": 36492, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055793516948, "dur": 3870373, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797387351, "dur": 1413, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797388773, "dur": 10441, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797399227, "dur": 129499, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797528743, "dur": 7044, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797535802, "dur": 8823, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797544640, "dur": 5207, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797549856, "dur": 5340, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797555210, "dur": 6782, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797561999, "dur": 279, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797562287, "dur": 774, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797563087, "dur": 924, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055797564020, "dur": 12007910, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809571955, "dur": 35918, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809607886, "dur": 652, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809614078, "dur": 48207, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809662609, "dur": 13521, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809695556, "dur": 236, "ph": "X", "name": "", "args": {} },
{ "pid": 35672, "tid": 1, "ts": 1752055809689860, "dur": 6558, "ph": "X", "name": "Write chrome-trace events", "args": {} },
