{"buildFiles": ["D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "game", "output": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\55s5g5i1\\obj\\arm64-v8a\\libgame.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}