﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139 (void);
extern void math_hash_m2D043DE6F877DBEC3DCF0D5482BB73A122C4A88E (void);
extern void math_hash_m8C5F1505830AD8957435AB88644C2B6E4AD2FD22 (void);
extern void math_hash_mCFEA70D42EF231EF8A962539112C58E5E1DD9B25 (void);
extern void math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB (void);
extern void math_float3x3_m9EFCC44816C0612E9EE86D0ADD54D38C0B2BDAFF (void);
extern void math_float3x3_m63BD623B75D81DD4349E93172851DA6CAB8D899F (void);
extern void math_hash_mCC0D346D77A7BAE4C16EB878E1FDF69E863A09C3 (void);
extern void math_float4_m16697C284FA0C25A84F3DC3E99F3D4C306B6BFBF (void);
extern void math_float4_mE54104D60E6B9A358C75CB6F378118AB4914BFC4 (void);
extern void math_hash_m53E875B2DC4324BD20573419DBE27D0F651FA4D4 (void);
extern void math_rotate_m8F4351B6EA22282955B109AB1D48A30852D23C6C (void);
extern void math_transform_m8E47EBD0098A58A9BEB51032E1B6C25C9BD90178 (void);
extern void math_hash_m20286BA0E4D2F6DC3C7013DA713AADCFFD87D444 (void);
extern void math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288 (void);
extern void math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139 (void);
extern void math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E (void);
extern void math_asuint_mDF3C61EF6F9D9D10A1D3EB9D0075149707B461B9 (void);
extern void math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9 (void);
extern void math_asfloat_m12607D976A0CF76E07E41340262CAEC45A9CB537 (void);
extern void math_isfinite_mC7FACC44EBD8D443AA9CE0F0F47F8426EB68CDF8 (void);
extern void math_min_m02D43DF516544C279AF660EA4731449C82991849 (void);
extern void math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB (void);
extern void math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B (void);
extern void math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE (void);
extern void math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43 (void);
extern void math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496 (void);
extern void math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84 (void);
extern void math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371 (void);
extern void math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C (void);
extern void math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E (void);
extern void math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB (void);
extern void math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1 (void);
extern void math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD (void);
extern void math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4 (void);
extern void math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF (void);
extern void math_acos_mD1CE88CB2686ED61B8228108D2C66AA29E56AAEF (void);
extern void math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF (void);
extern void math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48 (void);
extern void math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0 (void);
extern void math_frac_m3431E235C44627C745324499E1A06E81B9804A60 (void);
extern void math_rcp_mED2BCEE83560EEE59CE06EBD90332CAFA9C08024 (void);
extern void math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B (void);
extern void math_log2_m07B499B0DDA692EDD9DF4780349C26EB28199156 (void);
extern void math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A (void);
extern void math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72 (void);
extern void math_normalize_mF02431EFC9E3212E0245EFF5C13BC9DC34512399 (void);
extern void math_normalize_m598E318DE7A638AE5FE8A35528AC6A90B6E909D6 (void);
extern void math_normalizesafe_mB64EB7980954DDE101440F01D7EFC210644171F4 (void);
extern void math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257 (void);
extern void math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532 (void);
extern void math_distancesq_mA49E8B34404D0C4DB3C9D4E065CE4CA255C9770B (void);
extern void math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180 (void);
extern void math_all_mB8957D1E684773F171F74448AD9591F3619890A4 (void);
extern void math_all_mA5E096B33A3B9F345EA39AF7ABB900EBAF96F12F (void);
extern void math_select_m28DBBCCCA456FF5E0E94F04419474BB3558C1739 (void);
extern void math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E (void);
extern void math_select_m9AE2C8B94207D016E9AA462152BC0C7B468981A7 (void);
extern void math_select_m7B43FDAE30A8A4D7AD42684DF8C090FC697AF346 (void);
extern void math_sincos_mCA97E81FBD3BEEE76CD632E8407D21DBED186F18 (void);
extern void math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07 (void);
extern void math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C (void);
extern void math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769 (void);
extern void math_ceilpow2_m10A6250895829A587C846DEBBE5455C84C886366 (void);
extern void math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C (void);
extern void math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F (void);
extern void math_up_m0BE1699606608A1C5B2F4996F2D550938DB736C1 (void);
extern void math_forward_mEF04F56F9C98B915C3749AFEA329225EA4434653 (void);
extern void math_right_mAAC3943E3EF6A258B94CA0339D90213CE04D0E61 (void);
extern void math_mul_m0C1D080B5BFCAB1FD107CDB500C283E77247F87D (void);
extern void math_quaternion_m315B4CA2F8475CC33F3C73187F00AC8B64BBF939 (void);
extern void math_quaternion_mE9DBDC1E38A93968B447FF4D365823A7889B0749 (void);
extern void math_inverse_mAB8FC214DC9438E44EB31D19E3C70D3BDFAAF202 (void);
extern void math_lengthsq_mC78745E25271C5D443CA4F8A32ED03A0B21A5C8A (void);
extern void math_mul_m3CC0941E6A3DE5718C6439421E74D7F80793F652 (void);
extern void math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99 (void);
extern void math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F (void);
extern void math_hash_mEE8D76145C2CD7DDEE85990255444BCC3DCC161A (void);
extern void math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0 (void);
extern void math_hash_m31E070E721A961188B5629FCAC3C9559145F1A76 (void);
extern void math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336 (void);
extern void math_uint4_mFC77EE985745E49CC93EA9429C3DD798B8D955C3 (void);
extern void math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA (void);
extern void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2 (void);
extern void bool3_Equals_m2833AB3E744AA81CE8592FE6C48EDDD482AC4C02 (void);
extern void bool3_Equals_m77F48CEBC0BBCF1C3C06B77425E6BEA7211D78CE (void);
extern void bool3_GetHashCode_m0778D184FCC8E088AA7F23443371E794920DF7CE (void);
extern void bool3_ToString_m84A77AF48A490798E426F25748C380D59A1241F2 (void);
extern void bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88 (void);
extern void bool4_Equals_m98194D94ECE5D095F38CE48A1E121646211349C3 (void);
extern void bool4_Equals_mEAC4979E78DDE744481E272861B7E006B0F2B16C (void);
extern void bool4_GetHashCode_m9B585F39BC56C6385D8FCA6D0F56B7CE923CAB06 (void);
extern void bool4_ToString_mA83910873B6B8641DF76FC8851FEEC15DA574478 (void);
extern void bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26 (void);
extern void bool4x4_Equals_m745E786A24F9C3FF1C25782462EF54E176ED109F (void);
extern void bool4x4_Equals_m3E1EB27A6285FA42CE1F23B97D0682394C330B39 (void);
extern void bool4x4_GetHashCode_mA50C07231889386E2FBFE6F047A3F8CB01E113B9 (void);
extern void bool4x4_ToString_m60F42991E4A80899D5F5E91B1E6A96E08F60C896 (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9 (void);
extern void float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4 (void);
extern void float3_op_Implicit_m495864276F56A9757CE235005D9908DBB47D8031 (void);
extern void float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7 (void);
extern void float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81 (void);
extern void float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350 (void);
extern void float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B (void);
extern void float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2 (void);
extern void float3_op_Division_m59FB3E510B03034B8834D7D724561FB9EC4DBB81 (void);
extern void float3_op_UnaryNegation_m862876969881839716CBAF9AE074FA4BFDFABDF1 (void);
extern void float3_op_Equality_m3E2982E0AC63341FE1184A0F95D699EBE0B87D49 (void);
extern void float3_op_Equality_m4F6928ABA1A8D6D89786C6BB4C79210453BD06A0 (void);
extern void float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535 (void);
extern void float3_get_Item_mBDC42AE198E1173A9323D7C9909ACFD5B1851D26 (void);
extern void float3_set_Item_m73150115008953B6661619E909C7781F198659DF (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017 (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081 (void);
extern void float3_op_Implicit_m9CC301DFD67EEFAA15CA05E91913E862B22326F6 (void);
extern void float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB (void);
extern void float3x3__ctor_mA652DC011B892B36A8216646B51B2014F89CE93E (void);
extern void float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED (void);
extern void float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0 (void);
extern void float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA (void);
extern void float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705 (void);
extern void float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934 (void);
extern void float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522 (void);
extern void float3x3__cctor_mDD98D4703621A506785CC046B18E86B0B5388675 (void);
extern void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D (void);
extern void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345 (void);
extern void float4_op_Multiply_m0E98338FB7DFF55B101EBCD78A8703ADB9C08667 (void);
extern void float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB (void);
extern void float4_op_Multiply_m94C65B5751B7969CD82F15DADB8A6F182BFBD581 (void);
extern void float4_op_Addition_m2CF2E1B2DAD4996DE3C5B6DFB90185E4CC8F0F44 (void);
extern void float4_op_Subtraction_mBC40F52B8A8EF499A1AA3CC987E5935BD188B4E3 (void);
extern void float4_op_Equality_mDBB7FE23BF9C4CFC547DD8162F6677DBFFAFE1AF (void);
extern void float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C (void);
extern void float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD (void);
extern void float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223 (void);
extern void float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E (void);
extern void float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B (void);
extern void float4_get_zwxy_m8F8007FD25E49C0644615EABA414DA7672794563 (void);
extern void float4_get_wzyx_mA9531BFB41E8CC9E8070F8C4A1C23BCAAD7648E8 (void);
extern void float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480 (void);
extern void float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009 (void);
extern void float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06 (void);
extern void float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1 (void);
extern void float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4 (void);
extern void float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48 (void);
extern void float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA (void);
extern void float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A (void);
extern void float4_op_Implicit_m6D2091EB2CF6E0629A029A7BE9AD230F5F394CB2 (void);
extern void float4_op_Implicit_m5E3AEBAF5F12155549CC051E1EEEE81DF3516E92 (void);
extern void float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF (void);
extern void float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5 (void);
extern void float4x4_op_Equality_m2179DA55B6925A4EE5A48A161D1A4F35ACFA3EC0 (void);
extern void float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB (void);
extern void float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8 (void);
extern void float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7 (void);
extern void float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584 (void);
extern void float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81 (void);
extern void float4x4_op_Implicit_mCDD72C5454A8DAFB2A659484006D18D5979BE6AB (void);
extern void float4x4__cctor_m8AD96186FE409BAF9B9D69DF283AF3BBF336BE19 (void);
extern void quaternion_op_Implicit_m12C97CDC5FE642C53837F73E2F9B8761D530C502 (void);
extern void quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9 (void);
extern void quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F (void);
extern void quaternion__ctor_m354F09C0E50CA59DA43037E9993EAE9BF97E9120 (void);
extern void quaternion__ctor_m7A01BD711C8220B01414BCF7DCAC2D8195FB351F (void);
extern void quaternion_AxisAngle_mAFB418AC3B01E1E6360BD0603EB9DF36F4DDA986 (void);
extern void quaternion_LookRotationSafe_m66E9BBE50A4E0EF220CEE80EA248687D34F8D687 (void);
extern void quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE (void);
extern void quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20 (void);
extern void quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0 (void);
extern void quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8 (void);
extern void quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349 (void);
extern void quaternion__cctor_mB4B1769F995B8CB2E48C997119A490F8AEBA2239 (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF (void);
extern void uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD (void);
extern void uint3_op_Addition_mD11BEB362388E20A02C6D431C2ED912807585589 (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81 (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633 (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008 (void);
extern void uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F (void);
extern void uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3 (void);
extern void uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF (void);
extern void uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED (void);
extern void uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652 (void);
extern void uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F (void);
extern void uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875 (void);
extern void uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573 (void);
extern void uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889 (void);
extern void uint4_set_Item_m3DACDC36A353380F6DB544991D4582B5E43038F3 (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386 (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3 (void);
static Il2CppMethodPointer s_methodPointers[200] = 
{
	Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139,
	math_hash_m2D043DE6F877DBEC3DCF0D5482BB73A122C4A88E,
	math_hash_m8C5F1505830AD8957435AB88644C2B6E4AD2FD22,
	math_hash_mCFEA70D42EF231EF8A962539112C58E5E1DD9B25,
	math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB,
	math_float3x3_m9EFCC44816C0612E9EE86D0ADD54D38C0B2BDAFF,
	math_float3x3_m63BD623B75D81DD4349E93172851DA6CAB8D899F,
	math_hash_mCC0D346D77A7BAE4C16EB878E1FDF69E863A09C3,
	math_float4_m16697C284FA0C25A84F3DC3E99F3D4C306B6BFBF,
	math_float4_mE54104D60E6B9A358C75CB6F378118AB4914BFC4,
	math_hash_m53E875B2DC4324BD20573419DBE27D0F651FA4D4,
	math_rotate_m8F4351B6EA22282955B109AB1D48A30852D23C6C,
	math_transform_m8E47EBD0098A58A9BEB51032E1B6C25C9BD90178,
	math_hash_m20286BA0E4D2F6DC3C7013DA713AADCFFD87D444,
	math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288,
	math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139,
	math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E,
	math_asuint_mDF3C61EF6F9D9D10A1D3EB9D0075149707B461B9,
	math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9,
	math_asfloat_m12607D976A0CF76E07E41340262CAEC45A9CB537,
	math_isfinite_mC7FACC44EBD8D443AA9CE0F0F47F8426EB68CDF8,
	math_min_m02D43DF516544C279AF660EA4731449C82991849,
	math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB,
	math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B,
	math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE,
	math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43,
	math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496,
	math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84,
	math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371,
	math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C,
	math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E,
	math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB,
	math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1,
	math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD,
	math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4,
	math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF,
	math_acos_mD1CE88CB2686ED61B8228108D2C66AA29E56AAEF,
	math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF,
	math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48,
	math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0,
	math_frac_m3431E235C44627C745324499E1A06E81B9804A60,
	math_rcp_mED2BCEE83560EEE59CE06EBD90332CAFA9C08024,
	math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B,
	math_log2_m07B499B0DDA692EDD9DF4780349C26EB28199156,
	math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A,
	math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72,
	math_normalize_mF02431EFC9E3212E0245EFF5C13BC9DC34512399,
	math_normalize_m598E318DE7A638AE5FE8A35528AC6A90B6E909D6,
	math_normalizesafe_mB64EB7980954DDE101440F01D7EFC210644171F4,
	math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257,
	math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532,
	math_distancesq_mA49E8B34404D0C4DB3C9D4E065CE4CA255C9770B,
	math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180,
	math_all_mB8957D1E684773F171F74448AD9591F3619890A4,
	math_all_mA5E096B33A3B9F345EA39AF7ABB900EBAF96F12F,
	math_select_m28DBBCCCA456FF5E0E94F04419474BB3558C1739,
	math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E,
	math_select_m9AE2C8B94207D016E9AA462152BC0C7B468981A7,
	math_select_m7B43FDAE30A8A4D7AD42684DF8C090FC697AF346,
	math_sincos_mCA97E81FBD3BEEE76CD632E8407D21DBED186F18,
	math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07,
	math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C,
	math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769,
	math_ceilpow2_m10A6250895829A587C846DEBBE5455C84C886366,
	math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C,
	math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F,
	math_up_m0BE1699606608A1C5B2F4996F2D550938DB736C1,
	math_forward_mEF04F56F9C98B915C3749AFEA329225EA4434653,
	math_right_mAAC3943E3EF6A258B94CA0339D90213CE04D0E61,
	math_mul_m0C1D080B5BFCAB1FD107CDB500C283E77247F87D,
	math_quaternion_m315B4CA2F8475CC33F3C73187F00AC8B64BBF939,
	math_quaternion_mE9DBDC1E38A93968B447FF4D365823A7889B0749,
	math_inverse_mAB8FC214DC9438E44EB31D19E3C70D3BDFAAF202,
	math_lengthsq_mC78745E25271C5D443CA4F8A32ED03A0B21A5C8A,
	math_mul_m3CC0941E6A3DE5718C6439421E74D7F80793F652,
	math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99,
	math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F,
	math_hash_mEE8D76145C2CD7DDEE85990255444BCC3DCC161A,
	math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0,
	math_hash_m31E070E721A961188B5629FCAC3C9559145F1A76,
	math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336,
	math_uint4_mFC77EE985745E49CC93EA9429C3DD798B8D955C3,
	math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA,
	bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2,
	bool3_Equals_m2833AB3E744AA81CE8592FE6C48EDDD482AC4C02,
	bool3_Equals_m77F48CEBC0BBCF1C3C06B77425E6BEA7211D78CE,
	bool3_GetHashCode_m0778D184FCC8E088AA7F23443371E794920DF7CE,
	bool3_ToString_m84A77AF48A490798E426F25748C380D59A1241F2,
	bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88,
	bool4_Equals_m98194D94ECE5D095F38CE48A1E121646211349C3,
	bool4_Equals_mEAC4979E78DDE744481E272861B7E006B0F2B16C,
	bool4_GetHashCode_m9B585F39BC56C6385D8FCA6D0F56B7CE923CAB06,
	bool4_ToString_mA83910873B6B8641DF76FC8851FEEC15DA574478,
	bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26,
	bool4x4_Equals_m745E786A24F9C3FF1C25782462EF54E176ED109F,
	bool4x4_Equals_m3E1EB27A6285FA42CE1F23B97D0682394C330B39,
	bool4x4_GetHashCode_mA50C07231889386E2FBFE6F047A3F8CB01E113B9,
	bool4x4_ToString_m60F42991E4A80899D5F5E91B1E6A96E08F60C896,
	float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9,
	float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4,
	float3_op_Implicit_m495864276F56A9757CE235005D9908DBB47D8031,
	float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7,
	float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81,
	float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350,
	float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B,
	float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2,
	float3_op_Division_m59FB3E510B03034B8834D7D724561FB9EC4DBB81,
	float3_op_UnaryNegation_m862876969881839716CBAF9AE074FA4BFDFABDF1,
	float3_op_Equality_m3E2982E0AC63341FE1184A0F95D699EBE0B87D49,
	float3_op_Equality_m4F6928ABA1A8D6D89786C6BB4C79210453BD06A0,
	float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535,
	float3_get_Item_mBDC42AE198E1173A9323D7C9909ACFD5B1851D26,
	float3_set_Item_m73150115008953B6661619E909C7781F198659DF,
	float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A,
	float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C,
	float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017,
	float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA,
	float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081,
	float3_op_Implicit_m9CC301DFD67EEFAA15CA05E91913E862B22326F6,
	float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB,
	float3x3__ctor_mA652DC011B892B36A8216646B51B2014F89CE93E,
	float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED,
	float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0,
	float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA,
	float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705,
	float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934,
	float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522,
	float3x3__cctor_mDD98D4703621A506785CC046B18E86B0B5388675,
	float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D,
	float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345,
	float4_op_Multiply_m0E98338FB7DFF55B101EBCD78A8703ADB9C08667,
	float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB,
	float4_op_Multiply_m94C65B5751B7969CD82F15DADB8A6F182BFBD581,
	float4_op_Addition_m2CF2E1B2DAD4996DE3C5B6DFB90185E4CC8F0F44,
	float4_op_Subtraction_mBC40F52B8A8EF499A1AA3CC987E5935BD188B4E3,
	float4_op_Equality_mDBB7FE23BF9C4CFC547DD8162F6677DBFFAFE1AF,
	float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C,
	float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD,
	float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223,
	float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E,
	float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B,
	float4_get_zwxy_m8F8007FD25E49C0644615EABA414DA7672794563,
	float4_get_wzyx_mA9531BFB41E8CC9E8070F8C4A1C23BCAAD7648E8,
	float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480,
	float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009,
	float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06,
	float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1,
	float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4,
	float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48,
	float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA,
	float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A,
	float4_op_Implicit_m6D2091EB2CF6E0629A029A7BE9AD230F5F394CB2,
	float4_op_Implicit_m5E3AEBAF5F12155549CC051E1EEEE81DF3516E92,
	float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF,
	float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5,
	float4x4_op_Equality_m2179DA55B6925A4EE5A48A161D1A4F35ACFA3EC0,
	float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB,
	float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8,
	float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7,
	float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584,
	float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81,
	float4x4_op_Implicit_mCDD72C5454A8DAFB2A659484006D18D5979BE6AB,
	float4x4__cctor_m8AD96186FE409BAF9B9D69DF283AF3BBF336BE19,
	quaternion_op_Implicit_m12C97CDC5FE642C53837F73E2F9B8761D530C502,
	quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9,
	quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F,
	quaternion__ctor_m354F09C0E50CA59DA43037E9993EAE9BF97E9120,
	quaternion__ctor_m7A01BD711C8220B01414BCF7DCAC2D8195FB351F,
	quaternion_AxisAngle_mAFB418AC3B01E1E6360BD0603EB9DF36F4DDA986,
	quaternion_LookRotationSafe_m66E9BBE50A4E0EF220CEE80EA248687D34F8D687,
	quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE,
	quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20,
	quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0,
	quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8,
	quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349,
	quaternion__cctor_mB4B1769F995B8CB2E48C997119A490F8AEBA2239,
	uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF,
	uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD,
	uint3_op_Addition_mD11BEB362388E20A02C6D431C2ED912807585589,
	uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81,
	uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D,
	uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D,
	uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633,
	uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE,
	uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008,
	uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F,
	uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3,
	uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF,
	uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED,
	uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652,
	uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F,
	uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875,
	uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573,
	uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889,
	uint4_set_Item_m3DACDC36A353380F6DB544991D4582B5E43038F3,
	uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB,
	uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386,
	uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A,
	uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B,
	uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3,
};
extern void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_AdjustorThunk (void);
extern void bool3_Equals_m2833AB3E744AA81CE8592FE6C48EDDD482AC4C02_AdjustorThunk (void);
extern void bool3_Equals_m77F48CEBC0BBCF1C3C06B77425E6BEA7211D78CE_AdjustorThunk (void);
extern void bool3_GetHashCode_m0778D184FCC8E088AA7F23443371E794920DF7CE_AdjustorThunk (void);
extern void bool3_ToString_m84A77AF48A490798E426F25748C380D59A1241F2_AdjustorThunk (void);
extern void bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_AdjustorThunk (void);
extern void bool4_Equals_m98194D94ECE5D095F38CE48A1E121646211349C3_AdjustorThunk (void);
extern void bool4_Equals_mEAC4979E78DDE744481E272861B7E006B0F2B16C_AdjustorThunk (void);
extern void bool4_GetHashCode_m9B585F39BC56C6385D8FCA6D0F56B7CE923CAB06_AdjustorThunk (void);
extern void bool4_ToString_mA83910873B6B8641DF76FC8851FEEC15DA574478_AdjustorThunk (void);
extern void bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_AdjustorThunk (void);
extern void bool4x4_Equals_m745E786A24F9C3FF1C25782462EF54E176ED109F_AdjustorThunk (void);
extern void bool4x4_Equals_m3E1EB27A6285FA42CE1F23B97D0682394C330B39_AdjustorThunk (void);
extern void bool4x4_GetHashCode_mA50C07231889386E2FBFE6F047A3F8CB01E113B9_AdjustorThunk (void);
extern void bool4x4_ToString_m60F42991E4A80899D5F5E91B1E6A96E08F60C896_AdjustorThunk (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk (void);
extern void float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4_AdjustorThunk (void);
extern void float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_AdjustorThunk (void);
extern void float3_get_Item_mBDC42AE198E1173A9323D7C9909ACFD5B1851D26_AdjustorThunk (void);
extern void float3_set_Item_m73150115008953B6661619E909C7781F198659DF_AdjustorThunk (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk (void);
extern void float3x3__ctor_mA652DC011B892B36A8216646B51B2014F89CE93E_AdjustorThunk (void);
extern void float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED_AdjustorThunk (void);
extern void float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0_AdjustorThunk (void);
extern void float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA_AdjustorThunk (void);
extern void float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705_AdjustorThunk (void);
extern void float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934_AdjustorThunk (void);
extern void float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522_AdjustorThunk (void);
extern void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_AdjustorThunk (void);
extern void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_AdjustorThunk (void);
extern void float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C_AdjustorThunk (void);
extern void float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD_AdjustorThunk (void);
extern void float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223_AdjustorThunk (void);
extern void float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E_AdjustorThunk (void);
extern void float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B_AdjustorThunk (void);
extern void float4_get_zwxy_m8F8007FD25E49C0644615EABA414DA7672794563_AdjustorThunk (void);
extern void float4_get_wzyx_mA9531BFB41E8CC9E8070F8C4A1C23BCAAD7648E8_AdjustorThunk (void);
extern void float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480_AdjustorThunk (void);
extern void float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009_AdjustorThunk (void);
extern void float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_AdjustorThunk (void);
extern void float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1_AdjustorThunk (void);
extern void float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4_AdjustorThunk (void);
extern void float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48_AdjustorThunk (void);
extern void float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA_AdjustorThunk (void);
extern void float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A_AdjustorThunk (void);
extern void float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF_AdjustorThunk (void);
extern void float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5_AdjustorThunk (void);
extern void float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB_AdjustorThunk (void);
extern void float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8_AdjustorThunk (void);
extern void float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7_AdjustorThunk (void);
extern void float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584_AdjustorThunk (void);
extern void float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81_AdjustorThunk (void);
extern void quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9_AdjustorThunk (void);
extern void quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F_AdjustorThunk (void);
extern void quaternion__ctor_m354F09C0E50CA59DA43037E9993EAE9BF97E9120_AdjustorThunk (void);
extern void quaternion__ctor_m7A01BD711C8220B01414BCF7DCAC2D8195FB351F_AdjustorThunk (void);
extern void quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE_AdjustorThunk (void);
extern void quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20_AdjustorThunk (void);
extern void quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0_AdjustorThunk (void);
extern void quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8_AdjustorThunk (void);
extern void quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349_AdjustorThunk (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk (void);
extern void uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_AdjustorThunk (void);
extern void uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3_AdjustorThunk (void);
extern void uint4_set_Item_m3DACDC36A353380F6DB544991D4582B5E43038F3_AdjustorThunk (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[80] = 
{
	{ 0x06000054, bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_AdjustorThunk },
	{ 0x06000055, bool3_Equals_m2833AB3E744AA81CE8592FE6C48EDDD482AC4C02_AdjustorThunk },
	{ 0x06000056, bool3_Equals_m77F48CEBC0BBCF1C3C06B77425E6BEA7211D78CE_AdjustorThunk },
	{ 0x06000057, bool3_GetHashCode_m0778D184FCC8E088AA7F23443371E794920DF7CE_AdjustorThunk },
	{ 0x06000058, bool3_ToString_m84A77AF48A490798E426F25748C380D59A1241F2_AdjustorThunk },
	{ 0x06000059, bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_AdjustorThunk },
	{ 0x0600005A, bool4_Equals_m98194D94ECE5D095F38CE48A1E121646211349C3_AdjustorThunk },
	{ 0x0600005B, bool4_Equals_mEAC4979E78DDE744481E272861B7E006B0F2B16C_AdjustorThunk },
	{ 0x0600005C, bool4_GetHashCode_m9B585F39BC56C6385D8FCA6D0F56B7CE923CAB06_AdjustorThunk },
	{ 0x0600005D, bool4_ToString_mA83910873B6B8641DF76FC8851FEEC15DA574478_AdjustorThunk },
	{ 0x0600005E, bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_AdjustorThunk },
	{ 0x0600005F, bool4x4_Equals_m745E786A24F9C3FF1C25782462EF54E176ED109F_AdjustorThunk },
	{ 0x06000060, bool4x4_Equals_m3E1EB27A6285FA42CE1F23B97D0682394C330B39_AdjustorThunk },
	{ 0x06000061, bool4x4_GetHashCode_mA50C07231889386E2FBFE6F047A3F8CB01E113B9_AdjustorThunk },
	{ 0x06000062, bool4x4_ToString_m60F42991E4A80899D5F5E91B1E6A96E08F60C896_AdjustorThunk },
	{ 0x06000063, float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk },
	{ 0x06000064, float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4_AdjustorThunk },
	{ 0x0600006F, float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_AdjustorThunk },
	{ 0x06000070, float3_get_Item_mBDC42AE198E1173A9323D7C9909ACFD5B1851D26_AdjustorThunk },
	{ 0x06000071, float3_set_Item_m73150115008953B6661619E909C7781F198659DF_AdjustorThunk },
	{ 0x06000072, float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk },
	{ 0x06000073, float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk },
	{ 0x06000074, float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk },
	{ 0x06000075, float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk },
	{ 0x06000076, float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk },
	{ 0x06000079, float3x3__ctor_mA652DC011B892B36A8216646B51B2014F89CE93E_AdjustorThunk },
	{ 0x0600007A, float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED_AdjustorThunk },
	{ 0x0600007B, float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0_AdjustorThunk },
	{ 0x0600007C, float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA_AdjustorThunk },
	{ 0x0600007D, float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705_AdjustorThunk },
	{ 0x0600007E, float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934_AdjustorThunk },
	{ 0x0600007F, float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522_AdjustorThunk },
	{ 0x06000081, float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_AdjustorThunk },
	{ 0x06000082, float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_AdjustorThunk },
	{ 0x06000089, float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C_AdjustorThunk },
	{ 0x0600008A, float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD_AdjustorThunk },
	{ 0x0600008B, float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223_AdjustorThunk },
	{ 0x0600008C, float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E_AdjustorThunk },
	{ 0x0600008D, float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B_AdjustorThunk },
	{ 0x0600008E, float4_get_zwxy_m8F8007FD25E49C0644615EABA414DA7672794563_AdjustorThunk },
	{ 0x0600008F, float4_get_wzyx_mA9531BFB41E8CC9E8070F8C4A1C23BCAAD7648E8_AdjustorThunk },
	{ 0x06000090, float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480_AdjustorThunk },
	{ 0x06000091, float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009_AdjustorThunk },
	{ 0x06000092, float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_AdjustorThunk },
	{ 0x06000093, float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1_AdjustorThunk },
	{ 0x06000094, float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4_AdjustorThunk },
	{ 0x06000095, float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48_AdjustorThunk },
	{ 0x06000096, float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA_AdjustorThunk },
	{ 0x06000097, float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A_AdjustorThunk },
	{ 0x0600009A, float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF_AdjustorThunk },
	{ 0x0600009B, float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5_AdjustorThunk },
	{ 0x0600009D, float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB_AdjustorThunk },
	{ 0x0600009E, float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8_AdjustorThunk },
	{ 0x0600009F, float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7_AdjustorThunk },
	{ 0x060000A0, float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584_AdjustorThunk },
	{ 0x060000A1, float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81_AdjustorThunk },
	{ 0x060000A5, quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9_AdjustorThunk },
	{ 0x060000A6, quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F_AdjustorThunk },
	{ 0x060000A7, quaternion__ctor_m354F09C0E50CA59DA43037E9993EAE9BF97E9120_AdjustorThunk },
	{ 0x060000A8, quaternion__ctor_m7A01BD711C8220B01414BCF7DCAC2D8195FB351F_AdjustorThunk },
	{ 0x060000AB, quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE_AdjustorThunk },
	{ 0x060000AC, quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20_AdjustorThunk },
	{ 0x060000AD, quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0_AdjustorThunk },
	{ 0x060000AE, quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8_AdjustorThunk },
	{ 0x060000AF, quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349_AdjustorThunk },
	{ 0x060000B1, uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk },
	{ 0x060000B4, uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk },
	{ 0x060000B5, uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk },
	{ 0x060000B6, uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk },
	{ 0x060000B7, uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk },
	{ 0x060000B8, uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk },
	{ 0x060000B9, uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk },
	{ 0x060000BA, uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_AdjustorThunk },
	{ 0x060000BB, uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3_AdjustorThunk },
	{ 0x060000C3, uint4_set_Item_m3DACDC36A353380F6DB544991D4582B5E43038F3_AdjustorThunk },
	{ 0x060000C4, uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk },
	{ 0x060000C5, uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk },
	{ 0x060000C6, uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk },
	{ 0x060000C7, uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk },
	{ 0x060000C8, uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[200] = 
{
	10870,
	15913,
	15914,
	15915,
	15916,
	13741,
	11193,
	15917,
	12844,
	14886,
	15918,
	14882,
	14882,
	15919,
	15589,
	15908,
	16020,
	16023,
	15838,
	16014,
	15458,
	14186,
	14231,
	14442,
	14186,
	14231,
	14442,
	13377,
	13740,
	13149,
	13377,
	15579,
	15835,
	14450,
	14451,
	15835,
	15835,
	15835,
	15835,
	15835,
	15835,
	15835,
	14442,
	15835,
	15835,
	15835,
	16011,
	16013,
	14881,
	15843,
	15843,
	14450,
	14881,
	15468,
	15469,
	13744,
	13745,
	13739,
	13742,
	13716,
	15579,
	15592,
	15579,
	15910,
	15921,
	15922,
	16423,
	16423,
	16423,
	14884,
	16018,
	16017,
	16019,
	15844,
	14891,
	14883,
	14883,
	15920,
	13743,
	15921,
	12845,
	16021,
	15922,
	2030,
	6338,
	6166,
	10637,
	10698,
	1375,
	6339,
	6166,
	10637,
	10698,
	1602,
	6340,
	6166,
	10637,
	10698,
	2254,
	8700,
	16009,
	14881,
	14880,
	14878,
	14881,
	14881,
	14880,
	16011,
	14870,
	14869,
	10879,
	7632,
	4321,
	6341,
	6166,
	10637,
	10698,
	3533,
	15958,
	16010,
	2297,
	76,
	6342,
	6166,
	10637,
	10698,
	3533,
	16420,
	1576,
	4803,
	14888,
	14887,
	14885,
	14888,
	14888,
	14871,
	10880,
	10880,
	10880,
	10880,
	10880,
	10880,
	10880,
	10880,
	10880,
	10879,
	6343,
	6166,
	10637,
	10698,
	3533,
	16012,
	15967,
	1605,
	12,
	14872,
	6344,
	6166,
	10637,
	10698,
	3533,
	16015,
	16420,
	16016,
	1576,
	8802,
	8801,
	8803,
	14889,
	14890,
	6346,
	6166,
	10637,
	10698,
	3533,
	16420,
	2264,
	14892,
	14892,
	6347,
	6166,
	10637,
	10698,
	3533,
	1589,
	8776,
	8568,
	16022,
	14893,
	14893,
	16024,
	14893,
	14893,
	14893,
	4382,
	6348,
	6166,
	10637,
	10698,
	3533,
};
static TypeDefinitionIndex s_staticConstructorsToRunAtStartup[12] = 
{
	8082,
	8084,
	8086,
	8087,
	8089,
	8090,
	8092,
	8093,
	8094,
	8096,
	8098,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule = 
{
	"Unity.Mathematics.dll",
	200,
	s_methodPointers,
	80,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	s_staticConstructorsToRunAtStartup,
	NULL,
	NULL,
};
