﻿#include "pch-cpp.hpp"





struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2>
struct InterfaceActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct InterfaceFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};

struct Action_1_tB953813A651C365D3872C6813072676763767BA5;
struct Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C;
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A;
struct Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22;
struct Action_2_t5BCD350E28ADACED656596CC308132ED74DA0915;
struct Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA;
struct Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E;
struct Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457;
struct Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27;
struct IDictionary_2_t32BE5EB8FB339477299955B6732035BC7442094A;
struct IDictionary_2_t5C66CA874FF1BDA090CD606305BA7497929CA042;
struct IEnumerator_1_t55259DF17BC3DD8BD20C97403D960EB2B5D7121C;
struct IEnumerator_1_t43D2E4BA9246755F293DFA74F001FB1A70A648FD;
struct IEqualityComparer_1_t0C79004BFE79D9DBCE6C2250109D31D468A9A68E;
struct IList_1_t93ED4703263DD1FF8DBAB437D9860EA0A809A58F;
struct IObjectPool_1_t9AAD85B405B23EE259990FD41FB257D2FFBB330B;
struct IObjectPool_1_tE8CA6468BE6707A63684BDE52B62C116D73785BA;
struct KeyCollection_t15EEDCB8AD1B6D66B11102F6C608774BE45055C3;
struct List_1_t6BACD6D6792D7239C8334896E2BBAC43E3101FA8;
struct List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53;
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3;
struct ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889;
struct ObservableList_1_tD31D4A3E725CB7E11DBB3EB586834160B5ABDD55;
struct Queue_1_tD224EE31B5C1****************************;
struct ValueCollection_t7B3B437C7076A1C527F148763DB94220402D97D7;
struct EntryU5BU5D_tE9CB9A79C3F8882201B893D94A105690122BD854;
struct AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9;
struct AccessibilityNodeU5BU5D_t7DC2918BC1F06A7C80A3715179B2FBE0012C7FEB;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152;
struct AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39;
struct AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5;
struct AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3;
struct AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IAccessibilityNotificationDispatcher_tD658A5E653A35BD981B0CA47C69660F39046A805;
struct IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF;
struct String_t;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98;
struct NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5;

IL2CPP_EXTERN_C RuntimeClass* AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CollectionPool_2_t5B361942F59C43867F72F5B0D244C2D537EC0694_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IAccessibilityNotificationDispatcher_tD658A5E653A35BD981B0CA47C69660F39046A805_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ICollection_1_t1A630FA95772D922E7E91E15B46C12790269682E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDictionary_2_t32BE5EB8FB339477299955B6732035BC7442094A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDictionary_2_t5C66CA874FF1BDA090CD606305BA7497929CA042_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerator_1_t55259DF17BC3DD8BD20C97403D960EB2B5D7121C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IList_1_t93ED4703263DD1FF8DBAB437D9860EA0A809A58F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Queue_1_tD224EE31B5C1****************************_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* CollectionPool_2_Get_mAC90788170550C49BE7E07E08DC24C6F4B1F282B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_m7DF602445450A415A55C429BDC7EA83620B8B69C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_mA0CAC0931F7997449B2A845BCC80BFF6201931D7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m8F631C235E82B173EBC9A21DA4F36945BBCFBBE7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_mA221CF61A78EDEF724FBF689237EAF6255FAB0D1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m3E320BF6EF20F80BB468E899EAD811296875BD2B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m5EA27E87D7DFA62DC75B612A6793C777A0CBC984_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_GetEnumerator_mDD55511595F3AD2D4F9E3E08EC7135C74945D4C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_get_Item_m91FF17479989BACC1C32F8EE53542CD249BFCC46_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_remove_listChanged_m9FEBF743CCBE2D9A6FC57D8F6D49D92D946CCAFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObservableList_1_remove_listChanged_mA48A54BCEB3EBD0C66E91913A0780D0ED9566AEB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PooledObject_1_System_IDisposable_Dispose_m8625488F4A366060C813FE28136D9DF4F3CE5287_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ServiceManager_StopService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mB7E9384AD001976B09B8E5709D5232EDCE16AF1E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059;;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com;;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke;
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke;;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;

struct AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t6ADC7EC6D9EFCC82440E98136352618CA1F72735 
{
};
struct Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_tE9CB9A79C3F8882201B893D94A105690122BD854* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t15EEDCB8AD1B6D66B11102F6C608774BE45055C3* ____keys;
	ValueCollection_t7B3B437C7076A1C527F148763DB94220402D97D7* ____values;
	RuntimeObject* ____syncRoot;
};
struct List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53  : public RuntimeObject
{
	AccessibilityNodeU5BU5D_t7DC2918BC1F06A7C80A3715179B2FBE0012C7FEB* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3  : public RuntimeObject
{
	List_1_t6BACD6D6792D7239C8334896E2BBAC43E3101FA8* ___m_Items;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___listChanged;
};
struct ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889  : public RuntimeObject
{
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* ___m_Items;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___listChanged;
};
struct Queue_1_tD224EE31B5C1****************************  : public RuntimeObject
{
	NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* ____array;
	int32_t ____head;
	int32_t ____tail;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5  : public RuntimeObject
{
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* ___m_RootNodes;
	RuntimeObject* ___m_Nodes;
};
struct AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3  : public RuntimeObject
{
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* ___m_Hierarchy;
};
struct AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8  : public RuntimeObject
{
};
struct AccessibilityNodeManager_t288E7EFA87F883D55909ACD8A76492B215EB84C3  : public RuntimeObject
{
};
struct AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49  : public RuntimeObject
{
};
struct AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF  : public RuntimeObject
{
	RuntimeObject* ___m_Services;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98  : public RuntimeObject
{
	bool ___m_Disposed;
};
struct NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5  : public RuntimeObject
{
};
struct Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F 
{
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* ____list;
	int32_t ____index;
	int32_t ____version;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ____current;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE 
{
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___m_ToReturn;
	RuntimeObject* ___m_Pool;
};
struct PooledObject_1_tAA91CAE93DC8A19E0A5B6C1D78C3AE149F635F8E 
{
	RuntimeObject* ___m_ToReturn;
	RuntimeObject* ___m_Pool;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E 
{
	void* ___begin;
	int32_t ___length;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct ByReference_1_tDDF129F0BC02430629D5CD253C681112F166BAD4 
{
	intptr_t ____value;
};
struct AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___U3CactivatedU3Ek__BackingField;
};
struct AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___U3CactivatedU3Ek__BackingField;
};
struct AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_com
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___U3CactivatedU3Ek__BackingField;
};
struct AccessibilityNotification_t3275B813A36874907C1B64627F8C8F8A9A6C77F4 
{
	int32_t ___value__;
};
struct AccessibilityRole_tE81C1DB448A52DE82A7028F7E363D33A14F929C1 
{
	uint16_t ___value__;
};
struct AccessibilityState_t3EAB273904DC3E524AAD83A4CB285981C0E1ECF0 
{
	uint16_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct SystemLanguage_tDEDD64D7BFC2D67B538432ECAF8018FDCABAFD9E 
{
	int32_t ___value__;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0  : public RuntimeObject
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_pinvoke
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_com
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316 
{
	ByReference_1_tDDF129F0BC02430629D5CD253C681112F166BAD4 ____pointer;
	int32_t ____length;
};
struct AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A  : public RuntimeObject
{
	int32_t ___U3CidU3Ek__BackingField;
	Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* ___m_FrameGetter;
	Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* ___focusChanged;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___selected;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___incremented;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___decremented;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___dismissed;
	String_t* ___m_Label;
	String_t* ___m_Value;
	String_t* ___m_Hint;
	bool ___m_IsActive;
	uint16_t ___m_Role;
	bool ___m_AllowsDirectInteraction;
	uint16_t ___m_State;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___m_Parent;
	ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* ___m_Children;
	ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* ___m_Actions;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___m_Frame;
	int32_t ___m_Language;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* ___m_Hierarchy;
};
struct AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115 
{
	int32_t ___U3CidU3Ek__BackingField;
	bool ___U3CisActiveU3Ek__BackingField;
	String_t* ___U3ClabelU3Ek__BackingField;
	String_t* ___U3CvalueU3Ek__BackingField;
	String_t* ___U3ChintU3Ek__BackingField;
	uint16_t ___U3CroleU3Ek__BackingField;
	bool ___U3CallowsDirectInteractionU3Ek__BackingField;
	uint16_t ___U3CstateU3Ek__BackingField;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___U3CframeU3Ek__BackingField;
	int32_t ___U3CparentIdU3Ek__BackingField;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___U3CchildIdsU3Ek__BackingField;
	bool ___U3CisFocusedU3Ek__BackingField;
	int32_t ___U3ClanguageU3Ek__BackingField;
	bool ___U3CimplementsSelectedU3Ek__BackingField;
	bool ___U3CimplementsDismissedU3Ek__BackingField;
};
struct AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_pinvoke
{
	int32_t ___U3CidU3Ek__BackingField;
	int32_t ___U3CisActiveU3Ek__BackingField;
	char* ___U3ClabelU3Ek__BackingField;
	char* ___U3CvalueU3Ek__BackingField;
	char* ___U3ChintU3Ek__BackingField;
	uint16_t ___U3CroleU3Ek__BackingField;
	int32_t ___U3CallowsDirectInteractionU3Ek__BackingField;
	uint16_t ___U3CstateU3Ek__BackingField;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___U3CframeU3Ek__BackingField;
	int32_t ___U3CparentIdU3Ek__BackingField;
	Il2CppSafeArray* ___U3CchildIdsU3Ek__BackingField;
	int32_t ___U3CisFocusedU3Ek__BackingField;
	int32_t ___U3ClanguageU3Ek__BackingField;
	int32_t ___U3CimplementsSelectedU3Ek__BackingField;
	int32_t ___U3CimplementsDismissedU3Ek__BackingField;
};
struct AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_com
{
	int32_t ___U3CidU3Ek__BackingField;
	int32_t ___U3CisActiveU3Ek__BackingField;
	Il2CppChar* ___U3ClabelU3Ek__BackingField;
	Il2CppChar* ___U3CvalueU3Ek__BackingField;
	Il2CppChar* ___U3ChintU3Ek__BackingField;
	uint16_t ___U3CroleU3Ek__BackingField;
	int32_t ___U3CallowsDirectInteractionU3Ek__BackingField;
	uint16_t ___U3CstateU3Ek__BackingField;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___U3CframeU3Ek__BackingField;
	int32_t ___U3CparentIdU3Ek__BackingField;
	Il2CppSafeArray* ___U3CchildIdsU3Ek__BackingField;
	int32_t ___U3CisFocusedU3Ek__BackingField;
	int32_t ___U3ClanguageU3Ek__BackingField;
	int32_t ___U3CimplementsSelectedU3Ek__BackingField;
	int32_t ___U3CimplementsDismissedU3Ek__BackingField;
};
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	bool ___U3CisScreenReaderEnabledU3Ek__BackingField;
	String_t* ___U3CannouncementU3Ek__BackingField;
	bool ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	int32_t ___U3CcurrentNodeIdU3Ek__BackingField;
	int32_t ___U3CnextNodeIdU3Ek__BackingField;
};
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	int32_t ___U3CisScreenReaderEnabledU3Ek__BackingField;
	char* ___U3CannouncementU3Ek__BackingField;
	int32_t ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	int32_t ___U3CcurrentNodeIdU3Ek__BackingField;
	int32_t ___U3CnextNodeIdU3Ek__BackingField;
};
struct AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	int32_t ___U3CisScreenReaderEnabledU3Ek__BackingField;
	Il2CppChar* ___U3CannouncementU3Ek__BackingField;
	int32_t ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	int32_t ___U3CcurrentNodeIdU3Ek__BackingField;
	int32_t ___U3CnextNodeIdU3Ek__BackingField;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct Action_1_tB953813A651C365D3872C6813072676763767BA5  : public MulticastDelegate_t
{
};
struct Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C  : public MulticastDelegate_t
{
};
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87  : public MulticastDelegate_t
{
};
struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A  : public MulticastDelegate_t
{
};
struct Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22  : public MulticastDelegate_t
{
};
struct Action_2_t5BCD350E28ADACED656596CC308132ED74DA0915  : public MulticastDelegate_t
{
};
struct Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457  : public MulticastDelegate_t
{
};
struct Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27  : public MulticastDelegate_t
{
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A 
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	bool ___U3CisScreenReaderEnabledU3Ek__BackingField;
	String_t* ___U3CannouncementU3Ek__BackingField;
	bool ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CcurrentNodeU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CnextNodeU3Ek__BackingField;
	float ___U3CfontScaleU3Ek__BackingField;
	bool ___U3CisBoldTextEnabledU3Ek__BackingField;
	bool ___U3CisClosedCaptioningEnabledU3Ek__BackingField;
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 ___U3CnativeContextU3Ek__BackingField;
};
struct NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_pinvoke
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	int32_t ___U3CisScreenReaderEnabledU3Ek__BackingField;
	char* ___U3CannouncementU3Ek__BackingField;
	int32_t ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CcurrentNodeU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CnextNodeU3Ek__BackingField;
	float ___U3CfontScaleU3Ek__BackingField;
	int32_t ___U3CisBoldTextEnabledU3Ek__BackingField;
	int32_t ___U3CisClosedCaptioningEnabledU3Ek__BackingField;
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke ___U3CnativeContextU3Ek__BackingField;
};
struct NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_com
{
	int32_t ___U3CnotificationU3Ek__BackingField;
	int32_t ___U3CisScreenReaderEnabledU3Ek__BackingField;
	Il2CppChar* ___U3CannouncementU3Ek__BackingField;
	int32_t ___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CcurrentNodeU3Ek__BackingField;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___U3CnextNodeU3Ek__BackingField;
	float ___U3CfontScaleU3Ek__BackingField;
	int32_t ___U3CisBoldTextEnabledU3Ek__BackingField;
	int32_t ___U3CisClosedCaptioningEnabledU3Ek__BackingField;
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com ___U3CnativeContextU3Ek__BackingField;
};
struct List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53_StaticFields
{
	AccessibilityNodeU5BU5D_t7DC2918BC1F06A7C80A3715179B2FBE0012C7FEB* ___s_emptyArray;
};
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields
{
	Queue_1_tD224EE31B5C1***************************** ___s_AsyncNotificationContexts;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___screenReaderStatusChanged;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* ___nodeFocusChanged;
};
struct AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_StaticFields
{
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___fontScaleChanged;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___boldTextStatusChanged;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___closedCaptioningStatusChanged;
};
struct AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields
{
	Action_1_tB953813A651C365D3872C6813072676763767BA5* ___nodeFocusChanged;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___screenReaderStatusChanged;
	bool ___U3CisScreenReaderEnabledU3Ek__BackingField;
	RuntimeObject* ___U3CnotificationDispatcherU3Ek__BackingField;
	ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* ___s_ServiceManager;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152  : public RuntimeArray
{
	ALIGN_FIELD (8) NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A m_Items[1];

	inline NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CcurrentNodeU3Ek__BackingField), (void*)NULL);
		#endif
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CnextNodeU3Ek__BackingField), (void*)NULL);
		#endif
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((&((m_Items + index)->___U3CnativeContextU3Ek__BackingField))->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		#endif
	}
	inline NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CcurrentNodeU3Ek__BackingField), (void*)NULL);
		#endif
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CnextNodeU3Ek__BackingField), (void*)NULL);
		#endif
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((&((m_Items + index)->___U3CnativeContextU3Ek__BackingField))->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		#endif
	}
};
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9  : public RuntimeArray
{
	ALIGN_FIELD (8) AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* m_Items[1];

	inline AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};

IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled);
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke_back(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled);
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke_cleanup(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled);
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled);
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com_back(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled);
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com_cleanup(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled);

IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_gshared_inline (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805_gshared (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C_gshared (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_gshared_inline (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* __this, bool ___0_obj, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AssistiveSupport_GetService_TisRuntimeObject_m6794BFDFCCF9DE3D55ACBEE780E5DAA1B51DC524_gshared (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PooledObject_1_tAA91CAE93DC8A19E0A5B6C1D78C3AE149F635F8E CollectionPool_2_Get_m06A331A0FEF3608767F8D992F3DCC1A1984106E4_gshared (RuntimeObject** ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PooledObject_1_System_IDisposable_Dispose_mBDBE6E4606DF5793230E351CA0B89611C13606FC_gshared (PooledObject_1_tAA91CAE93DC8A19E0A5B6C1D78C3AE149F635F8E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_gshared_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, int32_t ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_gshared_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA_gshared (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377_gshared (Queue_1_tD224EE31B5C1***************************** __this, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559_gshared (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_gshared_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_array, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t* Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811_gshared (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_gshared_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mA8C3AC97D1F076EA5D1D0C10CEE6BD3E94711501_gshared (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ObservableList_1_GetEnumerator_m7722D49C0B97A47DBB0BC784F78875F7CDBBABA5_gshared (ObservableList_1_tD31D4A3E725CB7E11DBB3EB586834160B5ABDD55* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObservableList_1_remove_listChanged_mD4F793DEBAFF3AA1E938F4E13FE5B47F7CC18883_gshared (ObservableList_1_tD31D4A3E725CB7E11DBB3EB586834160B5ABDD55* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Func_1_Invoke_mB159D2E8978B240577A1C7A203FACF1B0F811011_gshared_inline (Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ObservableList_1_get_Count_m95A315B5DD265A0D488829CC68EF92C9B9B6E9EB_gshared (ObservableList_1_tD31D4A3E725CB7E11DBB3EB586834160B5ABDD55* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ObservableList_1_get_Item_m4113D672D480C0CD4C8D85FC1933CB60DC569F39_gshared (ObservableList_1_tD31D4A3E725CB7E11DBB3EB586834160B5ABDD55* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_mB2DD87F61EB655A33F6277F1E277246CE23B6625_gshared_inline (Action_2_t5BCD350E28ADACED656596CC308132ED74DA0915* __this, RuntimeObject* ___0_arg1, bool ___1_arg2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager_StopService_TisRuntimeObject_mEC39D4B04053482BE39A07BB6ABFAECF9464D139_gshared (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m378B392086AAB6F400944FA9839516326B3F7BB8_gshared (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, int32_t ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityAction_Dispose_m47E2DCB64AF8193F2A225433524603DC0A729254 (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, bool ___0_disposing, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GC_SuppressFinalize_m71815DBD5A0CD2EA1BE43317B08B7A14949EDC65 (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B_inline (intptr_t ___0_value1, intptr_t ___1_value2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94 (intptr_t ___0_ptr, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281_inline (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, const RuntimeMethod* method) ;
inline bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*, const RuntimeMethod*))Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport_Initialize_mDB3A5CE4DA477AF876D3A1DAB0577B559CEA1063 (const RuntimeMethod* method) ;
inline int32_t Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_inline (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Queue_1_tD224EE31B5C1*****************************, const RuntimeMethod*))Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149 (RuntimeObject* ___0_obj, bool* ___1_lockTaken, const RuntimeMethod* method) ;
inline NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805 (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method)
{
	return ((  NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* (*) (Queue_1_tD224EE31B5C1*****************************, const RuntimeMethod*))Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805_gshared)(__this, method);
}
inline void Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_tD224EE31B5C1*****************************, const RuntimeMethod*))Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AccessibilityManager_GetExclusiveLock_m81EB3F3F7018C08A22AE25AB3554AA1EF02EC2C7 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_inline (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* __this, bool ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*, bool, const RuntimeMethod*))Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_isNodeFocused, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m7C5AFCE416A088DEAF3A2C7AC190C5D3E3F02EAB_inline (Action_1_tB953813A651C365D3872C6813072676763767BA5* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tB953813A651C365D3872C6813072676763767BA5*, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeFontScaleChanged_m01DE529AC72A0FBE344F62C7E03FF028E7C196D0 (float ___0_newFontScale, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeBoldTextStatusChanged_mE3FAB90ED74180EF706A8AA160628EB14C202E7D (bool ___0_enabled, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeClosedCaptionStatusChanged_m501523ED4BB118A6FE6370D1C617ADDB007EE8A4 (bool ___0_enabled, const RuntimeMethod* method) ;
inline AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42 (const RuntimeMethod* method)
{
	return ((  AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* (*) (const RuntimeMethod*))AssistiveSupport_GetService_TisRuntimeObject_m6794BFDFCCF9DE3D55ACBEE780E5DAA1B51DC524_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_inline (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE CollectionPool_2_Get_mAC90788170550C49BE7E07E08DC24C6F4B1F282B (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73** ___0_value, const RuntimeMethod* method)
{
	return ((  PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE (*) (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73**, const RuntimeMethod*))CollectionPool_2_Get_m06A331A0FEF3608767F8D992F3DCC1A1984106E4_gshared)(___0_value, method);
}
inline void PooledObject_1_System_IDisposable_Dispose_m8625488F4A366060C813FE28136D9DF4F3CE5287 (PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE* __this, const RuntimeMethod* method)
{
	((  void (*) (PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE*, const RuntimeMethod*))PooledObject_1_System_IDisposable_Dispose_mBDBE6E4606DF5793230E351CA0B89611C13606FC_gshared)(__this, method);
}
inline AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* List_1_get_Item_m5EA27E87D7DFA62DC75B612A6793C777A0CBC984 (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* (*) (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
inline void List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, int32_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73*, int32_t, const RuntimeMethod*))List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_gshared_inline)(__this, ___0_item, method);
}
inline int32_t List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73*, const RuntimeMethod*))List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_gshared_inline)(__this, method);
}
inline Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, const RuntimeMethod* method)
{
	return ((  Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* (*) (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73*, const RuntimeMethod*))List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, int32_t ___0_id, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___1_node, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_GetNodeData_m6C7765891167AB6E820D68F4AF89F891F03ACE2C (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* ___0_nodeData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchyService_TryGetNodeAt_mBEDA9E8FBF12970339369F80650637BE17E2280F (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, float ___0_x, float ___1_y, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___2_node, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_nativeNotification, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A ___0_notification, const RuntimeMethod* method) ;
inline void Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377 (Queue_1_tD224EE31B5C1***************************** __this, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A ___0_item, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_tD224EE31B5C1*****************************, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A, const RuntimeMethod*))Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377_gshared)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock__ctor_m875188F2E41DC8113A37A71CE4DC8B6707D265F5 (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) ;
inline void Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559 (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_tD224EE31B5C1*****************************, const RuntimeMethod*))Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, int32_t ___0_id, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___1_node, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Finalize_mC98C96301CCABFE00F1A7EF8E15DF507CACD42B2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3 (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0 (int32_t ___0_id, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* ___1_frame, const RuntimeMethod* method) ;
inline void Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_array, const RuntimeMethod* method)
{
	((  void (*) (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316*, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*, const RuntimeMethod*))Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_gshared_inline)(__this, ___0_array, method);
}
inline int32_t* Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811 (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, const RuntimeMethod* method)
{
	return ((  int32_t* (*) (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316*, const RuntimeMethod*))Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811_gshared)(__this, method);
}
inline int32_t Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316*, const RuntimeMethod*))Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ManagedSpanWrapper__ctor_mB29647A21BB87EA4DF859E5C2FA2207F47E525D2 (ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E* __this, void* ___0_begin, int32_t ___1_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8 (int32_t ___0_id, ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E* ___1_childIds, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_NotifyFocusChanged_mEA14C3D3534CB0920ECD5AFF32350855BC6FD5E8 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_isNodeFocused, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_InvokeSelected_mBB094C8957C71FBCA0C033A4F9EE7127B8D1A7BA (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeIncremented_mC2B5AF750A1BC3177E92787DAA385360234B59AF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeDecremented_m3BF11CFA927B9303367B46C9EA7E0E99A5C422D5 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_Dismissed_m7492D9E72948C6718A13A2FDB628BDC4E81091EF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) ;
inline void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A*, float, const RuntimeMethod*))Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340_inline (bool ___0_value, const RuntimeMethod* method) ;
inline void Action_1__ctor_mA8C3AC97D1F076EA5D1D0C10CEE6BD3E94711501 (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mA8C3AC97D1F076EA5D1D0C10CEE6BD3E94711501_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696 (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___0_value, const RuntimeMethod* method) ;
inline void Action_1__ctor_mE888483BBBD9EFB1BAAEF97D4FEB78FC03F6DDDD (Action_1_tB953813A651C365D3872C6813072676763767BA5* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tB953813A651C365D3872C6813072676763767BA5*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_add_nodeFocusChanged_m5BFCE49D472600D1544C25A28931D8FBCD032CFA (Action_1_tB953813A651C365D3872C6813072676763767BA5* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager__ctor_m06C1D1E60CF676E1E526A3E281757604BA5C9F3A (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947_inline (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationDispatcher__ctor_m4C2107C7F72DA9205175A3D3F5832F328E548F68 (NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_context, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationDispatcher_Send_m44FE72134E006FF63A7E6A0B57629775C5ABE1FE (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_context, const RuntimeMethod* method) ;
inline Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F List_1_GetEnumerator_m3E320BF6EF20F80BB468E899EAD811296875BD2B (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F (*) (List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_mA0CAC0931F7997449B2A845BCC80BFF6201931D7 (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* Enumerator_get_Current_mA221CF61A78EDEF724FBF689237EAF6255FAB0D1_inline (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F* __this, const RuntimeMethod* method)
{
	return ((  AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* (*) (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_freeChildren, const RuntimeMethod* method) ;
inline bool Enumerator_MoveNext_m8F631C235E82B173EBC9A21DA4F36945BBCFBBE7 (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368 (RuntimeObject* ___0_nodes, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_pos, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_point, const RuntimeMethod* method) ;
inline RuntimeObject* ObservableList_1_GetEnumerator_mDD55511595F3AD2D4F9E3E08EC7135C74945D4C7 (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* __this, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889*, const RuntimeMethod*))ObservableList_1_GetEnumerator_m7722D49C0B97A47DBB0BC784F78875F7CDBBABA5_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
inline void ObservableList_1_remove_listChanged_m9FEBF743CCBE2D9A6FC57D8F6D49D92D946CCAFF (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method)
{
	((  void (*) (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889*, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*, const RuntimeMethod*))ObservableList_1_remove_listChanged_mD4F793DEBAFF3AA1E938F4E13FE5B47F7CC18883_gshared)(__this, ___0_value, method);
}
inline void ObservableList_1_remove_listChanged_mA48A54BCEB3EBD0C66E91913A0780D0ED9566AEB (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method)
{
	((  void (*) (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3*, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*, const RuntimeMethod*))ObservableList_1_remove_listChanged_mD4F793DEBAFF3AA1E938F4E13FE5B47F7CC18883_gshared)(__this, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43 (int32_t ___0_id, int32_t ___1_parentId, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_lhs, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_CalculateFrame_m0CD6C0D1DB2539DC74CDE281FAC23058D191E054 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetFrame_mE47923B10A1592690CEF29D975C2C22654AA6E0A (int32_t ___0_id, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___1_frame, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Rect_get_zero_m5341D8B63DEF1F4C308A685EEC8CFEA12A396C8D (const RuntimeMethod* method) ;
inline Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Func_1_Invoke_mB159D2E8978B240577A1C7A203FACF1B0F811011_inline (Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* __this, const RuntimeMethod* method)
{
	return ((  Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D (*) (Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27*, const RuntimeMethod*))Func_1_Invoke_mB159D2E8978B240577A1C7A203FACF1B0F811011_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_SetFrame_mF216E2796C4A53212FD410C58C9734293FB5396B (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_frame, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
inline int32_t ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125 (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889*, const RuntimeMethod*))ObservableList_1_get_Count_m95A315B5DD265A0D488829CC68EF92C9B9B6E9EB_gshared)(__this, method);
}
inline AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* (*) (ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889*, int32_t, const RuntimeMethod*))ObservableList_1_get_Item_m4113D672D480C0CD4C8D85FC1933CB60DC569F39_gshared)(__this, ___0_index, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetChildren_mE43C7A8C5CDC9D8049088AC1A89E0BDC4015C7C9 (int32_t ___0_id, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_childIds, const RuntimeMethod* method) ;
inline int32_t ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3*, const RuntimeMethod*))ObservableList_1_get_Count_m95A315B5DD265A0D488829CC68EF92C9B9B6E9EB_gshared)(__this, method);
}
inline AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* ObservableList_1_get_Item_m91FF17479989BACC1C32F8EE53542CD249BFCC46 (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* (*) (ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3*, int32_t, const RuntimeMethod*))ObservableList_1_get_Item_m4113D672D480C0CD4C8D85FC1933CB60DC569F39_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B (int32_t ___0_id, AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* ___1_actions, const RuntimeMethod* method) ;
inline void Action_2_Invoke_m30CDE1DE68599A78DD318994DC64A3BE51FC7081_inline (Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_arg1, bool ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22*, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*, bool, const RuntimeMethod*))Action_2_Invoke_mB2DD87F61EB655A33F6277F1E277246CE23B6625_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService_RemoveActiveHierarchy_m61D66D2B65B8E17B08948962B227203C35DF5A3A (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, bool ___0_notifyScreenChanged, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchy_FreeNative_m335828EAF3E5F150C16550316584F71CCAFB2758 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchy_TryGetNodeAt_m22C5D534645D8EFC73811373C2050E7E775F1990 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, float ___0_horizontalPosition, float ___1_verticalPosition, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___2_node, const RuntimeMethod* method) ;
inline void Dictionary_2__ctor_m7DF602445450A415A55C429BDC7EA83620B8B69C (Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E*, const RuntimeMethod*))Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582 (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, bool ___0_isScreenReaderEnabled, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService__ctor_mE2F69255846F000B58E412306376C09C02DB276B (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService_Start_mEC19D7908745553B772EC14E4AF0CE4492135B67 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) ;
inline void ServiceManager_StopService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mB7E9384AD001976B09B8E5709D5232EDCE16AF1E (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, const RuntimeMethod* method)
{
	((  void (*) (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF*, const RuntimeMethod*))ServiceManager_StopService_TisRuntimeObject_mEC39D4B04053482BE39A07BB6ABFAECF9464D139_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m378B392086AAB6F400944FA9839516326B3F7BB8 (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, int32_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73*, int32_t, const RuntimeMethod*))List_1_AddWithResize_m378B392086AAB6F400944FA9839516326B3F7BB8_gshared)(__this, ___0_item, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_pinvoke(const AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39& unmarshaled, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_pinvoke& marshaled)
{
	marshaled.___m_Ptr = unmarshaled.___m_Ptr;
	marshaled.___U3CactivatedU3Ek__BackingField = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.___U3CactivatedU3Ek__BackingField));
}
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_pinvoke_back(const AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_pinvoke& marshaled, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaledm_Ptr_temp_0;
	memset((&unmarshaledm_Ptr_temp_0), 0, sizeof(unmarshaledm_Ptr_temp_0));
	unmarshaledm_Ptr_temp_0 = marshaled.___m_Ptr;
	unmarshaled.___m_Ptr = unmarshaledm_Ptr_temp_0;
	unmarshaled.___U3CactivatedU3Ek__BackingField = il2cpp_codegen_marshal_function_ptr_to_delegate<Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457>(marshaled.___U3CactivatedU3Ek__BackingField, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CactivatedU3Ek__BackingField), (void*)il2cpp_codegen_marshal_function_ptr_to_delegate<Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457>(marshaled.___U3CactivatedU3Ek__BackingField, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var));
}
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_pinvoke_cleanup(AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_com(const AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39& unmarshaled, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_com& marshaled)
{
	marshaled.___m_Ptr = unmarshaled.___m_Ptr;
	marshaled.___U3CactivatedU3Ek__BackingField = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.___U3CactivatedU3Ek__BackingField));
}
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_com_back(const AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_com& marshaled, AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaledm_Ptr_temp_0;
	memset((&unmarshaledm_Ptr_temp_0), 0, sizeof(unmarshaledm_Ptr_temp_0));
	unmarshaledm_Ptr_temp_0 = marshaled.___m_Ptr;
	unmarshaled.___m_Ptr = unmarshaledm_Ptr_temp_0;
	unmarshaled.___U3CactivatedU3Ek__BackingField = il2cpp_codegen_marshal_function_ptr_to_delegate<Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457>(marshaled.___U3CactivatedU3Ek__BackingField, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CactivatedU3Ek__BackingField), (void*)il2cpp_codegen_marshal_function_ptr_to_delegate<Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457>(marshaled.___U3CactivatedU3Ek__BackingField, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457_il2cpp_TypeInfo_var));
}
IL2CPP_EXTERN_C void AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshal_com_cleanup(AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityAction_Dispose_m7C7E356F1EF00E9B23CAEFA9291D5FBBE78A8907 (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AccessibilityAction_Dispose_m47E2DCB64AF8193F2A225433524603DC0A729254(__this, (bool)1, NULL);
		il2cpp_codegen_runtime_class_init_inline(GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		GC_SuppressFinalize_m71815DBD5A0CD2EA1BE43317B08B7A14949EDC65(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityAction_Dispose_m47E2DCB64AF8193F2A225433524603DC0A729254 (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, bool ___0_disposing, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		intptr_t L_0 = __this->___m_Ptr;
		bool L_1;
		L_1 = IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B_inline(L_0, 0, NULL);
		V_0 = L_1;
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_002e;
		}
	}
	{
		intptr_t L_3 = __this->___m_Ptr;
		AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94(L_3, NULL);
		__this->___m_Ptr = 0;
	}

IL_002e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94 (intptr_t ___0_ptr, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94_ftn) (intptr_t);
	static AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityAction::Internal_Destroy(System.IntPtr)");
	_il2cpp_icall_func(___0_ptr);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281 (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, const RuntimeMethod* method) 
{
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = __this->___U3CactivatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityAction_Internal_InvokeActivated_mA861809EB68DD92BD8A00831D8CA83A5CDA4888A (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0;
		L_0 = AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281_inline(__this, NULL);
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_1;
		L_1 = AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281_inline(__this, NULL);
		NullCheck(L_1);
		bool L_2;
		L_2 = Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline(L_1, NULL);
		G_B3_0 = ((int32_t)(L_2));
		goto IL_0017;
	}

IL_0016:
	{
		G_B3_0 = 0;
	}

IL_0017:
	{
		V_0 = (bool)G_B3_0;
		goto IL_001a;
	}

IL_001a:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696 (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_1 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_2 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___screenReaderStatusChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_2 = V_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)Castclass((RuntimeObject*)L_4, Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var));
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_5 = V_2;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_6 = V_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*>((&((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___screenReaderStatusChanged), L_5, L_6);
		V_0 = L_7;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_8 = V_0;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)L_8) == ((RuntimeObject*)(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_remove_screenReaderStatusChanged_m2D03E603E74E4418CBA1C91C107AAB82ACABB010 (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_1 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* V_2 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___screenReaderStatusChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_2 = V_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)Castclass((RuntimeObject*)L_4, Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var));
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_5 = V_2;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_6 = V_1;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*>((&((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___screenReaderStatusChanged), L_5, L_6);
		V_0 = L_7;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_8 = V_0;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)L_8) == ((RuntimeObject*)(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_add_nodeFocusChanged_m5BFCE49D472600D1544C25A28931D8FBCD032CFA (Action_1_tB953813A651C365D3872C6813072676763767BA5* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_0 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_1 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_2 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___nodeFocusChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_2 = V_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_tB953813A651C365D3872C6813072676763767BA5*)Castclass((RuntimeObject*)L_4, Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var));
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_5 = V_2;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_6 = V_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_tB953813A651C365D3872C6813072676763767BA5*>((&((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___nodeFocusChanged), L_5, L_6);
		V_0 = L_7;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_8 = V_0;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB953813A651C365D3872C6813072676763767BA5*)L_8) == ((RuntimeObject*)(Action_1_tB953813A651C365D3872C6813072676763767BA5*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_remove_nodeFocusChanged_mE608913E1DAAD6F32EA974A9B26E66E6EE0C2203 (Action_1_tB953813A651C365D3872C6813072676763767BA5* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_0 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_1 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* V_2 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___nodeFocusChanged;
		V_0 = L_0;
	}

IL_0006:
	{
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_2 = V_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_tB953813A651C365D3872C6813072676763767BA5*)Castclass((RuntimeObject*)L_4, Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var));
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_5 = V_2;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_6 = V_1;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_tB953813A651C365D3872C6813072676763767BA5*>((&((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___nodeFocusChanged), L_5, L_6);
		V_0 = L_7;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_8 = V_0;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB953813A651C365D3872C6813072676763767BA5*)L_8) == ((RuntimeObject*)(Action_1_tB953813A651C365D3872C6813072676763767BA5*)L_9))))
		{
			goto IL_0006;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2 (const RuntimeMethod* method) 
{
	typedef bool (*AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2_ftn) ();
	static AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityManager::IsScreenReaderEnabled()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_context, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0_ftn) (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*);
	static AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityManager::SendAccessibilityNotification(UnityEngine.Accessibility.AccessibilityNotificationContext&)");
	_il2cpp_icall_func(___0_context);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Internal_Initialize_mBCC892503DA42AF7A8AD91918E0C5BB04DF62B84 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AssistiveSupport_Initialize_mDB3A5CE4DA477AF876D3A1DAB0577B559CEA1063(NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Internal_Update_m14966B56AEA0950BC940AB264999BCC172EE1DCE (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* V_0 = NULL;
	RuntimeObject* V_1 = NULL;
	bool V_2 = false;
	Queue_1_tD224EE31B5C1***************************** V_3 = NULL;
	bool V_4 = false;
	bool V_5 = false;
	NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* V_6 = NULL;
	int32_t V_7 = 0;
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A V_8;
	memset((&V_8), 0, sizeof(V_8));
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B15_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B14_0 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* G_B19_0 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* G_B18_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Queue_1_tD224EE31B5C1***************************** L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_inline(L_0, Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_RuntimeMethod_var);
		V_2 = (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		goto IL_0168;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Queue_1_tD224EE31B5C1***************************** L_3 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
		V_3 = L_3;
		V_4 = (bool)0;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_005b:
			{
				{
					bool L_4 = V_4;
					if (!L_4)
					{
						goto IL_0066;
					}
				}
				{
					Queue_1_tD224EE31B5C1***************************** L_5 = V_3;
					Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA(L_5, NULL);
				}

IL_0066:
				{
					return;
				}
			}
		});
		try
		{
			{
				Queue_1_tD224EE31B5C1***************************** L_6 = V_3;
				Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149(L_6, (&V_4), NULL);
				il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
				Queue_1_tD224EE31B5C1***************************** L_7 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
				NullCheck(L_7);
				int32_t L_8;
				L_8 = Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_inline(L_7, Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_RuntimeMethod_var);
				V_5 = (bool)((((int32_t)L_8) == ((int32_t)0))? 1 : 0);
				bool L_9 = V_5;
				if (!L_9)
				{
					goto IL_0042_1;
				}
			}
			{
				goto IL_0168;
			}

IL_0042_1:
			{
				il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
				Queue_1_tD224EE31B5C1***************************** L_10 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
				NullCheck(L_10);
				NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* L_11;
				L_11 = Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805(L_10, Queue_1_ToArray_m0D55F3F75F7C3F833E385896B9F9D2C3BAF5B805_RuntimeMethod_var);
				V_0 = L_11;
				Queue_1_tD224EE31B5C1***************************** L_12 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
				NullCheck(L_12);
				Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C(L_12, Queue_1_Clear_mC0664AD9450C784FED4111974E4E74294B79E87C_RuntimeMethod_var);
				goto IL_0067;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0067:
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		RuntimeObject* L_13;
		L_13 = AccessibilityManager_GetExclusiveLock_m81EB3F3F7018C08A22AE25AB3554AA1EF02EC2C7(NULL);
		V_1 = L_13;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_015d:
			{
				{
					RuntimeObject* L_14 = V_1;
					if (!L_14)
					{
						goto IL_0167;
					}
				}
				{
					RuntimeObject* L_15 = V_1;
					NullCheck(L_15);
					InterfaceActionInvoker0::Invoke(0, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var, L_15);
				}

IL_0167:
				{
					return;
				}
			}
		});
		try
		{
			{
				NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* L_16 = V_0;
				V_6 = L_16;
				V_7 = 0;
				goto IL_0150_1;
			}

IL_0079_1:
			{
				NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* L_17 = V_6;
				int32_t L_18 = V_7;
				NullCheck(L_17);
				int32_t L_19 = L_18;
				NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_20 = (L_17)->GetAt(static_cast<il2cpp_array_size_t>(L_19));
				V_8 = L_20;
				int32_t L_21;
				L_21 = NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_inline((&V_8), NULL);
				V_10 = L_21;
				int32_t L_22 = V_10;
				V_9 = L_22;
				int32_t L_23 = V_9;
				switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_23, 3)))
				{
					case 0:
					{
						goto IL_00c4_1;
					}
					case 1:
					{
						goto IL_0149_1;
					}
					case 2:
					{
						goto IL_0149_1;
					}
					case 3:
					{
						goto IL_0149_1;
					}
					case 4:
					{
						goto IL_00df_1;
					}
					case 5:
					{
						goto IL_0108_1;
					}
					case 6:
					{
						goto IL_0119_1;
					}
					case 7:
					{
						goto IL_0129_1;
					}
					case 8:
					{
						goto IL_0139_1;
					}
				}
			}
			{
				goto IL_0149_1;
			}

IL_00c4_1:
			{
				il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
				Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_24 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___screenReaderStatusChanged;
				Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_25 = L_24;
				if (L_25)
				{
					G_B15_0 = L_25;
					goto IL_00d0_1;
				}
				G_B14_0 = L_25;
			}
			{
				goto IL_00dd_1;
			}

IL_00d0_1:
			{
				bool L_26;
				L_26 = NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_inline((&V_8), NULL);
				NullCheck(G_B15_0);
				Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_inline(G_B15_0, L_26, NULL);
			}

IL_00dd_1:
			{
				goto IL_0149_1;
			}

IL_00df_1:
			{
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_27;
				L_27 = NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline((&V_8), NULL);
				NullCheck(L_27);
				AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A(L_27, (bool)1, NULL);
				il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
				Action_1_tB953813A651C365D3872C6813072676763767BA5* L_28 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___nodeFocusChanged;
				Action_1_tB953813A651C365D3872C6813072676763767BA5* L_29 = L_28;
				if (L_29)
				{
					G_B19_0 = L_29;
					goto IL_00f9_1;
				}
				G_B18_0 = L_29;
			}
			{
				goto IL_0106_1;
			}

IL_00f9_1:
			{
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_30;
				L_30 = NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline((&V_8), NULL);
				NullCheck(G_B19_0);
				Action_1_Invoke_m7C5AFCE416A088DEAF3A2C7AC190C5D3E3F02EAB_inline(G_B19_0, L_30, NULL);
			}

IL_0106_1:
			{
				goto IL_0149_1;
			}

IL_0108_1:
			{
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_31;
				L_31 = NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline((&V_8), NULL);
				NullCheck(L_31);
				AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A(L_31, (bool)0, NULL);
				goto IL_0149_1;
			}

IL_0119_1:
			{
				float L_32;
				L_32 = NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_inline((&V_8), NULL);
				AccessibilitySettings_InvokeFontScaleChanged_m01DE529AC72A0FBE344F62C7E03FF028E7C196D0(L_32, NULL);
				goto IL_0149_1;
			}

IL_0129_1:
			{
				bool L_33;
				L_33 = NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_inline((&V_8), NULL);
				AccessibilitySettings_InvokeBoldTextStatusChanged_mE3FAB90ED74180EF706A8AA160628EB14C202E7D(L_33, NULL);
				goto IL_0149_1;
			}

IL_0139_1:
			{
				bool L_34;
				L_34 = NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_inline((&V_8), NULL);
				AccessibilitySettings_InvokeClosedCaptionStatusChanged_m501523ED4BB118A6FE6370D1C617ADDB007EE8A4(L_34, NULL);
				goto IL_0149_1;
			}

IL_0149_1:
			{
				int32_t L_35 = V_7;
				V_7 = ((int32_t)il2cpp_codegen_add(L_35, 1));
			}

IL_0150_1:
			{
				int32_t L_36 = V_7;
				NotificationContextU5BU5D_t78BADDD46F29D1568CF037DB21E3CE3950642152* L_37 = V_6;
				NullCheck(L_37);
				if ((((int32_t)L_36) < ((int32_t)((int32_t)(((RuntimeArray*)L_37)->max_length)))))
				{
					goto IL_0079_1;
				}
			}
			{
				goto IL_0168;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0168:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* AccessibilityManager_Internal_GetRootNodeIds_mCB3855E8EDB937C5D097FD7CCD84BE50A6ADB6A5 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CollectionPool_2_Get_mAC90788170550C49BE7E07E08DC24C6F4B1F282B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CollectionPool_2_t5B361942F59C43867F72F5B0D244C2D537EC0694_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m5EA27E87D7DFA62DC75B612A6793C777A0CBC984_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PooledObject_1_System_IDisposable_Dispose_m8625488F4A366060C813FE28136D9DF4F3CE5287_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* V_1 = NULL;
	bool V_2 = false;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_3 = NULL;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* V_4 = NULL;
	PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE V_5;
	memset((&V_5), 0, sizeof(V_5));
	int32_t V_6 = 0;
	bool V_7 = false;
	bool V_8 = false;
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* G_B3_0 = NULL;
	int32_t G_B6_0 = 0;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		if (L_1)
		{
			goto IL_000d;
		}
	}
	{
		G_B3_0 = ((List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53*)(NULL));
		goto IL_0013;
	}

IL_000d:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_2 = V_0;
		NullCheck(L_2);
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_3;
		L_3 = AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2(L_2, NULL);
		G_B3_0 = L_3;
	}

IL_0013:
	{
		V_1 = G_B3_0;
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_4 = V_1;
		if (!L_4)
		{
			goto IL_0022;
		}
	}
	{
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_5 = V_1;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_inline(L_5, List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var);
		G_B6_0 = ((((int32_t)L_6) == ((int32_t)0))? 1 : 0);
		goto IL_0023;
	}

IL_0022:
	{
		G_B6_0 = 1;
	}

IL_0023:
	{
		V_2 = (bool)G_B6_0;
		bool L_7 = V_2;
		if (!L_7)
		{
			goto IL_002b;
		}
	}
	{
		V_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		goto IL_0092;
	}

IL_002b:
	{
		il2cpp_codegen_runtime_class_init_inline(CollectionPool_2_t5B361942F59C43867F72F5B0D244C2D537EC0694_il2cpp_TypeInfo_var);
		PooledObject_1_t9CB3B8254592D2A8A28461F56F28442CBB5BE8DE L_8;
		L_8 = CollectionPool_2_Get_mAC90788170550C49BE7E07E08DC24C6F4B1F282B((&V_4), CollectionPool_2_Get_mAC90788170550C49BE7E07E08DC24C6F4B1F282B_RuntimeMethod_var);
		V_5 = L_8;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0083:
			{
				PooledObject_1_System_IDisposable_Dispose_m8625488F4A366060C813FE28136D9DF4F3CE5287((&V_5), PooledObject_1_System_IDisposable_Dispose_m8625488F4A366060C813FE28136D9DF4F3CE5287_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				V_6 = 0;
				goto IL_0055_1;
			}

IL_003a_1:
			{
				List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* L_9 = V_4;
				List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_10 = V_1;
				int32_t L_11 = V_6;
				NullCheck(L_10);
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_12;
				L_12 = List_1_get_Item_m5EA27E87D7DFA62DC75B612A6793C777A0CBC984(L_10, L_11, List_1_get_Item_m5EA27E87D7DFA62DC75B612A6793C777A0CBC984_RuntimeMethod_var);
				NullCheck(L_12);
				int32_t L_13;
				L_13 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(L_12, NULL);
				NullCheck(L_9);
				List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_inline(L_9, L_13, List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_RuntimeMethod_var);
				int32_t L_14 = V_6;
				V_6 = ((int32_t)il2cpp_codegen_add(L_14, 1));
			}

IL_0055_1:
			{
				int32_t L_15 = V_6;
				List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_16 = V_1;
				NullCheck(L_16);
				int32_t L_17;
				L_17 = List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_inline(L_16, List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var);
				V_7 = (bool)((((int32_t)L_15) < ((int32_t)L_17))? 1 : 0);
				bool L_18 = V_7;
				if (L_18)
				{
					goto IL_003a_1;
				}
			}
			{
				List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* L_19 = V_4;
				NullCheck(L_19);
				int32_t L_20;
				L_20 = List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_inline(L_19, List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_RuntimeMethod_var);
				V_8 = (bool)((((int32_t)L_20) == ((int32_t)0))? 1 : 0);
				bool L_21 = V_8;
				if (!L_21)
				{
					goto IL_0079_1;
				}
			}
			{
				V_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
				goto IL_0092;
			}

IL_0079_1:
			{
				List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* L_22 = V_4;
				NullCheck(L_22);
				Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_23;
				L_23 = List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA(L_22, List_1_ToArray_m65479FB75A5FE539EA1A0D6681172717D23CEAAA_RuntimeMethod_var);
				V_3 = L_23;
				goto IL_0092;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0092:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = V_3;
		return L_24;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityManager_Internal_GetNode_mA5DBEEE6012BF17E04200FCC028B0729E3F67CA1 (int32_t ___0_id, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* ___1_nodeData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0014;
		}
	}
	{
		V_3 = (bool)0;
		goto IL_0034;
	}

IL_0014:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_4 = L_5;
		bool L_6 = V_4;
		if (!L_6)
		{
			goto IL_0030;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_8 = ___1_nodeData;
		NullCheck(L_7);
		AccessibilityNode_GetNodeData_m6C7765891167AB6E820D68F4AF89F891F03ACE2C(L_7, L_8, NULL);
		V_3 = (bool)1;
		goto IL_0034;
	}

IL_0030:
	{
		V_3 = (bool)0;
		goto IL_0034;
	}

IL_0034:
	{
		bool L_9 = V_3;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityManager_Internal_GetNodeIdAt_m55E20752B5F8C3ED7910ED649F0BD70627D974B3 (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* V_1 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_2 = NULL;
	bool V_3 = false;
	int32_t V_4 = 0;
	bool V_5 = false;
	bool V_6 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_3 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_3;
		if (!L_2)
		{
			goto IL_0014;
		}
	}
	{
		V_4 = (-1);
		goto IL_004f;
	}

IL_0014:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		NullCheck(L_3);
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_4;
		L_4 = AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2(L_3, NULL);
		V_1 = L_4;
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_5 = V_1;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_inline(L_5, List_1_get_Count_mC7892BF561DF2D70FECAA89AAC2EBF945FB23519_RuntimeMethod_var);
		V_5 = (bool)((((int32_t)L_6) == ((int32_t)0))? 1 : 0);
		bool L_7 = V_5;
		if (!L_7)
		{
			goto IL_002f;
		}
	}
	{
		V_4 = (-1);
		goto IL_004f;
	}

IL_002f:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_8 = V_0;
		float L_9 = ___0_x;
		float L_10 = ___1_y;
		NullCheck(L_8);
		bool L_11;
		L_11 = AccessibilityHierarchyService_TryGetNodeAt_mBEDA9E8FBF12970339369F80650637BE17E2280F(L_8, L_9, L_10, (&V_2), NULL);
		V_6 = L_11;
		bool L_12 = V_6;
		if (!L_12)
		{
			goto IL_004a;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_13 = V_2;
		NullCheck(L_13);
		int32_t L_14;
		L_14 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(L_13, NULL);
		V_4 = L_14;
		goto IL_004f;
	}

IL_004a:
	{
		V_4 = (-1);
		goto IL_004f;
	}

IL_004f:
	{
		int32_t L_15 = V_4;
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Internal_OnAccessibilityNotificationReceived_m1C65ECB977E8A5A7F6C0AD8F97D0B39A49832C86 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_context, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_0 = ___0_context;
		int32_t L_1;
		L_1 = AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_inline(L_0, NULL);
		V_0 = (bool)((((int32_t)L_1) == ((int32_t)7))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0010;
		}
	}
	{
		goto IL_001c;
	}

IL_0010:
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_3 = ___0_context;
		NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_4;
		memset((&L_4), 0, sizeof(L_4));
		NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024((&L_4), L_3, NULL);
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5(L_4, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A ___0_notification, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Queue_1_tD224EE31B5C1***************************** V_0 = NULL;
	bool V_1 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		Queue_1_tD224EE31B5C1***************************** L_0 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
		V_0 = L_0;
		V_1 = (bool)0;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0022:
			{
				{
					bool L_1 = V_1;
					if (!L_1)
					{
						goto IL_002c;
					}
				}
				{
					Queue_1_tD224EE31B5C1***************************** L_2 = V_0;
					Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA(L_2, NULL);
				}

IL_002c:
				{
					return;
				}
			}
		});
		try
		{
			Queue_1_tD224EE31B5C1***************************** L_3 = V_0;
			Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149(L_3, (&V_1), NULL);
			il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
			Queue_1_tD224EE31B5C1***************************** L_4 = ((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts;
			NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_5 = ___0_notification;
			NullCheck(L_4);
			Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377(L_4, L_5, Queue_1_Enqueue_m841A6D1049C0BCBDFA331052CB187AE4E7B4B377_RuntimeMethod_var);
			goto IL_002d;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_002d:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AccessibilityManager_GetExclusiveLock_m81EB3F3F7018C08A22AE25AB3554AA1EF02EC2C7 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	{
		ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* L_0 = (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98*)il2cpp_codegen_object_new(ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98_il2cpp_TypeInfo_var);
		ExclusiveLock__ctor_m875188F2E41DC8113A37A71CE4DC8B6707D265F5(L_0, NULL);
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		RuntimeObject* L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C (const RuntimeMethod* method) 
{
	typedef void (*AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C_ftn) ();
	static AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityManager::Lock()");
	_il2cpp_icall_func();
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110 (const RuntimeMethod* method) 
{
	typedef void (*AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110_ftn) ();
	static AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityManager::Unlock()");
	_il2cpp_icall_func();
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityManager__cctor_m04A7449F4F8891545FBA74BF400873CF71C928CC (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_tD224EE31B5C1****************************_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Queue_1_tD224EE31B5C1***************************** L_0 = (Queue_1_tD224EE31B5C1*****************************)il2cpp_codegen_object_new(Queue_1_tD224EE31B5C1****************************_il2cpp_TypeInfo_var);
		Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559(L_0, Queue_1__ctor_m2DE8F8B9DE1D5BC2E5F36001518419C9E6695559_RuntimeMethod_var);
		((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var))->___s_AsyncNotificationContexts), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif


IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_pinvoke(const NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A& unmarshaled, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CcurrentNodeU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CcurrentNodeU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_pinvoke_back(const NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_pinvoke& marshaled, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CcurrentNodeU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CcurrentNodeU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_pinvoke_cleanup(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_pinvoke& marshaled)
{
}


IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_com(const NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A& unmarshaled, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CcurrentNodeU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CcurrentNodeU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_com_back(const NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_com& marshaled, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CcurrentNodeU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A____U3CcurrentNodeU3Ek__BackingField_FieldInfo_var, NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CcurrentNodeU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshal_com_cleanup(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnotificationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnotificationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  bool NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	bool _returnValue;
	_returnValue = NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisScreenReaderEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3CannouncementU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CannouncementU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_AdjustorThunk (RuntimeObject* __this, String_t* ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CwasAnnouncementSuccessfulU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = __this->___U3CcurrentNodeU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* _returnValue;
	_returnValue = NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = ___0_value;
		__this->___U3CcurrentNodeU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CcurrentNodeU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_AdjustorThunk (RuntimeObject* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = ___0_value;
		__this->___U3CnextNodeU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CnextNodeU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_AdjustorThunk (RuntimeObject* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CfontScaleU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	float _returnValue;
	_returnValue = NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CfontScaleU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisBoldTextEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  bool NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	bool _returnValue;
	_returnValue = NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisBoldTextEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisClosedCaptioningEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  bool NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	bool _returnValue;
	_returnValue = NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisClosedCaptioningEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 L_0 = ___0_value;
		__this->___U3CnativeContextU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->___U3CnativeContextU3Ek__BackingField))->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_AdjustorThunk (RuntimeObject* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 ___0_value, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024 (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_nativeNotification, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B2_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B1_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B5_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B4_0 = NULL;
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_0 = ___0_nativeNotification;
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 L_1 = (*(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*)L_0);
		NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_inline(__this, L_1, NULL);
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_2 = ___0_nativeNotification;
		int32_t L_3;
		L_3 = AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_inline(L_2, NULL);
		NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline(__this, L_3, NULL);
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_4 = ___0_nativeNotification;
		bool L_5;
		L_5 = AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_inline(L_4, NULL);
		NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_inline(__this, L_5, NULL);
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_6 = ___0_nativeNotification;
		String_t* L_7;
		L_7 = AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_inline(L_6, NULL);
		NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_inline(__this, L_7, NULL);
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_8 = ___0_nativeNotification;
		bool L_9;
		L_9 = AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_inline(L_8, NULL);
		NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_inline(__this, L_9, NULL);
		V_0 = (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*)NULL;
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_10;
		L_10 = AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE(NULL);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_11 = L_10;
		if (L_11)
		{
			G_B2_0 = L_11;
			goto IL_004f;
		}
		G_B1_0 = L_11;
	}
	{
		goto IL_005d;
	}

IL_004f:
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_12 = ___0_nativeNotification;
		int32_t L_13;
		L_13 = AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_inline(L_12, NULL);
		NullCheck(G_B2_0);
		bool L_14;
		L_14 = AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1(G_B2_0, L_13, (&V_0), NULL);
	}

IL_005d:
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_15 = V_0;
		NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_inline(__this, L_15, NULL);
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_16;
		L_16 = AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE(NULL);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_17 = L_16;
		if (L_17)
		{
			G_B5_0 = L_17;
			goto IL_0070;
		}
		G_B4_0 = L_17;
	}
	{
		goto IL_007e;
	}

IL_0070:
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_18 = ___0_nativeNotification;
		int32_t L_19;
		L_19 = AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_inline(L_18, NULL);
		NullCheck(G_B5_0);
		bool L_20;
		L_20 = AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1(G_B5_0, L_19, (&V_0), NULL);
	}

IL_007e:
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_21 = V_0;
		NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_inline(__this, L_21, NULL);
		NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_inline(__this, (1.0f), NULL);
		NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_inline(__this, (bool)0, NULL);
		NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_inline(__this, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024_AdjustorThunk (RuntimeObject* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_nativeNotification, const RuntimeMethod* method)
{
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A*>(__this + _offset);
	NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024(_thisAdjusted, ___0_nativeNotification, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock__ctor_m875188F2E41DC8113A37A71CE4DC8B6707D265F5 (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C(NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock_Finalize_m5FAA7693E76CD44F15160418E97D384FC354CB68 (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) 
{
	{
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_000b:
			{
				Object_Finalize_mC98C96301CCABFE00F1A7EF8E15DF507CACD42B2(__this, NULL);
				return;
			}
		});
		try
		{
			ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3(__this, NULL);
			goto IL_0013;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3 (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		bool L_0 = __this->___m_Disposed;
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_001d;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110(NULL);
		__this->___m_Disposed = (bool)1;
	}

IL_001d:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExclusiveLock_Dispose_mD5D75B88E21B82A4F3FB65338FD81B813F524D4F (ExclusiveLock_tDC9E055C0A15B25A972CEE8CA9F540245261DB98* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		GC_SuppressFinalize_m71815DBD5A0CD2EA1BE43317B08B7A14949EDC65(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_pinvoke(const AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115& unmarshaled, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_pinvoke& marshaled)
{
	marshaled.___U3CidU3Ek__BackingField = unmarshaled.___U3CidU3Ek__BackingField;
	marshaled.___U3CisActiveU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisActiveU3Ek__BackingField);
	marshaled.___U3ClabelU3Ek__BackingField = il2cpp_codegen_marshal_string(unmarshaled.___U3ClabelU3Ek__BackingField);
	marshaled.___U3CvalueU3Ek__BackingField = il2cpp_codegen_marshal_string(unmarshaled.___U3CvalueU3Ek__BackingField);
	marshaled.___U3ChintU3Ek__BackingField = il2cpp_codegen_marshal_string(unmarshaled.___U3ChintU3Ek__BackingField);
	marshaled.___U3CroleU3Ek__BackingField = unmarshaled.___U3CroleU3Ek__BackingField;
	marshaled.___U3CallowsDirectInteractionU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CallowsDirectInteractionU3Ek__BackingField);
	marshaled.___U3CstateU3Ek__BackingField = unmarshaled.___U3CstateU3Ek__BackingField;
	marshaled.___U3CframeU3Ek__BackingField = unmarshaled.___U3CframeU3Ek__BackingField;
	marshaled.___U3CparentIdU3Ek__BackingField = unmarshaled.___U3CparentIdU3Ek__BackingField;
	marshaled.___U3CchildIdsU3Ek__BackingField = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I4, unmarshaled.___U3CchildIdsU3Ek__BackingField);
	marshaled.___U3CisFocusedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisFocusedU3Ek__BackingField);
	marshaled.___U3ClanguageU3Ek__BackingField = unmarshaled.___U3ClanguageU3Ek__BackingField;
	marshaled.___U3CimplementsSelectedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CimplementsSelectedU3Ek__BackingField);
	marshaled.___U3CimplementsDismissedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CimplementsDismissedU3Ek__BackingField);
}
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_pinvoke_back(const AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_pinvoke& marshaled, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115& unmarshaled)
{
	int32_t unmarshaledU3CidU3Ek__BackingField_temp_0 = 0;
	unmarshaledU3CidU3Ek__BackingField_temp_0 = marshaled.___U3CidU3Ek__BackingField;
	unmarshaled.___U3CidU3Ek__BackingField = unmarshaledU3CidU3Ek__BackingField_temp_0;
	bool unmarshaledU3CisActiveU3Ek__BackingField_temp_1 = false;
	unmarshaledU3CisActiveU3Ek__BackingField_temp_1 = static_cast<bool>(marshaled.___U3CisActiveU3Ek__BackingField);
	unmarshaled.___U3CisActiveU3Ek__BackingField = unmarshaledU3CisActiveU3Ek__BackingField_temp_1;
	unmarshaled.___U3ClabelU3Ek__BackingField = il2cpp_codegen_marshal_string_result(marshaled.___U3ClabelU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3ClabelU3Ek__BackingField), (void*)il2cpp_codegen_marshal_string_result(marshaled.___U3ClabelU3Ek__BackingField));
	unmarshaled.___U3CvalueU3Ek__BackingField = il2cpp_codegen_marshal_string_result(marshaled.___U3CvalueU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CvalueU3Ek__BackingField), (void*)il2cpp_codegen_marshal_string_result(marshaled.___U3CvalueU3Ek__BackingField));
	unmarshaled.___U3ChintU3Ek__BackingField = il2cpp_codegen_marshal_string_result(marshaled.___U3ChintU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3ChintU3Ek__BackingField), (void*)il2cpp_codegen_marshal_string_result(marshaled.___U3ChintU3Ek__BackingField));
	uint16_t unmarshaledU3CroleU3Ek__BackingField_temp_5 = 0;
	unmarshaledU3CroleU3Ek__BackingField_temp_5 = marshaled.___U3CroleU3Ek__BackingField;
	unmarshaled.___U3CroleU3Ek__BackingField = unmarshaledU3CroleU3Ek__BackingField_temp_5;
	bool unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6 = false;
	unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6 = static_cast<bool>(marshaled.___U3CallowsDirectInteractionU3Ek__BackingField);
	unmarshaled.___U3CallowsDirectInteractionU3Ek__BackingField = unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6;
	uint16_t unmarshaledU3CstateU3Ek__BackingField_temp_7 = 0;
	unmarshaledU3CstateU3Ek__BackingField_temp_7 = marshaled.___U3CstateU3Ek__BackingField;
	unmarshaled.___U3CstateU3Ek__BackingField = unmarshaledU3CstateU3Ek__BackingField_temp_7;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D unmarshaledU3CframeU3Ek__BackingField_temp_8;
	memset((&unmarshaledU3CframeU3Ek__BackingField_temp_8), 0, sizeof(unmarshaledU3CframeU3Ek__BackingField_temp_8));
	unmarshaledU3CframeU3Ek__BackingField_temp_8 = marshaled.___U3CframeU3Ek__BackingField;
	unmarshaled.___U3CframeU3Ek__BackingField = unmarshaledU3CframeU3Ek__BackingField_temp_8;
	int32_t unmarshaledU3CparentIdU3Ek__BackingField_temp_9 = 0;
	unmarshaledU3CparentIdU3Ek__BackingField_temp_9 = marshaled.___U3CparentIdU3Ek__BackingField;
	unmarshaled.___U3CparentIdU3Ek__BackingField = unmarshaledU3CparentIdU3Ek__BackingField_temp_9;
	unmarshaled.___U3CchildIdsU3Ek__BackingField = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, il2cpp_defaults.int32_class, marshaled.___U3CchildIdsU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CchildIdsU3Ek__BackingField), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, il2cpp_defaults.int32_class, marshaled.___U3CchildIdsU3Ek__BackingField));
	bool unmarshaledU3CisFocusedU3Ek__BackingField_temp_11 = false;
	unmarshaledU3CisFocusedU3Ek__BackingField_temp_11 = static_cast<bool>(marshaled.___U3CisFocusedU3Ek__BackingField);
	unmarshaled.___U3CisFocusedU3Ek__BackingField = unmarshaledU3CisFocusedU3Ek__BackingField_temp_11;
	int32_t unmarshaledU3ClanguageU3Ek__BackingField_temp_12 = 0;
	unmarshaledU3ClanguageU3Ek__BackingField_temp_12 = marshaled.___U3ClanguageU3Ek__BackingField;
	unmarshaled.___U3ClanguageU3Ek__BackingField = unmarshaledU3ClanguageU3Ek__BackingField_temp_12;
	bool unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13 = false;
	unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13 = static_cast<bool>(marshaled.___U3CimplementsSelectedU3Ek__BackingField);
	unmarshaled.___U3CimplementsSelectedU3Ek__BackingField = unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13;
	bool unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14 = false;
	unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14 = static_cast<bool>(marshaled.___U3CimplementsDismissedU3Ek__BackingField);
	unmarshaled.___U3CimplementsDismissedU3Ek__BackingField = unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14;
}
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_pinvoke_cleanup(AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_marshal_free(marshaled.___U3ClabelU3Ek__BackingField);
	marshaled.___U3ClabelU3Ek__BackingField = NULL;
	il2cpp_codegen_marshal_free(marshaled.___U3CvalueU3Ek__BackingField);
	marshaled.___U3CvalueU3Ek__BackingField = NULL;
	il2cpp_codegen_marshal_free(marshaled.___U3ChintU3Ek__BackingField);
	marshaled.___U3ChintU3Ek__BackingField = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___U3CchildIdsU3Ek__BackingField);
	marshaled.___U3CchildIdsU3Ek__BackingField = NULL;
}
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_com(const AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115& unmarshaled, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_com& marshaled)
{
	marshaled.___U3CidU3Ek__BackingField = unmarshaled.___U3CidU3Ek__BackingField;
	marshaled.___U3CisActiveU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisActiveU3Ek__BackingField);
	marshaled.___U3ClabelU3Ek__BackingField = il2cpp_codegen_marshal_bstring(unmarshaled.___U3ClabelU3Ek__BackingField);
	marshaled.___U3CvalueU3Ek__BackingField = il2cpp_codegen_marshal_bstring(unmarshaled.___U3CvalueU3Ek__BackingField);
	marshaled.___U3ChintU3Ek__BackingField = il2cpp_codegen_marshal_bstring(unmarshaled.___U3ChintU3Ek__BackingField);
	marshaled.___U3CroleU3Ek__BackingField = unmarshaled.___U3CroleU3Ek__BackingField;
	marshaled.___U3CallowsDirectInteractionU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CallowsDirectInteractionU3Ek__BackingField);
	marshaled.___U3CstateU3Ek__BackingField = unmarshaled.___U3CstateU3Ek__BackingField;
	marshaled.___U3CframeU3Ek__BackingField = unmarshaled.___U3CframeU3Ek__BackingField;
	marshaled.___U3CparentIdU3Ek__BackingField = unmarshaled.___U3CparentIdU3Ek__BackingField;
	marshaled.___U3CchildIdsU3Ek__BackingField = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I4, unmarshaled.___U3CchildIdsU3Ek__BackingField);
	marshaled.___U3CisFocusedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisFocusedU3Ek__BackingField);
	marshaled.___U3ClanguageU3Ek__BackingField = unmarshaled.___U3ClanguageU3Ek__BackingField;
	marshaled.___U3CimplementsSelectedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CimplementsSelectedU3Ek__BackingField);
	marshaled.___U3CimplementsDismissedU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CimplementsDismissedU3Ek__BackingField);
}
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_com_back(const AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_com& marshaled, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115& unmarshaled)
{
	int32_t unmarshaledU3CidU3Ek__BackingField_temp_0 = 0;
	unmarshaledU3CidU3Ek__BackingField_temp_0 = marshaled.___U3CidU3Ek__BackingField;
	unmarshaled.___U3CidU3Ek__BackingField = unmarshaledU3CidU3Ek__BackingField_temp_0;
	bool unmarshaledU3CisActiveU3Ek__BackingField_temp_1 = false;
	unmarshaledU3CisActiveU3Ek__BackingField_temp_1 = static_cast<bool>(marshaled.___U3CisActiveU3Ek__BackingField);
	unmarshaled.___U3CisActiveU3Ek__BackingField = unmarshaledU3CisActiveU3Ek__BackingField_temp_1;
	unmarshaled.___U3ClabelU3Ek__BackingField = il2cpp_codegen_marshal_bstring_result(marshaled.___U3ClabelU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3ClabelU3Ek__BackingField), (void*)il2cpp_codegen_marshal_bstring_result(marshaled.___U3ClabelU3Ek__BackingField));
	unmarshaled.___U3CvalueU3Ek__BackingField = il2cpp_codegen_marshal_bstring_result(marshaled.___U3CvalueU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CvalueU3Ek__BackingField), (void*)il2cpp_codegen_marshal_bstring_result(marshaled.___U3CvalueU3Ek__BackingField));
	unmarshaled.___U3ChintU3Ek__BackingField = il2cpp_codegen_marshal_bstring_result(marshaled.___U3ChintU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3ChintU3Ek__BackingField), (void*)il2cpp_codegen_marshal_bstring_result(marshaled.___U3ChintU3Ek__BackingField));
	uint16_t unmarshaledU3CroleU3Ek__BackingField_temp_5 = 0;
	unmarshaledU3CroleU3Ek__BackingField_temp_5 = marshaled.___U3CroleU3Ek__BackingField;
	unmarshaled.___U3CroleU3Ek__BackingField = unmarshaledU3CroleU3Ek__BackingField_temp_5;
	bool unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6 = false;
	unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6 = static_cast<bool>(marshaled.___U3CallowsDirectInteractionU3Ek__BackingField);
	unmarshaled.___U3CallowsDirectInteractionU3Ek__BackingField = unmarshaledU3CallowsDirectInteractionU3Ek__BackingField_temp_6;
	uint16_t unmarshaledU3CstateU3Ek__BackingField_temp_7 = 0;
	unmarshaledU3CstateU3Ek__BackingField_temp_7 = marshaled.___U3CstateU3Ek__BackingField;
	unmarshaled.___U3CstateU3Ek__BackingField = unmarshaledU3CstateU3Ek__BackingField_temp_7;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D unmarshaledU3CframeU3Ek__BackingField_temp_8;
	memset((&unmarshaledU3CframeU3Ek__BackingField_temp_8), 0, sizeof(unmarshaledU3CframeU3Ek__BackingField_temp_8));
	unmarshaledU3CframeU3Ek__BackingField_temp_8 = marshaled.___U3CframeU3Ek__BackingField;
	unmarshaled.___U3CframeU3Ek__BackingField = unmarshaledU3CframeU3Ek__BackingField_temp_8;
	int32_t unmarshaledU3CparentIdU3Ek__BackingField_temp_9 = 0;
	unmarshaledU3CparentIdU3Ek__BackingField_temp_9 = marshaled.___U3CparentIdU3Ek__BackingField;
	unmarshaled.___U3CparentIdU3Ek__BackingField = unmarshaledU3CparentIdU3Ek__BackingField_temp_9;
	unmarshaled.___U3CchildIdsU3Ek__BackingField = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, il2cpp_defaults.int32_class, marshaled.___U3CchildIdsU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CchildIdsU3Ek__BackingField), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, il2cpp_defaults.int32_class, marshaled.___U3CchildIdsU3Ek__BackingField));
	bool unmarshaledU3CisFocusedU3Ek__BackingField_temp_11 = false;
	unmarshaledU3CisFocusedU3Ek__BackingField_temp_11 = static_cast<bool>(marshaled.___U3CisFocusedU3Ek__BackingField);
	unmarshaled.___U3CisFocusedU3Ek__BackingField = unmarshaledU3CisFocusedU3Ek__BackingField_temp_11;
	int32_t unmarshaledU3ClanguageU3Ek__BackingField_temp_12 = 0;
	unmarshaledU3ClanguageU3Ek__BackingField_temp_12 = marshaled.___U3ClanguageU3Ek__BackingField;
	unmarshaled.___U3ClanguageU3Ek__BackingField = unmarshaledU3ClanguageU3Ek__BackingField_temp_12;
	bool unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13 = false;
	unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13 = static_cast<bool>(marshaled.___U3CimplementsSelectedU3Ek__BackingField);
	unmarshaled.___U3CimplementsSelectedU3Ek__BackingField = unmarshaledU3CimplementsSelectedU3Ek__BackingField_temp_13;
	bool unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14 = false;
	unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14 = static_cast<bool>(marshaled.___U3CimplementsDismissedU3Ek__BackingField);
	unmarshaled.___U3CimplementsDismissedU3Ek__BackingField = unmarshaledU3CimplementsDismissedU3Ek__BackingField_temp_14;
}
IL2CPP_EXTERN_C void AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshal_com_cleanup(AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115_marshaled_com& marshaled)
{
	il2cpp_codegen_marshal_free_bstring(marshaled.___U3ClabelU3Ek__BackingField);
	marshaled.___U3ClabelU3Ek__BackingField = NULL;
	il2cpp_codegen_marshal_free_bstring(marshaled.___U3CvalueU3Ek__BackingField);
	marshaled.___U3CvalueU3Ek__BackingField = NULL;
	il2cpp_codegen_marshal_free_bstring(marshaled.___U3ChintU3Ek__BackingField);
	marshaled.___U3ChintU3Ek__BackingField = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___U3CchildIdsU3Ek__BackingField);
	marshaled.___U3CchildIdsU3Ek__BackingField = NULL;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CidU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisActiveU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3ClabelU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3ClabelU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_AdjustorThunk (RuntimeObject* __this, String_t* ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3CvalueU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CvalueU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_AdjustorThunk (RuntimeObject* __this, String_t* ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3ChintU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3ChintU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_AdjustorThunk (RuntimeObject* __this, String_t* ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = ___0_value;
		__this->___U3CroleU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_AdjustorThunk (RuntimeObject* __this, uint16_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CallowsDirectInteractionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = ___0_value;
		__this->___U3CstateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_AdjustorThunk (RuntimeObject* __this, uint16_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method) 
{
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0 = ___0_value;
		__this->___U3CframeU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_AdjustorThunk (RuntimeObject* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CparentIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036 (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_value, const RuntimeMethod* method) 
{
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ___0_value;
		__this->___U3CchildIdsU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CchildIdsU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_AdjustorThunk (RuntimeObject* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3ClanguageU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CimplementsSelectedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CimplementsDismissedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115*>(__this + _offset);
	AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_inline(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43 (int32_t ___0_id, int32_t ___1_parentId, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43_ftn) (int32_t, int32_t);
	static AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityNodeManager::DestroyNativeNode(System.Int32,System.Int32)");
	_il2cpp_icall_func(___0_id, ___1_parentId);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetFrame_mE47923B10A1592690CEF29D975C2C22654AA6E0A (int32_t ___0_id, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___1_frame, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_id;
		AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0(L_0, (&___1_frame), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetChildren_mE43C7A8C5CDC9D8049088AC1A89E0BDC4015C7C9 (int32_t ___0_id, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_childIds, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t* V_1 = NULL;
	ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		int32_t L_0 = ___0_id;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___1_childIds;
		Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_inline((&V_0), L_1, Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_RuntimeMethod_var);
		int32_t* L_2;
		L_2 = Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811((&V_0), Span_1_GetPinnableReference_mF920821F83971F1D7D3E554CAD596D5902754811_RuntimeMethod_var);
		V_1 = L_2;
		int32_t* L_3 = V_1;
		int32_t L_4;
		L_4 = Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_inline((&V_0), Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_RuntimeMethod_var);
		ManagedSpanWrapper__ctor_mB29647A21BB87EA4DF859E5C2FA2207F47E525D2((&V_2), (void*)((uintptr_t)L_3), L_4, NULL);
		AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8(L_0, (&V_2), NULL);
		V_1 = (int32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B (int32_t ___0_id, AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* ___1_actions, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B_ftn) (int32_t, AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9*);
	static AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityNodeManager::SetActions(System.Int32,UnityEngine.Accessibility.AccessibilityAction[])");
	_il2cpp_icall_func(___0_id, ___1_actions);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_Internal_InvokeFocusChanged_mF52C86D20B4F7CEF594D028EB8A0D073CDC86F1C (int32_t ___0_id, bool ___1_isNodeFocused, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_0028;
	}

IL_0011:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_3 = L_5;
		bool L_6 = V_3;
		if (!L_6)
		{
			goto IL_0028;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		bool L_8 = ___1_isNodeFocused;
		NullCheck(L_7);
		AccessibilityNode_NotifyFocusChanged_mEA14C3D3534CB0920ECD5AFF32350855BC6FD5E8(L_7, L_8, NULL);
	}

IL_0028:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNodeManager_Internal_InvokeSelected_m3361DDF76F20E2F5B6240718B79A08B87BD1CC55 (int32_t ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0013;
		}
	}
	{
		V_3 = (bool)0;
		goto IL_0030;
	}

IL_0013:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_4 = L_5;
		bool L_6 = V_4;
		if (!L_6)
		{
			goto IL_002c;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		NullCheck(L_7);
		bool L_8;
		L_8 = AccessibilityNode_InvokeSelected_mBB094C8957C71FBCA0C033A4F9EE7127B8D1A7BA(L_7, NULL);
		V_3 = L_8;
		goto IL_0030;
	}

IL_002c:
	{
		V_3 = (bool)0;
		goto IL_0030;
	}

IL_0030:
	{
		bool L_9 = V_3;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_Internal_InvokeIncremented_m43911AF44D7577BFD428E7CAB48DBB1F7EE2EF02 (int32_t ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_0027;
	}

IL_0011:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_3 = L_5;
		bool L_6 = V_3;
		if (!L_6)
		{
			goto IL_0027;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		NullCheck(L_7);
		AccessibilityNode_InvokeIncremented_mC2B5AF750A1BC3177E92787DAA385360234B59AF(L_7, NULL);
	}

IL_0027:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_Internal_InvokeDecremented_mEFAAE7277E2856511A4FFB730338D59DB0E5227E (int32_t ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_0027;
	}

IL_0011:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_3 = L_5;
		bool L_6 = V_3;
		if (!L_6)
		{
			goto IL_0027;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		NullCheck(L_7);
		AccessibilityNode_InvokeDecremented_m3BF11CFA927B9303367B46C9EA7E0E99A5C422D5(L_7, NULL);
	}

IL_0027:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNodeManager_Internal_InvokeDismissed_m68A53CADDE358234374B04D231D071D0816DF974 (int32_t ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		V_0 = L_0;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = V_0;
		V_2 = (bool)((((RuntimeObject*)(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)L_1) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_2 = V_2;
		if (!L_2)
		{
			goto IL_0013;
		}
	}
	{
		V_3 = (bool)0;
		goto IL_0030;
	}

IL_0013:
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_3 = V_0;
		int32_t L_4 = ___0_id;
		NullCheck(L_3);
		bool L_5;
		L_5 = AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8(L_3, L_4, (&V_1), NULL);
		V_4 = L_5;
		bool L_6 = V_4;
		if (!L_6)
		{
			goto IL_002c;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = V_1;
		NullCheck(L_7);
		bool L_8;
		L_8 = AccessibilityNode_Dismissed_m7492D9E72948C6718A13A2FDB628BDC4E81091EF(L_7, NULL);
		V_3 = L_8;
		goto IL_0030;
	}

IL_002c:
	{
		V_3 = (bool)0;
		goto IL_0030;
	}

IL_0030:
	{
		bool L_9 = V_3;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0 (int32_t ___0_id, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* ___1_frame, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0_ftn) (int32_t, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*);
	static AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityNodeManager::SetFrame_Injected(System.Int32,UnityEngine.Rect&)");
	_il2cpp_icall_func(___0_id, ___1_frame);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8 (int32_t ___0_id, ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E* ___1_childIds, const RuntimeMethod* method) 
{
	typedef void (*AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8_ftn) (int32_t, ManagedSpanWrapper_tE7FC4BBB631B130757F8DEB15853D98FD3D5DC0E*);
	static AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Accessibility.AccessibilityNodeManager::SetChildren_Injected(System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&)");
	_il2cpp_icall_func(___0_id, ___1_childIds);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled)
{
	marshaled.___U3CnotificationU3Ek__BackingField = unmarshaled.___U3CnotificationU3Ek__BackingField;
	marshaled.___U3CisScreenReaderEnabledU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisScreenReaderEnabledU3Ek__BackingField);
	marshaled.___U3CannouncementU3Ek__BackingField = il2cpp_codegen_marshal_string(unmarshaled.___U3CannouncementU3Ek__BackingField);
	marshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField);
	marshaled.___U3CcurrentNodeIdU3Ek__BackingField = unmarshaled.___U3CcurrentNodeIdU3Ek__BackingField;
	marshaled.___U3CnextNodeIdU3Ek__BackingField = unmarshaled.___U3CnextNodeIdU3Ek__BackingField;
}
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke_back(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled)
{
	int32_t unmarshaledU3CnotificationU3Ek__BackingField_temp_0 = 0;
	unmarshaledU3CnotificationU3Ek__BackingField_temp_0 = marshaled.___U3CnotificationU3Ek__BackingField;
	unmarshaled.___U3CnotificationU3Ek__BackingField = unmarshaledU3CnotificationU3Ek__BackingField_temp_0;
	bool unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1 = false;
	unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1 = static_cast<bool>(marshaled.___U3CisScreenReaderEnabledU3Ek__BackingField);
	unmarshaled.___U3CisScreenReaderEnabledU3Ek__BackingField = unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1;
	unmarshaled.___U3CannouncementU3Ek__BackingField = il2cpp_codegen_marshal_string_result(marshaled.___U3CannouncementU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CannouncementU3Ek__BackingField), (void*)il2cpp_codegen_marshal_string_result(marshaled.___U3CannouncementU3Ek__BackingField));
	bool unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3 = false;
	unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3 = static_cast<bool>(marshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField);
	unmarshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField = unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3;
	int32_t unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4 = 0;
	unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4 = marshaled.___U3CcurrentNodeIdU3Ek__BackingField;
	unmarshaled.___U3CcurrentNodeIdU3Ek__BackingField = unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4;
	int32_t unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5 = 0;
	unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5 = marshaled.___U3CnextNodeIdU3Ek__BackingField;
	unmarshaled.___U3CnextNodeIdU3Ek__BackingField = unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5;
}
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_pinvoke_cleanup(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_marshal_free(marshaled.___U3CannouncementU3Ek__BackingField);
	marshaled.___U3CannouncementU3Ek__BackingField = NULL;
}
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled)
{
	marshaled.___U3CnotificationU3Ek__BackingField = unmarshaled.___U3CnotificationU3Ek__BackingField;
	marshaled.___U3CisScreenReaderEnabledU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CisScreenReaderEnabledU3Ek__BackingField);
	marshaled.___U3CannouncementU3Ek__BackingField = il2cpp_codegen_marshal_bstring(unmarshaled.___U3CannouncementU3Ek__BackingField);
	marshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField = static_cast<int32_t>(unmarshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField);
	marshaled.___U3CcurrentNodeIdU3Ek__BackingField = unmarshaled.___U3CcurrentNodeIdU3Ek__BackingField;
	marshaled.___U3CnextNodeIdU3Ek__BackingField = unmarshaled.___U3CnextNodeIdU3Ek__BackingField;
}
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com_back(const AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059& unmarshaled)
{
	int32_t unmarshaledU3CnotificationU3Ek__BackingField_temp_0 = 0;
	unmarshaledU3CnotificationU3Ek__BackingField_temp_0 = marshaled.___U3CnotificationU3Ek__BackingField;
	unmarshaled.___U3CnotificationU3Ek__BackingField = unmarshaledU3CnotificationU3Ek__BackingField_temp_0;
	bool unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1 = false;
	unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1 = static_cast<bool>(marshaled.___U3CisScreenReaderEnabledU3Ek__BackingField);
	unmarshaled.___U3CisScreenReaderEnabledU3Ek__BackingField = unmarshaledU3CisScreenReaderEnabledU3Ek__BackingField_temp_1;
	unmarshaled.___U3CannouncementU3Ek__BackingField = il2cpp_codegen_marshal_bstring_result(marshaled.___U3CannouncementU3Ek__BackingField);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___U3CannouncementU3Ek__BackingField), (void*)il2cpp_codegen_marshal_bstring_result(marshaled.___U3CannouncementU3Ek__BackingField));
	bool unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3 = false;
	unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3 = static_cast<bool>(marshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField);
	unmarshaled.___U3CwasAnnouncementSuccessfulU3Ek__BackingField = unmarshaledU3CwasAnnouncementSuccessfulU3Ek__BackingField_temp_3;
	int32_t unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4 = 0;
	unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4 = marshaled.___U3CcurrentNodeIdU3Ek__BackingField;
	unmarshaled.___U3CcurrentNodeIdU3Ek__BackingField = unmarshaledU3CcurrentNodeIdU3Ek__BackingField_temp_4;
	int32_t unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5 = 0;
	unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5 = marshaled.___U3CnextNodeIdU3Ek__BackingField;
	unmarshaled.___U3CnextNodeIdU3Ek__BackingField = unmarshaledU3CnextNodeIdU3Ek__BackingField_temp_5;
}
IL2CPP_EXTERN_C void AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshal_com_cleanup(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059_marshaled_com& marshaled)
{
	il2cpp_codegen_marshal_free_bstring(marshaled.___U3CannouncementU3Ek__BackingField);
	marshaled.___U3CannouncementU3Ek__BackingField = NULL;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnotificationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnotificationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  bool AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	bool _returnValue;
	_returnValue = AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CannouncementU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  String_t* AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  bool AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	bool _returnValue;
	_returnValue = AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CcurrentNodeIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514 (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnextNodeIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnextNodeIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059*>(__this + _offset);
	AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_inline(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_Internal_OnFontScaleChanged_m188BDB50617266AD9B80720FB3D70011DF06C58A (float ___0_newFontScale, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A));
		NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline((&V_0), ((int32_t)9), NULL);
		float L_0 = ___0_newFontScale;
		NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_inline((&V_0), L_0, NULL);
		NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_Internal_OnBoldTextStatusChanged_mB80B283779D5416E1162FFB9BB831F2B37AC1C0D (bool ___0_enabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A));
		NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline((&V_0), ((int32_t)10), NULL);
		bool L_0 = ___0_enabled;
		NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_inline((&V_0), L_0, NULL);
		NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_Internal_OnClosedCaptioningStatusChanged_m7B522D6FC70776E4D1A5A8B184578F64EE4D11E5 (bool ___0_enabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A));
		NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline((&V_0), ((int32_t)11), NULL);
		bool L_0 = ___0_enabled;
		NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_inline((&V_0), L_0, NULL);
		NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeFontScaleChanged_m01DE529AC72A0FBE344F62C7E03FF028E7C196D0 (float ___0_newFontScale, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* G_B2_0 = NULL;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* G_B1_0 = NULL;
	{
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_0 = ((AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var))->___fontScaleChanged;
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000c:
	{
		float L_2 = ___0_newFontScale;
		NullCheck(G_B2_0);
		Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline(G_B2_0, L_2, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeBoldTextStatusChanged_mE3FAB90ED74180EF706A8AA160628EB14C202E7D (bool ___0_enabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B2_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B1_0 = NULL;
	{
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_0 = ((AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var))->___boldTextStatusChanged;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000c:
	{
		bool L_2 = ___0_enabled;
		NullCheck(G_B2_0);
		Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_inline(G_B2_0, L_2, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilitySettings_InvokeClosedCaptionStatusChanged_m501523ED4BB118A6FE6370D1C617ADDB007EE8A4 (bool ___0_enabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B2_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B1_0 = NULL;
	{
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_0 = ((AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_StaticFields*)il2cpp_codegen_static_fields_for(AccessibilitySettings_tED9EDE1088524316E7EC6665927F2BB714A3EC49_il2cpp_TypeInfo_var))->___closedCaptioningStatusChanged;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000c:
	{
		bool L_2 = ___0_enabled;
		NullCheck(G_B2_0);
		Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_inline(G_B2_0, L_2, NULL);
	}

IL_0013:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		bool L_0 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340 (bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CisScreenReaderEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CnotificationDispatcherU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport_Initialize_mDB3A5CE4DA477AF876D3A1DAB0577B559CEA1063 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		bool L_0;
		L_0 = AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2(NULL);
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340_inline(L_0, NULL);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)il2cpp_codegen_object_new(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		Action_1__ctor_mA8C3AC97D1F076EA5D1D0C10CEE6BD3E94711501(L_1, NULL, (intptr_t)((void*)AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D_RuntimeMethod_var), NULL);
		AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696(L_1, NULL);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_2 = (Action_1_tB953813A651C365D3872C6813072676763767BA5*)il2cpp_codegen_object_new(Action_1_tB953813A651C365D3872C6813072676763767BA5_il2cpp_TypeInfo_var);
		Action_1__ctor_mE888483BBBD9EFB1BAAEF97D4FEB78FC03F6DDDD(L_2, NULL, (intptr_t)((void*)AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF_RuntimeMethod_var), NULL);
		AccessibilityManager_add_nodeFocusChanged_m5BFCE49D472600D1544C25A28931D8FBCD032CFA(L_2, NULL);
		ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* L_3 = (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF*)il2cpp_codegen_object_new(ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF_il2cpp_TypeInfo_var);
		ServiceManager__ctor_m06C1D1E60CF676E1E526A3E281757604BA5C9F3A(L_3, NULL);
		((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___s_ServiceManager = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___s_ServiceManager), (void*)L_3);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D (bool ___0_screenReaderEnabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B4_0 = NULL;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* G_B3_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		bool L_0;
		L_0 = AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80_inline(NULL);
		bool L_1 = ___0_screenReaderEnabled;
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)L_1))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0010;
		}
	}
	{
		goto IL_002d;
	}

IL_0010:
	{
		bool L_3 = ___0_screenReaderEnabled;
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340_inline(L_3, NULL);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_4 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___screenReaderStatusChanged;
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_5 = L_4;
		if (L_5)
		{
			G_B4_0 = L_5;
			goto IL_0022;
		}
		G_B3_0 = L_5;
	}
	{
		goto IL_002d;
	}

IL_0022:
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80_inline(NULL);
		NullCheck(G_B4_0);
		Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_inline(G_B4_0, L_6, NULL);
	}

IL_002d:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_currentNode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB953813A651C365D3872C6813072676763767BA5* G_B2_0 = NULL;
	Action_1_tB953813A651C365D3872C6813072676763767BA5* G_B1_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_0 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___nodeFocusChanged;
		Action_1_tB953813A651C365D3872C6813072676763767BA5* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000c:
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_2 = ___0_currentNode;
		NullCheck(G_B2_0);
		Action_1_Invoke_m7C5AFCE416A088DEAF3A2C7AC190C5D3E3F02EAB_inline(G_B2_0, L_2, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* G_B2_0 = NULL;
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* G_B1_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B3_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_0;
		L_0 = AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42(AssistiveSupport_GetService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mF3C5608F9DB4825CFF0E0B53D2A8D6214012BF42_RuntimeMethod_var);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)(NULL));
		goto IL_0011;
	}

IL_000c:
	{
		NullCheck(G_B2_0);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_2;
		L_2 = AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947_inline(G_B2_0, NULL);
		G_B3_0 = L_2;
	}

IL_0011:
	{
		return G_B3_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssistiveSupport__cctor_m4088D8A848615B8528F864DBF58A6E628179FAB2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5* L_0 = (NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5*)il2cpp_codegen_object_new(NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5_il2cpp_TypeInfo_var);
		NotificationDispatcher__ctor_m4C2107C7F72DA9205175A3D3F5832F328E548F68(L_0, NULL);
		((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CnotificationDispatcherU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CnotificationDispatcherU3Ek__BackingField), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationDispatcher_Send_m44FE72134E006FF63A7E6A0B57629775C5ABE1FE (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* ___0_context, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* L_0 = ___0_context;
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationDispatcher_SendScreenChanged_mC11676B524097EB3CC3FE09B4952C9385393F5D6 (NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_nodeToFocus, const RuntimeMethod* method) 
{
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 V_0;
	memset((&V_0), 0, sizeof(V_0));
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 V_1;
	memset((&V_1), 0, sizeof(V_1));
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* G_B2_0 = NULL;
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* G_B3_1 = NULL;
	{
		il2cpp_codegen_initobj((&V_1), sizeof(AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059));
		AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_inline((&V_1), 4, NULL);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = ___0_nodeToFocus;
		if (!L_0)
		{
			G_B2_0 = (&V_1);
			goto IL_001f;
		}
		G_B1_0 = (&V_1);
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_1 = ___0_nodeToFocus;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(L_1, NULL);
		G_B3_0 = L_2;
		G_B3_1 = G_B1_0;
		goto IL_0020;
	}

IL_001f:
	{
		G_B3_0 = (-1);
		G_B3_1 = G_B2_0;
	}

IL_0020:
	{
		AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_inline(G_B3_1, G_B3_0, NULL);
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 L_3 = V_1;
		V_0 = L_3;
		NotificationDispatcher_Send_m44FE72134E006FF63A7E6A0B57629775C5ABE1FE((&V_0), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotificationDispatcher__ctor_m4C2107C7F72DA9205175A3D3F5832F328E548F68 (NotificationDispatcher_t15D6F99BF2E652B4D1EB6C6185F4CE7BA3B7BEE5* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, int32_t ___0_id, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___1_node, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDictionary_2_t32BE5EB8FB339477299955B6732035BC7442094A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		RuntimeObject* L_0 = __this->___m_Nodes;
		int32_t L_1 = ___0_id;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_2 = ___1_node;
		NullCheck(L_0);
		bool L_3;
		L_3 = InterfaceFuncInvoker2< bool, int32_t, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** >::Invoke(6, IDictionary_2_t32BE5EB8FB339477299955B6732035BC7442094A_il2cpp_TypeInfo_var, L_0, L_1, L_2);
		V_0 = L_3;
		goto IL_0011;
	}

IL_0011:
	{
		bool L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchy_FreeNative_m335828EAF3E5F150C16550316584F71CCAFB2758 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_mA0CAC0931F7997449B2A845BCC80BFF6201931D7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m8F631C235E82B173EBC9A21DA4F36945BBCFBBE7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_mA221CF61A78EDEF724FBF689237EAF6255FAB0D1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m3E320BF6EF20F80BB468E899EAD811296875BD2B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F V_0;
	memset((&V_0), 0, sizeof(V_0));
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	{
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_0 = __this->___m_RootNodes;
		NullCheck(L_0);
		Enumerator_t6E2CA672A68D335A260B7B18C890271A98923E9F L_1;
		L_1 = List_1_GetEnumerator_m3E320BF6EF20F80BB468E899EAD811296875BD2B(L_0, List_1_GetEnumerator_m3E320BF6EF20F80BB468E899EAD811296875BD2B_RuntimeMethod_var);
		V_0 = L_1;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_002d:
			{
				Enumerator_Dispose_mA0CAC0931F7997449B2A845BCC80BFF6201931D7((&V_0), Enumerator_Dispose_mA0CAC0931F7997449B2A845BCC80BFF6201931D7_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_0022_1;
			}

IL_0010_1:
			{
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_2;
				L_2 = Enumerator_get_Current_mA221CF61A78EDEF724FBF689237EAF6255FAB0D1_inline((&V_0), Enumerator_get_Current_mA221CF61A78EDEF724FBF689237EAF6255FAB0D1_RuntimeMethod_var);
				V_1 = L_2;
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_3 = V_1;
				NullCheck(L_3);
				AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E(L_3, (bool)1, NULL);
			}

IL_0022_1:
			{
				bool L_4;
				L_4 = Enumerator_MoveNext_m8F631C235E82B173EBC9A21DA4F36945BBCFBBE7((&V_0), Enumerator_MoveNext_m8F631C235E82B173EBC9A21DA4F36945BBCFBBE7_RuntimeMethod_var);
				if (L_4)
				{
					goto IL_0010_1;
				}
			}
			{
				goto IL_003c;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_003c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchy_TryGetNodeAt_m22C5D534645D8EFC73811373C2050E7E775F1990 (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* __this, float ___0_horizontalPosition, float ___1_verticalPosition, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___2_node, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	bool V_1 = false;
	{
		float L_0 = ___0_horizontalPosition;
		float L_1 = ___1_verticalPosition;
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&V_0), L_0, L_1, NULL);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_2 = ___2_node;
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_3 = __this->___m_RootNodes;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = V_0;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_5;
		L_5 = AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368(L_3, L_4, NULL);
		*((RuntimeObject**)L_2) = (RuntimeObject*)L_5;
		Il2CppCodeGenWriteBarrier((void**)(RuntimeObject**)L_2, (void*)(RuntimeObject*)L_5);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_6 = ___2_node;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7 = *((AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A**)L_6);
		V_1 = (bool)((!(((RuntimeObject*)(AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*)L_7) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		goto IL_0021;
	}

IL_0021:
	{
		bool L_8 = V_1;
		return L_8;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368 (RuntimeObject* ___0_nodes, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_pos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ICollection_1_t1A630FA95772D922E7E91E15B46C12790269682E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IList_1_t93ED4703263DD1FF8DBAB437D9860EA0A809A58F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_1 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_2 = NULL;
	bool V_3 = false;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_4 = NULL;
	bool V_5 = false;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_6;
	memset((&V_6), 0, sizeof(V_6));
	bool V_7 = false;
	int32_t G_B6_0 = 0;
	{
		RuntimeObject* L_0 = ___0_nodes;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(0, ICollection_1_t1A630FA95772D922E7E91E15B46C12790269682E_il2cpp_TypeInfo_var, L_0);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		goto IL_005a;
	}

IL_000c:
	{
		RuntimeObject* L_2 = ___0_nodes;
		int32_t L_3 = V_0;
		NullCheck(L_2);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_4;
		L_4 = InterfaceFuncInvoker1< AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*, int32_t >::Invoke(0, IList_1_t93ED4703263DD1FF8DBAB437D9860EA0A809A58F_il2cpp_TypeInfo_var, L_2, L_3);
		V_1 = L_4;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_5 = V_1;
		NullCheck(L_5);
		RuntimeObject* L_6;
		L_6 = AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA_inline(L_5, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = ___1_pos;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_8;
		L_8 = AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368(L_6, L_7, NULL);
		V_2 = L_8;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_9 = V_2;
		V_3 = (bool)((!(((RuntimeObject*)(AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*)L_9) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		bool L_10 = V_3;
		if (!L_10)
		{
			goto IL_002f;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_11 = V_2;
		V_4 = L_11;
		goto IL_006c;
	}

IL_002f:
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_12 = V_1;
		NullCheck(L_12);
		bool L_13;
		L_13 = AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D_inline(L_12, NULL);
		if (!L_13)
		{
			goto IL_0049;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_14 = V_1;
		NullCheck(L_14);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_15;
		L_15 = AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59(L_14, NULL);
		V_6 = L_15;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_16 = ___1_pos;
		bool L_17;
		L_17 = Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B_inline((&V_6), L_16, NULL);
		G_B6_0 = ((int32_t)(L_17));
		goto IL_004a;
	}

IL_0049:
	{
		G_B6_0 = 0;
	}

IL_004a:
	{
		V_5 = (bool)G_B6_0;
		bool L_18 = V_5;
		if (!L_18)
		{
			goto IL_0055;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_19 = V_1;
		V_4 = L_19;
		goto IL_006c;
	}

IL_0055:
	{
		int32_t L_20 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_20, 1));
	}

IL_005a:
	{
		int32_t L_21 = V_0;
		V_7 = (bool)((((int32_t)((((int32_t)L_21) < ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		bool L_22 = V_7;
		if (L_22)
		{
			goto IL_000c;
		}
	}
	{
		V_4 = (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*)NULL;
		goto IL_006c;
	}

IL_006c:
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_23 = V_4;
		return L_23;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_freeChildren, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerator_1_t55259DF17BC3DD8BD20C97403D960EB2B5D7121C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_GetEnumerator_mDD55511595F3AD2D4F9E3E08EC7135C74945D4C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_remove_listChanged_m9FEBF743CCBE2D9A6FC57D8F6D49D92D946CCAFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_remove_listChanged_mA48A54BCEB3EBD0C66E91913A0780D0ED9566AEB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	RuntimeObject* V_1 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* V_2 = NULL;
	bool V_3 = false;
	int32_t V_4 = 0;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B13_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B12_0 = NULL;
	int32_t G_B14_0 = 0;
	{
		bool L_0 = ___0_freeChildren;
		V_0 = L_0;
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_003d;
		}
	}
	{
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_2 = __this->___m_Children;
		NullCheck(L_2);
		RuntimeObject* L_3;
		L_3 = ObservableList_1_GetEnumerator_mDD55511595F3AD2D4F9E3E08EC7135C74945D4C7(L_2, ObservableList_1_GetEnumerator_mDD55511595F3AD2D4F9E3E08EC7135C74945D4C7_RuntimeMethod_var);
		V_1 = L_3;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0031:
			{
				{
					RuntimeObject* L_4 = V_1;
					if (!L_4)
					{
						goto IL_003b;
					}
				}
				{
					RuntimeObject* L_5 = V_1;
					NullCheck(L_5);
					InterfaceActionInvoker0::Invoke(0, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var, L_5);
				}

IL_003b:
				{
					return;
				}
			}
		});
		try
		{
			{
				goto IL_0027_1;
			}

IL_0016_1:
			{
				RuntimeObject* L_6 = V_1;
				NullCheck(L_6);
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_7;
				L_7 = InterfaceFuncInvoker0< AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* >::Invoke(0, IEnumerator_1_t55259DF17BC3DD8BD20C97403D960EB2B5D7121C_il2cpp_TypeInfo_var, L_6);
				V_2 = L_7;
				AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_8 = V_2;
				NullCheck(L_8);
				AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E(L_8, (bool)1, NULL);
			}

IL_0027_1:
			{
				RuntimeObject* L_9 = V_1;
				NullCheck(L_9);
				bool L_10;
				L_10 = InterfaceFuncInvoker0< bool >::Invoke(0, IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var, L_9);
				if (L_10)
				{
					goto IL_0016_1;
				}
			}
			{
				goto IL_003c;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_003c:
	{
	}

IL_003d:
	{
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_11 = __this->___m_Children;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_12 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_12, __this, (intptr_t)((void*)AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF_RuntimeMethod_var), NULL);
		NullCheck(L_11);
		ObservableList_1_remove_listChanged_m9FEBF743CCBE2D9A6FC57D8F6D49D92D946CCAFF(L_11, L_12, ObservableList_1_remove_listChanged_m9FEBF743CCBE2D9A6FC57D8F6D49D92D946CCAFF_RuntimeMethod_var);
		ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* L_13 = __this->___m_Actions;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_14 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_14, __this, (intptr_t)((void*)AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2_RuntimeMethod_var), NULL);
		NullCheck(L_13);
		ObservableList_1_remove_listChanged_mA48A54BCEB3EBD0C66E91913A0780D0ED9566AEB(L_13, L_14, ObservableList_1_remove_listChanged_mA48A54BCEB3EBD0C66E91913A0780D0ED9566AEB_RuntimeMethod_var);
		bool L_15;
		L_15 = AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C(__this, NULL);
		V_3 = L_15;
		bool L_16 = V_3;
		if (!L_16)
		{
			goto IL_009b;
		}
	}
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_17;
		L_17 = AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11_inline(__this, NULL);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_18 = L_17;
		if (L_18)
		{
			G_B13_0 = L_18;
			goto IL_0085;
		}
		G_B12_0 = L_18;
	}
	{
		G_B14_0 = (-1);
		goto IL_008a;
	}

IL_0085:
	{
		NullCheck(G_B13_0);
		int32_t L_19;
		L_19 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(G_B13_0, NULL);
		G_B14_0 = L_19;
	}

IL_008a:
	{
		V_4 = G_B14_0;
		int32_t L_20;
		L_20 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(__this, NULL);
		int32_t L_21 = V_4;
		AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43(L_20, L_21, NULL);
	}

IL_009b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CidU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Label;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Value;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Hint;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_IsActive;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = __this->___m_Role;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_AllowsDirectInteraction;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = __this->___m_State;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = __this->___m_Parent;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_0 = __this->___m_Children;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	bool V_0 = false;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_1;
	memset((&V_1), 0, sizeof(V_1));
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0 = __this->___m_Frame;
		il2cpp_codegen_initobj((&V_1), sizeof(Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D));
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_1 = V_1;
		bool L_2;
		L_2 = Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F_inline(L_0, L_1, NULL);
		V_0 = L_2;
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_0022;
		}
	}
	{
		AccessibilityNode_CalculateFrame_m0CD6C0D1DB2539DC74CDE281FAC23058D191E054(__this, NULL);
	}

IL_0022:
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_4 = __this->___m_Frame;
		V_2 = L_4;
		goto IL_002b;
	}

IL_002b:
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_5 = V_2;
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_SetFrame_mF216E2796C4A53212FD410C58C9734293FB5396B (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_frame, const RuntimeMethod* method) 
{
	bool V_0 = false;
	bool V_1 = false;
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0 = __this->___m_Frame;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_1 = ___0_frame;
		bool L_2;
		L_2 = Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F_inline(L_0, L_1, NULL);
		V_0 = L_2;
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_0014;
		}
	}
	{
		goto IL_0034;
	}

IL_0014:
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_4 = ___0_frame;
		__this->___m_Frame = L_4;
		bool L_5;
		L_5 = AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C(__this, NULL);
		V_1 = L_5;
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_7;
		L_7 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(__this, NULL);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_8 = ___0_frame;
		AccessibilityNodeManager_SetFrame_mE47923B10A1592690CEF29D975C2C22654AA6E0A(L_7, L_8, NULL);
	}

IL_0034:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* L_0 = __this->___m_FrameGetter;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_CalculateFrame_m0CD6C0D1DB2539DC74CDE281FAC23058D191E054 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* G_B2_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B2_1 = NULL;
	Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* G_B1_0 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B1_1 = NULL;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B3_0;
	memset((&G_B3_0), 0, sizeof(G_B3_0));
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B3_1 = NULL;
	{
		Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* L_0;
		L_0 = AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB_inline(__this, NULL);
		Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			G_B2_1 = __this;
			goto IL_0013;
		}
		G_B1_0 = L_1;
		G_B1_1 = __this;
	}
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_2;
		L_2 = Rect_get_zero_m5341D8B63DEF1F4C308A685EEC8CFEA12A396C8D(NULL);
		G_B3_0 = L_2;
		G_B3_1 = G_B1_1;
		goto IL_0018;
	}

IL_0013:
	{
		NullCheck(G_B2_0);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_3;
		L_3 = Func_1_Invoke_mB159D2E8978B240577A1C7A203FACF1B0F811011_inline(G_B2_0, NULL);
		G_B3_0 = L_3;
		G_B3_1 = G_B2_1;
	}

IL_0018:
	{
		NullCheck(G_B3_1);
		AccessibilityNode_SetFrame_mF216E2796C4A53212FD410C58C9734293FB5396B(G_B3_1, G_B3_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_Language;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_GetNodeData_m6C7765891167AB6E820D68F4AF89F891F03ACE2C (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* ___0_nodeData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_0 = NULL;
	int32_t V_1 = 0;
	bool V_2 = false;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B2_0 = NULL;
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* G_B2_1 = NULL;
	AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* G_B1_0 = NULL;
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* G_B1_1 = NULL;
	int32_t G_B3_0 = 0;
	AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* G_B3_1 = NULL;
	{
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_0 = ___0_nodeData;
		int32_t L_1;
		L_1 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(__this, NULL);
		AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_inline(L_0, L_1, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_2 = ___0_nodeData;
		bool L_3;
		L_3 = AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D_inline(__this, NULL);
		AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_inline(L_2, L_3, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_4 = ___0_nodeData;
		String_t* L_5;
		L_5 = AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2_inline(__this, NULL);
		AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_inline(L_4, L_5, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_6 = ___0_nodeData;
		String_t* L_7;
		L_7 = AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657_inline(__this, NULL);
		AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_inline(L_6, L_7, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_8 = ___0_nodeData;
		String_t* L_9;
		L_9 = AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A_inline(__this, NULL);
		AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_inline(L_8, L_9, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_10 = ___0_nodeData;
		uint16_t L_11;
		L_11 = AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F_inline(__this, NULL);
		AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_inline(L_10, L_11, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_12 = ___0_nodeData;
		bool L_13;
		L_13 = AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1_inline(__this, NULL);
		AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_inline(L_12, L_13, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_14 = ___0_nodeData;
		uint16_t L_15;
		L_15 = AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F_inline(__this, NULL);
		AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_inline(L_14, L_15, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_16 = ___0_nodeData;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_17;
		L_17 = AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59(__this, NULL);
		AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_inline(L_16, L_17, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_18 = ___0_nodeData;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_19;
		L_19 = AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11_inline(__this, NULL);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_20 = L_19;
		if (L_20)
		{
			G_B2_0 = L_20;
			G_B2_1 = L_18;
			goto IL_0084;
		}
		G_B1_0 = L_20;
		G_B1_1 = L_18;
	}
	{
		G_B3_0 = (-1);
		G_B3_1 = G_B1_1;
		goto IL_0089;
	}

IL_0084:
	{
		NullCheck(G_B2_0);
		int32_t L_21;
		L_21 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(G_B2_0, NULL);
		G_B3_0 = L_21;
		G_B3_1 = G_B2_1;
	}

IL_0089:
	{
		AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_inline(G_B3_1, G_B3_0, NULL);
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_22 = __this->___m_Children;
		NullCheck(L_22);
		int32_t L_23;
		L_23 = ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125(L_22, ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_23);
		V_0 = L_24;
		V_1 = 0;
		goto IL_00be;
	}

IL_00a4:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_25 = V_0;
		int32_t L_26 = V_1;
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_27 = __this->___m_Children;
		int32_t L_28 = V_1;
		NullCheck(L_27);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_29;
		L_29 = ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B(L_27, L_28, ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B_RuntimeMethod_var);
		NullCheck(L_29);
		int32_t L_30;
		L_30 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(L_29, NULL);
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(L_26), (int32_t)L_30);
		int32_t L_31 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_31, 1));
	}

IL_00be:
	{
		int32_t L_32 = V_1;
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_33 = __this->___m_Children;
		NullCheck(L_33);
		int32_t L_34;
		L_34 = ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125(L_33, ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		V_2 = (bool)((((int32_t)L_32) < ((int32_t)L_34))? 1 : 0);
		bool L_35 = V_2;
		if (L_35)
		{
			goto IL_00a4;
		}
	}
	{
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_36 = ___0_nodeData;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_37 = V_0;
		AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_inline(L_36, L_37, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_38 = ___0_nodeData;
		int32_t L_39;
		L_39 = AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F_inline(__this, NULL);
		AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_inline(L_38, L_39, NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_40 = ___0_nodeData;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_41 = __this->___selected;
		AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_inline(L_40, (bool)((!(((RuntimeObject*)(Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*)L_41) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0), NULL);
		AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* L_42 = ___0_nodeData;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_43 = __this->___dismissed;
		AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_inline(L_42, (bool)((!(((RuntimeObject*)(Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*)L_43) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_0 = NULL;
	bool V_1 = false;
	int32_t V_2 = 0;
	bool V_3 = false;
	{
		bool L_0;
		L_0 = AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C(__this, NULL);
		V_1 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		bool L_1 = V_1;
		if (!L_1)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_005f;
	}

IL_0011:
	{
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_2 = __this->___m_Children;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125(L_2, ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_3);
		V_0 = L_4;
		V_2 = 0;
		goto IL_0040;
	}

IL_0026:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = V_0;
		int32_t L_6 = V_2;
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_7 = __this->___m_Children;
		int32_t L_8 = V_2;
		NullCheck(L_7);
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_9;
		L_9 = ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B(L_7, L_8, ObservableList_1_get_Item_m6B8E46652CA30C660F855101767EBC80F6EA905B_RuntimeMethod_var);
		NullCheck(L_9);
		int32_t L_10;
		L_10 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(L_9, NULL);
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(L_6), (int32_t)L_10);
		int32_t L_11 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_11, 1));
	}

IL_0040:
	{
		int32_t L_12 = V_2;
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_13 = __this->___m_Children;
		NullCheck(L_13);
		int32_t L_14;
		L_14 = ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125(L_13, ObservableList_1_get_Count_mD262AE41D44CC40F1650BBCABD73E2E72B4C3125_RuntimeMethod_var);
		V_3 = (bool)((((int32_t)L_12) < ((int32_t)L_14))? 1 : 0);
		bool L_15 = V_3;
		if (L_15)
		{
			goto IL_0026;
		}
	}
	{
		int32_t L_16;
		L_16 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(__this, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_17 = V_0;
		AccessibilityNodeManager_SetChildren_mE43C7A8C5CDC9D8049088AC1A89E0BDC4015C7C9(L_16, L_17, NULL);
	}

IL_005f:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObservableList_1_get_Item_m91FF17479989BACC1C32F8EE53542CD249BFCC46_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* V_0 = NULL;
	bool V_1 = false;
	int32_t V_2 = 0;
	bool V_3 = false;
	{
		bool L_0;
		L_0 = AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C(__this, NULL);
		V_1 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		bool L_1 = V_1;
		if (!L_1)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_005a;
	}

IL_0011:
	{
		ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* L_2 = __this->___m_Actions;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A(L_2, ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A_RuntimeMethod_var);
		AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* L_4 = (AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9*)(AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9*)SZArrayNew(AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9_il2cpp_TypeInfo_var, (uint32_t)L_3);
		V_0 = L_4;
		V_2 = 0;
		goto IL_003b;
	}

IL_0026:
	{
		AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* L_5 = V_0;
		int32_t L_6 = V_2;
		ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* L_7 = __this->___m_Actions;
		int32_t L_8 = V_2;
		NullCheck(L_7);
		AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* L_9;
		L_9 = ObservableList_1_get_Item_m91FF17479989BACC1C32F8EE53542CD249BFCC46(L_7, L_8, ObservableList_1_get_Item_m91FF17479989BACC1C32F8EE53542CD249BFCC46_RuntimeMethod_var);
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(L_6), (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39*)L_9);
		int32_t L_10 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_003b:
	{
		int32_t L_11 = V_2;
		ObservableList_1_t9D40D8D9742C416D029E3C3488C12AAD03FBCEF3* L_12 = __this->___m_Actions;
		NullCheck(L_12);
		int32_t L_13;
		L_13 = ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A(L_12, ObservableList_1_get_Count_m2B83F8BDE3EFF46833AF1E8BD8C18DCC904FD06A_RuntimeMethod_var);
		V_3 = (bool)((((int32_t)L_11) < ((int32_t)L_13))? 1 : 0);
		bool L_14 = V_3;
		if (L_14)
		{
			goto IL_0026;
		}
	}
	{
		int32_t L_15;
		L_15 = AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline(__this, NULL);
		AccessibilityActionU5BU5D_tC6842195F6997922EE0952E43E529F99AD52C6D9* L_16 = V_0;
		AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B(L_15, L_16, NULL);
	}

IL_005a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		if (!L_0)
		{
			goto IL_0018;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_1;
		L_1 = AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE(NULL);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_2 = __this->___m_Hierarchy;
		G_B3_0 = ((((RuntimeObject*)(AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)L_1) == ((RuntimeObject*)(AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)L_2))? 1 : 0);
		goto IL_0019;
	}

IL_0018:
	{
		G_B3_0 = 0;
	}

IL_0019:
	{
		V_0 = (bool)G_B3_0;
		goto IL_001c;
	}

IL_001c:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_NotifyFocusChanged_mEA14C3D3534CB0920ECD5AFF32350855BC6FD5E8 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_isNodeFocused, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A V_0;
	memset((&V_0), 0, sizeof(V_0));
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* G_B2_0 = NULL;
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* G_B3_1 = NULL;
	{
		il2cpp_codegen_initobj((&V_0), sizeof(NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A));
		bool L_0 = ___0_isNodeFocused;
		if (L_0)
		{
			G_B2_0 = (&V_0);
			goto IL_0011;
		}
		G_B1_0 = (&V_0);
	}
	{
		G_B3_0 = 8;
		G_B3_1 = G_B1_0;
		goto IL_0012;
	}

IL_0011:
	{
		G_B3_0 = 7;
		G_B3_1 = G_B2_0;
	}

IL_0012:
	{
		NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline(G_B3_1, G_B3_0, NULL);
		NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_inline((&V_0), __this, NULL);
		NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, bool ___0_isNodeFocused, const RuntimeMethod* method) 
{
	Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* G_B2_0 = NULL;
	Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* G_B1_0 = NULL;
	{
		Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* L_0 = __this->___focusChanged;
		Action_2_t5E0D02FD88380D5F5CE4CBCBB796B8841C1C6A22* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0015;
	}

IL_000d:
	{
		bool L_2 = ___0_isNodeFocused;
		NullCheck(G_B2_0);
		Action_2_Invoke_m30CDE1DE68599A78DD318994DC64A3BE51FC7081_inline(G_B2_0, __this, L_2, NULL);
	}

IL_0015:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_InvokeSelected_mBB094C8957C71FBCA0C033A4F9EE7127B8D1A7BA (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	bool V_0 = false;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B2_0 = NULL;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = __this->___selected;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000e;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = 0;
		goto IL_0013;
	}

IL_000e:
	{
		NullCheck(G_B2_0);
		bool L_2;
		L_2 = Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline(G_B2_0, NULL);
		G_B3_0 = ((int32_t)(L_2));
	}

IL_0013:
	{
		V_0 = (bool)G_B3_0;
		goto IL_0016;
	}

IL_0016:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeIncremented_mC2B5AF750A1BC3177E92787DAA385360234B59AF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___incremented;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityNode_InvokeDecremented_m3BF11CFA927B9303367B46C9EA7E0E99A5C422D5 (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___decremented;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000d;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0013;
	}

IL_000d:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0013:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityNode_Dismissed_m7492D9E72948C6718A13A2FDB628BDC4E81091EF (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	bool V_0 = false;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B2_0 = NULL;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = __this->___dismissed;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000e;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = 0;
		goto IL_0013;
	}

IL_000e:
	{
		NullCheck(G_B2_0);
		bool L_2;
		L_2 = Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline(G_B2_0, NULL);
		G_B3_0 = ((int32_t)(L_2));
	}

IL_0013:
	{
		V_0 = (bool)G_B3_0;
		goto IL_0016;
	}

IL_0016:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService_Start_mEC19D7908745553B772EC14E4AF0CE4492135B67 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService_Stop_mEA1D729479D0BC1458D851F1B1706EF420351794 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		V_0 = (bool)((((RuntimeObject*)(AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_0019;
	}

IL_0011:
	{
		AccessibilityHierarchyService_RemoveActiveHierarchy_m61D66D2B65B8E17B08948962B227203C35DF5A3A(__this, (bool)1, NULL);
	}

IL_0019:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService_RemoveActiveHierarchy_m61D66D2B65B8E17B08948962B227203C35DF5A3A (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, bool ___0_notifyScreenChanged, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAccessibilityNotificationDispatcher_tD658A5E653A35BD981B0CA47C69660F39046A805_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		V_0 = (bool)((((RuntimeObject*)(AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0011;
		}
	}
	{
		goto IL_0037;
	}

IL_0011:
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_2 = __this->___m_Hierarchy;
		NullCheck(L_2);
		AccessibilityHierarchy_FreeNative_m335828EAF3E5F150C16550316584F71CCAFB2758(L_2, NULL);
		__this->___m_Hierarchy = (AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Hierarchy), (void*)(AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5*)NULL);
		bool L_3 = ___0_notifyScreenChanged;
		V_1 = L_3;
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_0037;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		RuntimeObject* L_5;
		L_5 = AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC_inline(NULL);
		NullCheck(L_5);
		InterfaceActionInvoker1< AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* >::Invoke(0, IAccessibilityNotificationDispatcher_tD658A5E653A35BD981B0CA47C69660F39046A805_il2cpp_TypeInfo_var, L_5, (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A*)NULL);
	}

IL_0037:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, int32_t ___0_id, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___1_node, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_0 = ___1_node;
		*((RuntimeObject**)L_0) = (RuntimeObject*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(RuntimeObject**)L_0, (void*)(RuntimeObject*)NULL);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_1 = __this->___m_Hierarchy;
		if (!L_1)
		{
			goto IL_001b;
		}
	}
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_2 = __this->___m_Hierarchy;
		int32_t L_3 = ___0_id;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_4 = ___1_node;
		NullCheck(L_2);
		bool L_5;
		L_5 = AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1(L_2, L_3, L_4, NULL);
		G_B3_0 = ((int32_t)(L_5));
		goto IL_001c;
	}

IL_001b:
	{
		G_B3_0 = 0;
	}

IL_001c:
	{
		V_0 = (bool)G_B3_0;
		goto IL_001f;
	}

IL_001f:
	{
		bool L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2 (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* V_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B2_0 = NULL;
	AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* G_B1_0 = NULL;
	List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* G_B3_0 = NULL;
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000e;
		}
		G_B1_0 = L_1;
	}
	{
		G_B3_0 = ((List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53*)(NULL));
		goto IL_0013;
	}

IL_000e:
	{
		NullCheck(G_B2_0);
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_2 = G_B2_0->___m_RootNodes;
		G_B3_0 = L_2;
	}

IL_0013:
	{
		V_0 = G_B3_0;
		goto IL_0016;
	}

IL_0016:
	{
		List_1_t77DBBD799DC571D57C9E011C996CA77CC6A00A53* L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AccessibilityHierarchyService_TryGetNodeAt_mBEDA9E8FBF12970339369F80650637BE17E2280F (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, float ___0_x, float ___1_y, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** ___2_node, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_0 = ___2_node;
		*((RuntimeObject**)L_0) = (RuntimeObject*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(RuntimeObject**)L_0, (void*)(RuntimeObject*)NULL);
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_1 = __this->___m_Hierarchy;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_2 = __this->___m_Hierarchy;
		float L_3 = ___0_x;
		float L_4 = ___1_y;
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A** L_5 = ___2_node;
		NullCheck(L_2);
		bool L_6;
		L_6 = AccessibilityHierarchy_TryGetNodeAt_m22C5D534645D8EFC73811373C2050E7E775F1990(L_2, L_3, L_4, L_5, NULL);
		G_B3_0 = ((int32_t)(L_6));
		goto IL_001d;
	}

IL_001c:
	{
		G_B3_0 = 0;
	}

IL_001d:
	{
		V_0 = (bool)G_B3_0;
		goto IL_0020;
	}

IL_0020:
	{
		bool L_7 = V_0;
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AccessibilityHierarchyService__ctor_mE2F69255846F000B58E412306376C09C02DB276B (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager__ctor_m06C1D1E60CF676E1E526A3E281757604BA5C9F3A (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_m7DF602445450A415A55C429BDC7EA83620B8B69C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E* L_0 = (Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E*)il2cpp_codegen_object_new(Dictionary_2_t303DCA18E8F72F03F9743103D41E82C26EA2367E_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_m7DF602445450A415A55C429BDC7EA83620B8B69C(L_0, Dictionary_2__ctor_m7DF602445450A415A55C429BDC7EA83620B8B69C_RuntimeMethod_var);
		__this->___m_Services = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Services), (void*)L_0);
		Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* L_1 = (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C*)il2cpp_codegen_object_new(Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C_il2cpp_TypeInfo_var);
		Action_1__ctor_mA8C3AC97D1F076EA5D1D0C10CEE6BD3E94711501(L_1, __this, (intptr_t)((void*)ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(AccessibilityManager_t71794D0AED3CEA45E40BDFD6D8EFB62A5B4E91C8_il2cpp_TypeInfo_var);
		AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80_inline(NULL);
		ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582(__this, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582 (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, bool ___0_isScreenReaderEnabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDictionary_2_t5C66CA874FF1BDA090CD606305BA7497929CA042_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ServiceManager_StopService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mB7E9384AD001976B09B8E5709D5232EDCE16AF1E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* V_2 = NULL;
	{
		bool L_0 = ___0_isScreenReaderEnabled;
		V_0 = L_0;
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_004c;
		}
	}
	{
		RuntimeObject* L_2 = __this->___m_Services;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_3 = { reinterpret_cast<intptr_t> (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(il2cpp_defaults.systemtype_class);
		Type_t* L_4;
		L_4 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_3, NULL);
		NullCheck(L_2);
		bool L_5;
		L_5 = InterfaceFuncInvoker1< bool, Type_t* >::Invoke(3, IDictionary_2_t5C66CA874FF1BDA090CD606305BA7497929CA042_il2cpp_TypeInfo_var, L_2, L_4);
		V_1 = (bool)((((int32_t)L_5) == ((int32_t)0))? 1 : 0);
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0049;
		}
	}
	{
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_7 = (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3*)il2cpp_codegen_object_new(AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_il2cpp_TypeInfo_var);
		AccessibilityHierarchyService__ctor_mE2F69255846F000B58E412306376C09C02DB276B(L_7, NULL);
		V_2 = L_7;
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_8 = V_2;
		NullCheck(L_8);
		AccessibilityHierarchyService_Start_mEC19D7908745553B772EC14E4AF0CE4492135B67(L_8, NULL);
		RuntimeObject* L_9 = __this->___m_Services;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_10 = { reinterpret_cast<intptr_t> (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(il2cpp_defaults.systemtype_class);
		Type_t* L_11;
		L_11 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_10, NULL);
		AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* L_12 = V_2;
		NullCheck(L_9);
		InterfaceActionInvoker2< Type_t*, RuntimeObject* >::Invoke(4, IDictionary_2_t5C66CA874FF1BDA090CD606305BA7497929CA042_il2cpp_TypeInfo_var, L_9, L_11, L_12);
	}

IL_0049:
	{
		goto IL_0055;
	}

IL_004c:
	{
		ServiceManager_StopService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mB7E9384AD001976B09B8E5709D5232EDCE16AF1E(__this, ServiceManager_StopService_TisAccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3_mB7E9384AD001976B09B8E5709D5232EDCE16AF1E_RuntimeMethod_var);
	}

IL_0055:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2 (ServiceManager_t34B31F2EDF42C15BBD978EDCC6B759539743DEDF* __this, bool ___0_isScreenReaderEnabled, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_isScreenReaderEnabled;
		ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582(__this, L_0, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B_inline (intptr_t ___0_value1, intptr_t ___1_value2, const RuntimeMethod* method) 
{
	{
		intptr_t L_0 = ___0_value1;
		intptr_t L_1 = ___1_value2;
		return (bool)((((int32_t)((((intptr_t)L_0) == ((intptr_t)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281_inline (AccessibilityAction_t9D7F613262A6A131D2997F6802D4927131E46C39* __this, const RuntimeMethod* method) 
{
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = __this->___U3CactivatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnotificationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = __this->___U3CcurrentNodeU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CfontScaleU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisBoldTextEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisClosedCaptioningEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CidU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnotificationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnotificationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisScreenReaderEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3CannouncementU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CannouncementU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CwasAnnouncementSuccessfulU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = ___0_value;
		__this->___U3CcurrentNodeU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CcurrentNodeU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = ___0_value;
		__this->___U3CnextNodeU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CnextNodeU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CfontScaleU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisBoldTextEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisClosedCaptioningEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_inline (NotificationContext_tDD3427B7EC4DB358C79E077AEE029BBFE128AD2A* __this, AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 ___0_value, const RuntimeMethod* method) 
{
	{
		AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059 L_0 = ___0_value;
		__this->___U3CnativeContextU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->___U3CnativeContextU3Ek__BackingField))->___U3CannouncementU3Ek__BackingField), (void*)NULL);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CannouncementU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CwasAnnouncementSuccessfulU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CcurrentNodeIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CnextNodeIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CidU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisActiveU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3ClabelU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3ClabelU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3CvalueU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CvalueU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___U3ChintU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3ChintU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = ___0_value;
		__this->___U3CroleU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CallowsDirectInteractionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = ___0_value;
		__this->___U3CstateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method) 
{
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0 = ___0_value;
		__this->___U3CframeU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CparentIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_value, const RuntimeMethod* method) 
{
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ___0_value;
		__this->___U3CchildIdsU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CchildIdsU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3ClanguageU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CimplementsSelectedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_inline (AccessibilityNodeData_t71E51E68FC82F42482E2FFC6122745E9CCC80115* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CimplementsDismissedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnotificationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_inline (AccessibilityNotificationContext_t074DE74548FF9F28BB9D9A4A5714FCBD4F833059* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CnextNodeIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340_inline (bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CisScreenReaderEnabledU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		bool L_0 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CisScreenReaderEnabledU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947_inline (AccessibilityHierarchyService_tBD2815280FE4AB3C14158F8BF261B48CB66B9FB3* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityHierarchy_tC01958B8D225A54541D0E340027FC1CA9081EAD5* L_0 = __this->___m_Hierarchy;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		ObservableList_1_tD717114CD76F399DA2D08FF1E3731DE0E0668889* L_0 = __this->___m_Children;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_IsActive;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_point, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t G_B5_0 = 0;
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_point;
		float L_1 = L_0.___x;
		float L_2;
		L_2 = Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D_inline(__this, NULL);
		if ((!(((float)L_1) >= ((float)L_2))))
		{
			goto IL_003b;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___0_point;
		float L_4 = L_3.___x;
		float L_5;
		L_5 = Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F_inline(__this, NULL);
		if ((!(((float)L_4) < ((float)L_5))))
		{
			goto IL_003b;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = ___0_point;
		float L_7 = L_6.___y;
		float L_8;
		L_8 = Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F_inline(__this, NULL);
		if ((!(((float)L_7) >= ((float)L_8))))
		{
			goto IL_003b;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9 = ___0_point;
		float L_10 = L_9.___y;
		float L_11;
		L_11 = Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E_inline(__this, NULL);
		G_B5_0 = ((((float)L_10) < ((float)L_11))? 1 : 0);
		goto IL_003c;
	}

IL_003b:
	{
		G_B5_0 = 0;
	}

IL_003c:
	{
		V_0 = (bool)G_B5_0;
		goto IL_003f;
	}

IL_003f:
	{
		bool L_12 = V_0;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* L_0 = __this->___m_Parent;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_lhs, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___1_rhs, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t G_B5_0 = 0;
	{
		float L_0;
		L_0 = Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_inline((&___0_lhs), NULL);
		float L_1;
		L_1 = Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_inline((&___1_rhs), NULL);
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0043;
		}
	}
	{
		float L_2;
		L_2 = Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_inline((&___0_lhs), NULL);
		float L_3;
		L_3 = Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_inline((&___1_rhs), NULL);
		if ((!(((float)L_2) == ((float)L_3))))
		{
			goto IL_0043;
		}
	}
	{
		float L_4;
		L_4 = Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_inline((&___0_lhs), NULL);
		float L_5;
		L_5 = Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_inline((&___1_rhs), NULL);
		if ((!(((float)L_4) == ((float)L_5))))
		{
			goto IL_0043;
		}
	}
	{
		float L_6;
		L_6 = Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline((&___0_lhs), NULL);
		float L_7;
		L_7 = Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline((&___1_rhs), NULL);
		G_B5_0 = ((((float)L_6) == ((float)L_7))? 1 : 0);
		goto IL_0044;
	}

IL_0043:
	{
		G_B5_0 = 0;
	}

IL_0044:
	{
		V_0 = (bool)G_B5_0;
		goto IL_0047;
	}

IL_0047:
	{
		bool L_8 = V_0;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* L_0 = __this->___m_FrameGetter;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Label;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Value;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Hint;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = __this->___m_Role;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_AllowsDirectInteraction;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = __this->___m_State;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F_inline (AccessibilityNode_t9998B596BE3A0232C1CFE3B2610DF52E0338A49A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_Language;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_StaticFields*)il2cpp_codegen_static_fields_for(AssistiveSupport_t9D45A07E640AC2F1CD8A7A1C6D9CFB050CD43001_il2cpp_TypeInfo_var))->___U3CnotificationDispatcherU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_mFCF6B2262BA68FBF7AD3DE1B323BC76126AC7E6F_gshared_inline (Queue_1_tD224EE31B5C1***************************** __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m69C8773D6967F3B224777183E24EA621CE056F8F_gshared_inline (Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* __this, bool ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, bool, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_m0248A96C5334E9A93E6994B7780478BCD994EA3D_gshared_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, int32_t ___0_item, const RuntimeMethod* method) 
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = V_0;
		int32_t L_7 = V_1;
		int32_t L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (int32_t)L_8);
		return;
	}

IL_0034:
	{
		int32_t L_9 = ___0_item;
		List_1_AddWithResize_m378B392086AAB6F400944FA9839516326B3F7BB8(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_mF590592E32D421DE2C6E2F0D5C2F62FB14CCEFDF_gshared_inline (List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m176441CFA181B7C6097611CC13C24C5ED7F14CFF_gshared_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_array, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ___0_array;
		if (L_0)
		{
			goto IL_000b;
		}
	}
	{
		il2cpp_codegen_initobj(__this, sizeof(Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316));
		return;
	}

IL_000b:
	{
		il2cpp_codegen_initobj((&V_0), sizeof(int32_t));
		goto IL_0037;
	}

IL_0037:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = ___0_array;
		NullCheck((RuntimeArray*)L_2);
		uint8_t* L_3;
		L_3 = Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline((RuntimeArray*)L_2, NULL);
		int32_t* L_4;
		L_4 = il2cpp_unsafe_as_ref<int32_t>(L_3);
		ByReference_1_tDDF129F0BC02430629D5CD253C681112F166BAD4 L_5;
		memset((&L_5), 0, sizeof(L_5));
		il2cpp_codegen_by_reference_constructor((Il2CppByReference*)(&L_5), L_4);
		__this->____pointer = L_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = ___0_array;
		NullCheck(L_6);
		__this->____length = ((int32_t)(((RuntimeArray*)L_6)->max_length));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m87AB3C694F2E4802F14D006F21C020816045285F_gshared_inline (Span_1_t3C5DB525B005B1AC5A1F3BDD528900C5C7C7D316* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____length;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, float, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Func_1_Invoke_mB159D2E8978B240577A1C7A203FACF1B0F811011_gshared_inline (Func_1_t41D01DF7E7DCF728A5AC2E27578C9EF76548EF27* __this, const RuntimeMethod* method) 
{
	typedef Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_mB2DD87F61EB655A33F6277F1E277246CE23B6625_gshared_inline (Action_2_t5BCD350E28ADACED656596CC308132ED74DA0915* __this, RuntimeObject* ___0_arg1, bool ___1_arg2, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, bool, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_arg1, ___1_arg2, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_XMin;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Width;
		float L_1 = __this->___m_XMin;
		V_0 = ((float)il2cpp_codegen_add(L_0, L_1));
		goto IL_0011;
	}

IL_0011:
	{
		float L_2 = V_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_YMin;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Height;
		float L_1 = __this->___m_YMin;
		V_0 = ((float)il2cpp_codegen_add(L_0, L_1));
		goto IL_0011;
	}

IL_0011:
	{
		float L_2 = V_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_XMin;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_YMin;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Width;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Height;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) 
{
	{
		RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0* L_0;
		L_0 = il2cpp_unsafe_as<RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0*>(__this);
		NullCheck(L_0);
		uint8_t* L_1 = (uint8_t*)(&L_0->___Data);
		return L_1;
	}
}
