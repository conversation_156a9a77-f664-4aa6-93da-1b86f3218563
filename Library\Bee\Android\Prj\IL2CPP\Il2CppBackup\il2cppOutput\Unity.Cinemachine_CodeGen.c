﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m70904321B856163FB995EF18FA4AC8DF0713A7B3 (void);
extern void NullableAttribute__ctor_mAC620FEA91AFCA45A4702F215899630225CB5F8C (void);
extern void NullableAttribute__ctor_mC3A7B4A12D65E88F3FA0F8B3E6BE7F424C676E0E (void);
extern void NullableContextAttribute__ctor_m3E72024F166F94B95840C6F08F269F91A57931C4 (void);
extern void DeltaTimeScaleProcessor_Process_mD05943ABEA1F32E113448687077D8B3A13EB036E (void);
extern void DeltaTimeScaleProcessor_Initialize_mC3059939F7F27B1CAFD4943C0BFFC3B4C83E2148 (void);
extern void DeltaTimeScaleProcessor__ctor_m2847E957260B494B9C2DE8DF3CB413FDEFE6A23F (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m590CD099179B8C46ED732206271733292F530038 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE3032AA856CA2A08CC1F1821424B816CCE717EB6 (void);
extern void CinemachineBrain_OnValidate_m6BD74D5C6476F2E9B2E6639B55D764994E43F0AD (void);
extern void CinemachineBrain_Reset_m5F9D248F18428B84D03E4A6E88B96CD7968433D4 (void);
extern void CinemachineBrain_Awake_m7D6C58BE1DA5040291886D45A0EABF76EEC24816 (void);
extern void CinemachineBrain_Start_m3FD7E7B86B38428745D402B049C2BBA046D90D86 (void);
extern void CinemachineBrain_OnEnable_m15FE43E2D79E36F9E2821115FAC91A26000A9C64 (void);
extern void CinemachineBrain_OnDisable_mF6C43DC096BDBA016A102D9E9D5CD55B37D1F469 (void);
extern void CinemachineBrain_OnSceneLoaded_m613D48ED4946143A9F65C334AA9A3C70BABA7555 (void);
extern void CinemachineBrain_OnSceneUnloaded_m7F73D969A70AB5261A5BD2894C92BD9EC8582048 (void);
extern void CinemachineBrain_LateUpdate_m273D0B2C8D56257125FEA2E943EA7317224D52A1 (void);
extern void CinemachineBrain_AfterPhysics_mACD16F9B6B975FAC12264656C6D5E1F33340AC02 (void);
extern void CinemachineBrain_SetCameraOverride_mA3700DE459BC9D8CB218FC8CC36E90B1D8B545C7 (void);
extern void CinemachineBrain_ReleaseCameraOverride_m7563ECE0742605E04B44188D08FA1912FA1C126C (void);
extern void CinemachineBrain_get_DefaultWorldUp_m2122F7DD2D62FFCC723124B17B10C01A002F1DE6 (void);
extern void CinemachineBrain_get_Name_mDB3ED3322485D929661636716FDAE2A017240826 (void);
extern void CinemachineBrain_get_Description_m170518F8C93AEB594AE4ED181D7EB91EC32B320C (void);
extern void CinemachineBrain_get_State_m06BDD08B2A3E89CEF1E79BEE970743C34380D780 (void);
extern void CinemachineBrain_get_IsValid_m923DED4EC47D3F63E1B601144593D35853F72E89 (void);
extern void CinemachineBrain_get_ParentCamera_m2D655360E3E08636EFDFEB870E4A1F19259C025F (void);
extern void CinemachineBrain_UpdateCameraState_m05AD0FECAA5B6700D4714155BDB5F02C72C2AEFD (void);
extern void CinemachineBrain_OnCameraActivated_m995C58054D28F478DC2C748718D39CD88DEF98A9 (void);
extern void CinemachineBrain_IsLiveChild_m26488818988024D5DFE45523BCC8DF31CB3562B2 (void);
extern void CinemachineBrain_get_ActiveBrainCount_m6806FFBB7509410B5EA9DB3DF9C47075FDD03565 (void);
extern void CinemachineBrain_GetActiveBrain_mFBB9D0EB63B9AD7E28A0FC54872CD3921C52A406 (void);
extern void CinemachineBrain_get_ControlledObject_mD932AFC116C8EC2030B6D11719E557D3A0E5C73D (void);
extern void CinemachineBrain_set_ControlledObject_mB9E57A20E50494429FE91C66E1236B2DA470F72A (void);
extern void CinemachineBrain_get_OutputCamera_m5CFDD1E8407905D23B2FB08E022A1D814D8914C0 (void);
extern void CinemachineBrain_get_ActiveVirtualCamera_m0225A2B45278C98C997EFE7F2DC815EE4A097E39 (void);
extern void CinemachineBrain_ResetState_mC40C0088DE2EC4EF9FCCD609A8E6398599D38397 (void);
extern void CinemachineBrain_get_IsBlending_m16D72A89C93514280DEA838D441543D3EC000D47 (void);
extern void CinemachineBrain_get_ActiveBlend_m2CA0763A4C6DAF8158049048079C9505F90C346C (void);
extern void CinemachineBrain_set_ActiveBlend_m4E34E5F0BFF127EE547CA007B652F7B0BE05C958 (void);
extern void CinemachineBrain_IsValidChannel_mA5527B436E5E1EDA4F13A81F630528E07F19A392 (void);
extern void CinemachineBrain_IsLiveInBlend_m3F13625AE4D3B68A6B1508A193518E6DE76ACC1E (void);
extern void CinemachineBrain_ManualUpdate_mA69CA0F5CEA30C0D60100535D89046D81AFF7135 (void);
extern void CinemachineBrain_ManualUpdate_m5936507D04F17DA4FF4CD6328EED9782800C2BB8 (void);
extern void CinemachineBrain_DoNonFixedUpdate_m79D014CBEA5F5E1FAA41E2EDBBD4AC2AA76C2591 (void);
extern void CinemachineBrain_DoFixedUpdate_m2F5FCDA24AEB65DB21CF1D31714BDE168BE2CADF (void);
extern void CinemachineBrain_GetEffectiveDeltaTime_m474FC97364243742813814850864EF715EABA83E (void);
extern void CinemachineBrain_UpdateVirtualCameras_mF3CCAEC53FCC46788149976D0C623B5EC4C859E7 (void);
extern void CinemachineBrain_TopCameraFromPriorityQueue_mBDD27467260B33609EC22D2D8F4D3B4EEE0D646D (void);
extern void CinemachineBrain_LookupBlend_mD524EEED25317C8A700BFD4B47528BE8C926BFD1 (void);
extern void CinemachineBrain_ProcessActiveCamera_mDE9A82F45478D9A7B37A8977DA637DEC8449487F (void);
extern void CinemachineBrain_PushStateToUnityCamera_m052CF484325426A6C1124C8B185458EF62A821CE (void);
extern void CinemachineBrain__ctor_mBEA2B1A0DF89ACD59EA4FE69175763963FD3F179 (void);
extern void CinemachineBrain__cctor_m4BA08B4C4D05D0F0BD522677BBA9106EC39E2A04 (void);
extern void U3CAfterPhysicsU3Ed__30__ctor_m1CCD1D0619555E2C10AE28A798B7BBD6EF0FD33A (void);
extern void U3CAfterPhysicsU3Ed__30_System_IDisposable_Dispose_m9BE880E76D65195258F76FF744EC99A3BFF6B631 (void);
extern void U3CAfterPhysicsU3Ed__30_MoveNext_mA11155ACA4C3322D83D1F9CCB61B76D0ED921D08 (void);
extern void U3CAfterPhysicsU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB566F234A1ABA66A284CF1615B484CCF646CF150 (void);
extern void U3CAfterPhysicsU3Ed__30_System_Collections_IEnumerator_Reset_mA7DD32378FEAF33372FAA887BA01B252A91017C5 (void);
extern void U3CAfterPhysicsU3Ed__30_System_Collections_IEnumerator_get_Current_m34F243F1AADB96E300611381F07B87639ACE5DDD (void);
extern void CinemachineCamera_Reset_mB6F7B9372F2FDF75ECABAD75C9CFF65648EFBF41 (void);
extern void CinemachineCamera_OnValidate_m1A53BC3B538FFE4C5010A034BA6AC8535934C1CF (void);
extern void CinemachineCamera_get_State_m0181FB952F0405E45D48DFD04FB6D03CDC3A4DE2 (void);
extern void CinemachineCamera_get_LookAt_m6E4E26A668D21EDDB1A67F411E3A02792429AB14 (void);
extern void CinemachineCamera_set_LookAt_mE2316C558A8E9BDA5AE3ED182B314B561E7F338C (void);
extern void CinemachineCamera_get_Follow_m9C67B67F2032E494C75ADC442B1CBA63FB101250 (void);
extern void CinemachineCamera_set_Follow_m6F5256B4D24CC996ED66E9BEB970AEBAF9C60D39 (void);
extern void CinemachineCamera_OnTargetObjectWarped_mA7E7AC0437C4FE5F6D94EA6DAA17101E424C830C (void);
extern void CinemachineCamera_ForceCameraPosition_m18D61347A7FABE951979843B1B8C43110B262B2A (void);
extern void CinemachineCamera_GetMaxDampTime_m6EA3539A2DB9877DFA001A709B7343C13C99382E (void);
extern void CinemachineCamera_OnTransitionFromCamera_m54AEE8CC75D022C5F562C0D2F0761EFCF9EB99D3 (void);
extern void CinemachineCamera_InternalUpdateCameraState_m1EE98901BB5C5ABF935261D7B1C789BBC214955E (void);
extern void CinemachineCamera_InvokeComponentPipeline_m747363A75C8A01C8BA345A978CC0E3DB0787CBAD (void);
extern void CinemachineCamera_InvalidatePipelineCache_mD7DCC2F42134810B300E428E3029370DFD1ADB7E (void);
extern void CinemachineCamera_get_PipelineCacheInvalidated_mB95C76EE3C4299F5CA7D30B5034CC3A1D2E3B3E8 (void);
extern void CinemachineCamera_PeekPipelineCacheType_mE403C1694B2151B73E91334A6089D8C83F824CC4 (void);
extern void CinemachineCamera_UpdatePipelineCache_m831D5FCBE9BE9060A75B3E3C0388A08AC41CE115 (void);
extern void CinemachineCamera_GetCinemachineComponent_m250E78F88A39C9FAF77E21B2D7A8C4580233F5C2 (void);
extern void CinemachineCamera__ctor_m91522425D436D75B47674B716508EAE4B23CCB67 (void);
extern void CinemachineCameraOffset_Reset_m0CA4CE8CB36657281279874E9AFB9BB8DECF99E8 (void);
extern void CinemachineCameraOffset_PostPipelineStageCallback_mEF59D875A8926DB96BF3C30AFB17A48BB21E929E (void);
extern void CinemachineCameraOffset__ctor_m28E2D82DF4C9AC91640CDA02817D4E773CCCCA4F (void);
extern void CinemachineClearShot_Reset_mB01F353A2ABB60B661B1CD2C4CECD5EE5E475EAC (void);
extern void CinemachineClearShot_PerformLegacyUpgrade_m18AE49A93C1355FFABBE01878ED000D56C4FB624 (void);
extern void CinemachineClearShot_OnTransitionFromCamera_m720CCEBAFAD913316D6C78E77A1AF5C6DFF7E4D4 (void);
extern void CinemachineClearShot_ResetRandomization_mA2DF72F4CF6D92E8715F629F5855541B1F2CE8C1 (void);
extern void CinemachineClearShot_ChooseCurrentCamera_m4CAB1C1AD204215DEEE445270E15CBBCAAF676EC (void);
extern void CinemachineClearShot_Randomize_m3EF5CDA82C20C8E50820A21B3F7908B73DA4B0C7 (void);
extern void CinemachineClearShot__ctor_m61289C0079D4637A416603FDAAD6BCC9B2D83796 (void);
extern void U3CU3Ec__cctor_mDB1A22CCE4E4AEE4F6ED3E47592B948CF74CFBE5 (void);
extern void U3CU3Ec__ctor_m7618574879E8E254D1C0FC8D1B818B27447109A6 (void);
extern void U3CU3Ec_U3CRandomizeU3Eb__16_0_m5B008EF3619085477A51B58B121493360C14E392 (void);
extern void CinemachineConfiner2D_OnValidate_m7C471C69589F32A031720FFEA18E59105A444D83 (void);
extern void CinemachineConfiner2D_Reset_m94218B0F30AE33E69569F5DE181F710261681B3A (void);
extern void CinemachineConfiner2D_GetMaxDampTime_mCD997369FF39BFB57253F2D18C3F646386D92C0C (void);
extern void CinemachineConfiner2D_OnTargetObjectWarped_m448A90764B6A2022CB30AD54A0C28964EA8B034E (void);
extern void CinemachineConfiner2D_InvalidateLensCache_m96127F7E34E1B79BD88D9B88B2782E7C4C620644 (void);
extern void CinemachineConfiner2D_InvalidateBoundingShapeCache_m78594B546CDE7FB34D310FA1CD7CE10BD2537514 (void);
extern void CinemachineConfiner2D_InvalidateCache_m6EEA885A82AE5D41AA9ED511BB7C14870E33AB23 (void);
extern void CinemachineConfiner2D_get_BoundingShapeIsBaked_m94F29C2B20D6BF8A8DA01A03D6695BEFC17F7F75 (void);
extern void CinemachineConfiner2D_BakeBoundingShape_mC07814A2197D1173B431338B627CD46F75B788D2 (void);
extern void CinemachineConfiner2D_PostPipelineStageCallback_mCE904D8F7C085D41021BF5C766D78D60B106CA01 (void);
extern void CinemachineConfiner2D_ConfinePoint_m4E2313EB1F226A48A75008488FEECC619D06507D (void);
extern void CinemachineConfiner2D_GetDistanceFromEdge_m9603F166B193A9279770D456517015AF0567B66F (void);
extern void CinemachineConfiner2D_CalculateHalfFrustumHeight_m194311D3B35D3BF2F1B72AE12C99D60C8E69527E (void);
extern void CinemachineConfiner2D__ctor_m17126B879455B4DA6923B0E925DFACB1F9868A20 (void);
extern void VcamExtraState__ctor_mA479CCDAB69FBA5EB64579692BCEB6133F6241A9 (void);
extern void ShapeCache_Invalidate_m7EAE6FF99DB3AD5D61BEB9156C14E6BD0DE1AF3E (void);
extern void ShapeCache_ValidateCache_m9B20BC43138C2439DB9F4499BC3E9338504B640B (void);
extern void ShapeCache_IsValid_m29BFC1989D49FB1151484B62E53A003D95A9AB81 (void);
extern void ShapeCache_CalculateDeltaTransformationMatrix_m8E975270968FEC0D915DA7EBDB6459FF46A4EE6F (void);
extern void ShapeCache_U3CValidateCacheU3Eg__HasAnyPointsU7C10_0_m660EF4DD241B9E53F755A82C43D9C85B31498A77 (void);
extern void CinemachineConfiner3D_CameraWasDisplaced_m646C3FA18281E847EFB06BB2283B5275ED816CFF (void);
extern void CinemachineConfiner3D_GetCameraDisplacementDistance_mD04BBC4F296886FAC8023367E4037D75FA730BB6 (void);
extern void CinemachineConfiner3D_Reset_mD54999FA0C11CB95549CBB08A9C958698BBFCC92 (void);
extern void CinemachineConfiner3D_OnValidate_m71DC7E0907AA6E2A547B9F68952C1EDADA71CA1F (void);
extern void CinemachineConfiner3D_get_IsValid_m78ECE27FE186FCDB56FCF2EA6BE7CEBBC7E845A8 (void);
extern void CinemachineConfiner3D_GetMaxDampTime_m6809A899757877717C19D47E1D922177EB5B63DB (void);
extern void CinemachineConfiner3D_OnTargetObjectWarped_m14C0CC687ADF15D1A4CE1C409A02162B7D78252A (void);
extern void CinemachineConfiner3D_PostPipelineStageCallback_m378F10228EC072EDD338E9C7F7BE3B17121E560D (void);
extern void CinemachineConfiner3D_ConfinePoint_mDEA0C1D1E4030DCD071C91485E3F186F890AC40D (void);
extern void CinemachineConfiner3D_GetDistanceFromEdge_m399EC6F5B05B17BF86240E4D5266D165DA28D001 (void);
extern void CinemachineConfiner3D__ctor_m12CE522D114C441A172E046A974E905568AB5042 (void);
extern void VcamExtraState__ctor_m0794BED1D91310551151C924D419F90A6484DAFE (void);
extern void CinemachineDecollider_OnValidate_mC08AF0C31717A29C0A91C5F5058A6EB9FB3CDA8A (void);
extern void CinemachineDecollider_Reset_mE700FDE36663B464E54E4347D1DDE136AB3F4ED0 (void);
extern void CinemachineDecollider_OnDestroy_m0F0C072A3FEA2F03D1BA574F543039DFD60A12AC (void);
extern void CinemachineDecollider_GetMaxDampTime_m514A390008CF7955D32C09459016C8A880211CF2 (void);
extern void CinemachineDecollider_ForceCameraPosition_m7BB7BB5B572B290FA9F5DC21FF27DA936952F177 (void);
extern void CinemachineDecollider_PostPipelineStageCallback_m87AE96804D39F4F62A58FA8C39E12F25D70BF914 (void);
extern void CinemachineDecollider_GetAvoidanceResolutionTargetPoint_m3D9C5461D7A96F2BD4D8B245FB143E96AE3EC101 (void);
extern void CinemachineDecollider_ResolveTerrain_m04BFAB02E14C352BB78949FA8219C17D7205343E (void);
extern void CinemachineDecollider_DecollideCamera_m5EAA9B0A4B085CFE2A9D6046657E331D37001BDC (void);
extern void CinemachineDecollider_ApplySmoothingAndDamping_mA407E079A2DE73C766DD28C75CDA1746D005507B (void);
extern void CinemachineDecollider__ctor_m7D46287E18AC7C90B177162388972E33FFCB01CC (void);
extern void CinemachineDecollider__cctor_m19C0557661B516D72A615FF147E9A6B297C311A0 (void);
extern void VcamExtraState_UpdateDistanceSmoothing_mC773F67938BB6132EEA71E9BA75BCE3B3A5AE2B5 (void);
extern void VcamExtraState__ctor_mDDA9D7DF5B0AB20482D5D0E93DB9D66B29FC6275 (void);
extern void U3CU3Ec__cctor_m7141C8A567201CFDBB03B88267877DF009F2DC0D (void);
extern void U3CU3Ec__ctor_m56289C45DFAF853744975C3FCF0B87CB37410243 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__22_0_m2AF6CBB7C5E57C7AE1D531490B5F97960852AAC2 (void);
extern void CinemachineDeoccluder_IsTargetObscured_mBDBDBC822C5AF1B4E6DD899F450E48CF315BC7DC (void);
extern void CinemachineDeoccluder_CameraWasDisplaced_m37B38BA237462EF0BB9C52CEDABE2E2C3F705263 (void);
extern void CinemachineDeoccluder_GetCameraDisplacementDistance_mFEA1DB131D1A746E396A630A6956E4493ACEA080 (void);
extern void CinemachineDeoccluder_OnValidate_mD0349132AD59DB4109C4C849DEADAB3CB17C7A31 (void);
extern void CinemachineDeoccluder_Reset_m8A19C2B2F4F6510AB195D65F0CC2A243DEEDE7B2 (void);
extern void CinemachineDeoccluder_OnDestroy_mB4EEE7923F1C4AFF4A88758D27C15851E6660F6C (void);
extern void CinemachineDeoccluder_OnEnable_m1A0B8519F69C65DE99FA40BE1622C0CEE353CBDE (void);
extern void CinemachineDeoccluder_DebugCollisionPaths_m24136E1E710C702698E055A0BE54E6EBEE8E7EFE (void);
extern void CinemachineDeoccluder_GetMaxDampTime_m15F47D27F88EBAFC2F53D1D31219E540317EC17E (void);
extern void CinemachineDeoccluder_OnTargetObjectWarped_mF6E78F547EB86E372BCF079DBC48064FFBAA7318 (void);
extern void CinemachineDeoccluder_ForceCameraPosition_mE82E189DF0EB2D0B17B38A2BDBDC6C1C88A32BD4 (void);
extern void CinemachineDeoccluder_PostPipelineStageCallback_m19CBA5B799C135B5CAC8290780506FFC6F3E2585 (void);
extern void CinemachineDeoccluder_GetAvoidanceResolutionTargetPoint_m8F111FB9F05E6F811035316FDC17E34E0226CD83 (void);
extern void CinemachineDeoccluder_PreserveLineOfSight_m70DCFA45408D4779B54DFD28F5EFFC99F5033489 (void);
extern void CinemachineDeoccluder_PullCameraInFrontOfNearestObstacle_m91138003E079D183B97C8E5E45698351AEA53104 (void);
extern void CinemachineDeoccluder_PushCameraBack_m2079512A7C9C6D446D3CC52288DA38913C386EC6 (void);
extern void CinemachineDeoccluder_GetWalkingDirection_m0DE90A71C93DA2894D8D5C1779D345D1F5938D02 (void);
extern void CinemachineDeoccluder_GetPushBackDistance_mE0FECDF06391F31488DC8A6C71792C92D6704EF5 (void);
extern void CinemachineDeoccluder_ClampRayToBounds_mD7614EC2DF143DE49C0A4479A47EE4AF4A2FE74C (void);
extern void CinemachineDeoccluder_RespectCameraRadius_mCB553E24BF26A6C76874D0202977E2E6BEF5A83B (void);
extern void CinemachineDeoccluder_IsTargetObscured_m54D0CC7F8572807904C212F5F74928E0B7603A7A (void);
extern void CinemachineDeoccluder__ctor_m70CC7C25D900B1376FA5672107AC7B5362B327F7 (void);
extern void CinemachineDeoccluder__cctor_m227ED309F95DAAE497F76754B820B163A0BEC203 (void);
extern void ObstacleAvoidance_get_Default_m1E0297E7D6B58B35450D582260862F7511FA62B1 (void);
extern void QualityEvaluation_get_Default_m866E5F3F224D0839C9EA77615203ADD5FAD1CA51 (void);
extern void VcamExtraState_AddPointToDebugPath_m25FE360AFEA9767845BE4CDAC59B6B2F8D56388E (void);
extern void VcamExtraState_ApplyDistanceSmoothing_m85B2B823A475E616309658A6B39C98C0BE46130F (void);
extern void VcamExtraState_UpdateDistanceSmoothing_mB8870D8DE044245AE242C40FE1D13EA50FD6E403 (void);
extern void VcamExtraState_ResetDistanceSmoothing_mF3F47145B31CB7C1B781AAEBA31323CEDE88D523 (void);
extern void VcamExtraState__ctor_mE3C0FE1F0BE52C461B54A78E9641BC19C694101F (void);
extern void CinemachineExternalCamera_get_State_mB9BBC06A7316FA3994BF183E657082620AB7B17D (void);
extern void CinemachineExternalCamera_get_LookAt_m29D641BD9B316CFF2E9BD3F489E3224B5613AE93 (void);
extern void CinemachineExternalCamera_set_LookAt_m6A1622CE9C8CE837FD0F7667A52499F7E605735D (void);
extern void CinemachineExternalCamera_get_Follow_mABD656E191F4BB8328850ADC813AD651499FEAC2 (void);
extern void CinemachineExternalCamera_set_Follow_m9535B088DDDAAAD9998F11A6E4C6AD9408B34879 (void);
extern void CinemachineExternalCamera_InternalUpdateCameraState_m0EBB0C3B8A5C443EE8A27FABE62E2EF8EAF43B63 (void);
extern void CinemachineExternalCamera__ctor_m4EAD9E2BB6EF89B9C227022C1A1D03CDDE137FEC (void);
extern void CinemachineFollowZoom_Reset_m49A2632F3774FC14CD3D0CDA8FF781698B816AFD (void);
extern void CinemachineFollowZoom_OnValidate_m7DEEAB1CA2523E3D791D409AA94A811647731E34 (void);
extern void CinemachineFollowZoom_GetMaxDampTime_mEB15BA1EA85DE68078F2AC91FAEA0541B45146E3 (void);
extern void CinemachineFollowZoom_PostPipelineStageCallback_m5F4DE79DB5CC62625EB17ED360BF5E06B76C265F (void);
extern void CinemachineFollowZoom__ctor_mE45B17E38BE042F5C3A40220D767780650A6FE8B (void);
extern void VcamExtraState__ctor_m53A6B951FA3826ABB2840F70844E357FE54FD258 (void);
extern void CinemachineFreeLookModifier_OnValidate_m1A72C3DA3B042F5AF357515EFCCB36930EE985D7 (void);
extern void CinemachineFreeLookModifier_OnEnable_m12CC8B25642C736321CE811B9A2547F72A5C2CA1 (void);
extern void CinemachineFreeLookModifier_RefreshComponentCache_m96902FCDC49F7E04C28FF754D458FB640E4C2589 (void);
extern void CinemachineFreeLookModifier_HasValueSource_m9077489D377D563E9A1A6767F98F0CD17B9399FB (void);
extern void CinemachineFreeLookModifier_PrePipelineMutateCameraStateCallback_m3D24FB041AAA0B98F888189C5AA9A6C521364E4E (void);
extern void CinemachineFreeLookModifier_PostPipelineStageCallback_mC9A960642FB6C361AA774C5A843A7614310AB375 (void);
extern void CinemachineFreeLookModifier__ctor_m3FD448FA2E1D020B44AA69471DDDC3E8307A2CA6 (void);
extern void Modifier_Validate_m915EB25B59CC975EC035AB45270A5BE0B50597A9 (void);
extern void Modifier_Reset_mA0DF9610739560B2FDD666384108782C03DA6AB5 (void);
extern void Modifier_get_CachedComponentType_m6C716EFFF2F1C18C78A7E2DFAAECBB5A8DEB9F27 (void);
extern void Modifier_get_HasRequiredComponent_m29AB56253FE92BBEBE69D634453D1B37F265A09C (void);
extern void Modifier_RefreshCache_m6F0E9BD2C0FBABDAB99658CF4FCA8C01BAB4B3AB (void);
extern void Modifier_BeforePipeline_m58AF8EC4A00F1924C88FFFF93E5BC86D0DCEA2BE (void);
extern void Modifier_AfterPipeline_m3067412D245E19393B82095BE4E0212FEE4E632B (void);
extern void Modifier__ctor_m7DBF334FC84EC6C3D028D7196EF2A39BAEEB5012 (void);
extern void TiltModifier_Validate_m4B48B50FA68B545E7D9F62BEB2E20FC08A5CEB6D (void);
extern void TiltModifier_Reset_mE0C3B4EB6729910547BF2DD364649BEFA6D8E141 (void);
extern void TiltModifier_AfterPipeline_m42776093F458F8527CCF18E792C4C504F1991B55 (void);
extern void TiltModifier__ctor_mE3B33050DE46EE9C38FAEA17AD71A74BB54C3BF7 (void);
extern void LensModifier_Validate_m0A5D2B5D8117A6B9893E6D72FAA7532F0A79B9A2 (void);
extern void LensModifier_Reset_m2D874D2313516523E70E04727932D5A44FC75355 (void);
extern void LensModifier_BeforePipeline_m3F8F0A3ED7BDDCDFF7FA0D9ED33C435E323B55AF (void);
extern void LensModifier__ctor_m47809E6AFB8291DEC8604F010C511392A0E30661 (void);
extern void PositionDampingModifier_Validate_mABF021569EB81D99A6EA202805C33F2B230773D5 (void);
extern void PositionDampingModifier_Reset_m1678DD8AE01D1CB71FAB6BF0E91DCB8BA5A7CB6B (void);
extern void PositionDampingModifier_BeforePipeline_m152B989DE2B2C86E3E17557DED4C83C30A44EA4A (void);
extern void PositionDampingModifier_AfterPipeline_mF54A94D6CFCC014B7381299F1F3B6F8B39B9D3CD (void);
extern void PositionDampingModifier__ctor_mA4B0F686E391F1740C4654FD74F9C1B7DB69AC47 (void);
extern void CompositionModifier_Validate_m4A31AD91423975F1C9F5C34BCC10ABC0AA26B986 (void);
extern void CompositionModifier_Reset_m2ED36533159313F79F2B0204E2DFDD6220D8B63F (void);
extern void CompositionModifier_BeforePipeline_m60778DB53118337059BDA1A067C8E60A89A4E60D (void);
extern void CompositionModifier_AfterPipeline_mCE5BB65E89062D941D5B29935A2D0017D4E4A1CA (void);
extern void CompositionModifier__ctor_mCD8608F30946269B1E696AEBF13ABB9DE40BD004 (void);
extern void DistanceModifier_Validate_m6514387A0657540B7D9CED201002ECE5E3A3F931 (void);
extern void DistanceModifier_Reset_m2845507D9BF465AE3FDC652F3314F96E57A4986D (void);
extern void DistanceModifier_BeforePipeline_mED614C93952E4BE24E4347BFEDE5BB4E0F1BD34A (void);
extern void DistanceModifier_AfterPipeline_m4D44BD0F71499099663D9C702CB15ADDDF727241 (void);
extern void DistanceModifier__ctor_m08F9CD4D7E80145FA57F82AEEFBC44FFFB1C18AA (void);
extern void NoiseModifier_Reset_mE30E1C1BB8B27AD3FE18F7A42ADE3C79E39C148A (void);
extern void NoiseModifier_BeforePipeline_mB8EDF8F5528930200BF658623D8042DAFBC48FE3 (void);
extern void NoiseModifier_AfterPipeline_mE83A72BC5AF6B130A58A7B15EDE354D7CE03DCAE (void);
extern void NoiseModifier__ctor_m3ABDB340A635C901EC122B291A098BB3D1B5BACD (void);
extern void CinemachineGroupFraming_OnValidate_m70D192B593D59DAFBC7786AC22C25D8673C98AD1 (void);
extern void CinemachineGroupFraming_Reset_m514B037950ADF854C5ABB0E4B90B0116F791F183 (void);
extern void CinemachineGroupFraming_GetMaxDampTime_mFC43BC951253CAADB1DDFEADFC72D6DE4076FB4D (void);
extern void CinemachineGroupFraming_PostPipelineStageCallback_mD760DA6EF0F4774B5AAACAA1298250E5C3E65B7C (void);
extern void CinemachineGroupFraming_OrthoFraming_m5602A3502F95650A4A8E36705976615B2EBC157B (void);
extern void CinemachineGroupFraming_PerspectiveFraming_m08749C1BF0F957327FA1000EB9D18494385E98E3 (void);
extern void CinemachineGroupFraming_AdjustSize_mA74DCF74636624A2517EB5CE451E6233E764733F (void);
extern void CinemachineGroupFraming_ComputeCameraViewGroupBounds_m76AE668AF39F0948F758CD6C8835219C9B18FABA (void);
extern void CinemachineGroupFraming_GetFrameHeight_m9212B3B0324A10200D32AB91DDD4689318BEE7FA (void);
extern void CinemachineGroupFraming__ctor_m5A099F5F0CDA46331F0ACEA7915D6FE39F36656B (void);
extern void VcamExtraState_Reset_m7084A60FD1C3699F299914B313336DE411F06F2C (void);
extern void VcamExtraState__ctor_m7634780DA7DCB4D8FC9DD8BBFD391BB54A61ADDB (void);
extern void CinemachineMixingCamera_OnValidate_m6BD0F1F154CB7840BC6792DA79B9272F1DD4BC19 (void);
extern void CinemachineMixingCamera_Reset_m691AF7CAD8A77A2778AAC9B2EAD3D1A0857C30CF (void);
extern void CinemachineMixingCamera_get_State_m2C0A93A3E9F84545CB28CEFD9EA0ADAF8BC46196 (void);
extern void CinemachineMixingCamera_get_Description_m486E7F41A3FDC990119727D73075ACA43CE1DC6B (void);
extern void CinemachineMixingCamera_GetWeight_m6342A2A6786194F79566D0CE90F41F39DCD87FC4 (void);
extern void CinemachineMixingCamera_SetWeight_m2F14E726D70A7A1BA23FEDE8D6558F49B7CDF041 (void);
extern void CinemachineMixingCamera_GetWeight_m4D7ED9DB66C75DABD40689B13D1CBA4A4AEB0C4C (void);
extern void CinemachineMixingCamera_SetWeight_mEF30F10178B20AB416BA0D77788B9BB2C6A1C2E4 (void);
extern void CinemachineMixingCamera_IsLiveChild_m8A51DBCB589F49DB6586E2CE1D40D1A687EA347D (void);
extern void CinemachineMixingCamera_UpdateCameraCache_m34FBD49D2076EFCFF184FE0D09DCCAF738AE76C2 (void);
extern void CinemachineMixingCamera_OnTransitionFromCamera_m9CAE7A08549375F0DC1C7679C61E78B92ABCB60B (void);
extern void CinemachineMixingCamera_InternalUpdateCameraState_m75E16DCC12C064D9378B48BEEDECB3C15C0D37D3 (void);
extern void CinemachineMixingCamera_ChooseCurrentCamera_m7AC2A49B3C931F95B91087588EB691252104860D (void);
extern void CinemachineMixingCamera__ctor_mEBBE5A0517647F70102A7D3B59D4A8A3C686D856 (void);
extern void CinemachinePixelPerfect__ctor_mFF8E9674F21E57004F8AFD1EC2FA250DC05902D4 (void);
extern void CinemachineRecomposer_Reset_mAE28A433363B38291C46E89A219A752012A25081 (void);
extern void CinemachineRecomposer_OnValidate_m53F62BBA1DDDD29517CAA39B27FC0163DCCD635B (void);
extern void CinemachineRecomposer_PrePipelineMutateCameraStateCallback_mDA724A1F4AF400965BED7492B757BA3207DAEAE9 (void);
extern void CinemachineRecomposer_PostPipelineStageCallback_m471B6879AF9BE8778D0968423A74B3CF681E6028 (void);
extern void CinemachineRecomposer__ctor_m4E26C058E31CFD6607C1049A8F0C24906CC3778C (void);
extern void CinemachineSequencerCamera_Reset_mAEB898064F28FFEAA0F6E5C4E248719CC854068E (void);
extern void CinemachineSequencerCamera_OnValidate_mA89B672AC12628E8BE97E50C0F6ADC4478EE6647 (void);
extern void CinemachineSequencerCamera_PerformLegacyUpgrade_m2E3B3C81B6EEAE56B4D14BC8757C9F8919A3D00A (void);
extern void CinemachineSequencerCamera_OnTransitionFromCamera_m32C8CC58AADA14FF27884E88E1D0E90054C1F472 (void);
extern void CinemachineSequencerCamera_ChooseCurrentCamera_mFB1E878BCE6BE11173B83DAD309E9C29D701AF00 (void);
extern void CinemachineSequencerCamera_LookupBlend_mF931E3FD3EE1059B2F89E431A4A2CA2F97FA7F4F (void);
extern void CinemachineSequencerCamera_UpdateCameraCache_m21B5E23F8331BFF057EB1DFB9A06629317B86098 (void);
extern void CinemachineSequencerCamera_AdvanceCurrentInstruction_m0AE059FAE82B4637A7BCBFD58FD02D81813DBA77 (void);
extern void CinemachineSequencerCamera__ctor_m5459039D8987D7DF01AB4B5D9331C89D227203F3 (void);
extern void Instruction_Validate_m8178D703AE92163B2335C8EE46B8AB28EC01BF1F (void);
extern void CinemachineShotQualityEvaluator_OnValidate_mCAFB157FD133A321C0FDE5F37E559E401F359C06 (void);
extern void CinemachineShotQualityEvaluator_Reset_mFDA7D6A70DFEF5A4DB5AFCE48F28290F941B644A (void);
extern void CinemachineShotQualityEvaluator_PostPipelineStageCallback_mEB54ED44A27ED48DBB40D66B2E58B61E3BBC431D (void);
extern void CinemachineShotQualityEvaluator_IsTargetObscured_m3FEC7C16D84B3C6CF63CE2C33024445369BE7152 (void);
extern void CinemachineShotQualityEvaluator__ctor_m2193F109AA00BC06F1C5360F12383BCB4DA282C2 (void);
extern void DistanceEvaluationSettings_get_Default_mC0F7F48A113DFA5D72617E0B2AC47D6B14672A5B (void);
extern void CinemachineSplineCart_get_SplineSettings_m72366F879C4A2244896873C0BAA4AE344A19A1B4 (void);
extern void CinemachineSplineCart_get_Spline_m633FBE44C5BEC263F495816E2EEDE7710CDDAA07 (void);
extern void CinemachineSplineCart_set_Spline_mF31E2D49618BBFCA89A6D991D1CD1DB98BDD7693 (void);
extern void CinemachineSplineCart_get_SplinePosition_mD32F0C154B1700B51E0A4E93A038BAF443EBE6C0 (void);
extern void CinemachineSplineCart_set_SplinePosition_mEBE0AA723B162FD0A21BE073BCA1DF88E5939EAF (void);
extern void CinemachineSplineCart_get_PositionUnits_mEF36459A01FCCEB09D8AC1CF8231A3284E4668F1 (void);
extern void CinemachineSplineCart_set_PositionUnits_mB29823F8C464F39FDBD050E5905748AE19664D83 (void);
extern void CinemachineSplineCart_PerformLegacyUpgrade_m881C0764B0B5F29A3A42E2872A12D44BEACFE9CB (void);
extern void CinemachineSplineCart_OnValidate_m37A7EAB55C7FE394AA6488C74E6CB61652820CDD (void);
extern void CinemachineSplineCart_Reset_mEA75007F36ACAA7A866D470A138764FAB5199B28 (void);
extern void CinemachineSplineCart_OnEnable_m634A5FF67C2CD69A80FAEB583B291091E63AE535 (void);
extern void CinemachineSplineCart_OnDisable_m72C47C821DFF2A31F5D2DB67A17D653D165D92EB (void);
extern void CinemachineSplineCart_FixedUpdate_mA998B8329EBFCEA04C19ED39643FCD6D6E3A48B5 (void);
extern void CinemachineSplineCart_Update_m2FD5D4E89782B9C1DF23619BE98C6398D2FCE1FA (void);
extern void CinemachineSplineCart_LateUpdate_mD7EF44C407717D1EA18719147877CE838570B9DC (void);
extern void CinemachineSplineCart_UpdateCartPosition_mE24A3A920970FA3E0A38070E3F6D67AF82AE60DF (void);
extern void CinemachineSplineCart_SetCartPosition_m684C83C28E391BFCF8310B88891BFEB85387B16E (void);
extern void CinemachineSplineCart__ctor_mCCD5F463CA103D5DFF20408E3CA15A88A19D73E1 (void);
extern void CinemachineSplineRoll_GetInterpolator_m4807776EDA34CF771B224E533D3D0EE938C9FE65 (void);
extern void CinemachineSplineRoll_PerformLegacyUpgrade_m771FE9DCDDE8891EE6866506D48C3FC3A54950F6 (void);
extern void CinemachineSplineRoll_Reset_m80B435280C86CFD3460E01F2EAB5BFE1B6B0E4A4 (void);
extern void CinemachineSplineRoll_OnEnable_mA7DB6E34C30DDB7D6CAC5CEE6B68FA42306E1503 (void);
extern void CinemachineSplineRoll_OnBeforeSerialize_m68D6042F750B63B6977201691AA40C5C9FB09184 (void);
extern void CinemachineSplineRoll_OnAfterDeserialize_m57D91A088DAA6517A6E841D58EB7A1E94443E76A (void);
extern void CinemachineSplineRoll__ctor_mE26FA080055862294B2F52627D68CBA5E7944345 (void);
extern void RollData_op_Implicit_m7D9C4F9E45500F8A006B798EB4B365A13881F072 (void);
extern void RollData_op_Implicit_mD7E3EE70B693689BA5A6E1C4290A3E0A2D0FEC93 (void);
extern void LerpRollData_Interpolate_mCB9BC66ADFB6B3E320ED43AB11A121DAE116134E (void);
extern void LerpRollDataWithEasing_Interpolate_m97056DDAB2AC587935B5F6010023E96592917FDC (void);
extern void RollCache_Refresh_m1570582AE45E77CE46A95CDEAECD004C568B072C (void);
extern void RollCache_GetSplineRoll_mF4DEFB86AD16D1E13A2A620FB4EC35512625905D (void);
extern void CinemachineStateDrivenCamera_SetParentHash_m31730475BD0DEEC5D7E3A7EF4105333CADD8E36E (void);
extern void CinemachineStateDrivenCamera_Reset_m87808F6C0D704DFFDA8B7A90FE270EF5BB0E3CBC (void);
extern void CinemachineStateDrivenCamera_PerformLegacyUpgrade_m5B9DAFCE1F55862D80CD85EFEDA6A618421FBF0E (void);
extern void CinemachineStateDrivenCamera_CreateFakeHash_mE3F38A51644B9FA04A21EFEAD371642BED7DE741 (void);
extern void CinemachineStateDrivenCamera_LookupFakeHash_mB8256DA4192E72A0207E358F51DAECB79A1F57C9 (void);
extern void CinemachineStateDrivenCamera_ValidateInstructions_mC0D386CC5169FA2FB3362E333552D8E99C14D8F0 (void);
extern void CinemachineStateDrivenCamera_ChooseCurrentCamera_mF0BDF0B5F7FCCD4634FC8E17240D25A880A49E3B (void);
extern void CinemachineStateDrivenCamera_GetClipHash_m5CB5DDB21E924AF543287FE6D2785258DC0D7700 (void);
extern void CinemachineStateDrivenCamera_CancelWait_m24682024A7EF1C081FF86142D27B3153A722628C (void);
extern void CinemachineStateDrivenCamera__ctor_m6DBB830F2B9BA5680411F1FDFA25B20D897751F2 (void);
extern void CinemachineStoryboard_PostPipelineStageCallback_mF0AA2553CDDE871D8C53FB98D11682E8199E2048 (void);
extern void CinemachineStoryboard_UpdateRenderCanvas_mBC1E21D4697A16F48B64D9C51F0624D7E34FD8F1 (void);
extern void CinemachineStoryboard_ConnectToVcam_m3B3EDC5D5D5BB4F18604C2AA3E0960F6FF1C2023 (void);
extern void CinemachineStoryboard_get_CanvasName_m2E44F5B07725E17A29ED6249D7AB0697F1584EF2 (void);
extern void CinemachineStoryboard_CameraUpdatedCallback_m1CAF6968DAEDC81F985E7D1D3AE24E63338F3552 (void);
extern void CinemachineStoryboard_LocateMyCanvas_mAA75A83FD787864A06C0253C003EC9570EEEE063 (void);
extern void CinemachineStoryboard_CreateCanvas_m90E92A407F57C297C6374B005563E34A0B6D17F5 (void);
extern void CinemachineStoryboard_DestroyCanvas_m97FFDF78A29ACC4631E6B0B1B0603CFE264266EC (void);
extern void CinemachineStoryboard_PlaceImage_m526CDD0913252A5411FFD304E9BB8BE5FA003FC1 (void);
extern void CinemachineStoryboard_StaticBlendingHandler_mD291D42C1662EA2104780FC7ECB210CA00BFB7D4 (void);
extern void CinemachineStoryboard_InitializeModule_mE4B9F42F69EC0A317AD0D2A7DAD6EC8A9970C88F (void);
extern void CinemachineStoryboard__ctor_mE67D2B79B39A11B920B2C9CF84EB4D8946E0526C (void);
extern void CanvasInfo__ctor_mEFE0004747AA15A1B20D277BAD33BE11A39F9C8F (void);
extern void CinemachineTargetGroup_OnValidate_mF2C1D2E4DEF776010A42B5FA3311755E2E3FA228 (void);
extern void CinemachineTargetGroup_Reset_m2F6791E212A9512CEA0848F466EB4540F79AAFB8 (void);
extern void CinemachineTargetGroup_Awake_mE55E59E16F272B330DCF01FFE4FCBD1F5F835E2C (void);
extern void CinemachineTargetGroup_get_m_Targets_m32340F88B61076E8B1BDA77D30D25DE8BAAD2EFE (void);
extern void CinemachineTargetGroup_set_m_Targets_mC6D0282C62CE27F4BFCB9CD66F7C25868A599D4A (void);
extern void CinemachineTargetGroup_get_Transform_mBDE866FC4227F0BA446EADDC5F40EB1C78F70632 (void);
extern void CinemachineTargetGroup_get_IsValid_m223C458CE45331512728FC02134AE69B33D8A562 (void);
extern void CinemachineTargetGroup_get_BoundingBox_m1268B62BB2F439ACB18E37B83113306DCBE71350 (void);
extern void CinemachineTargetGroup_set_BoundingBox_mA09001A15B114A881BF41C3479BD95CDF6813B12 (void);
extern void CinemachineTargetGroup_get_Sphere_mB3BB5A8D1B37754F91ED9408088A0845B9FC9196 (void);
extern void CinemachineTargetGroup_set_Sphere_m991EE174F87AAAFF3203257ADCF09DB9E1CA898D (void);
extern void CinemachineTargetGroup_get_IsEmpty_m166ED3A5F9605EBF290C672DAFCCC1E14D91E9D0 (void);
extern void CinemachineTargetGroup_AddMember_mF6D56BECACF14C0BF41916098A637D45F6B0C691 (void);
extern void CinemachineTargetGroup_RemoveMember_m35176F5E563B6A5B2A874AD36125A8681C943174 (void);
extern void CinemachineTargetGroup_FindMember_m298C1CC36765AC2FA9AF03AF24ECBC21F1D1F7F4 (void);
extern void CinemachineTargetGroup_GetWeightedBoundsForMember_m818E7A6287719BE71A40C419215F13019D5A5E52 (void);
extern void CinemachineTargetGroup_GetViewSpaceBoundingBox_m5B31EB4DD1CB99D21ADF6E2CDE6B748BF6430308 (void);
extern void CinemachineTargetGroup_get_CachedCountIsValid_m6287B5B024F638C122911C7458D7ED5A6B820724 (void);
extern void CinemachineTargetGroup_IndexIsValid_m0CE4FCA85CC84DE18C9E39DFFA4D62C1058E27D8 (void);
extern void CinemachineTargetGroup_WeightedMemberBoundsForValidMember_m9A27A14DA906D1C3805A6E724A4B6993AAF13774 (void);
extern void CinemachineTargetGroup_DoUpdate_m93C935D8CAA338EC7974A720C39E19180FB306C3 (void);
extern void CinemachineTargetGroup_UpdateMemberValidity_m37A8026F8A7AA307E764C9DB56ACD0550FCC6B72 (void);
extern void CinemachineTargetGroup_CalculateAveragePosition_m2D9D8717B1DF56CD92A88F4E46AED6D14F9D8101 (void);
extern void CinemachineTargetGroup_CalculateBoundingBox_m4932CB90EF04123FBA880E60B7066A555A79108B (void);
extern void CinemachineTargetGroup_CalculateBoundingSphere_mBAE1C5D6D23E0A725FC4F3034DFDEFBE590016D4 (void);
extern void CinemachineTargetGroup_CalculateAverageOrientation_mD820DCAE0ADE1474CE4C65B2DA39DE888001D050 (void);
extern void CinemachineTargetGroup_FixedUpdate_mC87193CB9B9B01C5CA1F1F7C8850AF4BCBD0CE38 (void);
extern void CinemachineTargetGroup_Update_m5A4540EB66DC6D2B19D6EC236964F139AEF316A0 (void);
extern void CinemachineTargetGroup_LateUpdate_m043836C3D51FDA071A3CD57BA87A82DE0BD29D5A (void);
extern void CinemachineTargetGroup_GetViewSpaceAngularBounds_m90A281BFF37AFAD21384D1D75C4976854F79BB09 (void);
extern void CinemachineTargetGroup__ctor_m30C1BD7106CB9BE2FFC8A963AC3B6A42EC482838 (void);
extern void Target__ctor_m8EFD91AF3960885A2FC683A71EEA7D9648C394D5 (void);
extern void CinemachineThirdPersonAim_get_AimTarget_m9103B6463CA79130E2475D77FAA6A26FB3F547AA (void);
extern void CinemachineThirdPersonAim_set_AimTarget_mBD1A0C839AF90D2EB32DAD6C84A2CCB8E9438A8F (void);
extern void CinemachineThirdPersonAim_OnValidate_mA835880733B2B9A767E1896C041BCDBFB7D5FDFC (void);
extern void CinemachineThirdPersonAim_Reset_m91C1A20A102D211F9DAF6BEDC10809C27E5455ED (void);
extern void CinemachineThirdPersonAim_PostPipelineStageCallback_mF46F5D5D910289C314984022AD5BF7DE723FE180 (void);
extern void CinemachineThirdPersonAim_ComputeLookAtPoint_m691776F22E761C3E1A9BBFD10C1D2B821BC074FA (void);
extern void CinemachineThirdPersonAim_ComputeAimTarget_m63898BEC67A7E1AFCB7755928C95E53EF1390028 (void);
extern void CinemachineThirdPersonAim__ctor_mD69897D961C8C233F8B33D1B7C68A1CE08796379 (void);
extern void CinemachineBasicMultiChannelPerlin_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableNoise_get_NoiseAmplitudeFrequency_m7D7A7C68A1228965466D32B7DE7821CF0E0CCA4E (void);
extern void CinemachineBasicMultiChannelPerlin_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableNoise_set_NoiseAmplitudeFrequency_m97048546B5F4966D128706A920356CE35998D5C9 (void);
extern void CinemachineBasicMultiChannelPerlin_get_IsValid_m25C0688BA4B3B9B3806C601A992DAAEBE62DA6C1 (void);
extern void CinemachineBasicMultiChannelPerlin_get_Stage_m866F09298A21910B01D26D026798C242C43D1CD5 (void);
extern void CinemachineBasicMultiChannelPerlin_MutateCameraState_mFDC1EB4DEA20742A7D15264A44F11EBEE4D06754 (void);
extern void CinemachineBasicMultiChannelPerlin_ReSeed_mA0BFDF577E765DAFA724A0EA7E6D2D1C975339D2 (void);
extern void CinemachineBasicMultiChannelPerlin_Initialize_m73F84575F8E407FFC9ED249AE162586DCC28A5E0 (void);
extern void CinemachineBasicMultiChannelPerlin__ctor_m76EDF5931EC8A9F96F508F914CD0970EB28BF062 (void);
extern void CinemachineFollow_OnValidate_mB3469B45F98984708FF2F98A0E3FC38C39ACB774 (void);
extern void CinemachineFollow_Reset_m67720F86F5B19721DA87D77F8E322B1AEB0E7B12 (void);
extern void CinemachineFollow_get_EffectiveOffset_m70F5D7452DE04987C2C153E2CEE104C1933AE6F3 (void);
extern void CinemachineFollow_get_IsValid_m3A22BF868363036406830A238790A9D83F21B3F4 (void);
extern void CinemachineFollow_get_Stage_m4D76AB5640F75EF697F85A6EBCF1E5EC4E59C529 (void);
extern void CinemachineFollow_GetMaxDampTime_m4669AB43ED15E850CEE5870255D48DA32B500BA1 (void);
extern void CinemachineFollow_MutateCameraState_mC228777857D00E50EE04227A43DCE4E25317E089 (void);
extern void CinemachineFollow_OnTargetObjectWarped_m0639B382C2F788D92ADA2BF28C4A24EFD14F91BC (void);
extern void CinemachineFollow_ForceCameraPosition_m8ED6B0AF5380659E90C8479BEDB11700A1B2EFFA (void);
extern void CinemachineFollow_GetReferenceOrientation_mD584EF8C036D67AEFA66CE66676F94B32B8209EA (void);
extern void CinemachineFollow_GetDesiredCameraPosition_m3B9FBB76A5DA4B3EFC3DDF3D2A38BCF0A15F0D72 (void);
extern void CinemachineFollow__ctor_m98BAC48388C8078A73CF902CB42739F2F0957661 (void);
extern void CinemachineHardLockToTarget_get_IsValid_m47979DED6B2A7D729352804D893252E31F9D3CD3 (void);
extern void CinemachineHardLockToTarget_get_Stage_m00DACB866508022BE66389E2CAE979985A77D9A0 (void);
extern void CinemachineHardLockToTarget_GetMaxDampTime_m433125CF70BAB12F8B2F9CDC772733E74CAF57B5 (void);
extern void CinemachineHardLockToTarget_MutateCameraState_m67AADB57632431C64876D778D314A679A5A73EBB (void);
extern void CinemachineHardLockToTarget__ctor_m0AE526F6F08204C6E3D5299EE83C05CE18950611 (void);
extern void CinemachineHardLookAt_get_IsValid_mC7255977EBB871634BB27A71C5A633EA2804A0C7 (void);
extern void CinemachineHardLookAt_get_Stage_m5CAB6F58DF56471DC06090EA0C035230FEE84B2A (void);
extern void CinemachineHardLookAt_get_CameraLooksAtTarget_m35E9CA9DD16AB96C5B8AD5070B36441905187444 (void);
extern void CinemachineHardLookAt_Reset_m1FB92DD4E0D37A238F3F58B552324BCC2E2DB188 (void);
extern void CinemachineHardLookAt_MutateCameraState_mFBDF5F7E426F9B7F5C90C32580A7CA33A8F71854 (void);
extern void CinemachineHardLookAt__ctor_mBEBAFAA6A8D452348831119396DEE6C959B0CCF5 (void);
extern void CinemachineOrbitalFollow_get_TrackedPoint_m65548F6BBD262026C1F5702C66C0A559E0D6CF45 (void);
extern void CinemachineOrbitalFollow_set_TrackedPoint_m60B4059F0ACC55D79B28C7A0979425AD17359DF3 (void);
extern void CinemachineOrbitalFollow_OnValidate_m2A4D33C1AC75E15BE6E3E59D2583159528BC2BC7 (void);
extern void CinemachineOrbitalFollow_Reset_m0E9AB79CC4BE46A0EEB22ABDCB561911FB7FDC64 (void);
extern void CinemachineOrbitalFollow_get_DefaultHorizontal_m34BE51A65EBB9705DEBF404818972CE3D320D834 (void);
extern void CinemachineOrbitalFollow_get_DefaultVertical_m8CDA48B3983A268AB17EE05E4E3CBE1B625B1929 (void);
extern void CinemachineOrbitalFollow_get_DefaultRadial_m7C92D94F8A70BD7DD1C2450F7D99F5A9323D4D8C (void);
extern void CinemachineOrbitalFollow_get_IsValid_m0C59DED31F494A51B224824D38ECE01201D2D568 (void);
extern void CinemachineOrbitalFollow_get_Stage_m71B0C624768AA8ACFBD67AB9CB461B842FEC5450 (void);
extern void CinemachineOrbitalFollow_GetMaxDampTime_mD3039B8BEE32EFE3AA39DE50CD4DE3DCFB862219 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisOwner_GetInputAxes_m7BC81810798A50C2419ADAFDC3029AE6E23BB8E9 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_RegisterResetHandler_mFA7D8161577D4DE66E1279008571C1528F0C56CB (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_UnregisterResetHandler_mB04173C2B4F550E98D459D34D40F8FC95FFA8B10 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_get_HasResetHandler_m0909105F241DD8631390DB53BEBB71FEBA5F8FCB (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m5F502F9B8B942D05C82B41DB02BA6307CA599908 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m0DBADCF5A6E7E2EB82E1D2F62BC21AFECD06A949 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_m2D910B8E44A476B0F9ECFCF5E7BA02C4A2077E85 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m3D4950D3A947F8348E80EBB29AAF928FCDB24850 (void);
extern void CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m5ADF76933C766EB851D100959DC2ED2CCF0113D8 (void);
extern void CinemachineOrbitalFollow_GetCameraOffsetForNormalizedAxisValue_mA40C84E1011360E302CB71A156B1DE5E5F942E1E (void);
extern void CinemachineOrbitalFollow_GetCameraPoint_m424A9F6125D071C39217E3532807456E62DFF711 (void);
extern void CinemachineOrbitalFollow_OnTransitionFromCamera_mC1959BEB633AD662FEC16100C1D4FE6620489EA6 (void);
extern void CinemachineOrbitalFollow_ForceCameraPosition_m6B2AD4C4CC9C0D259D8A54C908CAE2E175C354D1 (void);
extern void CinemachineOrbitalFollow_InferAxesFromPosition_Sphere_m8D82354301A3AF91FD6A77965856150002173269 (void);
extern void CinemachineOrbitalFollow_InferAxesFromPosition_ThreeRing_m85E4D8FB57D3ED53D92EE59D983585CE538C6195 (void);
extern void CinemachineOrbitalFollow_OnTargetObjectWarped_mC9B656DA211B1CE1C961BE5642D8901FDAE233D5 (void);
extern void CinemachineOrbitalFollow_MutateCameraState_m7C533C0CA41DF74B6027BC925EE80BA0F81D0E6C (void);
extern void CinemachineOrbitalFollow_UpdateHorizontalCenter_m431290B907BB1E7EF18EC316EFF6778AA731FE74 (void);
extern void CinemachineOrbitalFollow_GetReferenceOrientation_mE357A5CE25CCC8231D4017AF10CABEDC3F843016 (void);
extern void CinemachineOrbitalFollow__ctor_mA5E7ACBA058B60854F7254E1BBF5BBCFAA1B9DC7 (void);
extern void CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_0_mE9826AA0E8C845B8649CCCF5061C076A5FA7B9B5 (void);
extern void CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_1_m323E571454861DEE22B4ED36A3EEA4E4BE7A9B67 (void);
extern void CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_2_m4E6F8635BA9CABC62036312C80F1172EF9F6A5DD (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__GetHorizontalAxisU7C50_0_m3A7CA91577381FF9B6A5660CB85A33ADB4EDCAFA (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__GetVerticalAxisClosestValueU7C50_1_mEC451AC3DDF3F4D5F00D57BEEB4BCE799A632E96 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__SteepestDescentU7C50_2_mA81C1BC91FCC28B033E6AC6D4EAC9C329BD7C6A4 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__AngleFunctionU7C50_4_m91FDB466E9C2DA50423665596030ACFD6346E687 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__SlopeOfAngleFunctionU7C50_5_mAAB6093C8E81C0B649D125BAF3790DE26E677557 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__InitialGuessU7C50_6_mAF30EFD7E56913C6D9B663DD185B44A36153D6F8 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__ChooseBestAngleU7C50_7_m8E53D917C5CC64B60A2A1F7631D6DCD34B487B30 (void);
extern void CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__MapTo01U7C50_3_m409AE10D70DB97CEDFDB544C5875CE5A70A85567 (void);
extern void Settings_get_Default_mDA8AD63191BD95F7F563A6DB36A4DCCE70B3425E (void);
extern void OrbitSplineCache_SettingsChanged_mE93B6AD4D70694FC04BCCE02118447F278CEB151 (void);
extern void OrbitSplineCache_UpdateOrbitCache_mEE095885509E49893CA6EAAEF08383EE05F9BEBD (void);
extern void OrbitSplineCache_SplineValue_m4EF5A34796ED0FF7210F0F7B0365070E7B2CEADD (void);
extern void CinemachinePanTilt_OnValidate_mEB368963E6D3D892FF2B159A4FC8D0F704422024 (void);
extern void CinemachinePanTilt_Reset_m442EDD54D7FA2D1CCE7BA5FF1B9CDADBA3360D58 (void);
extern void CinemachinePanTilt_get_DefaultPan_m1A2455E0CAB62E0F96764B8279B683D06EA3825E (void);
extern void CinemachinePanTilt_get_DefaultTilt_mCD80D869ECDA84B6B44D6DBB693F7469C762DFD5 (void);
extern void CinemachinePanTilt_Unity_Cinemachine_IInputAxisOwner_GetInputAxes_mB2276A8C1072B69FD73DCB59C94EEB72DC6BFD21 (void);
extern void CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_RegisterResetHandler_m4CACEE56391345AC84CBD11BCDC8DCF31F0BE386 (void);
extern void CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_UnregisterResetHandler_m2D19067DC671DDABA3C22AF377CB07F46EB7C787 (void);
extern void CinemachinePanTilt_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m1FB0285358F5A0D4EB6AE5E710582466341BA4BC (void);
extern void CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_get_HasResetHandler_mD8A83CD0632259A53A7E0EEE7274671C76A642E1 (void);
extern void CinemachinePanTilt_get_IsValid_m3D4617517E5730C0220379EDFB18F68A403E6302 (void);
extern void CinemachinePanTilt_get_Stage_mFAFA0EBA5E1ADABB0B34CDE836F8E2843BFF3028 (void);
extern void CinemachinePanTilt_PrePipelineMutateCameraState_m911EB22321C98911810BCB30E1064CCA690D891A (void);
extern void CinemachinePanTilt_MutateCameraState_m97FA2F7A8C07870858C100A593F58FBC875F2791 (void);
extern void CinemachinePanTilt_ForceCameraPosition_mFF8C80379DC5F9B991C4F76F81CF0EE3DA1899ED (void);
extern void CinemachinePanTilt_OnTransitionFromCamera_m4CE20ED429347F32177DEC99D26665BCE4E1BE3F (void);
extern void CinemachinePanTilt_SetAxesForRotation_mD657916690343AE8A81186FC2D78BF68AEDAB8CA (void);
extern void CinemachinePanTilt_GetReferenceFrame_m547364EB069E964DEDE9DC534809161B69A1D25B (void);
extern void CinemachinePanTilt_GetRecenterTarget_m3B0EE4AA6A1E76BABA668437068387903DABDC3B (void);
extern void CinemachinePanTilt__ctor_mBCB4F55B8DA59793DDC2B6143CE8FD5C9C9AB901 (void);
extern void CinemachinePanTilt_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__14_0_m2841BF42AB2F8BE4B137EAC7793349149A248495 (void);
extern void CinemachinePanTilt_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__14_1_mDE23936E05BB421C0BCE477C77CFBAC7EFB19435 (void);
extern void CinemachinePanTilt_U3CGetRecenterTargetU3Eg__NormalizeAngleU7C31_0_m78DD09531A73DBE3D54A65B31114B35B19505FA2 (void);
extern void CinemachinePositionComposer_get_GetEffectiveComposition_mFAE433732C019BDD897D5A576CB3CB8C0743E52D (void);
extern void CinemachinePositionComposer_Reset_mC6AEE40D90C95CCB62BE5D0609FA721930903F7A (void);
extern void CinemachinePositionComposer_OnValidate_m77E9821579C07E376397DE92831384B3F2079F04 (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_get_Composition_mF2032B6D352F9DE8887DF712B27094AB17651C3D (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_set_Composition_mFDF2E2BDC48D543C05BDFFEC2A5F3C5E0DAA1F1E (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m7887198B311A38FA5E631AAF8506BFAE74ADCAF4 (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_m60C3DD73923916E54DF03C4D722B84CC220DDC3C (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_mB57A8472CA0ED611644EA0177A3743DCD89CEE90 (void);
extern void CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m41741FF7B31941CCD752F12135F530E2F3702D1B (void);
extern void CinemachinePositionComposer_get_IsValid_m8860A24E2DFBCB96456828BDF91A838DFDB3961C (void);
extern void CinemachinePositionComposer_get_Stage_m025C2A298F01F4CCDBC76B7BB16EAC247ED874DE (void);
extern void CinemachinePositionComposer_get_BodyAppliesAfterAim_m67F09AE75D2E86FF22614245D554B2C51D86161F (void);
extern void CinemachinePositionComposer_get_TrackedPoint_mCCBF19E6DF8BE8C79DD1EA1E317807114435DF1E (void);
extern void CinemachinePositionComposer_set_TrackedPoint_m2F2F16E5D486BC430DEE0A456618787BBC268AF5 (void);
extern void CinemachinePositionComposer_OnTargetObjectWarped_m5E985899CD4BC7F94CE104C1F03A7909A9297ADD (void);
extern void CinemachinePositionComposer_ForceCameraPosition_mB63489141190E4A74E1FB91201E3B2F37C3788D8 (void);
extern void CinemachinePositionComposer_GetMaxDampTime_m2F2D5051F80BED023333782EBD97C31ED591DE13 (void);
extern void CinemachinePositionComposer_OnTransitionFromCamera_m03AE0BDC812160B4256397286027E1A069FF9D3B (void);
extern void CinemachinePositionComposer_ScreenToOrtho_m362BCD4CB1541C2030745A0E62A86B9B13C2434D (void);
extern void CinemachinePositionComposer_OrthoOffsetToScreenBounds_m88F4177CC359D817CF71CF2C5F5697ACED90B766 (void);
extern void CinemachinePositionComposer_MutateCameraState_m846EDCBC18AA6F774CBF3813B9CCD58E41D6E99B (void);
extern void CinemachinePositionComposer__ctor_m8BAD7685BB1C75FFF2D2EAFFA3F2ECD6C025D3F3 (void);
extern void CinemachineRotateWithFollowTarget_get_IsValid_m011E2DCE96D7D800513B8A9C2C91B58EF71DC78E (void);
extern void CinemachineRotateWithFollowTarget_get_Stage_m8B1986DF8AF6D25CED00B4B62B64678294658605 (void);
extern void CinemachineRotateWithFollowTarget_GetMaxDampTime_mC707D09D3F8F17CDE38226BCFC5615A9A8A3603E (void);
extern void CinemachineRotateWithFollowTarget_MutateCameraState_mC93BEBDEB3D717E490421A6BE55BABC9D3CCFB76 (void);
extern void CinemachineRotateWithFollowTarget__ctor_m1D49E4E085497D7111F7B8FCA87B7CE45A7E0091 (void);
extern void CinemachineRotationComposer_Reset_m9EB67C485D1906CA68AC257BA2948E57CF68015B (void);
extern void CinemachineRotationComposer_OnValidate_mBA676070BF8DFACD00449F349D5C4A71874DCB2E (void);
extern void CinemachineRotationComposer_get_IsValid_mF597D231A410FD6FEDBA4544B1673F69C8835BC5 (void);
extern void CinemachineRotationComposer_get_Stage_m202BBFEC2632E3E33FB5F0868276905AC3DB4782 (void);
extern void CinemachineRotationComposer_get_CameraLooksAtTarget_m40D45BAD5D0ECF224723EB1F89330CEF75DBB864 (void);
extern void CinemachineRotationComposer_get_TrackedPoint_mE7BC076074745CED759DA5BA9A4663C4B1A6E40D (void);
extern void CinemachineRotationComposer_set_TrackedPoint_m0A6F781CA6271496E36D76E9F29E05A6C27C6CC9 (void);
extern void CinemachineRotationComposer_get_GetEffectiveComposition_m3436B218B6C3C92AE0486BDFD6C481C73F34B370 (void);
extern void CinemachineRotationComposer_GetLookAtPointAndSetTrackedPoint_mB6824FFBE79AB78587399A347ED3EF2593C4A2BF (void);
extern void CinemachineRotationComposer_OnTargetObjectWarped_m81C264C9D50A82C103F1C5F2E448365806C26295 (void);
extern void CinemachineRotationComposer_ForceCameraPosition_mFDA90B8A897E826BDDB67F40AD1A2C27292082A2 (void);
extern void CinemachineRotationComposer_GetMaxDampTime_mE998A5E72BB5B1379E1482CD619CD04E671BD78F (void);
extern void CinemachineRotationComposer_PrePipelineMutateCameraState_m853D661A776B9C43DF903FC0D3EFAEE3517AF411 (void);
extern void CinemachineRotationComposer_MutateCameraState_m517B63B729A7FEEE5506C03587AED29C0FC35950 (void);
extern void CinemachineRotationComposer_RotateToScreenBounds_mDCCCF46AE265D22CDD6996C8BC85D538D6C9F2DF (void);
extern void CinemachineRotationComposer_ClampVerticalBounds_mBB6B805C67C5C6D080981B434A6F13DD3E4544A2 (void);
extern void CinemachineRotationComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_get_Composition_m077D0E3940C3625255B90E5C171BAF363AD6A7F0 (void);
extern void CinemachineRotationComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_set_Composition_m97C412BA759F528586F9DDFEE0E5264E07488416 (void);
extern void CinemachineRotationComposer__ctor_m34E958BFFC94B45048F4230B170778B474A2A395 (void);
extern void FovCache_UpdateCache_mBE2C2FD57DAD380D862B04A8374C6A3E6C60B99E (void);
extern void FovCache_ScreenToAngle_m697CC6600E25E343A92923A25F91567B3202669F (void);
extern void FovCache_DirectionFromScreen_mC6E97AA5783FD6A4FEABAAE42E3FA3C14C47144B (void);
extern void CinemachineSplineDolly_PerformLegacyUpgrade_mB15F1CA32A5F42E077ABAF8421A596834BE2F611 (void);
extern void CinemachineSplineDolly_get_SplineSettings_m0FEAC219FAE29EFFF51B08F1EFC764ECAFB55B6A (void);
extern void CinemachineSplineDolly_get_Spline_m041CFE464883EA4B3D539FADC9FC4364EF81E826 (void);
extern void CinemachineSplineDolly_set_Spline_m35075A8EF36AE412C8C5969462B37AB2D2DF9E47 (void);
extern void CinemachineSplineDolly_get_CameraPosition_m6F48E4837D4564CC15C631B85403C86FD323841A (void);
extern void CinemachineSplineDolly_set_CameraPosition_mC738F980AAF2A9CAE106B5CB3C95562160A1DE16 (void);
extern void CinemachineSplineDolly_get_PositionUnits_mED892ADBC97A52C3BFDBB26C37C20098EDE05296 (void);
extern void CinemachineSplineDolly_set_PositionUnits_m6EE169F0C3BC0E5EE6B9D600D2D2FEEB50AE516A (void);
extern void CinemachineSplineDolly_OnValidate_mA2528278EBE9FE17ABA179B6602079AAF6260BBB (void);
extern void CinemachineSplineDolly_Reset_m1384CF250F6BBF7CC47CFAD2BABB1B750AF981F7 (void);
extern void CinemachineSplineDolly_OnEnable_m6BDD3BBAFBCF134E9EAF7E310D4A6EE0C645982A (void);
extern void CinemachineSplineDolly_OnDisable_m471745B15C702AA5B32C5CC9D8E58ADDFDAC5586 (void);
extern void CinemachineSplineDolly_get_IsValid_mF12C38EB43AF615EFE26384509FE45A0FD959F83 (void);
extern void CinemachineSplineDolly_get_Stage_m4692BD68A2E147D517836D88DD056A8F142A4789 (void);
extern void CinemachineSplineDolly_GetMaxDampTime_mEB4406D893E2A030CBD7E715F7D628432E0D4497 (void);
extern void CinemachineSplineDolly_MutateCameraState_mC78B97FB6BAC9E9915C4EF9E7149B863293177B2 (void);
extern void CinemachineSplineDolly_GetCameraRotationAtSplinePoint_mA347EFF7F38D8DF1434B20CBE329B0A82EDA9B42 (void);
extern void CinemachineSplineDolly__ctor_m2E5B83CF4E777F536A8BEAE163DD2442C953FEF5 (void);
extern void CinemachineSplineDollyLookAtTargets_Reset_mCF14582D038BFBD0D1767DE0E0D9FC62A7AEC71A (void);
extern void CinemachineSplineDollyLookAtTargets_get_IsValid_mAB051C2104A99D7A0B11B2C2293C3F9AF89F9951 (void);
extern void CinemachineSplineDollyLookAtTargets_get_Stage_m4F0C28152881405C745C987BF1815AF5E2A3B1FE (void);
extern void CinemachineSplineDollyLookAtTargets_MutateCameraState_m2ACF028041E1155D302E5DE754F86DBFEE665500 (void);
extern void CinemachineSplineDollyLookAtTargets_GetGetSplineAndDolly_m52C897647678C8951447444CADCBC688C5F61968 (void);
extern void CinemachineSplineDollyLookAtTargets__ctor_mD7C03CB7A7A94DE5495B3FB388F01B0467B4E552 (void);
extern void Item_get_WorldLookAt_m5FC326A00B241D52FC2C5C8152BCB0FE23BB1E36 (void);
extern void Item_set_WorldLookAt_m8C05FC415D07807B4349FF9C858391BF04BBC24A (void);
extern void LerpItem_Interpolate_mC3591384873341D9D393C147FBA9F5EF94FA46BF (void);
extern void CinemachineThirdPersonFollow_get_CurrentObstacle_mEC20AA0E72B3CD01C2C4DCC7A68B98E6B01AC2A1 (void);
extern void CinemachineThirdPersonFollow_set_CurrentObstacle_m8F10CB8B3C7DDBDFDEDE354731AEEB82A7617961 (void);
extern void CinemachineThirdPersonFollow_OnValidate_mA5B3E521F0EDE8A201702BCC8BD3CB2CA2BB7814 (void);
extern void CinemachineThirdPersonFollow_Reset_m629B27ED3919A314034D0F5AF94A53FEF9AB4859 (void);
extern void CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m73294D7D610E05BA8A4A8856BFC569D8004B24EE (void);
extern void CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m8649C980990CA3EF97A9563806008A829A058364 (void);
extern void CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_mCF1D5DD1A43EF61778EC5484407F9C0C6942D943 (void);
extern void CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m956BB974C0243663A50B4B01EF9A3464101084E4 (void);
extern void CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m09966A3E4FF0926F1D8BD4273F1DD7B739C62836 (void);
extern void CinemachineThirdPersonFollow_get_IsValid_m2295836C8CC389ABB098E1BB5F680EA3E95CD719 (void);
extern void CinemachineThirdPersonFollow_get_Stage_m8BD02FAB60DC6CD855D22C2573469410E9E106B6 (void);
extern void CinemachineThirdPersonFollow_GetMaxDampTime_m733CE61BD86CC854F2DAC2AF1A542C77BCB64A34 (void);
extern void CinemachineThirdPersonFollow_MutateCameraState_mED66DEEC78719A9BA50680A982A87F5DFBBE86A8 (void);
extern void CinemachineThirdPersonFollow_OnTargetObjectWarped_m3B4E74883C0DF3F66F5229ACCC4420A74CA4E7F9 (void);
extern void CinemachineThirdPersonFollow_PositionCamera_mE85D97810EBD91108691EB77017211127C8162D4 (void);
extern void CinemachineThirdPersonFollow_GetRigPositions_m9D2A508DBAB0080915D2E4644AB014AA2DFC775B (void);
extern void CinemachineThirdPersonFollow_GetHeading_m794B4EA4F31F8C92153C9511741FEA55E6A6E3FE (void);
extern void CinemachineThirdPersonFollow_GetRawRigPositions_mF6175B4E9EED134D5CFA18436335195C6B35A005 (void);
extern void CinemachineThirdPersonFollow_ResolveCollisions_mE24DD4A21D7BF43B69113D323E43421FA8D0A7AE (void);
extern void CinemachineThirdPersonFollow__ctor_mC65B4D43E85565959938E8DAAFBF0BDEEF4B4B43 (void);
extern void ObstacleSettings_get_Default_mAEA852450D1E9BECD0BBB0364291296790F392A4 (void);
extern void BlendManager_OnEnable_m4E0000025C81A2130A6BDCA7E34E4C2F604AFC78 (void);
extern void BlendManager_get_ActiveVirtualCamera_m29E032AB3726D10B07B9520B4F09DF6838BA710C (void);
extern void BlendManager_DeepCamBFromBlend_mBBF8734887273F41BCDDE8C117855D2565D2C582 (void);
extern void BlendManager_get_ActiveBlend_mF44711D3574FBD5EDE5ACAAC8B4861A86B0F1391 (void);
extern void BlendManager_set_ActiveBlend_mC83F9C7532D5DA3BDC61AC1773B40AA4682B0506 (void);
extern void BlendManager_get_IsBlending_m6A397A4469F590EA64C3EDECE82EC6179D2B52C6 (void);
extern void BlendManager_get_Description_mBD6C4BBBF57F7C6D96AA8E0217ADE371B53994E5 (void);
extern void BlendManager_IsLiveInBlend_m3FA5E2D6620457DE488D30B2EBD0F1F3A62D0877 (void);
extern void BlendManager_IsLive_mE23B9958ECA2AA6E3364BFE876FAA2AB86834521 (void);
extern void BlendManager_get_CameraState_mE7815E7A60FFE3640F91A6A359F1B09ABB38C5E6 (void);
extern void BlendManager_ComputeCurrentBlend_m7469CC25BAACB7538442D2EC4388156603802030 (void);
extern void BlendManager_RefreshCurrentCameraState_m0587B0615F6BCDC9EB221AFD9956F732852236E0 (void);
extern void BlendManager_ProcessActiveCamera_m6D03C5D02130A8C84635324771B6AB8744EF5B3F (void);
extern void BlendManager__ctor_m6D824F8C5F9C2A7FD2B7E7344C72C08B009237B6 (void);
extern void BlendManager_U3CProcessActiveCameraU3Eg__CollectLiveCamerasU7C21_0_m3127987B812EF6925B8D2D5FD5F22EDE771C8B1A (void);
extern void CameraBlendStack_get_DefaultWorldUp_m66523230416DB99E62227B87B20FE97A996FF8FF (void);
extern void CameraBlendStack_SetCameraOverride_m83F5918402E4915D8E65504B7FDA5B27D9841645 (void);
extern void CameraBlendStack_ReleaseCameraOverride_mCD593715F64C0D3850BFD7E02D36223B82FD58DF (void);
extern void CameraBlendStack_OnEnable_m02D77290D65AD9A09E81B960AF9BF2A93A748016 (void);
extern void CameraBlendStack_OnDisable_mC17B1918ABBA1CC63E5AAA19F3A9EC33F8EB306A (void);
extern void CameraBlendStack_get_IsInitialized_mBEFB5CFD670C84DBAA98AE213138328D82D2AEA4 (void);
extern void CameraBlendStack_get_LookupBlendDelegate_mA653E86DB58865B73CBD97915F7C789BBC5CB177 (void);
extern void CameraBlendStack_set_LookupBlendDelegate_m7CE34F82C0F52DA5BF28CA8634501E52F74C6398 (void);
extern void CameraBlendStack_ResetRootFrame_mDBEA39C701F6BEA5BB7523809061E486DE47381E (void);
extern void CameraBlendStack_UpdateRootFrame_m380E44AC8A016C16EF5C2A377E50B7923E290F53 (void);
extern void CameraBlendStack_ProcessOverrideFrames_m81E7A82232BBAFF7B34E88822A03EC3F20361CC6 (void);
extern void CameraBlendStack_SetRootBlend_m9EF7F3EF5C547768C70209C504B5B98844E2C4E0 (void);
extern void CameraBlendStack_GetDeltaTimeOverride_m6751B96BCB998FF1FF81A8CC74A5B5AAA7195E75 (void);
extern void CameraBlendStack__ctor_m692C21630EBD2FAA23FD6F075A9FA67EECB24CB0 (void);
extern void CameraBlendStack__cctor_mFFB924A76892886F990E0E45D2F2D7EEB43B7BB0 (void);
extern void CameraBlendStack_U3CSetCameraOverrideU3Eg__FindFrameU7C7_0_mC511F6E9D8830DD526D8FD82162157F4552A485F (void);
extern void CameraBlendStack_U3CUpdateRootFrameU3Eg__AdvanceBlendU7C18_0_m60DC36BF49C95A311722BE940D13B7CCBA72D46B (void);
extern void StackFrame__ctor_mFCF6C137B34EB76AC197F0857DD60BAA4E84E447 (void);
extern void StackFrame_get_Active_m2BD2519893FFEF932DB31DAF621A3CC926D7538A (void);
extern void StackFrame_GetSnapshotIfAppropriate_m42DCE69A1562483EE8AE3A1DC984D063499DE5BE (void);
extern void SnapshotBlendSource_get_RemainingTimeInBlend_m6C1511FA87A6F5F068CE8C1B6A3FE3C2FB2C51F5 (void);
extern void SnapshotBlendSource_set_RemainingTimeInBlend_mD4C63E7C8BA89930999890AAF7A5E268C0274664 (void);
extern void SnapshotBlendSource__ctor_m25CF4762C2DD894BF0A8C7E0A9A0BCC8C7BBBEA5 (void);
extern void SnapshotBlendSource_get_Name_mC0C3BA66648BCC5E7F587A901235824FC20C0D4A (void);
extern void SnapshotBlendSource_get_Description_mE595AEB80D5FDFFEC39450F188A53BEB75BC7343 (void);
extern void SnapshotBlendSource_get_State_m2D12F08072357D64ED3A3B4FCFE5623B573C01FC (void);
extern void SnapshotBlendSource_get_IsValid_m8236EC54D50E9DD6893CE3681043D2FF1583CDD5 (void);
extern void SnapshotBlendSource_get_ParentCamera_m108E2B4897F03D783E196E2D6ED0800CBE593548 (void);
extern void SnapshotBlendSource_UpdateCameraState_mB819667A3FEE53972DDF63D174D7E3688A798281 (void);
extern void SnapshotBlendSource_OnCameraActivated_m4568709D55A05987424408B3BA2778359AEE0C54 (void);
extern void SnapshotBlendSource_TakeSnapshot_mBE81FB399C1B2B8FBFFFF481FCA3A745787FF458 (void);
extern void CameraState_get_Default_mD69074AD6B6D491AE36193203D78F6A748A7BF19 (void);
extern void CameraState_AddCustomBlendable_m0AA826256B3ED67F345FE5DBE2881830CC4CCCAB (void);
extern void CameraState_Lerp_mFD80713BCFBAA0B937D83BDFD1505706088D66DC (void);
extern void CameraState_InterpolateFOV_m5C09D707583A9EAC6A027CE93B49EA4A163C30BD (void);
extern void CameraState_ApplyPosBlendHint_m22B5EEC209158D78F3EC0C236F2B6DDD4A09613B (void);
extern void CameraState_ApplyRotBlendHint_m136A1971A3EF5367BFE064B483716B9973AAD705 (void);
extern void CameraState_InterpolatePosition_m8A7447EE2D5FED3614D73F884B64B49A11CDB1D4 (void);
extern void CameraState__cctor_mFFC4430D63CA26D3531FC5EA45290515ABB96B86 (void);
extern void CameraStateExtensions_HasLookAt_m7A95F08DBF4242DA032710DB8A4C08FD90E3A6E3 (void);
extern void CameraStateExtensions_GetCorrectedPosition_mB0FD3576F7A1173911B35598EF098C97CA0615FB (void);
extern void CameraStateExtensions_GetCorrectedOrientation_mF9C37FE55D80D50168F2E5A89A555A6EC8DF3D51 (void);
extern void CameraStateExtensions_GetFinalPosition_m94D469582F60CF0117C2CE9B93DB574EDF152AB0 (void);
extern void CameraStateExtensions_GetFinalOrientation_mEB5584BB2351BB42C2CA87CA5B5B7C89BDAC40F2 (void);
extern void CameraStateExtensions_GetNumCustomBlendables_mE7DCAAE4567E31F80D40A924977F7C7AF2D7D524 (void);
extern void CameraStateExtensions_GetCustomBlendable_mAF74CC00AB89208B005F0E720A1516B8112CCA17 (void);
extern void CameraStateExtensions_FindCustomBlendable_m4B9B4BAC928137CB8E709894F21239F8EAC3B32F (void);
extern void CameraStateExtensions_IsTargetOffscreen_mE05237C31B634F74E2209924342A75647C077BC8 (void);
extern void CameraUpdateManager_InitializeModule_m29CF9E2D4AF43477DBFF274A074AFEA513F49D72 (void);
extern void CameraUpdateManager_get_VirtualCameraCount_m77560DFF4A30601542778B09E1311ED4211881BE (void);
extern void CameraUpdateManager_GetVirtualCamera_m2A7B000759CF468F2D277E7C4BE7C78243986B07 (void);
extern void CameraUpdateManager_AddActiveCamera_mDE6D175910C2F74CC02192806330A31863619BF4 (void);
extern void CameraUpdateManager_RemoveActiveCamera_m40EFC9E6018206FFB121C86C6F704E6D10F90823 (void);
extern void CameraUpdateManager_CameraDestroyed_m80AEFAD29231C3840C7DFF90D3484AD33C102BF4 (void);
extern void CameraUpdateManager_CameraEnabled_mF20ECF96288C42846FF384C1F9781AE5EF3B0F5F (void);
extern void CameraUpdateManager_CameraDisabled_m1ECB5CCE9D7796E71400C411B27C8BDA684FBAEC (void);
extern void CameraUpdateManager_ForgetContext_m0D126C54343308E15D14501B2A9BC6C6D95BDC96 (void);
extern void CameraUpdateManager_UpdateAllActiveVirtualCameras_m19F9E944A70080E3FD0A02972FD33A4856C8FC6C (void);
extern void CameraUpdateManager_UpdateVirtualCamera_m86ECD688DFB565629940F27B614B06323F4417CC (void);
extern void CameraUpdateManager_GetUpdateTarget_m22B260DC6BEE6CC5D1DCBE003A712A86C93BE50C (void);
extern void CameraUpdateManager_GetVcamUpdateStatus_m98AA6A80F41B4158305106741EA48FE042546028 (void);
extern void CameraUpdateManager__cctor_m287FCD6DC066A9C5ABCD3D89E154976D0808B63D (void);
extern void UpdateStatus__ctor_m20AF6E04E04D0D2AD5E7D6BF21BF680583391645 (void);
extern void CinemachineBlend_get_BlendWeight_m97C082AA2A3AF1F8E75B3EA6DDA2AC7D18CDCDA2 (void);
extern void CinemachineBlend_get_CustomBlender_m965AE257516C38F6C680C2BDD755B68710CC7569 (void);
extern void CinemachineBlend_set_CustomBlender_m9A25BD8E5114EA22CC2C8222A32B12167C3BB29B (void);
extern void CinemachineBlend_get_IsValid_m5EE72C7A52D34FC083F2D6D6340C11D87CE55EB0 (void);
extern void CinemachineBlend_get_IsComplete_m947FBEF655E91D49C1706F03CE14D683B6017A54 (void);
extern void CinemachineBlend_get_Description_mAC7F69E790C47B95B56C8916C34ECAC5699D4B35 (void);
extern void CinemachineBlend_Uses_m92123D6ACA37D0BF71A81A86C5956BA739CFB4F1 (void);
extern void CinemachineBlend_CopyFrom_mBAA041B8980B173B783949EFDF8D29588B88D32A (void);
extern void CinemachineBlend_ClearBlend_m29C0F6784AB71F8ADE61FD3A32463BBB2156E5E8 (void);
extern void CinemachineBlend_UpdateCameraState_m5D905501623CC3511AC3AE2DC69C0DAADC747432 (void);
extern void CinemachineBlend_get_State_m3C3AD6FE6F78297DB4E1877F1C29621BDE94065F (void);
extern void CinemachineBlend__ctor_mDB183B3B9E68F4758B74D85E8D7BF30A36B62629 (void);
extern void CinemachineBlendDefinition_get_BlendTime_mEAAA78C5DA24961202ED2E0ACA2BA3876F13F0CA (void);
extern void CinemachineBlendDefinition__ctor_m39FCAAA94DC44BAE3882C37D8BE9CC903B3C3D0C (void);
extern void CinemachineBlendDefinition_CreateStandardCurves_m03315670810AA1CBA01C39471C2A53B291771D10 (void);
extern void CinemachineBlendDefinition_get_BlendCurve_m7613588597293BB3B062EA98D823D78C19AB9AB6 (void);
extern void LookupBlendDelegate__ctor_mD71CB40CDDE5786EE133528BE84B962996C9CC20 (void);
extern void LookupBlendDelegate_Invoke_m03596CD7B7B777D770B2EF6FBF9A959D562A7513 (void);
extern void LookupBlendDelegate_BeginInvoke_mA48F98E8EF05AACD5B0929BE56A9CEAFCD45CE16 (void);
extern void LookupBlendDelegate_EndInvoke_mCF87786030253AF8C591638DBE857255410E888F (void);
extern void NestedBlendSource__ctor_m7EBE3B4707F4EB471443E52272CAFECADB15B87D (void);
extern void NestedBlendSource_get_Blend_m4BDC8E79BC05FD3174233808BA8510B42EEF7F03 (void);
extern void NestedBlendSource_set_Blend_mD011D3413328BF2F07AB14539B17349F818547E6 (void);
extern void NestedBlendSource_get_Name_mD316266B336E7A2E0E5691F701A5437A906549D8 (void);
extern void NestedBlendSource_get_Description_m3317DCBADF9A42E2C32087C6D3BC8A9828590A62 (void);
extern void NestedBlendSource_get_State_mBA8354DC1527CD0162802C458D7AA72104346279 (void);
extern void NestedBlendSource_set_State_m8BBB5BC3919A0D65FDE212BEEF504B5A6429B5E7 (void);
extern void NestedBlendSource_get_IsValid_m7F8A87153A84B46E73E536A0BC3301C6B1ABB9A4 (void);
extern void NestedBlendSource_get_ParentCamera_m61CC6C074634C351DC76384D8D90D86F85A2349E (void);
extern void NestedBlendSource_UpdateCameraState_mDD3893BA47D8507086C742F92AB2C06D94ADDA9D (void);
extern void NestedBlendSource_OnCameraActivated_m4B350183065B333349C64943A6F9705513372310 (void);
extern void CinemachineBlenderSettings_GetBlendForVirtualCameras_m4951DD388E5E705A1D693B530F78E4C1FBC8FE06 (void);
extern void CinemachineBlenderSettings_LookupBlend_m490C2A69812AEC37601C476F8DB1D0D2F6764E7B (void);
extern void CinemachineBlenderSettings__ctor_mA194685EDC721F1CFCAB403632688EB4775EC248 (void);
extern void CinemachineCameraManagerBase_Reset_m3B5E5B6053111A23DF66DB9BFC13E255AEF31CD1 (void);
extern void CinemachineCameraManagerBase_OnEnable_m4D75E25AC79AE356BC7023732820114D38D7365B (void);
extern void CinemachineCameraManagerBase_OnDisable_mAC1961839FDA3FAD744062BB5416F7DB4D6707CE (void);
extern void CinemachineCameraManagerBase_get_Description_m8615103557B6F9F6F2971FC187E241FFB4718021 (void);
extern void CinemachineCameraManagerBase_get_State_m4B961BB28A6FE1BE1823666A3322EAA7990F46AD (void);
extern void CinemachineCameraManagerBase_IsLiveChild_mA6756878C0C6390536CEBB84BBB856BE819B635B (void);
extern void CinemachineCameraManagerBase_get_ChildCameras_mE90A6C9C1E20294423EC438B5758FBA262CE4DC2 (void);
extern void CinemachineCameraManagerBase_get_PreviousStateIsValid_m515D79F64A71A2EF220F59BF26A608BED2325556 (void);
extern void CinemachineCameraManagerBase_set_PreviousStateIsValid_m868DC9D6C56EB9204523985BB3434767CD035B43 (void);
extern void CinemachineCameraManagerBase_get_IsBlending_m5DA9AD35898E14CD00DF93B97B47DB3C912D8741 (void);
extern void CinemachineCameraManagerBase_get_ActiveBlend_m4E91A04AFE9DDA00AC8B5BAE81E48A5E65EBE0AF (void);
extern void CinemachineCameraManagerBase_set_ActiveBlend_m668AE15A189C7AF8762E0BA9A54492E3A36C3843 (void);
extern void CinemachineCameraManagerBase_get_LiveChild_mFAED3CD4B6564F5C92A4AF94780D3E59C02C318E (void);
extern void CinemachineCameraManagerBase_get_LookAt_mCD330D46B5217DF5F92A94C8A873D2732B20493A (void);
extern void CinemachineCameraManagerBase_set_LookAt_m59FC8788902093A39417082E5C0B9BECD3701DA0 (void);
extern void CinemachineCameraManagerBase_get_Follow_mAFB1722C56B048FDF07E5F8790A29E4F93716493 (void);
extern void CinemachineCameraManagerBase_set_Follow_m13EC394E925A0A649CB128CEA8C8D613F3CADD47 (void);
extern void CinemachineCameraManagerBase_InternalUpdateCameraState_m60D37331CE396C4A15710EB075921485DBE8E678 (void);
extern void CinemachineCameraManagerBase_LookupBlend_m598C971C905EC6730E7AA4E6F8F52E5573B31A9D (void);
extern void CinemachineCameraManagerBase_OnTargetObjectWarped_mC15D3F6E8BE020D013BE39C9B5C425ACCFCE2506 (void);
extern void CinemachineCameraManagerBase_ForceCameraPosition_m019E6DAF197702B5713FCB2B8DC7E7723B451762 (void);
extern void CinemachineCameraManagerBase_OnTransitionFromCamera_mE20E05E85379E4D144AEB8096AFB8702C8E51A75 (void);
extern void CinemachineCameraManagerBase_InvalidateCameraCache_m7608FC7337215720BA0C438F139F78BD28672CDA (void);
extern void CinemachineCameraManagerBase_UpdateCameraCache_m09FFD73FFD8E2FFC40BC341F243823F5B2CF9D9F (void);
extern void CinemachineCameraManagerBase_OnTransformChildrenChanged_m3324BF871DE74908088B87D457A8333B1DFFAA51 (void);
extern void CinemachineCameraManagerBase_SetLiveChild_mE5A612156CEA0934BB0AEF667777A8DB82CD5E18 (void);
extern void CinemachineCameraManagerBase_ResetLiveChild_mA1E777A316F7B4ACA4C644C3582A38BDBFE2C749 (void);
extern void CinemachineCameraManagerBase_FinalizeCameraState_m4483D1231FC4B83A5C714D73C0E6688086CEFC5E (void);
extern void CinemachineCameraManagerBase__ctor_m6DFB3B858A5652EDFBFA12A75A01DF3D041859E0 (void);
extern void CinemachineComponentBase_get_VirtualCamera_mD329B41E529512F6D6865C494250CE7752B3414D (void);
extern void CinemachineComponentBase_OnEnable_m26CDC97E5D8E23ACE8B57DA8D5D6FFDE05235825 (void);
extern void CinemachineComponentBase_OnDisable_m850A44908937828074D3C7D350D8D85490E0D881 (void);
extern void CinemachineComponentBase_get_FollowTarget_m5FD071F4CFC04A762C050C595FFA01AD65E7B735 (void);
extern void CinemachineComponentBase_get_LookAtTarget_mDC040B0253537EA6D79FA2426130B15F56A85100 (void);
extern void CinemachineComponentBase_get_FollowTargetAsGroup_m169DD0B2E175EF2FECEB53B351E4468C28E51561 (void);
extern void CinemachineComponentBase_get_FollowTargetPosition_mEEC29F64B4A9F7212414B27CF89F2E072D76E93B (void);
extern void CinemachineComponentBase_get_FollowTargetRotation_m2D82372276FCD518DA193F0D43736AFA3120EABF (void);
extern void CinemachineComponentBase_get_LookAtTargetAsGroup_m086E4D6F3EE6C1EDC253D411FCFB57CD8DEE2C43 (void);
extern void CinemachineComponentBase_get_LookAtTargetPosition_m53AEF0FF47E364639BD3DAC0C4350E99FAFB77C0 (void);
extern void CinemachineComponentBase_get_LookAtTargetRotation_m1620D9099CCE378D4985A16F8A22496021BB5F07 (void);
extern void CinemachineComponentBase_get_VcamState_m41D53E377042B0E17CF3DBD979A46887E25C79D9 (void);
extern void CinemachineComponentBase_PrePipelineMutateCameraState_mE370BA3BA6DDD2AEFAEF6B2EC71E6C897040165B (void);
extern void CinemachineComponentBase_get_BodyAppliesAfterAim_m3063532CC85496B923F330031AACC8DD54A98F9B (void);
extern void CinemachineComponentBase_OnTransitionFromCamera_m713C04FB25D7B46B404C446F2ACFADC1D7479B7D (void);
extern void CinemachineComponentBase_OnTargetObjectWarped_m5CFEEE60A835CAE290E25D522C3B42D8E7785C38 (void);
extern void CinemachineComponentBase_ForceCameraPosition_m9504C43B0710A0E9EAB145312CAEB8C7FAB07E89 (void);
extern void CinemachineComponentBase_GetMaxDampTime_mCB28FC34A068661AC79C83D827D3DEE6096FF0D7 (void);
extern void CinemachineComponentBase_get_CameraLooksAtTarget_m6942A1CF73E5D8D3AB7D451D91969B963DB4A372 (void);
extern void CinemachineComponentBase__ctor_mEC1EA23D0F82607A6833FFEE570EFB625E91D235 (void);
extern void CinemachineCore_get_CurrentUnscaledTime_m16A7A62D6D30E269D785C1A3BCFD455A3381AE31 (void);
extern void CinemachineCore_SoloGUIColor_m4F7B067B3005757A6BB5ACC0E806C9A2A8BCA437 (void);
extern void CinemachineCore_get_DeltaTime_m6935B26BF92AF69BCFC16FDB313AFD7E631AA5DE (void);
extern void CinemachineCore_get_CurrentTime_m4ACD45A4B881E0B14F287DEB646F1EED338F525C (void);
extern void CinemachineCore_get_CurrentUpdateFrame_m0CC2D4DD6910FCD993783BE5F8567D0A2858E9D1 (void);
extern void CinemachineCore_set_CurrentUpdateFrame_m3BC501FD9EA12E62A4ACF2D5BC63E47F78BC54AB (void);
extern void CinemachineCore_get_VirtualCameraCount_m670A6FE41DD623612C81D3E7C6EF342FC3269DCE (void);
extern void CinemachineCore_GetVirtualCamera_mD033E8701101A28C451762C7F7FDAA68B88D2791 (void);
extern void CinemachineCore_get_SoloCamera_mF61A59E26D24D08494589C8CEACE523A72CD06A1 (void);
extern void CinemachineCore_set_SoloCamera_m97EE35F39E74F49573A209D8DF5D64CA77225D2D (void);
extern void CinemachineCore_IsLive_m507627FEA55003CCD1DC244E7BE3240C0D277733 (void);
extern void CinemachineCore_IsLiveInBlend_m2B2C949DD1B63EC240072C4543960E8F86F58BAF (void);
extern void CinemachineCore_FindPotentialTargetBrain_mCE0C1074FB3C7977265C5670AAE2EBFB31B9C0C1 (void);
extern void CinemachineCore_OnTargetObjectWarped_mA8DA334FF1B7D2BB31C963E3A1E7E9CF93876C39 (void);
extern void CinemachineCore_ResetCameraState_m2143148A0E23473B023324C30FEF0530FFB5578E (void);
extern void CinemachineCore__cctor_m601115E2D1197CF6AA52A4144B9F3459DCDC5876 (void);
extern void AxisInputDelegate__ctor_m464267CCDE013210DD9C8FEE30A1DE12998E1C67 (void);
extern void AxisInputDelegate_Invoke_m7103ACD42DEEE2651E87DFCCC26B2B6DC332AC96 (void);
extern void AxisInputDelegate_BeginInvoke_m61158065FB156DC4AE98ECAFF71420A56E5D50EF (void);
extern void AxisInputDelegate_EndInvoke_m8CD32DC1153BFE7CDA871B41EEEB341D53DF86C5 (void);
extern void GetBlendOverrideDelegate__ctor_mD2DB6B6E201B9FB3980EC718A12CEAD330381F0C (void);
extern void GetBlendOverrideDelegate_Invoke_m9AF271245CD71AD1AAF666816908B00A70B349FA (void);
extern void GetBlendOverrideDelegate_BeginInvoke_mB72C85F9ABA04477E7E8246526D72B33F5080BEC (void);
extern void GetBlendOverrideDelegate_EndInvoke_m5A33D4DFA92034ADD1F0560DF7B6B8477D10B8B0 (void);
extern void GetCustomBlenderDelegate__ctor_mDB68F1A676F155DE6E17DE6BD3E05A189D773CD5 (void);
extern void GetCustomBlenderDelegate_Invoke_mEFBDEA65558C1C0AD43C7A5A2FACBBAAABA28738 (void);
extern void GetCustomBlenderDelegate_BeginInvoke_m3B434A910D0A60EA582F54E7669532D06B57B1FC (void);
extern void GetCustomBlenderDelegate_EndInvoke_m21F9201FF108578C4B5457F57A6083129AF4C13B (void);
extern void CameraEvent__ctor_m0257DA39D426A0442931A0D76EDD10E06F57538C (void);
extern void BrainEvent__ctor_mFBF01AA3AEFB13F13B34F791D61C486D92C7540D (void);
extern void BlendEvent__ctor_mFC48D2E26EF801AA92207EFBA47461F876435622 (void);
extern void CinemachineExtension_get_ComponentOwner_mB19806759148B6E4F01E610B107EC78B28877D65 (void);
extern void CinemachineExtension_Awake_m074204694B512A9E90F2313B134DB557EFE92DD3 (void);
extern void CinemachineExtension_OnDestroy_m889B537AF07ACB70B809AF2F91C23A021955CA14 (void);
extern void CinemachineExtension_OnEnable_m0B2BF8A873C4FA3D3570220E01156892FDE5DB61 (void);
extern void CinemachineExtension_EnsureStarted_m07FFB5E2F75E7325758F7F0691DF289C8D1D65B5 (void);
extern void CinemachineExtension_ConnectToVcam_m14DC2CA35BC3C27890E8E7F231DDAC12C95EEC17 (void);
extern void CinemachineExtension_PrePipelineMutateCameraStateCallback_m583F18A86EA1FC89B229AADCFDABF49B5F649C30 (void);
extern void CinemachineExtension_InvokePostPipelineStageCallback_m84AC82A3131947ED9AD1CF5CE15BD7EBBB18BA78 (void);
extern void CinemachineExtension_PostPipelineStageCallback_m07F9D2DC852DE7CCBC206130E6E963DEFCD5E356 (void);
extern void CinemachineExtension_OnTargetObjectWarped_mDE6D3748839F61950F32C5E9A8FA7EF4EEC89899 (void);
extern void CinemachineExtension_ForceCameraPosition_m58E817B4DA9D513E2480A2E338188BC7FB2C3401 (void);
extern void CinemachineExtension_ForceCameraPosition_m05C8AAE813EC7A2B376D22DE8D672990F72E2DFC (void);
extern void CinemachineExtension_OnTransitionFromCamera_m194A0420605CE8A91DA7FB5D8FEB0E71925DB8BE (void);
extern void CinemachineExtension_GetMaxDampTime_m2ED79E27E33F6A78AF5413B8F3BD98E8CF96A691 (void);
extern void CinemachineExtension__ctor_mCA69966AF11787D974CF8BDC0AD0640C6D522F33 (void);
extern void VcamExtraStateBase__ctor_m533285976B0B646B1684CE8D7DA7B26C8AF3CD40 (void);
extern void InputAxisNamePropertyAttribute__ctor_mA25741723DB3F3629EA763866A1F55D2C2357CC5 (void);
extern void HideFoldoutAttribute__ctor_m911FAFF8C5ACF1EC23B1A19D113E0C6F2F205ABB (void);
extern void HideIfNoComponentAttribute__ctor_m0D4C7632F9054F165A2C55F5616592DBA6D67529 (void);
extern void FoldoutWithEnabledButtonAttribute__ctor_mFDA196D781AA3808C11CBC0C70543ACADCE02B3A (void);
extern void EnabledPropertyAttribute__ctor_m5AF5629FC523CF4D93FDD223675BF12E5F81498B (void);
extern void RangeSliderAttribute__ctor_m29327AD56006418002FBC2F2A3E6FE30A9455A5F (void);
extern void MinMaxRangeSliderAttribute__ctor_m3644DDE8BE66D7B01168AC6CEF4848A1453B5B09 (void);
extern void LensSettingsHideModeOverridePropertyAttribute__ctor_m55F57997292EEA24F873E04FE49DCBFC164CC82C (void);
extern void SensorSizePropertyAttribute__ctor_mEF509899C1DCA32660C83194E733DD37CD5FFDC9 (void);
extern void TagFieldAttribute__ctor_m61C6F53F1F3A568C157AF57291DC5D9671D6FB7D (void);
extern void CinemachineEmbeddedAssetPropertyAttribute__ctor_m91E0DCE9A6C1A9A119CFEF51BED40A8D0B2B31C4 (void);
extern void Vector2AsRangeAttribute__ctor_m2B72D311E1BC62ABD8D787969EC0BD7BE3A4A1D6 (void);
extern void DelayedVectorAttribute__ctor_m9089F799241404BEA797144EC7F35C78810894BC (void);
extern void CameraPipelineAttribute_get_Stage_m4AEA2CCBA98F4AD9841920C816DB8C9A51AD6776 (void);
extern void CameraPipelineAttribute_set_Stage_mB676D6070776D22A38CDC5BB4459433EC7CBCF5E (void);
extern void CameraPipelineAttribute__ctor_m280FFFB15246372A2FCF057BE91FD0C4D64C3012 (void);
extern void RequiredTargetAttribute_get_RequiredTarget_mD1A2E2A9C3974853DEC8D46342381A9FA8E508FF (void);
extern void RequiredTargetAttribute_set_RequiredTarget_m6618F9EEB3ED26EE86ABE0143F536784BFDDC93F (void);
extern void RequiredTargetAttribute__ctor_m8E96FE95D2B1F037757077B97559335AED37FA67 (void);
extern void ChildCameraPropertyAttribute__ctor_m45806417956F3B04999593459669AAB981E58147 (void);
extern void EmbeddedBlenderSettingsPropertyAttribute__ctor_mE28735514D69AD992F020A0872E56C3980C636B2 (void);
extern void CinemachineVirtualCameraBase_get_IsDprecated_m613885D06614FDC3439D6E3924D50B72E562B1D8 (void);
extern void CinemachineVirtualCameraBase_PerformLegacyUpgrade_m8085B2C58C5E3B8F6D6FAE69FC6C46ED81977D62 (void);
extern void CinemachineVirtualCameraBase_GetMaxDampTime_mD9CD7D72CDA2A8E4EF4CBAF378D69D9B90DEE48B (void);
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_mC29470298D4619AF113007CED7665E4383EC6C74 (void);
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m0A2FB823A132EB8E051039559B27BC9B48A0FB9B (void);
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m8B089604D8752F2559D41A428A5899A52E22067F (void);
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m0C61AD23C6B627F433F89CCC77C9516343D9D05E (void);
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m94A23EBBF40B410B5B7E62BC948D5200786F2235 (void);
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m5E89DE7F81D8633FB26ABF543D35E43829D8C94D (void);
extern void CinemachineVirtualCameraBase_AddExtension_m3BD9F04749A64A8927DEF686C4E2BC4F7F2FCE3B (void);
extern void CinemachineVirtualCameraBase_RemoveExtension_m8E64C77F8DA1E9C0DE78C1F34A767DC4AFAA74E2 (void);
extern void CinemachineVirtualCameraBase_get_Extensions_mFDFF9C31CD292F5D3B52E5B8790C23738964DA18 (void);
extern void CinemachineVirtualCameraBase_set_Extensions_m32A3850B56674A0792522B19B325B82D885E10B6 (void);
extern void CinemachineVirtualCameraBase_InvokePostPipelineStageCallback_m308C5EA8BD881ACA6DE0AED007D4E867E568E2A6 (void);
extern void CinemachineVirtualCameraBase_InvokePrePipelineMutateCameraStateCallback_m3B8747BAEC1265100B32B1BED7B43281B9FD03DA (void);
extern void CinemachineVirtualCameraBase_InvokeOnTransitionInExtensions_mF738122F63FAA369BB6D315FF52F2C131327E04E (void);
extern void CinemachineVirtualCameraBase_get_Name_m871E53110E7A7CEAA9B3FB4C7DA2FCA4CEBA8EF4 (void);
extern void CinemachineVirtualCameraBase_get_Description_mCAE40209194E3B37A278C90B631903EC485843CA (void);
extern void CinemachineVirtualCameraBase_get_IsValid_m07F50E4AA48AD8725DC7946E61AD43CE0F92285E (void);
extern void CinemachineVirtualCameraBase_get_ParentCamera_m74F9284C7ADEE288B9CA32B868AC58C5A0AD79C5 (void);
extern void CinemachineVirtualCameraBase_get_PreviousStateIsValid_mEC4F08FCD1B68E9BDA87E8A6C644AF1810E7CCE1 (void);
extern void CinemachineVirtualCameraBase_set_PreviousStateIsValid_m3B16C7DF7E6204ED00717C698D82FAD764AFEBB6 (void);
extern void CinemachineVirtualCameraBase_UpdateCameraState_m15B09CAA0B7B7C9C4BF21C3CB7EE8C381C17A2B2 (void);
extern void CinemachineVirtualCameraBase_OnCameraActivated_m213854437800E918326AA65A83C6F954FC5020CE (void);
extern void CinemachineVirtualCameraBase_OnTransitionFromCamera_m967F7473B63B024076F068DD0F6121DF67A2D771 (void);
extern void CinemachineVirtualCameraBase_EnsureStarted_mC27538EB83BF0880C70C5DB02DBA1AD9B6026950 (void);
extern void CinemachineVirtualCameraBase_OnTransformParentChanged_m0BC7E299C926D756802D3E5B9AE7430C72EA3A78 (void);
extern void CinemachineVirtualCameraBase_OnDestroy_mDF22FD418B1C6F6E396DE2ED91A41EB9ED5B7A8D (void);
extern void CinemachineVirtualCameraBase_Start_m77423323B924DDC4BA53429212FB9EBB6E63D941 (void);
extern void CinemachineVirtualCameraBase_OnEnable_mF40447A683589D185774F668A336E4E4733483FB (void);
extern void CinemachineVirtualCameraBase_OnDisable_m3CCBEB61BC31AE740D17F1970151A78A1440FA69 (void);
extern void CinemachineVirtualCameraBase_Update_mE773DD68C09FAE4B3D8E7417B4041BA4BC4C3362 (void);
extern void CinemachineVirtualCameraBase_UpdateStatusAsChild_m79D5B6F938E972CB66CC5921725D0B533B349C64 (void);
extern void CinemachineVirtualCameraBase_ResolveLookAt_m09C36D36693FB3C8F8B977250C0699D27F90DC03 (void);
extern void CinemachineVirtualCameraBase_ResolveFollow_mC6034B56B6E59E7823CD6282487600D329D384CB (void);
extern void CinemachineVirtualCameraBase_UpdateVcamPoolStatus_m935D273B61760DD23EFFE49B4D98F1027CC46EF2 (void);
extern void CinemachineVirtualCameraBase_MoveToTopOfPrioritySubqueue_mE32B50D0760F29F04290A0C152C4A27B335542F5 (void);
extern void CinemachineVirtualCameraBase_Prioritize_m602C5C07F6D94E69AAA58C72D8028EDB862BCEF1 (void);
extern void CinemachineVirtualCameraBase_OnTargetObjectWarped_m4B7CE07D5DCEDF2CF1F040E702FA8D9BDCBA4B82 (void);
extern void CinemachineVirtualCameraBase_OnTargetObjectWarped_mD249AF7528240E89B96B4A8C11659984356A1230 (void);
extern void CinemachineVirtualCameraBase_ForceCameraPosition_m957D2484B865D7747176068C5F413C3F9A3D9C45 (void);
extern void CinemachineVirtualCameraBase_ForceCameraPosition_mA1E5EFDCD4CDB9F37B529EB133EF35412990ACF6 (void);
extern void CinemachineVirtualCameraBase_PullStateFromVirtualCamera_m0FA7B4F6F570306E5B84B96791146EA02FF40AE3 (void);
extern void CinemachineVirtualCameraBase_InvalidateCachedTargets_m876BC9A9C9A858AC645911683687A29B49F7E94E (void);
extern void CinemachineVirtualCameraBase_get_FollowTargetChanged_mD6319FA298724A1AC19C42E0E9143C2848B276D2 (void);
extern void CinemachineVirtualCameraBase_set_FollowTargetChanged_m036680814C4B5C7E26382D133B8C5939E0FE10D5 (void);
extern void CinemachineVirtualCameraBase_get_LookAtTargetChanged_m82BD81D2F6C0FB4202514520B6B4A521118301FC (void);
extern void CinemachineVirtualCameraBase_set_LookAtTargetChanged_mC2260BA382737B7F763D52288A2981CC24ABE5D0 (void);
extern void CinemachineVirtualCameraBase_UpdateTargetCache_mE9C70E351F4FC628796B0C7C81E58CEBF7B4D162 (void);
extern void CinemachineVirtualCameraBase_get_FollowTargetAsGroup_m00A6A7E9368F3FB19EBE03568559CB0495C741BF (void);
extern void CinemachineVirtualCameraBase_get_FollowTargetAsVcam_m9A9F1E6530607CB12ED58E1169D708314EE04048 (void);
extern void CinemachineVirtualCameraBase_get_LookAtTargetAsGroup_m5DC6FDBBEC8FABB71620695BF1495A682357701C (void);
extern void CinemachineVirtualCameraBase_get_LookAtTargetAsVcam_mBDFB42FA8343B7343AFDA011228BF82263C365C2 (void);
extern void CinemachineVirtualCameraBase_GetCinemachineComponent_m4008999DDB5B37F4B27F919A381120CC9F657305 (void);
extern void CinemachineVirtualCameraBase_get_IsLive_mF74581E7EAD9D325DAA5830B8BE203D6726F7FF0 (void);
extern void CinemachineVirtualCameraBase_IsParticipatingInBlend_m4628B6304130AF5AEDE432A9C578892EC6D20A24 (void);
extern void CinemachineVirtualCameraBase_CancelDamping_m9266B95194ED49FC48D8DAF3990C2D18E778C102 (void);
extern void CinemachineVirtualCameraBase__ctor_m54D6E829804A391FDCC9B80F99EBF79B081B32DF (void);
extern void ConfinerOven__ctor_m8E4DFA5A376CF2044613FF387B3CF8587090D215 (void);
extern void ConfinerOven_GetBakedSolution_m8E157F43C4675432384387A93F5E7F91D4C96C5C (void);
extern void ConfinerOven_get_State_m9F261080E8975C6B12A13E57F8C511F9C78B62CC (void);
extern void ConfinerOven_set_State_mF2C4BCD19A0D0BE6EDEB120536CFAF44A85BAD04 (void);
extern void ConfinerOven_Initialize_mEE7234B6AEA4DDE99864446B15FA825C9B9E8DDE (void);
extern void ConfinerOven_BakeConfiner_mFD48829388B5B12A9A2DA3E589A85B812E2E4745 (void);
extern void ConfinerOven_U3CInitializeU3Eg__GetPolygonBoundingBoxU7C24_0_m82682694495C1BA133EAD6111F6AB8E65D695167 (void);
extern void ConfinerOven_U3CInitializeU3Eg__MidPointOfIntRectU7C24_1_m3130A59D289EEB7D421CEDC30BF348DFE1BD8987 (void);
extern void ConfinerOven_U3CBakeConfinerU3Eg__ComputeSkeletonU7C25_0_m321AB5A37B6FB1DE2A3B0FBC2F39C27C809083D1 (void);
extern void FloatToIntScaler_FloatToInt_m2C8F87AC2231245186FF3E3DCACEBBB726ABC617 (void);
extern void FloatToIntScaler_IntToFloat_mA505F9EBBC60E66115587256DE38F09C87FA14E8 (void);
extern void FloatToIntScaler_get_ClipperEpsilon_mD87546D732EAE3830C6E78F06CB66A4D300E3583 (void);
extern void FloatToIntScaler__ctor_m8088E3C77768B1CE534A7FDDFA5B912F4BBBCDB1 (void);
extern void BakedSolution__ctor_mF821FC173A634C663606ADD16F19E962ACC26BFB (void);
extern void BakedSolution_IsValid_m262984864D41DD3745E300C7C1D4E9ED6E573122 (void);
extern void BakedSolution_ConfinePoint_mB54B2B936BA204D2955AB7A50EC255747CF5A0D7 (void);
extern void BakedSolution_FindIntersection_m4DDACC615C5FD803B8A327DB5C499602E1657C02 (void);
extern void BakedSolution_U3CConfinePointU3Eg__IntPointLerpU7C9_0_m9EB77C25C2DBD2C3964460D7863ED496A3CBF90B (void);
extern void BakedSolution_U3CConfinePointU3Eg__IsInsideOriginalU7C9_1_m075371B86E76ADA99BD437AC809BEBC11D052F29 (void);
extern void BakedSolution_U3CConfinePointU3Eg__ClosestPointOnSegmentU7C9_2_mB3960D474D34A35070E9567BCA209B78B898F1A1 (void);
extern void BakedSolution_U3CConfinePointU3Eg__DoesIntersectOriginalU7C9_3_mA5BFA2AA066B746FF7A914E5AD1C3D6C1AC227D3 (void);
extern void BakedSolution_U3CFindIntersectionU3Eg__IntPointDiffSqrMagnitudeU7C10_0_m92D483C95B9A39994A7A4A6B766D60270E14DC84 (void);
extern void AspectStretcher_get_Aspect_m9E6B3A0AB70146498E1BAD185698573EC606F687 (void);
extern void AspectStretcher__ctor_mFF295333F630563CEBB2E78810A0821F15AF4CD3 (void);
extern void AspectStretcher_Stretch_m86BC52CD00CE835B7A1E8881877534E761E1A86B (void);
extern void AspectStretcher_Unstretch_m95AFEFCD91BAE10A23E6640BEA785C0082A327E0 (void);
extern void PolygonSolution_StateChanged_mC389F789218AE923B31D6C0AA436436BCDD7CBCF (void);
extern void PolygonSolution_get_IsNull_m467E000AEC921A9EB76D814C0611F2BC9FB5AA15 (void);
extern void GaussianWindow1D_Vector3__ctor_m3EE3C181115DE9B21E26DC966C321F95D588971A (void);
extern void GaussianWindow1D_Vector3_Compute_m304EFF4256F3858789C38D0BAE6E179E749876F6 (void);
extern void GaussianWindow1D_Quaternion__ctor_mE8D482808CEE826DEB5C00892D564B6531736EF2 (void);
extern void GaussianWindow1D_Quaternion_Compute_m667A5E4A77FD4106BA958896296EEB1AFB8BF505 (void);
extern void GaussianWindow1D_CameraRotation__ctor_m9E7724E984A051E3B006D29E736F7BDCE9DA3AE8 (void);
extern void GaussianWindow1D_CameraRotation_Compute_m616882A244CCC5A8DA619DEDF0E80248E0ED27EE (void);
extern void ActivationEvent__ctor_m3957EA43143C3F6745A6E55A7A3A33D6DE8E933D (void);
extern void AxisGetter__ctor_m034F15020098173B81CBCBE0426E38501DA6DAD0 (void);
extern void AxisGetter_Invoke_m97C87897F65D4B47FB28D5262C0CFB450C9D4647 (void);
extern void AxisGetter_BeginInvoke_mA522E78E9D646589CBD645037544B0EB0910C172 (void);
extern void AxisGetter_EndInvoke_mEF4ECB882B8B8BF82E00230E23B0C62A3BC354E0 (void);
extern void InputAxis_ClampValue_m9B998276127396874FBC7D934D22217A2B1F7983 (void);
extern void InputAxis_GetNormalizedValue_m6A3EED70C1988D787B19B725D8071993F7EFD411 (void);
extern void InputAxis_GetClampedValue_m8828F8C144F025EF8203C94F9A00FE2C26656013 (void);
extern void InputAxis_Validate_mD5883DE4C3F177CB83F4554222480DEBD1B160A2 (void);
extern void InputAxis_Reset_mFBC7BAF970104C4927D5F73C62BE55F889071DB9 (void);
extern void InputAxis_get_DefaultMomentary_m26C9722370DAF0010279AA8ACCAF815F27061BDF (void);
extern void InputAxis_TrackValueChange_mCB6E35D0B19AEB110BC712F4B1075B13350FFCBF (void);
extern void InputAxis_SetValueAndLastValue_m0587AE4B73578F3144756FDCDA7DAE6DB7233067 (void);
extern void InputAxis_UpdateRecentering_mA17553DCBBEBA694E54A12088A30EDE575DF5800 (void);
extern void InputAxis_UpdateRecentering_m44844384DFA8B93AB3847B51B23B4F61223FD10F (void);
extern void InputAxis_TriggerRecentering_mDC766F564C495683B8F136D7A6E02C0360F710FA (void);
extern void InputAxis_CancelRecentering_m34D92FBFC0234058E6A7F68F10ABED0116F86558 (void);
extern void RecenteringSettings_get_Default_m1DB630F62C13DA84B9675BB330F45830A6F3E0DD (void);
extern void RecenteringSettings_Validate_m6EEB2F6774899BDC39A2246C4D74AFEDC16D404D (void);
extern void RecenteringState_get_CurrentTime_mCF4B043C5A00951F3BAD6E1070657F481B6952FC (void);
extern void DefaultInputAxisDriver_Validate_m29DC58F7A6E8B2BD7C54C3FFDA88D602E7E59135 (void);
extern void DefaultInputAxisDriver_get_Default_m5AD45D8E6860D5DBEC385009819B97FC64D59394 (void);
extern void DefaultInputAxisDriver_ProcessInput_m942B6A2B5C55C8936D74BF2D62E872A1DC735A03 (void);
extern void DefaultInputAxisDriver_Reset_m258D1F1BBAF2400C7449AEFD16C50BD5C536859D (void);
extern void InputAxisControllerManagerAttribute__ctor_mCF3A2BED03E1E31155D175DFA2C5DC128D228708 (void);
extern void LensSettings_get_Orthographic_m2AF510A849B57A9485FB4000835234532D83A2E9 (void);
extern void LensSettings_get_IsPhysicalCamera_m2700AD42D56E90B57D18438C765F929E09CC6D10 (void);
extern void LensSettings_get_Aspect_m24F38278056BAB1241945C9E75983B9A9FBDA177 (void);
extern void LensSettings_get_Default_m81FA4439AD6CEAE550FFC9C9B95B7A1E391AF78E (void);
extern void LensSettings_FromCamera_m96A14A2FA3AF5787455A2E7034CFBF15B4F7BBAD (void);
extern void LensSettings_PullInheritedPropertiesFromCamera_mAC72C4DE1DA21BF3CE38AA8C318312D3EB8CA3F5 (void);
extern void LensSettings_CopyCameraMode_m687969BF551F739E019BE706A6827F5E43D4BB76 (void);
extern void LensSettings_Lerp_mB261958D7635251CC1350A7C8F37DBBDDC125BF6 (void);
extern void LensSettings_Lerp_m17E59426452E33421D279C13D83BE1D9D64B37BD (void);
extern void LensSettings_Validate_mFE36866AB5B16F9B0405E410CACB999F29519265 (void);
extern void LensSettings_AreEqual_m3354C383423AF6EBE6E8AB2A9CB99F167F45F31A (void);
extern void NoiseSettings_GetCombinedFilterResults_m36851B75C2834CDED986361FEAE884B0F07818D7 (void);
extern void NoiseSettings_get_SignalDuration_m604F891E67C1AB1DA9B3C7FF6AF926B2FA52615F (void);
extern void NoiseSettings_GetSignal_m81854E60FED3D1F5A75EC9CA396A053D262D3D8A (void);
extern void NoiseSettings__ctor_m2AF765DD9D96EFCF99246FA051E7FBD5889419D4 (void);
extern void NoiseParams_GetValueAt_m1B5D915FA8782DAD5230854FA7FFDA503A34D021 (void);
extern void TransformNoiseParams_GetValueAt_m757A109B904184C5AAE5BD2B466DC80026474FF6 (void);
extern void PositionPredictor_get_IsEmpty_m234E8623FD6865187578B480D0FA79B3E0F8AB05 (void);
extern void PositionPredictor_get_CurrentPosition_m0736A2E1D68141B5BEC2BD9078B2624EB0005018 (void);
extern void PositionPredictor_ApplyTransformDelta_mC33429167317BB4EE95AFF4EB66E341958463DA4 (void);
extern void PositionPredictor_ApplyRotationDelta_m0EDD7BF0D0321D32668DD102887CF8F90CE6CA9D (void);
extern void PositionPredictor_Reset_m1C332992802B45BCD6677A1C04B54F9FE47962D8 (void);
extern void PositionPredictor_AddPosition_m521CEDF019A2B4289EDBDF17B7478524B6CBB67F (void);
extern void PositionPredictor_PredictPositionDelta_m58018C09253901A9A4D1A39325684D1EB82CB882 (void);
extern void Damper_DecayConstant_mECE09E43773ACC93199D7D3409F52435E5AC42B5 (void);
extern void Damper_DecayedRemainder_mEF68D05AF9EBD9B88C177D6A201B92CD4E929672 (void);
extern void Damper_Damp_m8B702D3084078F17BEE09AAC063D6F4C2AD1C176 (void);
extern void Damper_Damp_mEC5B6A02F900E7E74103A42D1BECEBFC917285A3 (void);
extern void Damper_Damp_m07CBB05E6E8B73B96260FAE384C12B46197D090A (void);
extern void Damper_StandardDamp_mCA3A60AA8AA9FEE0F54BC777803AA9000A9C8DC1 (void);
extern void Damper_StableDamp_m044117749C70B505BE64CA5795C088B8AF087BCF (void);
extern void AverageFrameRateTracker_get_FPS_m3E937ABDD808857917AE4CBB2EF54864A9EF5015 (void);
extern void AverageFrameRateTracker_set_FPS_m955C66FEB47D5160435325A50F09BF777BC3C019 (void);
extern void AverageFrameRateTracker_get_DampTimeScale_m73BE3180C4D680E898689656044927E7FC98A33B (void);
extern void AverageFrameRateTracker_set_DampTimeScale_m7BDA8485592B7A10468008DD109315D7B4F78084 (void);
extern void AverageFrameRateTracker_Initialize_mF98B5D72BB69E30D6B407100A46B54168D7350F2 (void);
extern void AverageFrameRateTracker_OnSceneLoaded_m19A47294EE213FC24B4A1F6B481168ED2A9AC9D9 (void);
extern void AverageFrameRateTracker_Reset_m26A9D5BF8F446765E7AD662CC8732AF4354C811F (void);
extern void AverageFrameRateTracker_Append_mAB6A06DF0ADA879BA57B48FB0A42C669B85D2668 (void);
extern void AverageFrameRateTracker_SetDampTimeScale_m3DEA9974A8037A4F35B9124BE6E25B4639C9DD84 (void);
extern void AverageFrameRateTracker__cctor_m1713DA0E413D248984A39A69B30549E5CC3957CB (void);
extern void PrioritySettings_get_Value_mD2377C4F969CCA1E306F3A188F78DA1C31CB5F3C (void);
extern void PrioritySettings_set_Value_m6C9D23382F5F5AE929899110938132552027B440 (void);
extern void PrioritySettings_op_Implicit_m365AAF1AFE4BF159BABF41A0CE6B46B2B72F5FDB (void);
extern void PrioritySettings_op_Implicit_m70FEAAC6052DEB1F4FBF49DF618F99640E49B6E3 (void);
extern void RuntimeUtility_DestroyObject_mA0B0FC6E7BBA370D6E580ED130E51E03380AE3C5 (void);
extern void RuntimeUtility_IsPrefab_m6DC9033458A8E47262F121AF7680C633C3F91B48 (void);
extern void RuntimeUtility_RaycastIgnoreTag_mB9C9C3736B95648C2FFD5C19588BFACAD5730A59 (void);
extern void RuntimeUtility_SphereCastIgnoreTag_m276D5530B79F1752104575E1EC4A5EF7BF8E346E (void);
extern void RuntimeUtility_GetScratchCollider_mC8016F3A33DF0E369622045CE248807109F16D80 (void);
extern void RuntimeUtility_DestroyScratchCollider_mD1BFC728D834B9FEE35DF2405E554BF2B7576C2A (void);
extern void RuntimeUtility_NormalizeCurve_mD9A17D430CC9591AAAD8406BA128F16BE6AD109E (void);
extern void RuntimeUtility__cctor_m7EFCCFDD999C9B16DDA4498ED6CCFBE9854A5B75 (void);
extern void ScreenComposerSettings_Validate_mC2F21833234FFE27191159ADEE6A2CC6DAC38CCE (void);
extern void ScreenComposerSettings_get_EffectiveDeadZoneSize_m67B0C04C458112121CFE381069227FF96470EA61 (void);
extern void ScreenComposerSettings_get_EffectiveHardLimitSize_mC4A9B50A42FD6446505DCC222EEEFCD688B8CE29 (void);
extern void ScreenComposerSettings_get_DeadZoneRect_m411B6054F72D2D6CD33562F3970EF01E7A4D25E8 (void);
extern void ScreenComposerSettings_set_DeadZoneRect_mB8DAA838A8727F3D9F52F92C8E37E0B2EAAB1991 (void);
extern void ScreenComposerSettings_get_HardLimitsRect_mAAF8C4A2F753C614FEF2107191DFE3112C665743 (void);
extern void ScreenComposerSettings_set_HardLimitsRect_m537078AB83AFB3298BDFEE946951D8628974E42C (void);
extern void ScreenComposerSettings_Lerp_m43E5EDDEA7F6AFA0C71BA8565720E9604C709F20 (void);
extern void ScreenComposerSettings_Approximately_m4C15F068C20E333005A6B0EC55C2FA59CDC4D4FF (void);
extern void ScreenComposerSettings_get_Default_m553CB9FD1CE1817FE53DB24DF207EBE4AA299D08 (void);
extern void SignalSourceAsset__ctor_mB4028F5F34A0DFFA7645D3A9E4135D46AC14A5F9 (void);
extern void FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Validate_m74C78176F5772767ED80F04F3E247404A4C95040 (void);
extern void FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Reset_mA6CDBC5DA8F6FD983BA2B8689D8590253533959B (void);
extern void FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_get_RequiresTrackingTarget_m5140437E08AAB7A0D171A2C04F91353E377EED5A (void);
extern void FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_GetSplinePosition_mDAA9D3C6FA24CA31D8DCBED5D591B120E9336C8C (void);
extern void FixedSpeed__ctor_mA3B4AAC6F424ABA14E28F6A8E1980804666194AD (void);
extern void NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Validate_mDA5CEDE38EDA13DCAF878AE1DFB82E046DA1A56D (void);
extern void NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Reset_mE3BA74818517653A88BA058FF5F2480B10D1A79C (void);
extern void NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_get_RequiresTrackingTarget_m1B57D3E66084390DCBB1271D022EECF7899D8FB3 (void);
extern void NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_GetSplinePosition_m98A3A8C1697C1501669ABC080E162FF202E03DF1 (void);
extern void NearestPointToTarget__ctor_m5416A1E0BA75E8BC555716CD60641588419BE9C5 (void);
extern void SplineContainerExtensions_IsValid_mD729728516744EA63643D0C3A80DA513D4B80139 (void);
extern void SplineContainerExtensions_LocalEvaluateSplineWithRoll_m459BA6DDB25FABDC356F1799A0BB5D8F88AACD4D (void);
extern void SplineContainerExtensions_EvaluateSplineWithRoll_m5B03FC3538975CBD17D3C0410A14C3C804B6D6DD (void);
extern void SplineContainerExtensions_EvaluateSplinePosition_m0BB77C79547F3F45159C6642910744956663309D (void);
extern void SplineContainerExtensions_GetMaxPosition_mA82D00477FFB951A386CBCAF23334B3ADDC9146C (void);
extern void SplineContainerExtensions_StandardizePosition_m2758EAAE06875D8E2CF9DA156A12027945F88A11 (void);
extern void SplineContainerExtensions_U3CLocalEvaluateSplineWithRollU3Eg__RollAroundForwardU7C1_0_m9BF64D75EC98C4763BF0F5BB53A2B140514281BC (void);
extern void SplineHelpers_Bezier3_m7F87EF920A07BB0720AC2EA2E44141D21062C5DC (void);
extern void SplineHelpers_BezierTangent3_mB410323048107D35E51789592EAB662D7A847E80 (void);
extern void SplineHelpers_BezierTangentWeights3_mA00093AFE65074A33EAEE1E0DD85E92CC8F6C2CB (void);
extern void SplineHelpers_Bezier1_mE6E32E17731E3460E8D896F419E8D1C7550CD1C8 (void);
extern void SplineHelpers_BezierTangent1_m9C8CD3661CF2D2C19D03D7E5EA947721C9C12FF5 (void);
extern void SplineHelpers_ComputeSmoothControlPoints_mFCDECAA84A1F671B82A5AC8EC0AF5C187EC73630 (void);
extern void SplineHelpers_ComputeSmoothControlPointsLooped_m44F3F79DF0F25AAF3593E1BDD7B83D7C1BFEEF94 (void);
extern void SplineHelpers_ComputeSmoothControlPoints_m31F4B7535C0879B100AE10DA4A94EF5A80AC4366 (void);
extern void SplineHelpers_ComputeSmoothControlPointsLooped_m2A824F8E1A547B9775D57D7B3C0354350B48C6A3 (void);
extern void SplineSettings_ChangeUnitPreservePosition_m5BA3FA8B14F1E0F650D9C8605BC4E38D8EC62956 (void);
extern void SplineSettings_GetCachedSpline_m413EFDEC0D7E7541E17A2D493371FA599D8AFFA1 (void);
extern void SplineSettings_InvalidateCache_m99A3ADEEA8ABB0949C26A23C867A91DCC2F3BB88 (void);
extern void CachedScaledSpline__ctor_mA369524A88F256C2FB0D34BA35FFF509DA71118F (void);
extern void CachedScaledSpline_Dispose_m6429258694491EECEEF5494B4D901A567472943B (void);
extern void CachedScaledSpline_IsCrudelyValid_m4DFADCA4BF37C994A63D212550F244D5C91643AA (void);
extern void CachedScaledSpline_KnotsAreValid_m3B0B6FD6F04827995F9880FC8F11EC41DA74A8BC (void);
extern void CachedScaledSpline_get_Item_m6F910A22DAE4709DD615B16665D66E0BE63EF3C3 (void);
extern void CachedScaledSpline_get_Closed_m5D5CDD69F92593D7B75B6A893C5DB0976639E433 (void);
extern void CachedScaledSpline_get_Count_m1CC3161473832424C73EF28BAF2EAC303A027260 (void);
extern void CachedScaledSpline_GetCurve_m1AEEA0E3D177D87B71DCA29F8597ECF149CEA66A (void);
extern void CachedScaledSpline_GetCurveInterpolation_mB29A42613DE9D6C00ED17F9014634676DD566F3A (void);
extern void CachedScaledSpline_GetCurveLength_m13FB517E3656A50364C4A88A74D6129F860203F9 (void);
extern void CachedScaledSpline_GetCurveUpVector_m2C9CF45C5C6CE5B2E505A10A5CA0D3F01BD086D1 (void);
extern void CachedScaledSpline_GetEnumerator_m7E754D3593DE9207E0E54A7646025F763ADC7A74 (void);
extern void CachedScaledSpline_GetLength_m17FBE003936998B1B641822316C35BD361416A0A (void);
extern void CachedScaledSpline_System_Collections_IEnumerable_GetEnumerator_m814F488A676EF28CBD29CA515C55F83A60081781 (void);
extern void TargetPositionCache_get_CacheMode_m045C1D7C48006FE8F2167125AE517E7520BAD987 (void);
extern void TargetPositionCache_set_CacheMode_mDC64D9C79153B9B3D7591A4AE9149F4B96CCD41F (void);
extern void TargetPositionCache_get_IsRecording_mB16D76FE556847B99334AEE28A2D6361C376D5CC (void);
extern void TargetPositionCache_get_CurrentPlaybackTimeValid_mEBC82C9F30FEC88B9EE726ADD2EEC0414FFE9CC1 (void);
extern void TargetPositionCache_get_IsEmpty_mB02355A787E17C773C225FCE0C446C3C67558AFF (void);
extern void TargetPositionCache_get_CacheTimeRange_m44600940141B87875AFC6758676C553305FEDDB3 (void);
extern void TargetPositionCache_get_HasCurrentTime_m3DC0F1E1E64AA355107519490672AD8DDFD15DC9 (void);
extern void TargetPositionCache_ClearCache_mEAC1BADA50EB4F8C255577D677854FEF5007398A (void);
extern void TargetPositionCache_CreatePlaybackCurves_m234A8A18A44B60771384489180DB25D0925E0FB8 (void);
extern void TargetPositionCache_GetTargetPosition_m56E56AE498B6762B222110307704C07651D39364 (void);
extern void TargetPositionCache_GetTargetRotation_m34E682B381F4E781734BB027AE7C912EF2F743C1 (void);
extern void TargetPositionCache__ctor_m030DA1B7921F25550F415CEB1053E0BB9D734135 (void);
extern void CacheCurve_get_Count_mC1ABAF4434BF406623444D7EDD8CC37D7F0D0011 (void);
extern void CacheCurve__ctor_m188804DE79B8AD04C9A40DAC5525F650C8D1D5BE (void);
extern void CacheCurve_Add_m6CD994E3DCFE5D0DB41B294C445CE63CEE47D82E (void);
extern void CacheCurve_AddUntil_m68BFB175DBB6BD27C30FA4CADB522E101750DA42 (void);
extern void CacheCurve_Evaluate_m278E0F3E2E25A3B42D9A5E6F3357AECE4BC6AAD7 (void);
extern void Item_Lerp_m823202A1C7D0027FDB52BB980BDE7C927D15BC1D (void);
extern void Item_get_Empty_mDB4BE921C2BB309083E40F1FCA190AA7FBF06284 (void);
extern void CacheEntry_AddRawItem_mD06A71DA3CC675F4A139D0DA1A0FF47113D486B6 (void);
extern void CacheEntry_CreateCurves_m56B64C6585A01F270335F7EE178682D4DF5D684C (void);
extern void CacheEntry__ctor_mA5627E1A79382A0606EDE21F3FD85254F0AE2D54 (void);
extern void TimeRange_get_IsEmpty_m856A3BAB8551D7AE7EBECB42CE4FEB20F0EEFF24 (void);
extern void TimeRange_Contains_m29397CAB8AB82D9C8C1510F37E5F86648B25E85F (void);
extern void TimeRange_get_Empty_mFCD38C47F562F5FE409AE83A4F6857386127B2F5 (void);
extern void TimeRange_Include_mBFF0A417B53F24B32B5048D99E99EE9FD32BB103 (void);
extern void UnityVectorExtensions_IsNaN_m969CADE1F5A725344E04DD5AC802D31DB1B8D79B (void);
extern void UnityVectorExtensions_IsNaN_mED320A7D2D91E180476D34DA16B49F46F54F1FE4 (void);
extern void UnityVectorExtensions_ClosestPointOnSegment_mA34DB043BC4C8A159E4B83C34DA4170EF1646090 (void);
extern void UnityVectorExtensions_ClosestPointOnSegment_m45E515427CD8561DEED42A4DE9675D86EB0E9195 (void);
extern void UnityVectorExtensions_ProjectOntoPlane_m77B38EF796EB3B782B0776EEEA193E623A445ECE (void);
extern void UnityVectorExtensions_SquareNormalize_m3910DC5715713B7331CFF9BCAF2C0464B6D80073 (void);
extern void UnityVectorExtensions_FindIntersection_m9830D118D76BDE138959B24D51A946D405C00289 (void);
extern void UnityVectorExtensions_Cross_mEB2DDD692E0953048DFD57C8A48A020CA5876F61 (void);
extern void UnityVectorExtensions_Abs_m84660BB13F23722B12A558CD56C81E7F9291BD34 (void);
extern void UnityVectorExtensions_Abs_m953FE3119ADCE56D51AA2E26B1602D66D233955A (void);
extern void UnityVectorExtensions_IsUniform_mEAAB9C36B34502A0627A75C0C659356019A8FEC6 (void);
extern void UnityVectorExtensions_IsUniform_m40B08DDF0E9EE9DF582EC84E82906969556441BE (void);
extern void UnityVectorExtensions_AlmostZero_m3DEF48A1216986914EDC18611380D48098B680E6 (void);
extern void UnityVectorExtensions_ConservativeSetPositionAndRotation_mE464415AAC8D77CD5063AEC67872E9B09300AEBD (void);
extern void UnityVectorExtensions_Angle_m0AE04C65BFE17AECC8004BFA8111C5EAD9879FE4 (void);
extern void UnityVectorExtensions_SignedAngle_m89B35E7B4423DDAD39D7B0B8870B284F8BF68B6A (void);
extern void UnityVectorExtensions_SafeFromToRotation_m2224E4AFA4D4690FA9A64F04CB2FCE67305379FE (void);
extern void UnityVectorExtensions_SlerpWithReferenceUp_mD396806ED54804F5F98F44AEFB49F7FC5C541358 (void);
extern void UnityVectorExtensions_NormalizeAngle_m74253FF7FC9B096D66A73BDC5384E67AFF75F42B (void);
extern void UnityQuaternionExtensions_SlerpWithReferenceUp_mA259B0FBF1578FE283D1EABB1A955C3026699054 (void);
extern void UnityQuaternionExtensions_GetCameraRotationToTarget_mAC945EFD2ADC7E99FE351F20933BCD77B84F87CE (void);
extern void UnityQuaternionExtensions_ApplyCameraRotation_m9B52A2FC0546F44A767DB8F57533FB5E617F0E4A (void);
extern void UnityRectExtensions_Inflated_m7EAF6244D1A316CBE7D88E71E83104116F9707A9 (void);
extern void UpdateTracker_InitializeModule_m319C8F8C3961A8C8AA1019EDD79B69F8448582C7 (void);
extern void UpdateTracker_UpdateTargets_m7F6B2C530C2A66D3849DE425B9955FA488F823F3 (void);
extern void UpdateTracker_GetPreferredUpdate_mB656C104D1BA1843C031F997F18BBCC7315C5332 (void);
extern void UpdateTracker_OnUpdate_m3839C252A196C7D01FE504B3A2144BD372A968F9 (void);
extern void UpdateTracker_ForgetContext_mA8B5E5E8B8A72D0E6AAC0EE20CAABF0EBE2F43E8 (void);
extern void UpdateTracker__ctor_mF8CDFBAC6B41A61ED8EE64570494676A03F1F295 (void);
extern void UpdateTracker__cctor_mE94BDD134865DCD7BADD25A78274F03C3E46859F (void);
extern void UpdateStatus_get_PreferredUpdate_m0139ED8CDB6CF356E9BA0F69E3C0D54347FA33B1 (void);
extern void UpdateStatus_set_PreferredUpdate_mF55CC6B2BE19C4C8CFF1889A534353F691E47A43 (void);
extern void UpdateStatus__ctor_m2E41EA71CE4C03DE7623A29DEE62EBDF5E207B61 (void);
extern void UpdateStatus_OnUpdate_mEB3A3378AE81829D0F38E2B13AB0CF93CD265F50 (void);
extern void VirtualCameraRegistry_get_AllCamerasSortedByNestingLevel_mB8443929A6E1FDD13F160448DBFA132B3975523E (void);
extern void VirtualCameraRegistry_get_ActiveCameraCount_m0F6EBF82D74474F4A68AB0246A31FFF0477BD3F9 (void);
extern void VirtualCameraRegistry_GetActiveCamera_m969F12A3F8618BE1AB5C1580DC4B3C8819192E09 (void);
extern void VirtualCameraRegistry_AddActiveCamera_mAF49B92C4059EFE30EC65E74987ACB190883CED2 (void);
extern void VirtualCameraRegistry_RemoveActiveCamera_mAC546C41967072CA13337EE5A936F7DF2CF851FE (void);
extern void VirtualCameraRegistry_CameraDestroyed_mF341421E602B220D976AFFF4D99FB526F52ACFE6 (void);
extern void VirtualCameraRegistry_CameraEnabled_m6BEE51817F6B30DF69280AB447B850BEAC363BFB (void);
extern void VirtualCameraRegistry_CameraDisabled_m0B9CB64455F12B3F7DFD5E4E60FB6A1C36307B7E (void);
extern void VirtualCameraRegistry__ctor_mE08BE68203AD239DD0A9E3C00F288B466937D55B (void);
extern void U3CU3Ec__cctor_mE03791F29689884B39D0989EE68F6E4EEBAF1D8B (void);
extern void U3CU3Ec__ctor_mF1B46FD8D59C1E93AE5079A978A38502589E72BA (void);
extern void U3CU3Ec_U3CGetActiveCameraU3Eb__8_0_mD2F56E9122FBF90DB68E7930C072C6AFFFB62A5A (void);
extern void CinemachineDebug_SBFromPool_mD72B0073894675497D6C33D3CDD40E7C21E01316 (void);
extern void CinemachineDebug_ReturnToPool_mDEC25F0380F48FA13289721CE5D5BFCD42A44634 (void);
extern void AxisState__ctor_m23329A65D540AACEA3D12AD0E31EB3998A90514C (void);
extern void AxisState_Validate_m3B61D89177847EE19351A98751E603C7039ED1B8 (void);
extern void AxisState_Reset_m09D45EA8AF1672C0AFB8C8186C2116970BEAE363 (void);
extern void AxisState_SetInputAxisProvider_m9D63DCDEF7DBC2134E1240F0A181F65F212AF3EC (void);
extern void AxisState_get_HasInputProvider_m6E45E8D133FACCFD8447188FD32B43509FB925BC (void);
extern void AxisState_Update_mD7790DB75E0B27E7A684A1191A7D70E29AB707E6 (void);
extern void AxisState_ClampValue_mECEF16C692361CF42EEA975781D7001F0AD3BA8D (void);
extern void AxisState_MaxSpeedUpdate_m712A58C8CBB77C9FE3218938A92CD4EA1CE73C6A (void);
extern void AxisState_GetMaxSpeed_m6F8A4B4AE39B234407D6CAA3A4456D2832727097 (void);
extern void AxisState_get_ValueRangeLocked_mD7974E42395AB05026ECEC99828FBE7918E86368 (void);
extern void AxisState_set_ValueRangeLocked_mC5ED4CAF1070E329AAE7B21894201F727550E3AB (void);
extern void AxisState_get_HasRecentering_m8470DD49BB3D804C165A68A035D70C0BCBF3BAB2 (void);
extern void AxisState_set_HasRecentering_mA36545B79D4DC3862C5C33EC8724DCBF4FA23404 (void);
extern void Recentering__ctor_m3B930E9904BC3388AA82FBEFC51E14011941CA5E (void);
extern void Recentering_Validate_mD19D0E03095F0E10845ACE1522A0DF6E491AB13C (void);
extern void Recentering_CopyStateFrom_mD5862CCAAD12BE76C99F664A3B8E7DD5046D75B0 (void);
extern void Recentering_CancelRecentering_m1AF21DE017EAC0419747C0CAF3C366DFCC478075 (void);
extern void Recentering_RecenterNow_m79D915974F8ED61A61E2A876EAD98CCC3AD0B41F (void);
extern void Recentering_DoRecentering_m3A4E02DD1E33E97FD311BC711C2C00E3AC541836 (void);
extern void Recentering_LegacyUpgrade_m3ED8B90A073DCC80ECDEB056019F73656C89C539 (void);
extern void Cinemachine3rdPersonFollow_OnValidate_m9F6C0D9BB847E0CA037E3BA785284E313258631B (void);
extern void Cinemachine3rdPersonFollow_Reset_m7C6DFBA16328009BF966A9119107BD280CDE0BDE (void);
extern void Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m0DBC0C005FD8C8AA8237A8BB6B9FD0EB68844320 (void);
extern void Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_mD7FF345DC7E06DE26F561159CFEF0BD316ED3066 (void);
extern void Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_mDDB3DFB3C48D921B33B52DF4928E5BE91FF9A2EC (void);
extern void Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m711F1DC5434664F69050E246F07342F2D75218B9 (void);
extern void Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_mA27F08BBB1F4D18F5A56BFD5696A5A34D7B685D0 (void);
extern void Cinemachine3rdPersonFollow_get_IsValid_m7071E49663EB128FE7C1D69129231D6FF1AECDDA (void);
extern void Cinemachine3rdPersonFollow_get_Stage_m20CA88F58548A2A4392B1026BFDE179308768FBE (void);
extern void Cinemachine3rdPersonFollow_GetMaxDampTime_m39E41649E32C236182C0F0F4B1F2EAE9DBEF2FEA (void);
extern void Cinemachine3rdPersonFollow_MutateCameraState_m23D1DDCC45F8CA37C0B1956AD7C9122F14538148 (void);
extern void Cinemachine3rdPersonFollow_OnTargetObjectWarped_m41953DC07F4CF0E45AC1DACD73ADB6B0F09F41CE (void);
extern void Cinemachine3rdPersonFollow_PositionCamera_m87F615F422A2E02716B0E3FD5EA08E11FF473AFA (void);
extern void Cinemachine3rdPersonFollow_GetRigPositions_m2571B8C20E3C9498A8BB6499D752F5D5BF226EAA (void);
extern void Cinemachine3rdPersonFollow_GetHeading_m8CE3DE92A79041471AEFF16D9132C3A51309B206 (void);
extern void Cinemachine3rdPersonFollow_GetRawRigPositions_m626519EC404F8B92BA2C8BCEF974FE66C52DEDA2 (void);
extern void Cinemachine3rdPersonFollow_ResolveCollisions_mC466354440257439A82F60A3E795F6DAA4D701E6 (void);
extern void Cinemachine3rdPersonFollow_UpgradeToCm3_mA87BA3939F4919A9D9BA48DE7DE82CCBC26592D0 (void);
extern void Cinemachine3rdPersonFollow__ctor_m0B53B501194B1041012F40DAC36BE24267FF0C8F (void);
extern void CinemachineCollider_IsTargetObscured_mD20DE581C8C4636ECDF387A9475FE15187842B3F (void);
extern void CinemachineCollider_CameraWasDisplaced_m15065AC55D0DBFD7D8E6B14974FEB410DC6F444D (void);
extern void CinemachineCollider_GetCameraDisplacementDistance_m155C6CD9EC3247DF447DBB41AE9688CAB3BA0461 (void);
extern void CinemachineCollider_OnValidate_mB72B9B243D1E0DD3CB7AC37C5161CB8B3224AEC6 (void);
extern void CinemachineCollider_OnDestroy_mE44616F55BA4B9BBF4D8B7550287867D82027362 (void);
extern void CinemachineCollider_get_DebugPaths_mC63AEBAB5FE270117FF3984D274C58115453C755 (void);
extern void CinemachineCollider_GetMaxDampTime_mFB2BD485F4256A819977ABA0175AE82790DBAD41 (void);
extern void CinemachineCollider_PostPipelineStageCallback_mAD4A444EAA6CA5CADB903EA004A9050A3CBB7038 (void);
extern void CinemachineCollider_PreserveLineOfSight_mDA910A155CB6C165DB4460C111F2ACFB81C8D70A (void);
extern void CinemachineCollider_PullCameraInFrontOfNearestObstacle_m9BA69562C222452DF684B692DC6FC83F0DB8F890 (void);
extern void CinemachineCollider_PushCameraBack_mC6036621C388D6795EC44B38BBC3D773B50A161D (void);
extern void CinemachineCollider_GetWalkingDirection_m500FD2E00E7C14EAE5728CC5939757F4CCF9B964 (void);
extern void CinemachineCollider_GetPushBackDistance_m3B75E64A915E54B788963981B452BE9E3507C3B5 (void);
extern void CinemachineCollider_ClampRayToBounds_m89AFAB24FB9A4E473081F776DDF59976B38F7615 (void);
extern void CinemachineCollider_RespectCameraRadius_m6E2CDF18BA0299F9F28073C0BCA62571A3187A7B (void);
extern void CinemachineCollider_CheckForTargetObstructions_mAB8330FC7BB13D34AE808A537908D41399907FDB (void);
extern void CinemachineCollider_IsTargetOffscreen_m0D77F28C65AD30E489B3FC6E9DB7A1C3BBF01B64 (void);
extern void CinemachineCollider_UpgradeToCm3_m131DC61A9DC9BDFBDDA04C862151832F2E802FBF (void);
extern void CinemachineCollider__ctor_m33504A6939C585E40E315ED21A0FEE3E1410600E (void);
extern void CinemachineCollider__cctor_mF5B31519C19F7C6AB66C3E2C1D9027AA64ABC7D1 (void);
extern void VcamExtraState_AddPointToDebugPath_m2F14422ECB21D5AAF9CFD529C8068A1A2E95B6C9 (void);
extern void VcamExtraState_ApplyDistanceSmoothing_mC5A5550408F64899458236636EAD1418598882A7 (void);
extern void VcamExtraState_UpdateDistanceSmoothing_m9303D36B6F129A3A32F89F69B28505A25121685A (void);
extern void VcamExtraState_ResetDistanceSmoothing_m01F1110A923E433EB5777EEDEC104ACD7CC9FBDC (void);
extern void VcamExtraState__ctor_m3A3EFCC13C5242E1D3FE4ABA92D155E7A81C6EDA (void);
extern void CinemachineComposer_get_IsValid_m48B424C1F6EC85269987ECFD2AD1C4129D720AE1 (void);
extern void CinemachineComposer_get_Stage_m33E24995C8957A1EE3002DBC32BA8E25D367AA32 (void);
extern void CinemachineComposer_get_TrackedPoint_m17FA628CBAA4C2A9C8CC98652FD04538AAB4BF40 (void);
extern void CinemachineComposer_set_TrackedPoint_m82C5CB9EF35902C59B954D592DC1DB4FB24CF949 (void);
extern void CinemachineComposer_GetLookAtPointAndSetTrackedPoint_mF5563C01405F05E5852AC3A21666364088F26E97 (void);
extern void CinemachineComposer_OnTargetObjectWarped_m56391CB3BAAE11B0912083EF703A504B13EBAAF2 (void);
extern void CinemachineComposer_ForceCameraPosition_mFAC884E7992FED519A04A0A5BACE32127EDC0EEA (void);
extern void CinemachineComposer_GetMaxDampTime_mE24B53C789A44988C806891D8812CBD7AA08AB63 (void);
extern void CinemachineComposer_PrePipelineMutateCameraState_m64D80C52C996B2070EC268B7CF3C9E51658C2C4A (void);
extern void CinemachineComposer_MutateCameraState_m8A4D21D4793E8F0D6C31A6EA1192B6380CB198CE (void);
extern void CinemachineComposer_get_SoftGuideRect_m508DA0621951A72CFAC6CC0F7BE523552062A433 (void);
extern void CinemachineComposer_set_SoftGuideRect_mFF86B04741A25AFFB1ADB8F9D412FBD293AC5882 (void);
extern void CinemachineComposer_get_HardGuideRect_m764D5861C1F9CD9529B02128CCE9EC3E2EF51C4F (void);
extern void CinemachineComposer_set_HardGuideRect_m3EEE252D72860A6D063936487B55FB15DCB80616 (void);
extern void CinemachineComposer_RotateToScreenBounds_mC52D231D2CED0D0A467DAAD1AE91F2DDE4421C2C (void);
extern void CinemachineComposer_ClampVerticalBounds_m36F538FA96783C84D68AA0AC20AEEEF61CEB801D (void);
extern void CinemachineComposer_get_Composition_m93221C9589DDE8EE4CE8AB55FDD676C46B4A2AC7 (void);
extern void CinemachineComposer_set_Composition_mE79927D911D5D3460F5AFAAA40DC5F8232037E7C (void);
extern void CinemachineComposer_UpgradeToCm3_m75D24B12D97695155AAB8E54805DF738A7404955 (void);
extern void CinemachineComposer__ctor_m0B3196E312A2EC9255A45BC60D6D8732382DBD19 (void);
extern void FovCache_UpdateCache_mD5487E1D88B365D823D207D4C18DBDC120481CE4 (void);
extern void FovCache_ScreenToFOV_m7FC58E4F1B8DC1FECB4AF316428058A48ED7AFE8 (void);
extern void CinemachineConfiner_CameraWasDisplaced_m5D18B778CA884292A3C4B0D530B407C4F3070F99 (void);
extern void CinemachineConfiner_GetCameraDisplacementDistance_m68E820FD079FAE1F729541182B1158DD908CCEBD (void);
extern void CinemachineConfiner_Reset_mF3955188F3046A9341DCE61A243DB756FF37B618 (void);
extern void CinemachineConfiner_OnValidate_m56702690476247E0D295124EE39380DDEF0CE096 (void);
extern void CinemachineConfiner_ConnectToVcam_mF6D90F7C813E6E0A6ED254B2E861413293877B0F (void);
extern void CinemachineConfiner_get_IsValid_m9967184F23A332245BAD9B0A6BD1822ED7D972C2 (void);
extern void CinemachineConfiner_GetMaxDampTime_mABC2BA69E53D07C4D1074DDB6BFEEDA1D3FE16BA (void);
extern void CinemachineConfiner_PostPipelineStageCallback_m7D915A3761FFB3542BA0B2234D42233CE29E6AAD (void);
extern void CinemachineConfiner_InvalidatePathCache_mA9E10FC549EF941F56833F5F433DD36796DCF107 (void);
extern void CinemachineConfiner_InvalidateCache_m13504547336071A6BB0543126C3E29674276C844 (void);
extern void CinemachineConfiner_ValidatePathCache_m11439C5FCA2C74D4EA43903A868E6B4F84ECE994 (void);
extern void CinemachineConfiner_ConfinePoint_m94102EC70561D2CAEDAE1A2BD6C3F4FD828BD2B1 (void);
extern void CinemachineConfiner_ConfineOrthoCameraToScreenEdges_m6667AF5C52440D7C6047AA4D67DF5351F8C60C00 (void);
extern void CinemachineConfiner_UpgradeToCm3_GetTargetType_m814C33FD220CD68183BBE8B3AA83BC70471EC1DE (void);
extern void CinemachineConfiner_UpgradeToCm3_m19333C2758F346D7D34138767B3A90AD51AAC73C (void);
extern void CinemachineConfiner_UpgradeToCm3_mCC4ABA4A6B7364D415817B013BCA2D2D342F3D43 (void);
extern void CinemachineConfiner__ctor_m996E0EF507917902647FB591B6E360029F620C9D (void);
extern void VcamExtraState__ctor_m2C9EED514335B9004BF926FC0F2AE35F02FE9253 (void);
extern void CinemachineDollyCart_FixedUpdate_m6B014ACF8780CA4A65858F1F964614177A697D7E (void);
extern void CinemachineDollyCart_Update_m89F7DA17A6535C04AC92E059198A6C19614ABEE5 (void);
extern void CinemachineDollyCart_LateUpdate_m32FB9B11366B1A4BDCCB3BC2D07D1DE363F9CAB2 (void);
extern void CinemachineDollyCart_SetCartPosition_mB1F5E38A82198AF14B3F54EE6EFCF3F8E23B68AE (void);
extern void CinemachineDollyCart_UpgradeToCm3_m57A906704D3EBB9D3B4F5601129BEB070F133434 (void);
extern void CinemachineDollyCart__ctor_mA47AA45EF8ED3E108C7C81D9462E927891D18562 (void);
extern void CinemachineDoNotUpgrade__ctor_m2BCE8145D7D6BD8C0EE030D592FDE86D1F1DFEE3 (void);
extern void CinemachineFramingTransposer_get_SoftGuideRect_m83D20AE6B97BFF1D18D87A7A815B78CDE8BECAD8 (void);
extern void CinemachineFramingTransposer_set_SoftGuideRect_m243193E77EC03F978424812032587299BA0EADC2 (void);
extern void CinemachineFramingTransposer_get_HardGuideRect_mE4FE9E3A32E01D0097AA3A359C72EE4034865710 (void);
extern void CinemachineFramingTransposer_set_HardGuideRect_mFB7AE55FA001912BFE038C05DBB1DD19417DD53A (void);
extern void CinemachineFramingTransposer_OnValidate_m5CC34FC8001530400C820997C6BE49214175ACC8 (void);
extern void CinemachineFramingTransposer_get_IsValid_m2D98BD599DCDDB47F69C7878A794971DD536124B (void);
extern void CinemachineFramingTransposer_get_Stage_mE1310804108A2AD0D2B48F3197869DD1CD453A1E (void);
extern void CinemachineFramingTransposer_get_BodyAppliesAfterAim_m0E09A60E5FA964DF453794687C8C6FA420592018 (void);
extern void CinemachineFramingTransposer_get_TrackedPoint_m3D58B30506EB65466C961C0E64234A2928EF75B5 (void);
extern void CinemachineFramingTransposer_set_TrackedPoint_mA0CA2C5597E3FE901D32B4CAD82D203D72A44932 (void);
extern void CinemachineFramingTransposer_OnTargetObjectWarped_mA71D169CB700B4B8DF0A9F42D74C02E3DB308FFD (void);
extern void CinemachineFramingTransposer_ForceCameraPosition_m21EA6BFB3A42909D1F0506BB00A28B96D65A97BA (void);
extern void CinemachineFramingTransposer_GetMaxDampTime_m75D70D7C9FA158625947BCD0A9916A9041FC9589 (void);
extern void CinemachineFramingTransposer_OnTransitionFromCamera_mE11245253C3ED9BF2274703A0A30561E631E56DE (void);
extern void CinemachineFramingTransposer_ScreenToOrtho_mEAD258A02DCFCAC034DE9A1B08F465BCD149001B (void);
extern void CinemachineFramingTransposer_OrthoOffsetToScreenBounds_m59D579AD56E8D75381A1BCD90521BEDE4F1554F0 (void);
extern void CinemachineFramingTransposer_get_LastBounds_m4DCAEC33A4112B23DF3AAF140968641DF578995B (void);
extern void CinemachineFramingTransposer_set_LastBounds_m994C70A03147A0B93D5F1A4B2FBC4C93978CAA21 (void);
extern void CinemachineFramingTransposer_get_LastBoundsMatrix_mF280BE6A460347767231AAF16ADDED82D0AF2F5B (void);
extern void CinemachineFramingTransposer_set_LastBoundsMatrix_m99EA9A7C087AC301B38B994AB8D5D970534C9380 (void);
extern void CinemachineFramingTransposer_MutateCameraState_m5FCADAD8CBA3E14ADA2AEFF0A678CAF2719CF456 (void);
extern void CinemachineFramingTransposer_GetTargetHeight_mDD73BF6EED9F4CD6B6140CA493DB95584FDB3045 (void);
extern void CinemachineFramingTransposer_ComputeGroupBounds_mD8CCAEEB72A14DD3FCBDE59517339CD767F9CCAA (void);
extern void CinemachineFramingTransposer_GetScreenSpaceGroupBoundingBox_m8DD095FF48442391445B761C8ABD8ED638777CA3 (void);
extern void CinemachineFramingTransposer_get_Composition_mB8762265D00E9E93670AC1F1F77807CE18943545 (void);
extern void CinemachineFramingTransposer_set_Composition_mD1CB635C2C6E986A9ADDDB3EFF6320D583572D3E (void);
extern void CinemachineFramingTransposer_UpgradeToCm3_m9ED85E4E0EE5ABC5A980E7B921F2E7B15B22FED5 (void);
extern void CinemachineFramingTransposer_UpgradeToCm3_m5F44DE244A4967781BFFE98B4D4D49A20DFCFA80 (void);
extern void CinemachineFramingTransposer__ctor_m689FA8F6AE2046BDF39F05B44493BE6FC69D6643 (void);
extern void CinemachineFreeLook_PerformLegacyUpgrade_m99CF898E574EE256857A0B46C3BFCDD407405E0E (void);
extern void CinemachineFreeLook_get_IsDprecated_m88D6AF54D6FF18608EC8527EF48D785A1FC3DBC6 (void);
extern void CinemachineFreeLook_OnValidate_m44FFF11DF20F465A5E5203AC4CE6FA81BF68BA65 (void);
extern void CinemachineFreeLook_GetRig_m2D4F355319EDB98C1594EEE46B6E9A4E3AF601AC (void);
extern void CinemachineFreeLook_get_RigsAreCreated_m9F577128696D888BFA47E78B01C156DB32076619 (void);
extern void CinemachineFreeLook_get_RigNames_mBCA70FB931D76D68DE7AFADD4F87F654B617D5E4 (void);
extern void CinemachineFreeLook_OnEnable_mC446431DC1DACABF912FA89E1F738E43E7DFB9BE (void);
extern void CinemachineFreeLook_UpdateInputAxisProvider_m577AF397745D46720008434566F2534F9720180E (void);
extern void CinemachineFreeLook_OnDestroy_mA17A0480999D5D6575F807A8B8FF126B020654EA (void);
extern void CinemachineFreeLook_OnTransformChildrenChanged_m7A0A30C8D7D5413793ECC45C1873C5A96AC3676D (void);
extern void CinemachineFreeLook_Reset_mCC7BBB47DBEB265AF277ABDE75D3EE54EF343259 (void);
extern void CinemachineFreeLook_get_PreviousStateIsValid_m76B18F2666F83833C9E650726E3777D292D4D2D1 (void);
extern void CinemachineFreeLook_set_PreviousStateIsValid_m74910407A4A3FE83EA70DD77037EA2EC736F1D77 (void);
extern void CinemachineFreeLook_get_State_m1CAAC9038A74F1914E48675BBB03E2FE1050F51C (void);
extern void CinemachineFreeLook_get_LookAt_mFBD08797DBB4BFF5B36FD0D2F804E9AE6F6476DF (void);
extern void CinemachineFreeLook_set_LookAt_m4344AC345F753044980B0254E7696826A5468BF1 (void);
extern void CinemachineFreeLook_get_Follow_m37DD23AD4BAF5D6ABA3D028587B5165E4118838D (void);
extern void CinemachineFreeLook_set_Follow_mE9D9D96D46D82195F28E9BEE0663DAA142FB3C5D (void);
extern void CinemachineFreeLook_IsLiveChild_m3938F6DFD7BBEFE7C0D2BCAE4D408FF3DDAD38DE (void);
extern void CinemachineFreeLook_OnTargetObjectWarped_m7F3AFFF4309D82E6B9B9FBE8083627159C2D4540 (void);
extern void CinemachineFreeLook_ForceCameraPosition_mD1FA34EE978F7E56CBC9468FC5F33F4EE3AA7852 (void);
extern void CinemachineFreeLook_InternalUpdateCameraState_mC860D45758E9846042B4CA5EEDA9E0498A33D1CB (void);
extern void CinemachineFreeLook_OnTransitionFromCamera_mB9170F481E881B7A256D6BAEA0E8FD1DBB8546E0 (void);
extern void CinemachineFreeLook_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_mBDE8AF12357720E4A27E8369C0985371C95814E0 (void);
extern void CinemachineFreeLook_GetYAxisClosestValue_m835524A5EFE21F7B4B0D99E299F5624416E3EC6E (void);
extern void CinemachineFreeLook_SteepestDescent_m1CF5877C82EA61C23596FE151C4ACDF165B8E92F (void);
extern void CinemachineFreeLook_InvalidateRigCache_m1415D4925274916549845A131DC1EF5815F152D9 (void);
extern void CinemachineFreeLook_DestroyRigs_m316FE82C2B85E09EB6E740457B4A6ADD273680B8 (void);
extern void CinemachineFreeLook_CreateRigs_m229E68545DDC9414FD86A817AA4F4EF8E2976507 (void);
extern void CinemachineFreeLook_UpdateRigCache_m2BDA9449AC8C3C2A44F0D2D90C2E8F548F2F14DF (void);
extern void CinemachineFreeLook_LocateExistingRigs_m6D9ACA5FDCAE2C5892F1737355ACE03E94BAFAD4 (void);
extern void CinemachineFreeLook_UpdateXAxisHeading_m67D7F9103A098968CA41FD0C46510F8740D3B1AF (void);
extern void CinemachineFreeLook_PushSettingsToRigs_mFAD40EBE27B8E8DEF7B1356F1277A9C3801CDA3B (void);
extern void CinemachineFreeLook_GetYAxisValue_m65488E6EB92D60E5F4838542850CC8E7C04F02C9 (void);
extern void CinemachineFreeLook_CalculateNewState_m39CB4DEBDBD80B945236D220D103D4D63D32FDA6 (void);
extern void CinemachineFreeLook_GetLocalPositionForCameraFromInput_mF494794AC4BE5B4EE72D14A38120DB39DF7085DD (void);
extern void CinemachineFreeLook_UpdateCachedSpline_m0C1C87A94BB544A9E4CF63AF0DA68E6D7FD3B3A4 (void);
extern void CinemachineFreeLook__ctor_mC3F83BF9717B582F4284C2CA17AAE70DFB03DA9E (void);
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__AngleFunctionU7C52_0_mC11699787EFA6293ED78BA3336E7580EB93C6235 (void);
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__SlopeOfAngleFunctionU7C52_1_mFCC93B209E9B6ADC8E738285B10F0A186AE6CAB9 (void);
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__InitialGuessU7C52_2_m43180E7CCF6AB49CAE1B8F39F952B8F5AD2CF0D0 (void);
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__ChooseBestAngleU7C52_3_mC5DB4A03A7DAB3B0306812DC68CE2415D04DAC39 (void);
extern void Orbit__ctor_m8022DA2F7290722B44C38A6A7F4782B39A375CAE (void);
extern void CreateRigDelegate__ctor_m1BD652C55A6F078509ADC349A011C3E9EE2079DD (void);
extern void CreateRigDelegate_Invoke_m672111599A43B7D9E16A45332D8664FFDB31AE15 (void);
extern void CreateRigDelegate_BeginInvoke_m9F4C0274EEF060272253F3663F2E3257A225CE36 (void);
extern void CreateRigDelegate_EndInvoke_m1E54378187660830754E026AD010E5E101B345E0 (void);
extern void DestroyRigDelegate__ctor_m1A1B2BF53220E8DA6044544CDD2BF4634DD66EA4 (void);
extern void DestroyRigDelegate_Invoke_mF9B7611AFCA9EC5F7093CA0EE3EB52CEC043008B (void);
extern void DestroyRigDelegate_BeginInvoke_m63025A2220E79B6358BAD7D8414938655376C27A (void);
extern void DestroyRigDelegate_EndInvoke_m2A68D901C11E6A90382610F31386EB725B301B4C (void);
extern void CinemachineGroupComposer_OnValidate_m899426059E843FFBF5E84DB501F02AB4966E4C90 (void);
extern void CinemachineGroupComposer_Reset_mA1F25FF12B96C1165963B74F08CF91B5D7E38641 (void);
extern void CinemachineGroupComposer_get_LastBounds_mA82ADC5037A17285DC11F23C1B916DA2B4203598 (void);
extern void CinemachineGroupComposer_set_LastBounds_m093A6FF6DE0E63C60BED961ED14CE47C6701D52E (void);
extern void CinemachineGroupComposer_get_LastBoundsMatrix_m6BB98740041EB9843976843164C5F6578F94343B (void);
extern void CinemachineGroupComposer_set_LastBoundsMatrix_m3CAC84879481A947A997DDFAD68330F2C664DD88 (void);
extern void CinemachineGroupComposer_GetMaxDampTime_m9DD5656E4C0CB0BE1F661A25F4CC3BE9E8B9AA04 (void);
extern void CinemachineGroupComposer_MutateCameraState_m386F6F9EF1479031B7AFE0FF3B03D44626B6867A (void);
extern void CinemachineGroupComposer_GetTargetHeight_mF17365ED88A00D8BC26281335D7C256C894463DE (void);
extern void CinemachineGroupComposer_GetScreenSpaceGroupBoundingBox_m00C34895DFA2273DCB4B069B6A61A45F9F2E671D (void);
extern void CinemachineGroupComposer_UpgradeToCm3_m3A9A5F360A86DE7D2034BD1102D812053674CD9C (void);
extern void CinemachineGroupComposer__ctor_mB46EF0A8B2A8D570A49438E0FC91DDD701D71003 (void);
extern void AxisBase_Validate_m346100A07229E141A781DFA36B2F8B7A99AEAFB5 (void);
extern void CinemachineInputAxisDriver_Validate_m84655856991F2EDC28367081279BF16A80009CAC (void);
extern void CinemachineInputAxisDriver_Update_m481B879CF4FBD76457792D1533DF505D014DE4B1 (void);
extern void CinemachineInputAxisDriver_Update_mB5463E7B9F6C10C51903F3BF4D8A27263E259D2C (void);
extern void CinemachineInputAxisDriver_ClampValue_mABC231D2F9842EABC91604D26F296C5AE30F3F44 (void);
extern void CinemachineInputProvider_GetAxisValue_m5709F61EC48799DB5302A1EBE27641BEFEC6A633 (void);
extern void CinemachineInputProvider_ResolveForPlayer_mEBB6573C0F529AD528E19DFF3381EEA045D67E7C (void);
extern void CinemachineInputProvider_OnDisable_m2E86A211186A6439142619372870C25B3E3968E3 (void);
extern void CinemachineInputProvider__ctor_mD89A2574141B577201A1A7E942005E38A855FE16 (void);
extern void CinemachineInputProvider_U3CResolveForPlayerU3Eg__GetFirstMatchU7C7_0_mA04002433420500DEF76E611EC238F93DDCFB571 (void);
extern void U3CU3Ec__DisplayClass7_0__ctor_m70B586881BA5FAD5260CEC933CEABF4FA39836FA (void);
extern void U3CU3Ec__DisplayClass7_0_U3CResolveForPlayerU3Eb__1_m7A714021553634D0DA8824E9C715C77EFA66BF7C (void);
extern void CinemachineInputProviderExtensions_GetInputAxisProvider_m75E65D3BF9AE2A9E46A5DBF5CD95D868EAD267BA (void);
extern void CinemachineLegacyCameraEvents_OnEnable_mC483821F77A7D41A8CF8C37F2203966AD7C1F071 (void);
extern void CinemachineLegacyCameraEvents_OnDisable_mB90EA14C25A4D9E934B0FEF06EFF4D209AB53F2B (void);
extern void CinemachineLegacyCameraEvents_OnCameraActivated_m55959B8CF1A27842E79B1234548CFE748BD729BD (void);
extern void CinemachineLegacyCameraEvents__ctor_mFB89C2CAD5E404B16371238D26CB2229F880EDBE (void);
extern void OnCameraLiveEvent__ctor_mDD84F7682414813B9CB869B347152D6DC0F9A484 (void);
extern void HeadingTracker__ctor_mE44ADB60804CBF308F06DABED0EBF109CEE43E35 (void);
extern void HeadingTracker_get_FilterSize_mCD39231CE59B1CAA94ADE6C856547FBD6E7AF26E (void);
extern void HeadingTracker_ClearHistory_m9C4EC790785C4E5F28EB35E847079EB9C4FA9765 (void);
extern void HeadingTracker_Decay_m6186639A5F462340F36B848D6A0C57917B87A2CA (void);
extern void HeadingTracker_Add_m59F20F9FC66ACE908B78692B2E2C48235714E247 (void);
extern void HeadingTracker_PopBottom_m049C60BA33D0A37D858A3A323251C8300FE07D65 (void);
extern void HeadingTracker_DecayHistory_m7A81A41460216AD7AE18BD8644866F7B152F9202 (void);
extern void HeadingTracker_GetReliableHeading_m06B0753676E0EBEE99CDDB35289188F17E971799 (void);
extern void CinemachineOrbitalTransposer_OnValidate_m27AFA1E76C2F1D1C50995D6D6EA5C8BF0CE57F16 (void);
extern void CinemachineOrbitalTransposer_UpdateHeading_mAA765E33A9D8A6FFB87305F086067F359446F7F1 (void);
extern void CinemachineOrbitalTransposer_UpdateHeading_mFD6E865B3766378E30315B47EF5ACEF2A04D9D8A (void);
extern void CinemachineOrbitalTransposer_OnEnable_m4039EAA60B25A82FF105FE8D70CD09DFB6742F49 (void);
extern void CinemachineOrbitalTransposer_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m026B9333A2FA5CF8541147E4426C5EDA3BEA1193 (void);
extern void CinemachineOrbitalTransposer_UpdateInputAxisProvider_mDA52E1EE2F28B186D6BBA52B2D860DA2DBE19314 (void);
extern void CinemachineOrbitalTransposer_OnTargetObjectWarped_m8DBFF886B18E158EEC994CC5D719E7ABBA3F3254 (void);
extern void CinemachineOrbitalTransposer_ForceCameraPosition_m1CC1AA49303299F6271EB1742F5F8455417BEB2C (void);
extern void CinemachineOrbitalTransposer_OnTransitionFromCamera_m9F0D6B6E7307748D3AC371C1054728F72D55973D (void);
extern void CinemachineOrbitalTransposer_GetAxisClosestValue_m80F69D95B306047CADC66162AFF7B7F581E412E9 (void);
extern void CinemachineOrbitalTransposer_MutateCameraState_m71E4466F2087A6DF7CF42882F83DFCF5BC68DE66 (void);
extern void CinemachineOrbitalTransposer_GetTargetCameraPosition_m0C205CFE791DB38B520AEA61BEE8458FB6477785 (void);
extern void CinemachineOrbitalTransposer_GetTargetHeading_m2DEFA6F70A596EDB7AC3D67F819C7957E3C1308B (void);
extern void CinemachineOrbitalTransposer_UpgradeToCm3_mA27A7DA6C26B261A45DBE8FFC357EC271A719470 (void);
extern void CinemachineOrbitalTransposer__ctor_mAACA858D22B8652838E12EB38A8E68DF60D57933 (void);
extern void Heading__ctor_m5CBDDB066E6DF303F92BC1264BF5A34C96F16382 (void);
extern void UpdateHeadingDelegate__ctor_m3D4109E08FE806FA2CBC36F4BF54EDDC82571490 (void);
extern void UpdateHeadingDelegate_Invoke_mB27A3CF5BB09802E3E989991A77C14E672C37F6A (void);
extern void UpdateHeadingDelegate_BeginInvoke_m2637A0D71F166CC74CF5E45D6D42433DE378D776 (void);
extern void UpdateHeadingDelegate_EndInvoke_mF444A6CF8BCF534CDDD3B14D34A4BEE8EF242219 (void);
extern void U3CU3Ec__cctor_mE9E8806F7D554A77B9AD830A8A271AE7D9DF6BFE (void);
extern void U3CU3Ec__ctor_m93127D69718D1B675220C18FFB3F40033560B68A (void);
extern void U3CU3Ec_U3C_ctorU3Eb__31_0_m46FDBC41B1B996E59C57F1917160D58DA6D554F6 (void);
extern void CinemachinePath_get_MinPos_m242BFD54E2EA15F3D42E73D0AA91EF20DB5A89FE (void);
extern void CinemachinePath_get_MaxPos_mF18C1A821F79473C474F203E2D242CF6812C5333 (void);
extern void CinemachinePath_get_Looped_m09A52B423D4CC031C72489A682A68CAFDED10918 (void);
extern void CinemachinePath_Reset_m46C889DCF2D757C25B2A8557878F8768951E11D5 (void);
extern void CinemachinePath_OnValidate_m9D1457DC1FF88C8AA057CF8F88EE5CB7EF872CAF (void);
extern void CinemachinePath_get_DistanceCacheSampleStepsPerSegment_m22FC53EBD61CA6D4617C1ECB6E0842F6B1D3D74B (void);
extern void CinemachinePath_GetBoundingIndices_mE8E2B2FBE56850FF0A8373C99529BACF7C4A84EB (void);
extern void CinemachinePath_EvaluateLocalPosition_m2544E21A704BAC9FFC2851772025447E857D66BA (void);
extern void CinemachinePath_EvaluateLocalTangent_mEA2CA96D1DC561A72EDEBA49A475014687CF26AA (void);
extern void CinemachinePath_EvaluateLocalOrientation_mDAA63D623E64A8DA8434248D200ED6008FC0D786 (void);
extern void CinemachinePath_GetRoll_mB03391DE6E23E2F89AD0F758671CA383D80C89F6 (void);
extern void CinemachinePath_RollAroundForward_mFBA039EDB89A179D0CC17D673940CBA44EDF208D (void);
extern void CinemachinePath__ctor_m61372CA2207403085AA1A71FD2285F3B4F20E48C (void);
extern void CinemachinePathBase_StandardizePos_mC5BDA5E1CF9AFB0759D408B66742B64CCF19A536 (void);
extern void CinemachinePathBase_EvaluatePosition_mECBDF202924BD8B139616E4DEBC2DE89C5F68D41 (void);
extern void CinemachinePathBase_EvaluateTangent_m1ECF609F052EAF83E04CE80C00F59CCEBF4E5068 (void);
extern void CinemachinePathBase_EvaluateOrientation_mD2B4B1E52A627DC73BDAA27B6FD646807B1B1E91 (void);
extern void CinemachinePathBase_FindClosestPoint_m5408B5DBB0952F10EC1FEBA2057C9B8B1046CDEB (void);
extern void CinemachinePathBase_MinUnit_m471F2FF54F563F9A19DC32E9275A8467284A2424 (void);
extern void CinemachinePathBase_MaxUnit_mB995170CFCA094A7C3E9B31B4F9D6A2553B1E56B (void);
extern void CinemachinePathBase_StandardizeUnit_m994E42C73E007A38D0F3039E1A99364DFA74211A (void);
extern void CinemachinePathBase_EvaluatePositionAtUnit_m47FDF0D57A865B4F6B5C6BC7460EB3BD5FFA5B65 (void);
extern void CinemachinePathBase_EvaluateTangentAtUnit_mBD047A731AFCC51D5B145476B65A72C994651AA5 (void);
extern void CinemachinePathBase_EvaluateOrientationAtUnit_m2185E6E2EC73BEF736EF7F6C2C4DF6EFE222843A (void);
extern void CinemachinePathBase_InvalidateDistanceCache_mEB69E50239A70868E9AB984172EA6DD5AA308944 (void);
extern void CinemachinePathBase_DistanceCacheIsValid_m8BC6A6302FC87D6A1E3B5309FFA135E2255694CA (void);
extern void CinemachinePathBase_get_PathLength_m652EE55DF497D83E59E451A4D584A7A7BFB121F7 (void);
extern void CinemachinePathBase_StandardizePathDistance_m4A401FCE67BEAA92F1C7864E89160507A3FEADDB (void);
extern void CinemachinePathBase_ToNativePathUnits_m9FE04A368DDED74F3175D4F4DB2090DB5FBC57F0 (void);
extern void CinemachinePathBase_FromPathNativeUnits_mF9B0FD93F95C79AACC988AFB217607FBA3995104 (void);
extern void CinemachinePathBase_OnEnable_m384DE1B42EAD98BE31690C78E3CBC4C9AAFA663D (void);
extern void CinemachinePathBase_ResamplePath_m8F0793AE39ACFB8BA515E0A973D7ADF9D7520FFD (void);
extern void CinemachinePathBase__ctor_mA5BC84DA8D48A1A3484EA17201F948E6980096A1 (void);
extern void Appearance__ctor_mADFB8A11271BA7F20FA20CBD18DE7A471104009D (void);
extern void CinemachinePipeline__ctor_m588E6BB31B1C2345FC123CFA1C6C78C2B61EDD73 (void);
extern void CinemachinePOV_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m2472F38919118BF5864AA29CF9F0F034DA9F871A (void);
extern void CinemachinePOV_get_IsValid_mC090D4DF48E39F5DDF2BBCEB708E39EAE653363E (void);
extern void CinemachinePOV_get_Stage_mEDD446A71EF0B1A7C0FF42691C79C11338B38DBC (void);
extern void CinemachinePOV_OnValidate_m5E388DF722E0FA9972964FAF2E734AC337B477E6 (void);
extern void CinemachinePOV_OnEnable_m8A8AAA5F8FCA291F9180158C71BC90CA37C4E9FA (void);
extern void CinemachinePOV_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m6FCA40A576A2725E1A2CBE12774FD08F2B3084D9 (void);
extern void CinemachinePOV_UpdateInputAxisProvider_m96B616E27C17568F5DD0997BBC9AF1042117C63A (void);
extern void CinemachinePOV_PrePipelineMutateCameraState_mBCEBE5E28593C8569C53FF3FD74F850AF429C805 (void);
extern void CinemachinePOV_MutateCameraState_m5B7BBD55C148A5AF045FEB4C8D60C52503589238 (void);
extern void CinemachinePOV_GetRecenterTarget_m2D7249FD4307B34464613479E0637C350B2E2A43 (void);
extern void CinemachinePOV_NormalizeAngle_m120DAA70B95351E50F6910153F56F7068045FF8C (void);
extern void CinemachinePOV_ForceCameraPosition_mD86ECCBA35EC168C4109BBF72B11306D8456D7F2 (void);
extern void CinemachinePOV_OnTransitionFromCamera_m26897DA1B83CFD88842E5229D8FB36F341902147 (void);
extern void CinemachinePOV_SetAxesForRotation_m1A1EF7C7B3C50A8C873F1D6EFCAD65C7D7F71066 (void);
extern void CinemachinePOV_UpgradeToCm3_m3F38965D6ED535BA573E340C03BCFB61D1A64313 (void);
extern void CinemachinePOV__ctor_mB9B972066CE057058815A47745E15CF6C6C704E6 (void);
extern void CinemachineSameAsFollowTarget_get_IsValid_mE53D0FA3AC6C62B6A199C95564728E9ACC202451 (void);
extern void CinemachineSameAsFollowTarget_get_Stage_mF27DF24729542FFBB2CA1BF1FD19ADFFF26B8988 (void);
extern void CinemachineSameAsFollowTarget_GetMaxDampTime_m4F7306DA606C90A8A7C72B72406C58155899D8D9 (void);
extern void CinemachineSameAsFollowTarget_MutateCameraState_m0EF320BEDCF7D79A83F53C4A67A5EC291ADC1CD4 (void);
extern void CinemachineSameAsFollowTarget_UpgradeToCm3_mCD0F2B20921A61E731496D2908BB7B65EF41709D (void);
extern void CinemachineSameAsFollowTarget__ctor_m443B13544B8F6B0430FC5DE711B4CDC6C2199D67 (void);
extern void CinemachineSmoothPath_get_MinPos_m910524110AA6B88E68B458110ED67913856EC7C8 (void);
extern void CinemachineSmoothPath_get_MaxPos_mA62F7994CF3F1C557297B8EF606264E80E30EF02 (void);
extern void CinemachineSmoothPath_get_Looped_m39E0E3F8E867D3ADCA14B1B8F572631A748B637B (void);
extern void CinemachineSmoothPath_get_DistanceCacheSampleStepsPerSegment_m6E3FDF8570FE8B31793B1161F878B0E2EBA71E7B (void);
extern void CinemachineSmoothPath_OnValidate_m012C279042947B45690AE7AD51C7B736EA7106FF (void);
extern void CinemachineSmoothPath_Reset_mF848956AE8411201C675B8FAE376134D55419E9E (void);
extern void CinemachineSmoothPath_InvalidateDistanceCache_m86F480F4790B3D39FEAC2314A688CD69683526A9 (void);
extern void CinemachineSmoothPath_UpdateControlPoints_m2C03B480AA4C475E74EB59A42061D6EBEAACD651 (void);
extern void CinemachineSmoothPath_GetBoundingIndices_m263F00A9F76F4689E1ECB01530C4A86660298DE2 (void);
extern void CinemachineSmoothPath_EvaluateLocalPosition_mEDB2FBFCC7B326E566986F2C8D5869F0F014B41B (void);
extern void CinemachineSmoothPath_EvaluateLocalTangent_m7AD1129C4FCCA5EA80C1CCB1758EACBC76D092D3 (void);
extern void CinemachineSmoothPath_EvaluateLocalOrientation_mE5A8073F4864F88008828FA8FB0655844E9EC37C (void);
extern void CinemachineSmoothPath_RollAroundForward_m7313CE50FF501476FCF53C836E5E50D7AAB7A551 (void);
extern void CinemachineSmoothPath__ctor_m78C8971A1BB3718FF626AB69225E17D096C4DE82 (void);
extern void Waypoint_get_AsVector4_m358E9E0B3C5BF20F20ACB52873D9E9446D961E36 (void);
extern void Waypoint_FromVector4_mD3CAE1774293C487073AE38DED4A1D4766ACBFC7 (void);
extern void CinemachineTouchInputMapper_Start_m254207B98E7CE6FAD5993C8C65453EAD73BA9761 (void);
extern void CinemachineTouchInputMapper_GetInputAxis_mCB3F8EDCA569CA229ECECE121479CBFBCA4ECC75 (void);
extern void CinemachineTouchInputMapper__ctor_mFA32270DC73505DB6F1B437927C8842DD17D0403 (void);
extern void CinemachineTrackedDolly_get_IsValid_m1403467E726A3EDD6C3C2527272B4F0EBDA1134D (void);
extern void CinemachineTrackedDolly_get_Stage_m0172E64081775C35E056FDD8F1B09A5EC75F0359 (void);
extern void CinemachineTrackedDolly_GetMaxDampTime_m5C78894035F699CE3BAA512C13ADA292E966C237 (void);
extern void CinemachineTrackedDolly_MutateCameraState_m7140B176C4FF84906A96B8B66F0921304FFAF741 (void);
extern void CinemachineTrackedDolly_GetCameraOrientationAtPathPoint_m1949C9ED3DCD558D2A7F27E8CC075DD2065C013F (void);
extern void CinemachineTrackedDolly_get_AngularDamping_mB7A2F30451BB0C082D55D5437B679BDDA11A9310 (void);
extern void CinemachineTrackedDolly_UpgradeToCm3_mB08A863F720B96FD34AAE5F07BC302C6815F72AC (void);
extern void CinemachineTrackedDolly__ctor_m4E026B5C47E38F9A87F014811AFD52A5DB81BB10 (void);
extern void AutoDolly__ctor_m3B7C437D0F1F270E1FCE4710910A590FFD243CC0 (void);
extern void CinemachineTransposer_get_TrackerSettings_m04830B82E096DF5E347A1256356D8BDE2643DD16 (void);
extern void CinemachineTransposer_OnValidate_m606DDE20AE388161717BE364362463B2EE7EDBD4 (void);
extern void CinemachineTransposer_get_HideOffsetInInspector_mF7329AB3746F04FA98FE6FF8949F52DFF9F538DA (void);
extern void CinemachineTransposer_set_HideOffsetInInspector_mF2D2D2D0B57F4869EE3E50B6B8278866B6CDBE4B (void);
extern void CinemachineTransposer_get_EffectiveOffset_m0B4B3AA79286EA6F5F16FA8401A1911257C18386 (void);
extern void CinemachineTransposer_get_IsValid_m51029A45C7008AFCED66842E91E8D4B736BC5D1E (void);
extern void CinemachineTransposer_get_Stage_m9F4CAF5EFACE473B759C6FCBB707BBE96F86CAC2 (void);
extern void CinemachineTransposer_GetMaxDampTime_mECB9E5D32672ADA5167A36D51634DE37FD113236 (void);
extern void CinemachineTransposer_MutateCameraState_m54A04E7B15B3EEF997F3F7874AEEF13AA05198E1 (void);
extern void CinemachineTransposer_OnTargetObjectWarped_m01A0FB117CC85A19CF21C0C24498683D5414BFD4 (void);
extern void CinemachineTransposer_ForceCameraPosition_m5FD9FB370899D082FE20F26174B01CD67DEE2E48 (void);
extern void CinemachineTransposer_GetReferenceOrientation_mE864F0CA360317576821F6D709D0872C8D2A663F (void);
extern void CinemachineTransposer_GetTargetCameraPosition_mC6F3BB4CC9DBF64A85A060B3475B4061DB901EDA (void);
extern void CinemachineTransposer_UpgradeToCm3_m3C11165E10A40E83B53158E87120A9BFAE0BE1E3 (void);
extern void CinemachineTransposer__ctor_m747039A1923268409E4F0B0F3F4311BAC0286335 (void);
extern void CinemachineVirtualCamera_PerformLegacyUpgrade_m75043A3568B73763172F08127B0188FEDC335C18 (void);
extern void CinemachineVirtualCamera_get_IsDprecated_mADFA043287577B5B241915389DE62A61E17016D9 (void);
extern void CinemachineVirtualCamera_get_State_m2B975126FF35A9433915EDCA7153495D74C16BC7 (void);
extern void CinemachineVirtualCamera_get_LookAt_mD912826BA1F07E89B4C72E2E14271819E3698D70 (void);
extern void CinemachineVirtualCamera_set_LookAt_m731B086636370206EF8A1B3A1E1CB7D5311EB6E1 (void);
extern void CinemachineVirtualCamera_get_Follow_mB7F8F586CAD2DC03A489AD3A1B1D6C97C1B02348 (void);
extern void CinemachineVirtualCamera_set_Follow_m4A371BEB0A4476F3337B00C2FCCDF3B91284FC64 (void);
extern void CinemachineVirtualCamera_GetMaxDampTime_mA67B8DF7584E92325E6E231B3FCAAF4B536DD613 (void);
extern void CinemachineVirtualCamera_InternalUpdateCameraState_m1013A42B5A8E93C00EED7698833BB6CBD722A118 (void);
extern void CinemachineVirtualCamera_OnEnable_m76E86C78F8CE023500DBC47D9BB1A776C60E5837 (void);
extern void CinemachineVirtualCamera_OnDestroy_mA2A2D126795D1F9ECB0CB3C76304041B7971BC93 (void);
extern void CinemachineVirtualCamera_OnValidate_mA7930EEBEF017FDE84F8BB7C6F2B343475800B3E (void);
extern void CinemachineVirtualCamera_OnTransformChildrenChanged_m84112E4040D6C6F338AA2545989742BC9E2B600D (void);
extern void CinemachineVirtualCamera_Reset_m02AFCCA318CCA0BBC5FB9B627129E34E46F7E743 (void);
extern void CinemachineVirtualCamera_DestroyPipeline_mDA5D57328A912657F0A29FC4D91ED472DA08D54D (void);
extern void CinemachineVirtualCamera_CreatePipeline_m540F432BECA028656FFD94DE9D9754BA589A008A (void);
extern void CinemachineVirtualCamera_InvalidateComponentPipeline_m74EFE071B3D4C73B2A5CDF73C817D59C21432438 (void);
extern void CinemachineVirtualCamera_GetComponentOwner_mD1D86A1EEF34F6C79927A7EBED9C83CDE1E67FD1 (void);
extern void CinemachineVirtualCamera_GetComponentPipeline_m55048017AD1951408530C306C849090F64570F54 (void);
extern void CinemachineVirtualCamera_GetCinemachineComponent_m96FF29242A33A8929E9C60AA80959A8EDF001392 (void);
extern void CinemachineVirtualCamera_UpdateComponentPipeline_m771722F8D819DE66779F541255A8C5D2158FAC29 (void);
extern void CinemachineVirtualCamera_SetFlagsForHiddenChild_m8A58A93E7379050861BB6AD30D77A678C788CB43 (void);
extern void CinemachineVirtualCamera_CalculateNewState_mAE96EFF8DE98E3B03B09A0EB19D27B8B8D287C90 (void);
extern void CinemachineVirtualCamera_OnTargetObjectWarped_mA36AFF744413B299E89ED32A85C6CC03E1AF65E7 (void);
extern void CinemachineVirtualCamera_ForceCameraPosition_mF1D429C0B71D6CA71AE3133048A204CC74FC2DE7 (void);
extern void CinemachineVirtualCamera_SetStateRawPosition_m65A1315C334895AD27A32BA3454330DB09B33751 (void);
extern void CinemachineVirtualCamera_OnTransitionFromCamera_m274BB20F96EDF8F4CE6E45B5B0535E6D5C3AED7A (void);
extern void CinemachineVirtualCamera_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m31DE0FEB413302BF606C23F711681D175925C71A (void);
extern void CinemachineVirtualCamera__ctor_m8ED96240A29D5C373D76210613D079B87F372C2F (void);
extern void CreatePipelineDelegate__ctor_m69643E8561A4E503B52314E0AB11EAEDA86EFCD8 (void);
extern void CreatePipelineDelegate_Invoke_m6E6AC41C832AC1BC1F83261EBD9452EE4F1B26E5 (void);
extern void CreatePipelineDelegate_BeginInvoke_m86D3EAD2BFEE25C736AE3DE997CDAA29B2D5A780 (void);
extern void CreatePipelineDelegate_EndInvoke_m3BA5461CB30EF0F7C407A95D24A593C7BA42806A (void);
extern void DestroyPipelineDelegate__ctor_m417AAACDE7A3186B5B8BAA036A4E150485033164 (void);
extern void DestroyPipelineDelegate_Invoke_m3109307C35DC548E3C0A26C39251A59E347BBD0B (void);
extern void DestroyPipelineDelegate_BeginInvoke_m8322F79EFC947602180CB5E6A19331550D1EBE2E (void);
extern void DestroyPipelineDelegate_EndInvoke_m592E51044B970A682BB9F3B88EF600F931535BFB (void);
extern void U3CU3Ec__cctor_m5902DA0D2693C77B4F2A389BEF0FD4658BD45FEE (void);
extern void U3CU3Ec__ctor_mB50F1566116B30BCBE2AB1A4FCB7970BB8EFEBA0 (void);
extern void U3CU3Ec_U3CUpdateComponentPipelineU3Eb__44_0_m36914D634EE6E38683F6654DE8AFBA1882C1A400 (void);
extern void VcamTargetPropertyAttribute__ctor_m59083A45573C38A071C24FD9E24585756C85536A (void);
extern void LegacyLensSettings_ToLensSettings_mB72D0B6673F19BC5697AB92AF1AAC5B9B7CB2CF6 (void);
extern void LegacyLensSettings_SetFromLensSettings_m1EBBC2E3012094938CFE36A470A1B5709669DCAB (void);
extern void LegacyLensSettings_Validate_m7D021CE1F7DDDAB75E8DB632DE6C361F4F02D183 (void);
extern void LegacyLensSettings_get_Default_m9DF19927BD4DA161D9B8164D408A9385DA83082B (void);
extern void CinemachineBrainEvents_GetMixer_mBF316A88CA46E6EF08AEDC85FC42F6B0558D39C2 (void);
extern void CinemachineBrainEvents_OnEnable_m9FE0874E885DB802B54C83335223F866AF828B38 (void);
extern void CinemachineBrainEvents_OnDisable_mA5FA42343B49BD087F61CEF9E387A8D9A01D083C (void);
extern void CinemachineBrainEvents_OnCameraUpdated_m5115CBA44936650F24A05365471F652E07BF2E5E (void);
extern void CinemachineBrainEvents__ctor_m300E6954E1A00E0664DE88A476609990E75A0E63 (void);
extern void CinemachineCameraEvents_OnEnable_m5BDFC3FE94BCC51CC61092E0F964E3CFEB0E3FDD (void);
extern void CinemachineCameraEvents_OnDisable_mF18FBA269408ECABD01769165A7FC42006417AA9 (void);
extern void CinemachineCameraEvents_OnCameraActivated_mD638E29F5EEFEDDAE452A203EFD44E2671E98B0D (void);
extern void CinemachineCameraEvents_OnBlendCreated_m713481243CE2C55D62CDD226280DC076BC44A51B (void);
extern void CinemachineCameraEvents_OnBlendFinished_mA205F6229D30B32FA6F6A2E80EA7DE48F12DB97E (void);
extern void CinemachineCameraEvents_OnCameraDeactivated_mFEF1435E8DBA146371BA55702C26571885582939 (void);
extern void CinemachineCameraEvents__ctor_m2B67581C41DF90751D038D0991A806C4AE12F312 (void);
extern void CinemachineCameraManagerEvents_GetMixer_m6A84ACEF0E88C37CD950B115A61469494D7E5793 (void);
extern void CinemachineCameraManagerEvents_OnEnable_m88A6A5E799CE8168F0317F6302A75699518D7A7C (void);
extern void CinemachineCameraManagerEvents_OnDisable_mFD7D44DEA91481DEACE3D9FB0633E1F3889800FF (void);
extern void CinemachineCameraManagerEvents__ctor_m801A8DCC4D9401931807CEF9ADD53652CB4F31EB (void);
extern void CinemachineInputAxisController_Reset_mE68EBFDDE9609A0FA27A0814A790F7A072B992CB (void);
extern void CinemachineInputAxisController_InitializeControllerDefaultsForAxis_mB0B7561252EED6765132E7165939D599DF2D8246 (void);
extern void CinemachineInputAxisController_Update_mEBAF2A94D290DAB75F240886010713CD999DE4DC (void);
extern void CinemachineInputAxisController__ctor_mF1D628CF0B067EADE1738570BDF0F807F96239AC (void);
extern void SetControlDefaultsForAxis__ctor_m2C7D27199774523F0B5B0790E3C8ECDC7717417A (void);
extern void SetControlDefaultsForAxis_Invoke_mA7751023CB446A7FD6AACB2C0A5C92F7F2F7E701 (void);
extern void SetControlDefaultsForAxis_BeginInvoke_m0B485DAFB61FE48E586D36380C96656BDB7C1C41 (void);
extern void SetControlDefaultsForAxis_EndInvoke_m16B348996D9FDD38C7ACED2FF570DFD54F2CD5B1 (void);
extern void Reader_GetValue_m4ED97721BDC84F248FE3D085431EA3D921253F58 (void);
extern void Reader_ResolveAndReadInputAction_m22A5F39EAFBEBA607C13A9EEDC632BF27DC224FA (void);
extern void Reader_ReadInput_m4DD490F8CC8AE41F05AAD07350B0136CFD3AD19C (void);
extern void Reader__ctor_m63F40D0157CF3F324637968E92334344948E5DA2 (void);
extern void Reader_U3CResolveAndReadInputActionU3Eg__GetFirstMatchU7C8_0_mC0E1D59C4B0009C424D1A7E6FE19AAD5FA75B405 (void);
extern void ControlValueReader__ctor_mD9F083553203BB2DE6CE0A1D209929AFD06BCCCC (void);
extern void ControlValueReader_Invoke_m17280C755ADD9E87BA7F5979351CCC0A7214A515 (void);
extern void ControlValueReader_BeginInvoke_mDDF92D85CEF9EFB414FECB966C714B52614482BD (void);
extern void ControlValueReader_EndInvoke_mA537C17D10AE8ACF23E9B0EC3322F4375B623DAC (void);
extern void CinemachineMixerEventsBase_InstallHandlers_m15BDFBD31CDD12BD6F1C16F4496409D4F3791385 (void);
extern void CinemachineMixerEventsBase_UninstallHandlers_mBBD2D828E9DA293919204C9783C80D88CBD4C72B (void);
extern void CinemachineMixerEventsBase_OnCameraActivated_mEECC261CE2941B6A2F8F25A12BACB1F924D1F000 (void);
extern void CinemachineMixerEventsBase_OnCameraDeactivated_m972028AC75380825A01544E70C1F44916033FA86 (void);
extern void CinemachineMixerEventsBase_OnBlendCreated_mEBC8A2118F728AE9439C333758220A51E8730018 (void);
extern void CinemachineMixerEventsBase_OnBlendFinished_m5A88C2B351431E9B3A4C9A6490298B4EB21592D6 (void);
extern void CinemachineMixerEventsBase__ctor_mE84DA33ADBDD40C137A40172D617EB332667DCAC (void);
extern void CinemachineSplineSmoother_SmoothSplineNow_mA0FF23D04880F9FAF17A75BBE35E874F81CF9DBF (void);
extern void CinemachineSplineSmoother__ctor_mD75416FD30F780E34C5B044C5A52732554B8FE89 (void);
extern void CinemachineTriggerAction_Filter_m36D9BC246ACB12F21E9AFB318999FDD08D38F4C4 (void);
extern void CinemachineTriggerAction_InternalDoTriggerEnter_m7CDCE62E612D8FEB1480FF8C359C115758AEF779 (void);
extern void CinemachineTriggerAction_InternalDoTriggerExit_mB3543B8137F8F265F7D35BBDE3D79D6C19898B95 (void);
extern void CinemachineTriggerAction_OnTriggerEnter_m08FDFB756EF4414156FEB61005A0F375B85D4C66 (void);
extern void CinemachineTriggerAction_OnTriggerExit_m372A68390BFEACC424899B7322EDEC4C92F796BE (void);
extern void CinemachineTriggerAction_OnCollisionEnter_m07598D4C497DB457989AE93DC0FCA94BA8024EFD (void);
extern void CinemachineTriggerAction_OnCollisionExit_mD69340A4FCD852B90084A20003FD1A7B49497F57 (void);
extern void CinemachineTriggerAction_OnTriggerEnter2D_m0D41DA8B265E668B4D43703BDEE1B3E948212938 (void);
extern void CinemachineTriggerAction_OnTriggerExit2D_m1E682C95A166BE011B1F77159A515BE24D530AF2 (void);
extern void CinemachineTriggerAction_OnCollisionEnter2D_m8DC667AA67E6624D781698D4668F10AA42443334 (void);
extern void CinemachineTriggerAction_OnCollisionExit2D_mAAA808BFD294DC4EF8AE35BDA09B4DB0631ADD83 (void);
extern void CinemachineTriggerAction_OnEnable_m738581AA42EC4664475981A86EDFDBA001E35038 (void);
extern void CinemachineTriggerAction__ctor_m70E57ED2AA3C80E74C8C27E35221898BA6B838CD (void);
extern void ActionSettings__ctor_m634C3CD9EC38605901BA50D58E89EB27BC5F1A56 (void);
extern void ActionSettings_Invoke_m0909B351B47486770E46521676789D6DCD11E6FE (void);
extern void TriggerEvent__ctor_mD8370E1F7E0912BD1DF377835495FC287990B169 (void);
extern void GroupWeightManipulator_Start_mA1F0B126DAF266088946482651762E8E26148801 (void);
extern void GroupWeightManipulator_OnValidate_m8103AB555F999250B55D11F968A0849310180643 (void);
extern void GroupWeightManipulator_Update_mCEFEC124080ADCCDB357CEF20FE8D0DE8916A20A (void);
extern void GroupWeightManipulator_UpdateWeights_mAA06E7BA50BF0F83FBC83FCEC03B28E3D65736C5 (void);
extern void GroupWeightManipulator__ctor_mBF62C9730629DD57A68A3CE94E2C76009D1C2FF7 (void);
extern void CinemachineCollisionImpulseSource_Reset_m4C0BD9B25B7AC7BA8C2955E2221929969C18F51F (void);
extern void CinemachineCollisionImpulseSource_Start_mCA8DC740D676B26289D1A68C5F3EA94BA5EC9C39 (void);
extern void CinemachineCollisionImpulseSource_OnEnable_m2B96C7209FBF654F99B26F91E238351EEA11669C (void);
extern void CinemachineCollisionImpulseSource_OnCollisionEnter_mD36ECDAD22B650FD448CED69127EA631E454AEF1 (void);
extern void CinemachineCollisionImpulseSource_OnTriggerEnter_mC55CE5E757A6B7DBA6FE4A212B05DB1581836322 (void);
extern void CinemachineCollisionImpulseSource_GetMassAndVelocity_m6D011C303EAE6502F3A55E29B419F2D2D9AD9E18 (void);
extern void CinemachineCollisionImpulseSource_GenerateImpactEvent_m25CFB3D82355142D01FF5CDCD9E0309FA6A981E0 (void);
extern void CinemachineCollisionImpulseSource_OnCollisionEnter2D_m3F9E3B5AED0371BF324A89D166C00DF6076A753B (void);
extern void CinemachineCollisionImpulseSource_OnTriggerEnter2D_mFCF9E09617B1F36E789BEE51586D63812CEAA921 (void);
extern void CinemachineCollisionImpulseSource_GetMassAndVelocity2D_mFEED066CEF8B730CA1CA7F48A45737ABF1BCE3E6 (void);
extern void CinemachineCollisionImpulseSource_GenerateImpactEvent2D_m9E350C0D8680E5D00789D2C92996EDBC7C66BF98 (void);
extern void CinemachineCollisionImpulseSource__ctor_mA92E5BF4E42F12175524C6EB79EB02079E81BA6C (void);
extern void CinemachineFixedSignal_get_SignalDuration_m804E774BF6108692BC4EC742EEE07569BD591220 (void);
extern void CinemachineFixedSignal_AxisDuration_m6A1BE86CB19EF1D84E44F2B6C66BAE1CC5E9F802 (void);
extern void CinemachineFixedSignal_GetSignal_m16036EE6491457C8A7BACA89E2B257C4BBBC9ED7 (void);
extern void CinemachineFixedSignal_AxisValue_m6A6D0895F7DB9E6DD28C2C698F3737E99F18E24E (void);
extern void CinemachineFixedSignal__ctor_mBFA1043F287A6E5E6C7BC5F2CA9C935450B1C544 (void);
extern void CinemachineImpulseDefinition_OnValidate_m61EEB2D519F6D2BFCDA1449456AD12F7451E1C9D (void);
extern void CinemachineImpulseDefinition_CreateStandardShapes_m278EDDEC7FA48FB02874E2E3ADA877D732824521 (void);
extern void CinemachineImpulseDefinition_GetStandardCurve_mC3C6534C54B283F4F0A99DCF6723A92D91ED2E1D (void);
extern void CinemachineImpulseDefinition_get_ImpulseCurve_m6829C232BCD8DFB595C028C6B85A151802BD27F0 (void);
extern void CinemachineImpulseDefinition_CreateEvent_m5169990F921BB0D026C6E099EB965C09E4A1D45E (void);
extern void CinemachineImpulseDefinition_CreateAndReturnEvent_m5B2DD42B512C246F6C88768AD159DD12FE35261C (void);
extern void CinemachineImpulseDefinition_LegacyCreateAndReturnEvent_m4674B4481E5C8749D58034A0EA0B83602FFFEB1E (void);
extern void CinemachineImpulseDefinition__ctor_m0DF7DA7FF9F2C545FF621FD0482024C5924B4E30 (void);
extern void SignalSource__ctor_mB47DBDB4C80B809FE4D4D14A82A1B39FDA2489D2 (void);
extern void SignalSource_get_SignalDuration_m12D1741D975D3F72759A3B8EE376351AC4B0329E (void);
extern void SignalSource_GetSignal_mA630937F85C20FACD1C86A64B725C15880E07468 (void);
extern void LegacySignalSource__ctor_m47E5D665D6EA8803261F56D4162B76E49944435D (void);
extern void LegacySignalSource_get_SignalDuration_mDCB9255ACFB20AC6A0C1A13E9C5CAB3D8AD79CA7 (void);
extern void LegacySignalSource_GetSignal_mD04FECB2C2533D50EED31458CA710D96F1D9EC18 (void);
extern void CinemachineImpulseListener_Reset_m96EA430B6241450795795B1397770C9BFCE3B168 (void);
extern void CinemachineImpulseListener_PostPipelineStageCallback_m683B33008E9FA1A00A22D1FB37199BAFE24F2D7F (void);
extern void CinemachineImpulseListener__ctor_mA63E963CAC79E2C50550D22528C3D8B5625E7B64 (void);
extern void ImpulseReaction_ReSeed_m80E36F4DD6AA9D7387796A4D20B73F1D530D0CD7 (void);
extern void ImpulseReaction_GetReaction_m3DEF1931AAC8E9F2EB1BDF6A93DDEA0CE2E074C8 (void);
extern void CinemachineImpulseChannelPropertyAttribute__ctor_mE648AB30E624A586DA267734AEF59D2C8F1A02F0 (void);
extern void CinemachineImpulseManager__ctor_m346881347C73DCD5000686C9408FE6F3C0BE3E6E (void);
extern void CinemachineImpulseManager_get_Instance_m796E40ED8A795EAEF6828F1E78BEC548AE948311 (void);
extern void CinemachineImpulseManager_InitializeModule_m5C60A823F87B7A975AA794120FCFF07B6779B1F9 (void);
extern void CinemachineImpulseManager_EvaluateDissipationScale_m86364CA6D5E4CDAB1DCDF0BBD2D045DE909F1FF0 (void);
extern void CinemachineImpulseManager_GetImpulseAt_mA009FA51EB3967996FBC3CE30AB092742058E377 (void);
extern void CinemachineImpulseManager_GetStrongestImpulseAt_m8B6FEF2DB52B1CE2D97E6906773E16B4672E867F (void);
extern void CinemachineImpulseManager_get_CurrentTime_mE288B4778997FF9457672B6B6570408D3D5DF6DB (void);
extern void CinemachineImpulseManager_NewImpulseEvent_m8EF13DBA4E6CC92CD749D65DBA28BFB565F4F082 (void);
extern void CinemachineImpulseManager_AddImpulseEvent_m2086866EF411B9D5C81CFD63B4EE72E2FAF457A3 (void);
extern void CinemachineImpulseManager_Clear_m078DA8D56609BD863014346C37E39907E315DC86 (void);
extern void EnvelopeDefinition_get_Default_mC645BD6D012F136B443ED09B518D2C80801B2E65 (void);
extern void EnvelopeDefinition_get_Duration_m99ECD0F243185637F28F813DF6C7E4D8B8E22DA8 (void);
extern void EnvelopeDefinition_GetValueAt_m321C9E091D2281616FB23F0217C448A1E748F8C5 (void);
extern void EnvelopeDefinition_ChangeStopTime_mCD399ABC589C3A7085DF613BEF7D658D3F5F4537 (void);
extern void EnvelopeDefinition_Clear_m9D7A2C7235329CDC066F74184252D5042A884307 (void);
extern void EnvelopeDefinition_Validate_m3D386A841D7D70AF6BA8418C923105610ED5E243 (void);
extern void ImpulseEvent_get_Expired_m678FF8A3DA2C847006E5B8B7F0C026AD8BB998F4 (void);
extern void ImpulseEvent_Cancel_m9040AAA5618EF16159A4CE1055A4A8F8C8B40A8C (void);
extern void ImpulseEvent_DistanceDecay_mFE048B4DB8487B3B57F88F32559FBAA1C974360E (void);
extern void ImpulseEvent_GetDecayedSignal_m0C1E1BC9D9DBD3CF9502F2A78E9CE76C88CB1DC0 (void);
extern void ImpulseEvent_Clear_mC722697D5FF4F4CCB6310785B3B0B2A0E942256E (void);
extern void ImpulseEvent__ctor_m928FFFDFEC1E724119D9E2A00A83ADEC7A123C56 (void);
extern void CinemachineImpulseSource_OnValidate_m2EB54151674202C38E0214B275D66F11AC9824E5 (void);
extern void CinemachineImpulseSource_Reset_m82EC4F453F875ABCD307D0C776D78191F7805F0A (void);
extern void CinemachineImpulseSource_GenerateImpulseAtPositionWithVelocity_mE36667217B861ECCDD200C561D35F8CE35179DD1 (void);
extern void CinemachineImpulseSource_GenerateImpulseWithVelocity_mE371B43094774C20DE1B47CB1B07E7CEB8E09483 (void);
extern void CinemachineImpulseSource_GenerateImpulseWithForce_mB1854F631FD1CB51EB28E04696BE1093E0B9CD2E (void);
extern void CinemachineImpulseSource_GenerateImpulse_m636EBFFB7F218AB71EC03630646C17FD7D1ABC52 (void);
extern void CinemachineImpulseSource_GenerateImpulseAt_m849718CB5F4D37BC6C7A742672E06E4617708112 (void);
extern void CinemachineImpulseSource_GenerateImpulse_mA2E8EB5EC05355CDD6FA14D472E7992D5018447F (void);
extern void CinemachineImpulseSource_GenerateImpulse_m2942BE53CBAB6BEFFF94DF5E95BCC03630474622 (void);
extern void CinemachineImpulseSource__ctor_mA2237C72339A5405AA8B06F8761D67103D291C6B (void);
extern void CinemachineExternalImpulseListener_Reset_m9B690DFF8FEB9B1B23E12B266FA448B07985845E (void);
extern void CinemachineExternalImpulseListener_OnEnable_mE8ECB30D4E61A0C71671D3DAD15023B537980C41 (void);
extern void CinemachineExternalImpulseListener_Update_mFCA27C65AC1A2BF05F5C55F6C52DBDA71B2A4B7F (void);
extern void CinemachineExternalImpulseListener_LateUpdate_m65350EDEDA526E465D508EF3E4E8BA85E646EA84 (void);
extern void CinemachineExternalImpulseListener__ctor_m41E8BEF5EADA91C4A457DF9FDEE4C6B5E031B098 (void);
extern void CinemachineAutoFocus_Reset_m96B425EE2326AED45EA64753CC3851879E7D0867 (void);
extern void CinemachineAutoFocus_OnValidate_mA804D0D3FC44012D377F3113618AE5F33CCB9E79 (void);
extern void CinemachineAutoFocus_PostPipelineStageCallback_mDBBFFE344F5CF3367F2E490C324CBCBDB6751C0B (void);
extern void CinemachineAutoFocus__ctor_mA74C9E0B3893D5739CFB28B42D5979119BF553DD (void);
extern void VcamExtraState__ctor_mFBFE5A0B1EFAAB4A2FF1B5BECDFF196D4E527669 (void);
extern void CinemachinePostProcessing_get_CalculatedFocusDistance_mFA0B8E9F02700B7F2033132A4A6E666A1FBBF696 (void);
extern void CinemachinePostProcessing_set_CalculatedFocusDistance_m8F46D8AF5C005DC67C867D8905A0E73F272E3427 (void);
extern void CinemachinePostProcessing_get_IsValid_m685E482D072ABD987A1F368153EF76E1A06CFB78 (void);
extern void CinemachinePostProcessing_OnValidate_m3C525BA4FBE5DC8119616B9D6D2A7E9DF43F273E (void);
extern void CinemachinePostProcessing_Reset_mC377941DEAE2331F0D77F79D7FAFFE859ED423A6 (void);
extern void CinemachinePostProcessing_OnEnable_m57FE0912173FD4BABD56C3D2650B386EFC434207 (void);
extern void CinemachinePostProcessing_OnDestroy_m9D19AB44DE33BA0C13FCD9F8A239386322A6223D (void);
extern void CinemachinePostProcessing_InvalidateCachedProfile_m5C8C9BA51D065B3EED70E5ADAA4513BC00EB5275 (void);
extern void CinemachinePostProcessing_PostPipelineStageCallback_m44D91FDA3230310E1319DE146C0134A7945FF240 (void);
extern void CinemachinePostProcessing_OnCameraCut_mFE200B8A8EE07C12B30A82D2DED264DAC48E9D9E (void);
extern void CinemachinePostProcessing_ApplyPostFX_mFCB3FAEA8EC0B2A1B9A52CD165A1FB50B667F798 (void);
extern void CinemachinePostProcessing_GetDynamicBrainVolumes_m9833A144683205F63999571D688E40417B3E0541 (void);
extern void CinemachinePostProcessing_GetPPLayer_m1ED3492C2E2E9835DE3ADCC1C9B4D97AFA769C2D (void);
extern void CinemachinePostProcessing_CleanupLookupTable_m7698E5BAEC4E368B001966C62F23F25AAA499081 (void);
extern void CinemachinePostProcessing_InitializeModule_m037F8387DB48F3B21BF5E9E7B157E8DE8F7BC99A (void);
extern void CinemachinePostProcessing__ctor_m489BE10A349C4A8BE65B3D46C6178FBBEBA06376 (void);
extern void CinemachinePostProcessing__cctor_mE83704D20B93369F35046DCC9301695BF1C5B3B8 (void);
extern void VcamExtraState_CreateProfileCopy_m42717CA4C0D54ADF3A72793D7790855D4AE7C7E7 (void);
extern void VcamExtraState_DestroyProfileCopy_m48BF732B6A064E8E305A8402A557389DDD2E73A9 (void);
extern void VcamExtraState__ctor_m01F1D9B616E656157F683CC1B749D57E8CA0D8BC (void);
extern void U3CU3Ec__cctor_m279199F45F011561613351E8B1856EA36AAEE9DC (void);
extern void U3CU3Ec__ctor_mEA767480AB6B7036B7DA0B5F982B9E1D7FD9EF2E (void);
extern void U3CU3Ec_U3CInitializeModuleU3Eb__29_0_m8FA681BCDFDF5D3015A0CBF20C5987003C55D149 (void);
extern void SaveDuringPlayAttribute__ctor_mF55C962144DC218836F6D7BD4EF05E3C89865D65 (void);
extern void NoSaveDuringPlayAttribute__ctor_m5407A216DEEB663D7D97B748271A7DE91D0400DF (void);
extern void Point64__ctor_m5EB70A4258455C7BF2A8F9988906B13BEE9CBD33 (void);
extern void Point64__ctor_m9A2A8514FA57059A1E35A472B12C5FAADB791175 (void);
extern void Point64__ctor_m42E3840AD7E7CB739E24F994EC2A80AD9C82B6B5 (void);
extern void Point64__ctor_m9E3602EF25ACD25C87C1E8F8A1DCEB6195583EF0 (void);
extern void Point64__ctor_mB75675457F560A9A56BD0E83BF82BE4D0619D797 (void);
extern void Point64__ctor_mA6D9ADE4B7E4E625A510BB2A38666035C2ADB439 (void);
extern void Point64_op_Equality_m533566B7842BD7883D47B43A2B2367E4250E1FFD (void);
extern void Point64_op_Inequality_mB4B9424D7E64E33071DC3260A52399506316BBE1 (void);
extern void Point64_op_Addition_m5267335FA0B03821E9F16775C5A3BB997225B109 (void);
extern void Point64_op_Subtraction_m40F4346AA88B2F98969C96C88DFFC8334E11836C (void);
extern void Point64_ToString_mD638C7638543E53040AF9B8F5EBDBD1297CFA5C7 (void);
extern void Point64_Equals_m9867809E23F9C6E38A9C016EA31DCCD959A9FA16 (void);
extern void Point64_GetHashCode_m78A3E7E42A2DE0050262E6CA7AC488752DCBB557 (void);
extern void PointD__ctor_mD7F71F13290EE30EC27140C79FAD97DBBB6AB693 (void);
extern void PointD__ctor_m76B965465D81252C8CA9EA4AA9DB761DAABCACF6 (void);
extern void PointD__ctor_m9F826315168AF7531D2B9B1B6BAC781F3B232845 (void);
extern void PointD__ctor_m768C67DB1D245EEACC1C035DB6CCB6307E718D61 (void);
extern void PointD__ctor_mC60B9B78B15396C7BF505A9E4CAFA7A28B2D97E2 (void);
extern void PointD__ctor_mD6A84584FCB6FA3FCE7C369420AC192B55B0764A (void);
extern void PointD_ToString_mD6DD8937CFA667AB2E1993A6BA9B691CF01B4816 (void);
extern void PointD_IsAlmostZero_m00428B2A16410BB43461565457CB8E5B6B9FA95A (void);
extern void PointD_op_Equality_mC15DC7291675D4B72B63C292A08C3EC8FDFDFA44 (void);
extern void PointD_op_Inequality_m62912227B963100E672B5AFB05B0131ECF9C321E (void);
extern void PointD_Equals_mEE31449D9D892E12AD7DFBC4338083BCF2EA2C95 (void);
extern void PointD_GetHashCode_mC3F108E35D20A3BC90FCB8D63D3960E4357016B8 (void);
extern void Rect64__ctor_m45E1192E0667467D907B8EAC1DF491F87BE35AE6 (void);
extern void Rect64__ctor_m5CC7EB6188A744E984EF75A80C475F69F8CC36C7 (void);
extern void Rect64_get_Width_m14E5462BBF19831F4A1ECCC4AC774F742D6B9D79 (void);
extern void Rect64_set_Width_m78CE851721CBFD86A6784DBD8E3CECF49AEF7CB2 (void);
extern void Rect64_get_Height_mFA6F40B071095EFA741B587D48DFA2DF6B213A91 (void);
extern void Rect64_set_Height_mDF2C950030E9DAA14F3982D4054C7E32B016A612 (void);
extern void Rect64_IsEmpty_mA1909BCE253440E19D6FCF27BCB713B55CD0F1CA (void);
extern void Rect64_MidPoint_mBAD1B4C2A407D545A79D57DAF96A11643A116291 (void);
extern void Rect64_Contains_m491E4B0656953BDFB487F127EB689BEA614DFB2F (void);
extern void Rect64_Contains_mEA0DF8A8952D68B91A659BAE6E1CA80F095C0C61 (void);
extern void RectD__ctor_m16EAF9EACEA6C15525DC8DFFC4AC6F56D2232371 (void);
extern void RectD__ctor_m97BDBE86BF2072754834BF40952F96D8F58EF569 (void);
extern void RectD_get_Width_m36E3639CE7CAE7DBBE017C681ECC288D38D18F60 (void);
extern void RectD_set_Width_m50EC47998EADEB1E8E754DF9A5DAF7490859EAE9 (void);
extern void RectD_get_Height_m0954D6B12C7EA5D3100435F39F1DC9DA97A6AB72 (void);
extern void RectD_set_Height_m57A967EDB839AF8E2AD356E31EB33C5D1D86B8D1 (void);
extern void RectD_IsEmpty_mD5E0765FFEF030C9A4F800CFA082A1C7D08276A2 (void);
extern void RectD_MidPoint_m86B304B65390BF7D14A0DAA466E000DF14B7FAAC (void);
extern void RectD_PtIsInside_m6ED1F239CFF91D99F828E9503ED112F802F4DB6E (void);
extern void InternalClipper_CrossProduct_m63AC99360F8F749E6D9B6D98D47F495AE2523204 (void);
extern void InternalClipper_DotProduct_m8500EA30A859184F30DFB8F75448FCEAC2B5D517 (void);
extern void InternalClipper_DotProduct_m5C89226B95B7774FCD9EA8D27AAA094CA2764A7F (void);
extern void InternalClipper_GetIntersectPoint_m60EB44C838395B5DB9E638A648061DF6C431204E (void);
extern void InternalClipper_SegmentsIntersect_mCE20DEDC3CFD906BB55717F83A275F68CB803DB3 (void);
extern void InternalClipper_PointInPolygon_m52292F3C90E8F1A26AC475D101AE1B26B5677528 (void);
extern void Clipper_Intersect_mF94C92319A0A709CB2CB256AD1F40000E456A82B (void);
extern void Clipper_Intersect_m4CE872D5CCF42BBDFF6073B2E4BE7BBB27B52E75 (void);
extern void Clipper_Union_m0A5D385ED9CC99B042D378D08CFDD97921B0F361 (void);
extern void Clipper_Union_m2361172BF012A55B36BE6BA104B03E352E479129 (void);
extern void Clipper_Union_m16184FA4C5C7109C189CD27B73FFD2F623F9865F (void);
extern void Clipper_Union_m77329CF78CA8EDB92B70670919CD6A00241AEF83 (void);
extern void Clipper_Difference_mD7007303D8A90017024CC78870DA6BCCBB2447D5 (void);
extern void Clipper_Difference_m63F409BC748290A884237E908E75FCCB43899544 (void);
extern void Clipper_Xor_m191AE38812AECE227B5777B508A31B11A6474A49 (void);
extern void Clipper_Xor_m4A0760A6B1E973B5C5F41D27898C7216F4DEB193 (void);
extern void Clipper_BooleanOp_m9D1DF67E67C6B736A02E08D85AC4379F6B6A96E0 (void);
extern void Clipper_BooleanOp_m48E37ABA8A6A68A7D5954373D8D25087C83A9AB1 (void);
extern void Clipper_InflatePaths_mAD15F1415A16D40F7AB9F5ABDDA397CB1F473B1C (void);
extern void Clipper_InflatePaths_mEF8773463F841C66671FC30E4C9A005D04B9FF4D (void);
extern void Clipper_MinkowskiSum_mDD1FE8F104D708FCBBCB5294C2260D907C70BF7C (void);
extern void Clipper_MinkowskiDiff_mE66C4B8A9AB90861744D427E2BD82BF96BCCA4B7 (void);
extern void Clipper_Area_m9BA24ABC1B19BC5F0332D8D460BF5CA948B002CE (void);
extern void Clipper_Area_m0AAF4070E776868EFD019DAB10D7A9811D38EA64 (void);
extern void Clipper_Area_mA48B13C9ABA02B3B0DB18E97D056DFFF1A3EF00E (void);
extern void Clipper_Area_mCA8AABB15DE439C9D4B6CCEEEF8C2A18CC25E2E0 (void);
extern void Clipper_IsPositive_m50894DF274EBCFE6626CD24AB35C54D3736923F5 (void);
extern void Clipper_IsPositive_m475B7AEA20A3DE6A89D4A92601857EA3B80B1FBD (void);
extern void Clipper_Path64ToString_m4E6A42606A4445DC8A06B439880E5B984FA6E0FA (void);
extern void Clipper_Paths64ToString_m641C0C89AE946FB1E4CD7BA0A2F7DBD3111CD03C (void);
extern void Clipper_PathDToString_m31719E09AC0CAE021B42503A6471E927FB3A37CC (void);
extern void Clipper_PathsDToString_m2B7A9EBDF254D1F1A8914A3DC127A18FA7C8EA54 (void);
extern void Clipper_OffsetPath_m0CE176A4EE6A2DE38F50C89B640D0F485928F4EB (void);
extern void Clipper_ScalePoint64_mC0CF74302270EFA4148EE58A507D4BB583647423 (void);
extern void Clipper_ScalePointD_m69EDCED4FE50FF270E40B257F11140C13E8BC309 (void);
extern void Clipper_ScalePath_m8551F85D9E9B91E619954C35C296646584E69055 (void);
extern void Clipper_ScalePaths_m99176F63BDBD6BCDFF3C8CDD06FB935E61F76659 (void);
extern void Clipper_ScalePath_m48C4CEACA1D22578E87CF5E7621835A6AC26014C (void);
extern void Clipper_ScalePaths_m04C8A48A6E2F88B3B7CA81849969928E0226F545 (void);
extern void Clipper_ScalePath64_mCFF8D4326636E6B8EB54E0EC6E7558A38D3B7955 (void);
extern void Clipper_ScalePaths64_mB85B01BB3B1DC0D08BABB6CF29EF23B832574398 (void);
extern void Clipper_ScalePathD_m2FB6CAE621463C3FB7089EE31195817E5C4B8BE6 (void);
extern void Clipper_ScalePathsD_mB2834B99D7B27D4671227249101FED7EF9E8E554 (void);
extern void Clipper_Path64_m0F4D3A81EDF7BB7DC5F5E3846468E3017C0DB715 (void);
extern void Clipper_Paths64_mE360CA8F10558E9B3E2F4F2716E26D51F72E915D (void);
extern void Clipper_PathsD_mDDA011C7723F83F6F6A10F1BC146D4EA96C3950B (void);
extern void Clipper_PathD_mF3A22497B45643C6D934F4AA4C18DF95567DEDBC (void);
extern void Clipper_TranslatePath_mC95214554AF35F34FA29FEDFB46ECCD6F6518447 (void);
extern void Clipper_TranslatePaths_m35B30F1CD9E8C1A820BC6FB928EA1EEEC4972CC4 (void);
extern void Clipper_TranslatePath_mCAAF79D4C9B32CD8956B229F5FE960FF9BC76469 (void);
extern void Clipper_TranslatePaths_m81ADA4C18BD4032046CFC4BE0E7AB52A023D38B6 (void);
extern void Clipper_ReversePath_m4D70229BD383312F335D92B6C3CB76718E9DFBA4 (void);
extern void Clipper_ReversePath_m66747DE2F39798988E23316BA336B7132CC9C447 (void);
extern void Clipper_ReversePaths_mAA7E16CDBD26F32506E5719CE2C3A4B9114D6C81 (void);
extern void Clipper_ReversePaths_m9F304C90B74905FDC781490540B790612CC77F87 (void);
extern void Clipper_GetBounds_mA0B74D94F68141CB4961856B51407A2754813666 (void);
extern void Clipper_GetBounds_m83E232ABBB124699E8D8EE8881BE1ABDFAE889AB (void);
extern void Clipper_MakePath_m285E9191B65BAAB0266E5B7C3C069DA704C12423 (void);
extern void Clipper_MakePath_m1857FE7CA04BEFD50D8C5B234DF5056975999E03 (void);
extern void Clipper_MakePath_m0CEE58E0491BFEED6CA779FA6835B5916DA0E502 (void);
extern void Clipper_Sqr_mD546F7C4FDC2B7072B0327B4330EC82523B81A1C (void);
extern void Clipper_PointsNearEqual_m7893EAD949C5328933459DA488F6E171E48BAC35 (void);
extern void Clipper_StripNearDuplicates_m37A1A924F73A544263556741217BD2B2979C5A19 (void);
extern void Clipper_StripDuplicates_mBBDE77B1208D6591ED82AA122040AE02A2E453B6 (void);
extern void Clipper_AddPolyNodeToPaths_m2CF75BC3FE843E01D20456262C6DAFE720CCE932 (void);
extern void Clipper_PolyTreeToPaths64_m5A2F579CAC271943EC008F7D3782ACAB5CCAC07E (void);
extern void Clipper_AddPolyNodeToPathsD_mCAF9A5128E9CBCE9F0E478DEC8B6B22ADF2DC195 (void);
extern void Clipper_PolyTreeToPathsD_m0C7B9B09C84E70A8E4D3BA67BFC3B7522B0152F6 (void);
extern void Clipper_PerpendicDistFromLineSqrd_mC9D7F4A630B546FF1FCCB70C87D956CEC794D8FB (void);
extern void Clipper_PerpendicDistFromLineSqrd_m8A169591B07F12AF0AB29306C2A896F51631E53C (void);
extern void Clipper_RDP_m1D9DA0B586C7A42D83D28CE1C9E56E8BAFEA3467 (void);
extern void Clipper_RamerDouglasPeucker_mB210EFB97FC834C4B51D0B5F490098924B42EA9C (void);
extern void Clipper_RamerDouglasPeucker_mC582E722B714F238E100524CBAA1B0F7E9D6E27C (void);
extern void Clipper_RDP_m809A525B2B3DAF7D797903F9B5993CF53490E80B (void);
extern void Clipper_RamerDouglasPeucker_m3D986575659388334F67F744ED39D492998BC610 (void);
extern void Clipper_RamerDouglasPeucker_mE836CF9E56A361BE6992B4541E169202FE75709D (void);
extern void Clipper_TrimCollinear_m364C4AC3B1171FAF18904DB9CC708C652BF9B8AE (void);
extern void Clipper_TrimCollinear_m34D4351793CDE4FAC94038DB2BD5D4DF17CABB7F (void);
extern void Clipper_PointInPolygon_m6661346977C51B35D654E5887F4265800B51CE90 (void);
extern void Clipper__cctor_mC8D7A6F74A5789A5D161E65A27131C5DEED19777 (void);
extern void Vertex__ctor_mBED6850150FAF517F7AD9CF8E41BE0B4CD8BCFDF (void);
extern void LocalMinima__ctor_m5F6CBC2F2A77AF724F270687C77B9CB6C6DC72FC (void);
extern void LocalMinima_op_Equality_mFE4055A76B5EE542E678B40AF00EB987009A6D88 (void);
extern void LocalMinima_op_Inequality_m49A7290CFA803184D0D35EB428356BB42D1EFB48 (void);
extern void LocalMinima_Equals_m1277D897943A6BFD7E7D863054002C607077D985 (void);
extern void LocalMinima_GetHashCode_mCD15DB133E348BE5D7EF4CB5F967A13734BE3981 (void);
extern void IntersectNode__ctor_mF58FC6DC9D4776F4A5823130E7186E55DBA70269 (void);
extern void LocMinSorter_Compare_mC60E39AE99F875F5459C66078950CF1AA50D67B7 (void);
extern void OutPt__ctor_mA2A33D081F46CC410EF22AAC73F09F43D83C7275 (void);
extern void OutRec__ctor_mAE8AC3021E727E86BDE4EDF6F539DDE53E8B0BA5 (void);
extern void Joiner__ctor_m93DD3E66087A5A327F98CD391CAC4405ABF880B6 (void);
extern void Active__ctor_m8A418DBC21CF856F68CCEF069C21E4CF0E81BEE7 (void);
extern void ClipperBase_get_PreserveCollinear_m884F36E93A9CF2925D7A90A47D0D4879E9CBF170 (void);
extern void ClipperBase_set_PreserveCollinear_m2EA8A84D4C894541ACD80670231B8768FB5A923D (void);
extern void ClipperBase_get_ReverseSolution_m274C0141939DE41147535AACFE4E7354C9F3738F (void);
extern void ClipperBase_set_ReverseSolution_m33289691FAC628FBAD82766199C09EEA6B7AD985 (void);
extern void ClipperBase__ctor_m3377C3B7E8707CF9691174B115F0755B0860F64E (void);
extern void ClipperBase_IsOdd_m278170AD17D458CC5D12AADB4BE103778039530B (void);
extern void ClipperBase_IsHotEdge_mC676B22C061AFE833E03471FEFD88B350FC858E2 (void);
extern void ClipperBase_IsOpen_m22951C2BE3620958046B18FB9E6A47733B0E5050 (void);
extern void ClipperBase_IsOpenEnd_mA850FE8D0993EEDDF1F26146B6B3D76EF74B8EB8 (void);
extern void ClipperBase_IsOpenEnd_mA637897E566D52FF15B8E999A7F8517AA577BB60 (void);
extern void ClipperBase_GetPrevHotEdge_m996312E7DFA4F884B18866CA2B6EFADFBB114EBD (void);
extern void ClipperBase_IsFront_mD28DA953694FF6117E55E9D6933C133FDA54744D (void);
extern void ClipperBase_GetDx_m08AA79E73C3DAD89AC4DEB5486B91700EE323998 (void);
extern void ClipperBase_TopX_m8A62D77000F391837552843AC07FE4ED150D11F0 (void);
extern void ClipperBase_IsHorizontal_m1692D7867C953908856CF62030ABB4CB9DE75685 (void);
extern void ClipperBase_IsHeadingRightHorz_mDB2204EA300302A0D408198ABF2AB897EA5129C3 (void);
extern void ClipperBase_IsHeadingLeftHorz_mB84DBA5B7A37878D5CAC69B302BD4B74A67EA400 (void);
extern void ClipperBase_SwapActives_m8A5C8081EEF70929E68496D8C986C5A116E56618 (void);
extern void ClipperBase_GetPolyType_m741BB0E77410109734055D00175B59573BF503FE (void);
extern void ClipperBase_IsSamePolyType_mDE785DEF1C48563916A08C4A7D276ED8BF22F6E1 (void);
extern void ClipperBase_GetIntersectPoint_m4AD57489985047E3EA9B9EA37774FBB3AB9D8BB8 (void);
extern void ClipperBase_SetDx_mDF7110C172D48EA818545F25637C05E7CCD35C17 (void);
extern void ClipperBase_NextVertex_mBED342F034C90AE536EDBCC45A0BB200E71EC9F7 (void);
extern void ClipperBase_PrevPrevVertex_m8639FC76AD6D52E6527D4DF989F42011359CED88 (void);
extern void ClipperBase_IsMaxima_m11E2EB952D562FD426061D1A37FE66DB62B6E2BD (void);
extern void ClipperBase_IsMaxima_mE23D8D08AAF2D240C9523F9F9131BE9C4431AFD2 (void);
extern void ClipperBase_GetMaximaPair_m6B24637D9F9BAE18F78CC03D80257AAE21E64D49 (void);
extern void ClipperBase_GetCurrYMaximaVertex_mC64924C09B518BC2D2D79F09407EFA6C1E3C74DF (void);
extern void ClipperBase_GetHorzMaximaPair_m3385748EE2C638B47280CF7E5806C9F162B03C7C (void);
extern void ClipperBase_SetSides_mB7AC14B9DBA135C2A193351634A85C157CF7AEF4 (void);
extern void ClipperBase_SwapOutrecs_m04F635A703EC3533740CAD6FFAD906D87C339F25 (void);
extern void ClipperBase_Area_mD42D1A1C6F68B13F4B4C60A05FB6127B5A305AB3 (void);
extern void ClipperBase_AreaTriangle_m5AA07E4FF02A9897B91F5D659B0E3B14BA086B0E (void);
extern void ClipperBase_GetRealOutRec_m61E940B13D222A6A1D8AC76D17D00B224E6FF536 (void);
extern void ClipperBase_UncoupleOutRec_m22890408939D336C1ABA042B8D7953FE4B908089 (void);
extern void ClipperBase_OutrecIsAscending_m178DAD1F829ECA431F336727C1418E072D1B1BB5 (void);
extern void ClipperBase_SwapFrontBackSides_mC1256FBEB4A1C15383A9CCC734C67BA3165439E8 (void);
extern void ClipperBase_EdgesAdjacentInAEL_m0CB19434574D3B2098FBB68110FAC12026D5D9B5 (void);
extern void ClipperBase_ClearSolution_m8EAEC4BA1F5DC802EC73B232855734F0029AD98D (void);
extern void ClipperBase_Clear_m233C5BA84AB1788555678E9BA228B2E8BB20561F (void);
extern void ClipperBase_Reset_m42AB438C536535E39288ED45BD0521938F0833E1 (void);
extern void ClipperBase_InsertScanline_mFC659C51CC122A800C176F131581A7291A3B4D3E (void);
extern void ClipperBase_PopScanline_mB2C1A5CDC638F5F7294BD32B4D2B685A4EAFB873 (void);
extern void ClipperBase_HasLocMinAtY_m7F529905CCFD4737E098323762F806C889E06C9E (void);
extern void ClipperBase_PopLocalMinima_mC71F5CEC5E4AE28AF14AF92A1DF95D3AA1576745 (void);
extern void ClipperBase_AddLocMin_m731137148E1C27058E7BCF2C63D943B193041B84 (void);
extern void ClipperBase_AddPathsToVertexList_m60AC0B010EBEE7F3B44A84A68798DA2D29E15364 (void);
extern void ClipperBase_AddSubject_m1ADB44258541E8F9689EAD6A84AC472C3904B44E (void);
extern void ClipperBase_AddOpenSubject_mF9D1FF3935EC5272EAEED601E0EF2C469A9961D0 (void);
extern void ClipperBase_AddClip_m0CE6DA2418F5178CF6243CA92826DEB334563986 (void);
extern void ClipperBase_AddPath_mC229D797EED38746B7FE62EAD24FB8AEAC36B728 (void);
extern void ClipperBase_AddPaths_mABEC70A7EE5FD36A491E41E81B7D498B93CC6011 (void);
extern void ClipperBase_IsContributingClosed_m1352FB40656F72475E1439CC74BE1C399017439B (void);
extern void ClipperBase_IsContributingOpen_mD76C8A801BD4E2AF141A4FC5DB27E92CB9B8806F (void);
extern void ClipperBase_SetWindCountForClosedPathEdge_m9D6BC44B172B72DE8FE205C88690B6906DC4605C (void);
extern void ClipperBase_SetWindCountForOpenPathEdge_mBE168497A0DA50A5A651B1588103AC33011583B0 (void);
extern void ClipperBase_IsValidAelOrder_mAB7B29B92A678F18A13EBADFCE4998C923036293 (void);
extern void ClipperBase_InsertLeftEdge_mDBD6FB3F63CFE665418599B8CD37DABB33EBDD3A (void);
extern void ClipperBase_InsertRightEdge_m3BD5F4A587007979CF9FE9F2C686CAF45A81ED1F (void);
extern void ClipperBase_InsertLocalMinimaIntoAEL_mEEC6A55D2033DC46DCF7C242030FFE65B8F647D7 (void);
extern void ClipperBase_PushHorz_m1827650F0B60C16A04B29DD61E3F1C383A8BA848 (void);
extern void ClipperBase_PopHorz_m644DC7AA9B43A38727649817D484A4D989E2BDDA (void);
extern void ClipperBase_TestJoinWithPrev1_m5ABC4B433FC41FA962738EC5A21513A8ABEE2F8D (void);
extern void ClipperBase_TestJoinWithPrev2_m6D7D06FF7C96493CD844803AEBA96A2D332054B5 (void);
extern void ClipperBase_TestJoinWithNext1_m6079D22ACD17904191CDADF68323F00A2341D614 (void);
extern void ClipperBase_TestJoinWithNext2_m1EEE0E9451F97AAE1E3CC23ADB1D8BF32F87E077 (void);
extern void ClipperBase_AddLocalMinPoly_m7EE4107F7C5A0192265A5969AC80781F0FCCF90F (void);
extern void ClipperBase_AddLocalMaxPoly_mC7ADDCFEAC60A745D30BC4ED674F18B619BEEE95 (void);
extern void ClipperBase_JoinOutrecPaths_m20C9C5FBA4EEADEF857734A83AFC302293F56E35 (void);
extern void ClipperBase_AddOutPt_mF63FF842B322347CC9EF7657B217DAF69E6F2DD4 (void);
extern void ClipperBase_StartOpenPath_mB5491561FB23DAB237F04B58CF3FE7D74C2F9CE3 (void);
extern void ClipperBase_UpdateEdgeIntoAEL_m37DA3969746C4FE39BA5EBF1D58A52BE9AE5BA87 (void);
extern void ClipperBase_FindEdgeWithMatchingLocMin_m10F0397F43440BEFE3BB90CDAA5CC17EE93DEF20 (void);
extern void ClipperBase_IntersectEdges_m782CE1843263C1B46A447B9AD7D22AC52F8658E0 (void);
extern void ClipperBase_DeleteFromAEL_m6C3D2F623AD0405D571F21AB26887499487E7843 (void);
extern void ClipperBase_AdjustCurrXAndCopyToSEL_mE1C8C6971748EB474A975F6572BEEA01D10B0068 (void);
extern void ClipperBase_ExecuteInternal_mE2B20F005B3D1BBB64245576D0AD138C46388C21 (void);
extern void ClipperBase_DoIntersections_m79E1D7039EBE16E83976330EB912E067FCB52B77 (void);
extern void ClipperBase_DisposeIntersectNodes_m6BEA90C33EE3005FD9DA50BC4815D0564D61C022 (void);
extern void ClipperBase_AddNewIntersectNode_m837BD1744A6942E571F42E6DAB8F1DE5407A22D9 (void);
extern void ClipperBase_ExtractFromSEL_m5D22C9126471B9AC1345BBFB37BE7A69AB48C55F (void);
extern void ClipperBase_Insert1Before2InSEL_mF9CE36D808835BD0A0A7F245C9702E8F9F1BF9B9 (void);
extern void ClipperBase_BuildIntersectList_mBC017D612CACD60B48A5D14E2A87FC5C34B3D6A7 (void);
extern void ClipperBase_ProcessIntersectList_m3BB4D0746870EB2FFC947D0D0E221A4EB6A93DDD (void);
extern void ClipperBase_SwapPositionsInAEL_m73AB50026B0B13D36AB7D7999FF156E69F70CE16 (void);
extern void ClipperBase_ResetHorzDirection_mD051239C59ED695641E44B5793E48C51AFEF794F (void);
extern void ClipperBase_HorzIsSpike_mE363A87C004376AE41FE89BFC0D646BBEF7A6C60 (void);
extern void ClipperBase_TrimHorz_m96375B5B9BB8E36F05C55072166ADD081CC27F02 (void);
extern void ClipperBase_DoHorizontal_m89D7E0E0D1BE0B52575E30ED668384A3382D74ED (void);
extern void ClipperBase_DoTopOfScanbeam_mB8542D876AF2351848FBDB064DD26819BB24009B (void);
extern void ClipperBase_DoMaxima_mBCD63C7B9D595BA8BAE6BBD02C15976C609D939B (void);
extern void ClipperBase_IsValidPath_m4A6A4261CD2656BBEA994974D744CC1970C6FF5B (void);
extern void ClipperBase_AreReallyClose_m0CB08A087791CDE2D6CBE06ABC59245505276AA0 (void);
extern void ClipperBase_IsValidClosedPath_m9BDB793958F77047BC6943774941AA425E4D6BD1 (void);
extern void ClipperBase_ValueBetween_mC7E79835A793785D52B917856834D5CE16011108 (void);
extern void ClipperBase_ValueEqualOrBetween_m21550523E9ACF5F8E0DE003B01B4AAEF5162A334 (void);
extern void ClipperBase_PointBetween_mBA693B2881DB0E9A63501D90012CAEC7C1C327E5 (void);
extern void ClipperBase_CollinearSegsOverlap_m6A09AF2E2328DC8798F54A56821FCBCFACE81348 (void);
extern void ClipperBase_HorzEdgesOverlap_m34C01213B2415C4F352EFC2A36CCA933CEE0457A (void);
extern void ClipperBase_GetHorzTrialParent_mF8585835AD44D92F939BEF2FFE3C8EB1C6620ABC (void);
extern void ClipperBase_OutPtInTrialHorzList_mA11AC0DAC904C71C55812C9B40224C67194696CA (void);
extern void ClipperBase_ValidateClosedPathEx_mF64A4DD8944AC26C1828F0D7A32D34DDCCD1BD2A (void);
extern void ClipperBase_InsertOp_mE917A3A84A18833FFBA1BD95153F9B70A1735D9D (void);
extern void ClipperBase_DisposeOutPt_mFEF5EB205D4C5A47B36ACB86411720F87CB06E9E (void);
extern void ClipperBase_SafeDisposeOutPts_mCC7278FF462786ECFB1F0299093027A32030C958 (void);
extern void ClipperBase_SafeDeleteOutPtJoiners_m98AFF4E0A2FE970CA38243C711FF3EA5B9A6A00B (void);
extern void ClipperBase_AddTrialHorzJoin_mC14D3969C6AB0B939DE1282424011D42E468A492 (void);
extern void ClipperBase_FindTrialJoinParent_m54FA0A5C86A2C5E139421E7CA4D25F249F9C6FDB (void);
extern void ClipperBase_DeleteTrialHorzJoin_m1C099923EEAB4223461739094D3DAF400D060CFB (void);
extern void ClipperBase_GetHorzExtendedHorzSeg_m4E7926788326203C937606CAD7697F1804FDBB2E (void);
extern void ClipperBase_ConvertHorzTrialsToJoins_mA177417AF153F1813350BF5A1BD764CFD4C77547 (void);
extern void ClipperBase_AddJoin_mE74B403A7D3FCCF2F9FA1BDC84FF035BFC97D0E5 (void);
extern void ClipperBase_FindJoinParent_mCC8C9A816C714BED301C9E64BC45878621554770 (void);
extern void ClipperBase_DeleteJoin_mA50E94C741854BB15C89E22BED1C4C039BD6DE78 (void);
extern void ClipperBase_ProcessJoinList_mCFDD120CD4D1B5B32BD30ADA58376B419E805BE0 (void);
extern void ClipperBase_CheckDisposeAdjacent_mC20ED7A34CF29D813BF4E9514CC085C7C9828A2A (void);
extern void ClipperBase_DistanceFromLineSqrd_m8F139AE6DCC2087D43ABB784BC1F3A4391CBD10C (void);
extern void ClipperBase_DistanceSqr_m1E07B2BC535E14C93A3ECAC01CE2D537BD5170DE (void);
extern void ClipperBase_ProcessJoin_m1E0165E0A829ACF58B93A7BFD069111BD1186773 (void);
extern void ClipperBase_UpdateOutrecOwner_mEBBFDB524821EDBA9347DBFE47A09304289F15F3 (void);
extern void ClipperBase_CompleteSplit_mD6B84AF5B330CECD61E9F91192174E41858BA0FB (void);
extern void ClipperBase_CleanCollinear_mD6E96F28435321850F63DF37354B7B1F12967311 (void);
extern void ClipperBase_DoSplitOp_mDD082A518BB662241DFD6DDF04E5B6EC437203AF (void);
extern void ClipperBase_FixSelfIntersects_m0480C143081AC01FAD037533163A021BF870AD5D (void);
extern void ClipperBase_BuildPath_mB2C8ABFB3EE94619AC8CD435B8CF7C9DE20C5FBA (void);
extern void ClipperBase_BuildPaths_m779EAFF6AB234617F6697E15F5B5FF1CE8060F5E (void);
extern void ClipperBase_Path1InsidePath2_m581D2B694D1BDC7F7781D46187F9EB2251DAB008 (void);
extern void ClipperBase_GetBounds_mBE7C3E594E3057FF959BB035D2F5B9FE453B80A5 (void);
extern void ClipperBase_DeepCheckOwner_m5CDFD90C49FB7F862D69C9E4B66F3C2D810CACD7 (void);
extern void ClipperBase_BuildTree_mD803DF2B2ADAAB6CA91A272ACBC4D30B941C0583 (void);
extern void ClipperBase_GetBounds_m1CA8845EB91981C3EB4C5535A463801446A82719 (void);
extern void IntersectListSort_Compare_m8B6F16B72168215A62DCC94F47817F6C4A06AFD7 (void);
extern void Clipper64__ctor_mD7F57490D5A0F55A0401F504F914FF044965B7BE (void);
extern void Clipper64_AddPath_m92239EE232CD5E2C6C7C38679EF14C2A97A9D7AD (void);
extern void Clipper64_AddPaths_m4E1BA351D175D464F244AD96F66C2BB642668505 (void);
extern void Clipper64_AddSubject_m2D7944587E377D95D871F4F2CAA7DCA5DACD55DE (void);
extern void Clipper64_AddOpenSubject_m9A74F31933638F0EAA6A6695D9F2A49E7E16BF55 (void);
extern void Clipper64_AddClip_m46DC4A4A6A9B54A3553F537A8C80F3461FDAAF99 (void);
extern void Clipper64_Execute_mA5BD23FA79851FA3659B5012C257780F52857F8A (void);
extern void Clipper64_Execute_m6B9ECAA8BCCA3D11996F642C355F9E0660261A97 (void);
extern void Clipper64_Execute_m7D08F40FB34AC98BCEFB7834FFC701D21303A901 (void);
extern void Clipper64_Execute_m3E2E7B7E9B4244177C39A2C52EB44DD5697867D7 (void);
extern void ClipperD__ctor_mE7B5A7CC43655E73F67E3373AC406594237A37DC (void);
extern void ClipperD_AddPath_m4F6E5CB18285E5144D5CE23DBB9BF7212DD09B94 (void);
extern void ClipperD_AddPaths_m73F811E5AB517CD1A5090B5E92ECC27407ED239C (void);
extern void ClipperD_AddSubject_mAA6ACBE6283BFB950B00D5E106E88EBF7B9E9801 (void);
extern void ClipperD_AddOpenSubject_mE681B20FD13647FCF902E3D4C3CD7C218EE493D8 (void);
extern void ClipperD_AddClip_mECEA14DE0BD6066953DE3897FC16C350223C3FE9 (void);
extern void ClipperD_AddSubject_m540EE1963865A6C17E1E9E44F7391A872096A132 (void);
extern void ClipperD_AddOpenSubject_m4990A6FAC1F6D3B05A055CF73F3EF91E44F8E887 (void);
extern void ClipperD_AddClip_mB34D456FDE8684862D7C398143BFBD26F5C36A88 (void);
extern void ClipperD_Execute_m98CC3DEA8503C9E09D2916F1FE1C8633A4967D29 (void);
extern void ClipperD_Execute_m4DFC11A0AB84A69EC7A9A18F65C8D43B1FD6CD5C (void);
extern void ClipperD_Execute_mD83952B879D3F86C0AFD1D45B2136E5E33BD672B (void);
extern void ClipperD_Execute_m4BF3F81176D9D89D37A21F92B3C23AE724FC7360 (void);
extern void PolyPathBase_GetEnumerator_m757EA5B6B5C9CF33C793FC1688F806D6C6DDEBB2 (void);
extern void PolyPathBase_System_Collections_IEnumerable_GetEnumerator_mCD919DAA12C9FD1C70E4268BD4CD13AEA7B026D4 (void);
extern void PolyPathBase_get_IsHole_m4EF5FC1764C9E87C0B403A81417B298B89EDED3F (void);
extern void PolyPathBase__ctor_mC01752BBF2643B614C29EC03B2B1E71FB57FBBF6 (void);
extern void PolyPathBase_GetIsHole_m83FFD8CC2F9FFDA80454B443B4D3C423F49C2017 (void);
extern void PolyPathBase_get_Count_m2BE66946E74CF32A62DFD129F73ED99A9A1C8809 (void);
extern void PolyPathBase_Clear_mA5401C1B473B24F03102CC5ECCF4FA775D75F198 (void);
extern void PolyPathEnum__ctor_m301C275A82F48AD72F9B87B01CA72089822B69B9 (void);
extern void PolyPathEnum_MoveNext_m365FE87B6526A651BBE1351BBA06A52CCD89219E (void);
extern void PolyPathEnum_Reset_m34E4F4E003B88BA05CD656412BE822ED2C8A6E5F (void);
extern void PolyPathEnum_get_Current_mA6856B935140CB345D8DE716388177273D0AE4C2 (void);
extern void PolyPathEnum_System_Collections_IEnumerator_get_Current_m89CACC27AF0DA7E089DB77D83597002304CA4A5D (void);
extern void PolyPath64_get_Polygon_m09838F3009F102410EF6FD6BBC399D4591DA291C (void);
extern void PolyPath64_set_Polygon_m090CF4FBB804DD2A75FF8D672E210CA6F7A01BA1 (void);
extern void PolyPath64__ctor_m280F23C97135E84896B5AA1787EA24D1196A228A (void);
extern void PolyPath64_AddChild_m4900DF946B026D77E9A5262E861CDA436D8FECAC (void);
extern void PolyPath64_get_Child_m128FE59E349F0FD2C8511D1505AA9CB0232A8667 (void);
extern void PolyPath64_Area_m7776BA7AE19557FA4DCB252CE69145A339B562A8 (void);
extern void PolyPathD_get_Scale_m6507D170709F8C498F979CCD7BFDAB456241242E (void);
extern void PolyPathD_set_Scale_m352A10AE02AB6C2B80ABAC34664F26577C696DFF (void);
extern void PolyPathD_get_Polygon_mCD84F613699E928FA14E65E77C3F94D395374C13 (void);
extern void PolyPathD_set_Polygon_mCAD9925D6C69BA47BB32995377AC0A53148FE54A (void);
extern void PolyPathD__ctor_mCABCA9798C5C95038F016C4CAF4E94066FB4E1CE (void);
extern void PolyPathD_AddChild_m275F1CEA2ED7CAFC2B9B18EBDFAB33AE38611765 (void);
extern void PolyPathD_get_Child_m0225B909AFDDF9CD705E83F168873AC7035C669C (void);
extern void PolyPathD_Area_mC63C7A5233A3EEEC5E2B49D61A6CCB5A54002642 (void);
extern void PolyTree64__ctor_m5C532F826C31E7C7439DB5C2CFA8151E2B245026 (void);
extern void PolyTreeD_get_Scale_m8B360EBAC6EC68D9A29D0EF471AB6680BF49D35E (void);
extern void PolyTreeD__ctor_mF18D57DEAF88C2C2F5C80F1561856732C3266704 (void);
extern void ClipperLibException__ctor_m9462A5848E7FCF39A4B0D917DC1378FD9046E2D9 (void);
extern void Minkowski_MinkowskiInternal_m977C74D953AD031768A97C2D44D602F74FEAC8EA (void);
extern void Minkowski_Sum_m8A8E42B41873740061A51103B15FF88E64C32144 (void);
extern void Minkowski_Sum_mA27EEC41FBBCEB5781B3937B0C35637DE6180FBD (void);
extern void Minkowski_Diff_mD501FE629DF34294AED49942F9296C8856FB43FB (void);
extern void Minkowski_Diff_m1693CC4319EE8F9933412039B01B331E012A1831 (void);
extern void Minkowski__ctor_m6D3531CE8C2F80DA0354E69AC5FC8770D23EB5F7 (void);
extern void PathGroup__ctor_mB491E5FAB07A99A82FDD1765AFF45BB5867C6269 (void);
extern void ClipperOffset_get_ArcTolerance_mD01E65F73E83F39DBF0820E69EBB71CA767F66C3 (void);
extern void ClipperOffset_set_ArcTolerance_m417ED66D7BAF4FFF26377DE261C3357881190609 (void);
extern void ClipperOffset_get_MergeGroups_m085EBEC3841F64B2B5AA25063553CA77A65ED861 (void);
extern void ClipperOffset_set_MergeGroups_m84D491969A57C312C9EF7CAE3B18017FB1D22007 (void);
extern void ClipperOffset_get_MiterLimit_mEE41076816ED8D7E9B95C366AC3E844665C0EB36 (void);
extern void ClipperOffset_set_MiterLimit_m0265DDAEAF8FAF787BE72E0684473894D73774E9 (void);
extern void ClipperOffset_get_PreserveCollinear_m0D204F10652EAD0F5EBED348D1426B284CF343FD (void);
extern void ClipperOffset_set_PreserveCollinear_m6E40EFB4F540B828ADB03C42E890AFC07720D5E2 (void);
extern void ClipperOffset_get_ReverseSolution_m323515AFCE89F5DE51B807CDF8DCC75CE9C0556A (void);
extern void ClipperOffset_set_ReverseSolution_m6704E4F2CDC2B5DD686D83F3D125046CD46AD911 (void);
extern void ClipperOffset__ctor_mC2B401F9DCCC0D75FC04A3BE296CB5C218765117 (void);
extern void ClipperOffset_Clear_m5566299BC22F28883A844B2D14EF028BB254363A (void);
extern void ClipperOffset_AddPath_m969306160BBAF29DB547230952D82A8DBDACC438 (void);
extern void ClipperOffset_AddPaths_m432691C01083D6BF2B50C159034EEBCEF52C3198 (void);
extern void ClipperOffset_AddPath_mAE2FA2AEB1D9818F69787A1AF184D08D3200D75B (void);
extern void ClipperOffset_AddPaths_mF739CD8C0649037DDB0D1DF17A9F7C492EA65820 (void);
extern void ClipperOffset_Execute_m19AFCEF180405E790405E5C88D667CCFFEB38E11 (void);
extern void ClipperOffset_GetUnitNormal_mD87821DCE28C5287D8E2C7AE876631F13472222E (void);
extern void ClipperOffset_GetLowestPolygonIdx_m812E5758CF1130A319743FFB4B23AAAF635293C0 (void);
extern void ClipperOffset_TranslatePoint_mBAABFDF6DDD3C74DB61777EDAF9FF3CE2C6FD46B (void);
extern void ClipperOffset_ReflectPoint_m98FFDF2A94DD3D82EF4D84832183FA220B3227E1 (void);
extern void ClipperOffset_AlmostZero_m3FA32FE92D06B3D825C5A23C7C276CF8B0AD2384 (void);
extern void ClipperOffset_Hypotenuse_mBF6C34BAF86DB86331E6570F69E1D95CE21ABE55 (void);
extern void ClipperOffset_NormalizeVector_m1C3295296135C9A10C16DBBB259531286CDFCB61 (void);
extern void ClipperOffset_GetAvgUnitVector_mC9D0378C1997F1046E681797571DCFBFE7BD5828 (void);
extern void ClipperOffset_IntersectPoint_m4B59C25C54631781D081D22D2178736AB80D1B9C (void);
extern void ClipperOffset_DoSquare_m83408508052C282F823959CADE1E0E1D5E54060D (void);
extern void ClipperOffset_DoMiter_m49DFD6F57BD35879AB6BB63D68085D9F81FE0FFD (void);
extern void ClipperOffset_DoRound_m31B75D3953B4C6CC2D84BA5D10D2E97C8CA2DEE4 (void);
extern void ClipperOffset_BuildNormals_mB1AC7D53EF4066BD95311730430D04E3A5382056 (void);
extern void ClipperOffset_OffsetPoint_mEA7B92FB9B0B2AE00AB6670AC4686F657A2FF47A (void);
extern void ClipperOffset_OffsetPolygon_m085C68F71521D7963FD4CEAEEB02053CF9CB6324 (void);
extern void ClipperOffset_OffsetOpenJoined_m2260AB1498D47577582BE78F9A7489811626F2A2 (void);
extern void ClipperOffset_OffsetOpenPath_m613FDC55390337609EDD574C84927587C1F29D13 (void);
extern void ClipperOffset_IsFullyOpenEndType_mBF88D37022D48398D9FE55E9E94F7E38657C2F98 (void);
extern void ClipperOffset_DoGroupOffset_mE9AB8D44249456BB7FD8B6129247869DB92017CA (void);
extern void CinemachinePlayableMixer_OnPlayableDestroy_m028A80CB5B9851B584BA738583715CB43CDCAC8E (void);
extern void CinemachinePlayableMixer_PrepareFrame_mC5BDD90E9383712485D4E8C8E2C1B7463AA39DA3 (void);
extern void CinemachinePlayableMixer_ProcessFrame_m85F1A2389BC43CFBBB6FEE5880C87F509E43F749 (void);
extern void CinemachinePlayableMixer_GetDeltaTime_m9C79B5A40F18ED60D898D8DA0398DA7569C0F4F4 (void);
extern void CinemachinePlayableMixer__ctor_mF9D8F32BAD80EAB2BDD5E77B8C404F8D7D89873B (void);
extern void MasterDirectorDelegate__ctor_m178AAB7DC100B6474C6F6FF1EE578251EED56E2F (void);
extern void MasterDirectorDelegate_Invoke_mB16645851739385543DFEBF6C2C63F3CB816C82D (void);
extern void MasterDirectorDelegate_BeginInvoke_m8C6CCF365354890AA3F4E1AAD314A4F727657ED5 (void);
extern void MasterDirectorDelegate_EndInvoke_mB0EC894637D6F53EE9F26E78586EC17518D31AE6 (void);
extern void CinemachineShot_CreatePlayable_mEAB6AA6FF023EA6B752A730737E422FB221F0A79 (void);
extern void CinemachineShot_GatherProperties_m9EAC8210F7BEA1CE76745B07C885C7FA4D6FB18D (void);
extern void CinemachineShot__ctor_m1A3DBD1AAFF2E06D85E28F827AC34522100B08B0 (void);
extern void CinemachineShotPlayable_get_IsValid_m7A5EF626BEF91096CC497B752F54FB119AA6DC3C (void);
extern void CinemachineShotPlayable__ctor_m4C6F8B9615410D6954ECDA93A8880C5786FCDDA4 (void);
extern void CinemachineTrack_CreateTrackMixer_mF847D3583C1E75108DD71CAC50130351ED77719E (void);
extern void CinemachineTrack__ctor_m19999D2D7FCAC28D93F1E62694C5FE7DAF86B6B3 (void);
extern void TrackerSettings_get_Default_m8E415BB3D0AD83EABCABB98E592C1C14D70062FE (void);
extern void TrackerSettings_Validate_m39F755B16D1D9850129598FFDD642263727B5FBE (void);
extern void TrackerSettingsExtensions_GetMaxDampTime_m623C441E60820B00C91B790CD1C4D489CFE2405A (void);
extern void TrackerSettingsExtensions_GetEffectivePositionDamping_mBFE2AB354F87DA056A1E0131C5DBC3A935AF6643 (void);
extern void TrackerSettingsExtensions_GetEffectiveRotationDamping_mCC4F77628859B407EC524006B4CECCDDE865C102 (void);
extern void Tracker_get_PreviousTargetPosition_mF5F405294137CDA1A119EF5B756882AB84E62520 (void);
extern void Tracker_set_PreviousTargetPosition_m4BDFFF69C93C6535B2EA145D84A3B965C9653CD4 (void);
extern void Tracker_get_PreviousReferenceOrientation_m225BDC07837F9BA115290A0AEC57F14AE3A1C8D9 (void);
extern void Tracker_set_PreviousReferenceOrientation_m1FAF567E391E866E6737253EBDEADDBAAE231905 (void);
extern void Tracker_InitStateInfo_mF66FEF8AD511965D882D267C455527461F1E7A7A (void);
extern void Tracker_GetReferenceOrientation_m787693044C37713DC332241FB2108373C12FD40F (void);
extern void Tracker_TrackTarget_m8CAD3274FAD87F564E9D8D749229041EEEA07D9A (void);
extern void Tracker_GetOffsetForMinimumTargetDistance_m10279516242F65DF2226B6A5C02AEF36B7431419 (void);
extern void Tracker_OnTargetObjectWarped_m7E42DEF855CEAEE2A19BD5A670A1798715EB66D3 (void);
extern void Tracker_OnForceCameraPosition_m952EC37B25CD2FA1E1306FB1AD6A41363FA830C8 (void);
static Il2CppMethodPointer s_methodPointers[2161] = 
{
	EmbeddedAttribute__ctor_m70904321B856163FB995EF18FA4AC8DF0713A7B3,
	NullableAttribute__ctor_mAC620FEA91AFCA45A4702F215899630225CB5F8C,
	NullableAttribute__ctor_mC3A7B4A12D65E88F3FA0F8B3E6BE7F424C676E0E,
	NullableContextAttribute__ctor_m3E72024F166F94B95840C6F08F269F91A57931C4,
	DeltaTimeScaleProcessor_Process_mD05943ABEA1F32E113448687077D8B3A13EB036E,
	DeltaTimeScaleProcessor_Initialize_mC3059939F7F27B1CAFD4943C0BFFC3B4C83E2148,
	DeltaTimeScaleProcessor__ctor_m2847E957260B494B9C2DE8DF3CB413FDEFE6A23F,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m590CD099179B8C46ED732206271733292F530038,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE3032AA856CA2A08CC1F1821424B816CCE717EB6,
	CinemachineBrain_OnValidate_m6BD74D5C6476F2E9B2E6639B55D764994E43F0AD,
	CinemachineBrain_Reset_m5F9D248F18428B84D03E4A6E88B96CD7968433D4,
	CinemachineBrain_Awake_m7D6C58BE1DA5040291886D45A0EABF76EEC24816,
	CinemachineBrain_Start_m3FD7E7B86B38428745D402B049C2BBA046D90D86,
	CinemachineBrain_OnEnable_m15FE43E2D79E36F9E2821115FAC91A26000A9C64,
	CinemachineBrain_OnDisable_mF6C43DC096BDBA016A102D9E9D5CD55B37D1F469,
	CinemachineBrain_OnSceneLoaded_m613D48ED4946143A9F65C334AA9A3C70BABA7555,
	CinemachineBrain_OnSceneUnloaded_m7F73D969A70AB5261A5BD2894C92BD9EC8582048,
	CinemachineBrain_LateUpdate_m273D0B2C8D56257125FEA2E943EA7317224D52A1,
	CinemachineBrain_AfterPhysics_mACD16F9B6B975FAC12264656C6D5E1F33340AC02,
	CinemachineBrain_SetCameraOverride_mA3700DE459BC9D8CB218FC8CC36E90B1D8B545C7,
	CinemachineBrain_ReleaseCameraOverride_m7563ECE0742605E04B44188D08FA1912FA1C126C,
	CinemachineBrain_get_DefaultWorldUp_m2122F7DD2D62FFCC723124B17B10C01A002F1DE6,
	CinemachineBrain_get_Name_mDB3ED3322485D929661636716FDAE2A017240826,
	CinemachineBrain_get_Description_m170518F8C93AEB594AE4ED181D7EB91EC32B320C,
	CinemachineBrain_get_State_m06BDD08B2A3E89CEF1E79BEE970743C34380D780,
	CinemachineBrain_get_IsValid_m923DED4EC47D3F63E1B601144593D35853F72E89,
	CinemachineBrain_get_ParentCamera_m2D655360E3E08636EFDFEB870E4A1F19259C025F,
	CinemachineBrain_UpdateCameraState_m05AD0FECAA5B6700D4714155BDB5F02C72C2AEFD,
	CinemachineBrain_OnCameraActivated_m995C58054D28F478DC2C748718D39CD88DEF98A9,
	CinemachineBrain_IsLiveChild_m26488818988024D5DFE45523BCC8DF31CB3562B2,
	CinemachineBrain_get_ActiveBrainCount_m6806FFBB7509410B5EA9DB3DF9C47075FDD03565,
	CinemachineBrain_GetActiveBrain_mFBB9D0EB63B9AD7E28A0FC54872CD3921C52A406,
	CinemachineBrain_get_ControlledObject_mD932AFC116C8EC2030B6D11719E557D3A0E5C73D,
	CinemachineBrain_set_ControlledObject_mB9E57A20E50494429FE91C66E1236B2DA470F72A,
	CinemachineBrain_get_OutputCamera_m5CFDD1E8407905D23B2FB08E022A1D814D8914C0,
	CinemachineBrain_get_ActiveVirtualCamera_m0225A2B45278C98C997EFE7F2DC815EE4A097E39,
	CinemachineBrain_ResetState_mC40C0088DE2EC4EF9FCCD609A8E6398599D38397,
	CinemachineBrain_get_IsBlending_m16D72A89C93514280DEA838D441543D3EC000D47,
	CinemachineBrain_get_ActiveBlend_m2CA0763A4C6DAF8158049048079C9505F90C346C,
	CinemachineBrain_set_ActiveBlend_m4E34E5F0BFF127EE547CA007B652F7B0BE05C958,
	CinemachineBrain_IsValidChannel_mA5527B436E5E1EDA4F13A81F630528E07F19A392,
	CinemachineBrain_IsLiveInBlend_m3F13625AE4D3B68A6B1508A193518E6DE76ACC1E,
	CinemachineBrain_ManualUpdate_mA69CA0F5CEA30C0D60100535D89046D81AFF7135,
	CinemachineBrain_ManualUpdate_m5936507D04F17DA4FF4CD6328EED9782800C2BB8,
	CinemachineBrain_DoNonFixedUpdate_m79D014CBEA5F5E1FAA41E2EDBBD4AC2AA76C2591,
	CinemachineBrain_DoFixedUpdate_m2F5FCDA24AEB65DB21CF1D31714BDE168BE2CADF,
	CinemachineBrain_GetEffectiveDeltaTime_m474FC97364243742813814850864EF715EABA83E,
	CinemachineBrain_UpdateVirtualCameras_mF3CCAEC53FCC46788149976D0C623B5EC4C859E7,
	CinemachineBrain_TopCameraFromPriorityQueue_mBDD27467260B33609EC22D2D8F4D3B4EEE0D646D,
	CinemachineBrain_LookupBlend_mD524EEED25317C8A700BFD4B47528BE8C926BFD1,
	CinemachineBrain_ProcessActiveCamera_mDE9A82F45478D9A7B37A8977DA637DEC8449487F,
	CinemachineBrain_PushStateToUnityCamera_m052CF484325426A6C1124C8B185458EF62A821CE,
	CinemachineBrain__ctor_mBEA2B1A0DF89ACD59EA4FE69175763963FD3F179,
	CinemachineBrain__cctor_m4BA08B4C4D05D0F0BD522677BBA9106EC39E2A04,
	U3CAfterPhysicsU3Ed__30__ctor_m1CCD1D0619555E2C10AE28A798B7BBD6EF0FD33A,
	U3CAfterPhysicsU3Ed__30_System_IDisposable_Dispose_m9BE880E76D65195258F76FF744EC99A3BFF6B631,
	U3CAfterPhysicsU3Ed__30_MoveNext_mA11155ACA4C3322D83D1F9CCB61B76D0ED921D08,
	U3CAfterPhysicsU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB566F234A1ABA66A284CF1615B484CCF646CF150,
	U3CAfterPhysicsU3Ed__30_System_Collections_IEnumerator_Reset_mA7DD32378FEAF33372FAA887BA01B252A91017C5,
	U3CAfterPhysicsU3Ed__30_System_Collections_IEnumerator_get_Current_m34F243F1AADB96E300611381F07B87639ACE5DDD,
	CinemachineCamera_Reset_mB6F7B9372F2FDF75ECABAD75C9CFF65648EFBF41,
	CinemachineCamera_OnValidate_m1A53BC3B538FFE4C5010A034BA6AC8535934C1CF,
	CinemachineCamera_get_State_m0181FB952F0405E45D48DFD04FB6D03CDC3A4DE2,
	CinemachineCamera_get_LookAt_m6E4E26A668D21EDDB1A67F411E3A02792429AB14,
	CinemachineCamera_set_LookAt_mE2316C558A8E9BDA5AE3ED182B314B561E7F338C,
	CinemachineCamera_get_Follow_m9C67B67F2032E494C75ADC442B1CBA63FB101250,
	CinemachineCamera_set_Follow_m6F5256B4D24CC996ED66E9BEB970AEBAF9C60D39,
	CinemachineCamera_OnTargetObjectWarped_mA7E7AC0437C4FE5F6D94EA6DAA17101E424C830C,
	CinemachineCamera_ForceCameraPosition_m18D61347A7FABE951979843B1B8C43110B262B2A,
	CinemachineCamera_GetMaxDampTime_m6EA3539A2DB9877DFA001A709B7343C13C99382E,
	CinemachineCamera_OnTransitionFromCamera_m54AEE8CC75D022C5F562C0D2F0761EFCF9EB99D3,
	CinemachineCamera_InternalUpdateCameraState_m1EE98901BB5C5ABF935261D7B1C789BBC214955E,
	CinemachineCamera_InvokeComponentPipeline_m747363A75C8A01C8BA345A978CC0E3DB0787CBAD,
	CinemachineCamera_InvalidatePipelineCache_mD7DCC2F42134810B300E428E3029370DFD1ADB7E,
	CinemachineCamera_get_PipelineCacheInvalidated_mB95C76EE3C4299F5CA7D30B5034CC3A1D2E3B3E8,
	CinemachineCamera_PeekPipelineCacheType_mE403C1694B2151B73E91334A6089D8C83F824CC4,
	CinemachineCamera_UpdatePipelineCache_m831D5FCBE9BE9060A75B3E3C0388A08AC41CE115,
	CinemachineCamera_GetCinemachineComponent_m250E78F88A39C9FAF77E21B2D7A8C4580233F5C2,
	CinemachineCamera__ctor_m91522425D436D75B47674B716508EAE4B23CCB67,
	CinemachineCameraOffset_Reset_m0CA4CE8CB36657281279874E9AFB9BB8DECF99E8,
	CinemachineCameraOffset_PostPipelineStageCallback_mEF59D875A8926DB96BF3C30AFB17A48BB21E929E,
	CinemachineCameraOffset__ctor_m28E2D82DF4C9AC91640CDA02817D4E773CCCCA4F,
	CinemachineClearShot_Reset_mB01F353A2ABB60B661B1CD2C4CECD5EE5E475EAC,
	CinemachineClearShot_PerformLegacyUpgrade_m18AE49A93C1355FFABBE01878ED000D56C4FB624,
	CinemachineClearShot_OnTransitionFromCamera_m720CCEBAFAD913316D6C78E77A1AF5C6DFF7E4D4,
	CinemachineClearShot_ResetRandomization_mA2DF72F4CF6D92E8715F629F5855541B1F2CE8C1,
	CinemachineClearShot_ChooseCurrentCamera_m4CAB1C1AD204215DEEE445270E15CBBCAAF676EC,
	CinemachineClearShot_Randomize_m3EF5CDA82C20C8E50820A21B3F7908B73DA4B0C7,
	CinemachineClearShot__ctor_m61289C0079D4637A416603FDAAD6BCC9B2D83796,
	U3CU3Ec__cctor_mDB1A22CCE4E4AEE4F6ED3E47592B948CF74CFBE5,
	U3CU3Ec__ctor_m7618574879E8E254D1C0FC8D1B818B27447109A6,
	U3CU3Ec_U3CRandomizeU3Eb__16_0_m5B008EF3619085477A51B58B121493360C14E392,
	CinemachineConfiner2D_OnValidate_m7C471C69589F32A031720FFEA18E59105A444D83,
	CinemachineConfiner2D_Reset_m94218B0F30AE33E69569F5DE181F710261681B3A,
	CinemachineConfiner2D_GetMaxDampTime_mCD997369FF39BFB57253F2D18C3F646386D92C0C,
	CinemachineConfiner2D_OnTargetObjectWarped_m448A90764B6A2022CB30AD54A0C28964EA8B034E,
	CinemachineConfiner2D_InvalidateLensCache_m96127F7E34E1B79BD88D9B88B2782E7C4C620644,
	CinemachineConfiner2D_InvalidateBoundingShapeCache_m78594B546CDE7FB34D310FA1CD7CE10BD2537514,
	CinemachineConfiner2D_InvalidateCache_m6EEA885A82AE5D41AA9ED511BB7C14870E33AB23,
	CinemachineConfiner2D_get_BoundingShapeIsBaked_m94F29C2B20D6BF8A8DA01A03D6695BEFC17F7F75,
	CinemachineConfiner2D_BakeBoundingShape_mC07814A2197D1173B431338B627CD46F75B788D2,
	CinemachineConfiner2D_PostPipelineStageCallback_mCE904D8F7C085D41021BF5C766D78D60B106CA01,
	CinemachineConfiner2D_ConfinePoint_m4E2313EB1F226A48A75008488FEECC619D06507D,
	CinemachineConfiner2D_GetDistanceFromEdge_m9603F166B193A9279770D456517015AF0567B66F,
	CinemachineConfiner2D_CalculateHalfFrustumHeight_m194311D3B35D3BF2F1B72AE12C99D60C8E69527E,
	CinemachineConfiner2D__ctor_m17126B879455B4DA6923B0E925DFACB1F9868A20,
	VcamExtraState__ctor_mA479CCDAB69FBA5EB64579692BCEB6133F6241A9,
	ShapeCache_Invalidate_m7EAE6FF99DB3AD5D61BEB9156C14E6BD0DE1AF3E,
	ShapeCache_ValidateCache_m9B20BC43138C2439DB9F4499BC3E9338504B640B,
	ShapeCache_IsValid_m29BFC1989D49FB1151484B62E53A003D95A9AB81,
	ShapeCache_CalculateDeltaTransformationMatrix_m8E975270968FEC0D915DA7EBDB6459FF46A4EE6F,
	ShapeCache_U3CValidateCacheU3Eg__HasAnyPointsU7C10_0_m660EF4DD241B9E53F755A82C43D9C85B31498A77,
	CinemachineConfiner3D_CameraWasDisplaced_m646C3FA18281E847EFB06BB2283B5275ED816CFF,
	CinemachineConfiner3D_GetCameraDisplacementDistance_mD04BBC4F296886FAC8023367E4037D75FA730BB6,
	CinemachineConfiner3D_Reset_mD54999FA0C11CB95549CBB08A9C958698BBFCC92,
	CinemachineConfiner3D_OnValidate_m71DC7E0907AA6E2A547B9F68952C1EDADA71CA1F,
	CinemachineConfiner3D_get_IsValid_m78ECE27FE186FCDB56FCF2EA6BE7CEBBC7E845A8,
	CinemachineConfiner3D_GetMaxDampTime_m6809A899757877717C19D47E1D922177EB5B63DB,
	CinemachineConfiner3D_OnTargetObjectWarped_m14C0CC687ADF15D1A4CE1C409A02162B7D78252A,
	CinemachineConfiner3D_PostPipelineStageCallback_m378F10228EC072EDD338E9C7F7BE3B17121E560D,
	CinemachineConfiner3D_ConfinePoint_mDEA0C1D1E4030DCD071C91485E3F186F890AC40D,
	CinemachineConfiner3D_GetDistanceFromEdge_m399EC6F5B05B17BF86240E4D5266D165DA28D001,
	CinemachineConfiner3D__ctor_m12CE522D114C441A172E046A974E905568AB5042,
	VcamExtraState__ctor_m0794BED1D91310551151C924D419F90A6484DAFE,
	CinemachineDecollider_OnValidate_mC08AF0C31717A29C0A91C5F5058A6EB9FB3CDA8A,
	CinemachineDecollider_Reset_mE700FDE36663B464E54E4347D1DDE136AB3F4ED0,
	CinemachineDecollider_OnDestroy_m0F0C072A3FEA2F03D1BA574F543039DFD60A12AC,
	CinemachineDecollider_GetMaxDampTime_m514A390008CF7955D32C09459016C8A880211CF2,
	CinemachineDecollider_ForceCameraPosition_m7BB7BB5B572B290FA9F5DC21FF27DA936952F177,
	CinemachineDecollider_PostPipelineStageCallback_m87AE96804D39F4F62A58FA8C39E12F25D70BF914,
	CinemachineDecollider_GetAvoidanceResolutionTargetPoint_m3D9C5461D7A96F2BD4D8B245FB143E96AE3EC101,
	CinemachineDecollider_ResolveTerrain_m04BFAB02E14C352BB78949FA8219C17D7205343E,
	CinemachineDecollider_DecollideCamera_m5EAA9B0A4B085CFE2A9D6046657E331D37001BDC,
	CinemachineDecollider_ApplySmoothingAndDamping_mA407E079A2DE73C766DD28C75CDA1746D005507B,
	CinemachineDecollider__ctor_m7D46287E18AC7C90B177162388972E33FFCB01CC,
	CinemachineDecollider__cctor_m19C0557661B516D72A615FF147E9A6B297C311A0,
	VcamExtraState_UpdateDistanceSmoothing_mC773F67938BB6132EEA71E9BA75BCE3B3A5AE2B5,
	VcamExtraState__ctor_mDDA9D7DF5B0AB20482D5D0E93DB9D66B29FC6275,
	U3CU3Ec__cctor_m7141C8A567201CFDBB03B88267877DF009F2DC0D,
	U3CU3Ec__ctor_m56289C45DFAF853744975C3FCF0B87CB37410243,
	U3CU3Ec_U3C_cctorU3Eb__22_0_m2AF6CBB7C5E57C7AE1D531490B5F97960852AAC2,
	CinemachineDeoccluder_IsTargetObscured_mBDBDBC822C5AF1B4E6DD899F450E48CF315BC7DC,
	CinemachineDeoccluder_CameraWasDisplaced_m37B38BA237462EF0BB9C52CEDABE2E2C3F705263,
	CinemachineDeoccluder_GetCameraDisplacementDistance_mFEA1DB131D1A746E396A630A6956E4493ACEA080,
	CinemachineDeoccluder_OnValidate_mD0349132AD59DB4109C4C849DEADAB3CB17C7A31,
	CinemachineDeoccluder_Reset_m8A19C2B2F4F6510AB195D65F0CC2A243DEEDE7B2,
	CinemachineDeoccluder_OnDestroy_mB4EEE7923F1C4AFF4A88758D27C15851E6660F6C,
	CinemachineDeoccluder_OnEnable_m1A0B8519F69C65DE99FA40BE1622C0CEE353CBDE,
	CinemachineDeoccluder_DebugCollisionPaths_m24136E1E710C702698E055A0BE54E6EBEE8E7EFE,
	CinemachineDeoccluder_GetMaxDampTime_m15F47D27F88EBAFC2F53D1D31219E540317EC17E,
	CinemachineDeoccluder_OnTargetObjectWarped_mF6E78F547EB86E372BCF079DBC48064FFBAA7318,
	CinemachineDeoccluder_ForceCameraPosition_mE82E189DF0EB2D0B17B38A2BDBDC6C1C88A32BD4,
	CinemachineDeoccluder_PostPipelineStageCallback_m19CBA5B799C135B5CAC8290780506FFC6F3E2585,
	CinemachineDeoccluder_GetAvoidanceResolutionTargetPoint_m8F111FB9F05E6F811035316FDC17E34E0226CD83,
	CinemachineDeoccluder_PreserveLineOfSight_m70DCFA45408D4779B54DFD28F5EFFC99F5033489,
	CinemachineDeoccluder_PullCameraInFrontOfNearestObstacle_m91138003E079D183B97C8E5E45698351AEA53104,
	CinemachineDeoccluder_PushCameraBack_m2079512A7C9C6D446D3CC52288DA38913C386EC6,
	CinemachineDeoccluder_GetWalkingDirection_m0DE90A71C93DA2894D8D5C1779D345D1F5938D02,
	CinemachineDeoccluder_GetPushBackDistance_mE0FECDF06391F31488DC8A6C71792C92D6704EF5,
	CinemachineDeoccluder_ClampRayToBounds_mD7614EC2DF143DE49C0A4479A47EE4AF4A2FE74C,
	CinemachineDeoccluder_RespectCameraRadius_mCB553E24BF26A6C76874D0202977E2E6BEF5A83B,
	CinemachineDeoccluder_IsTargetObscured_m54D0CC7F8572807904C212F5F74928E0B7603A7A,
	CinemachineDeoccluder__ctor_m70CC7C25D900B1376FA5672107AC7B5362B327F7,
	CinemachineDeoccluder__cctor_m227ED309F95DAAE497F76754B820B163A0BEC203,
	ObstacleAvoidance_get_Default_m1E0297E7D6B58B35450D582260862F7511FA62B1,
	QualityEvaluation_get_Default_m866E5F3F224D0839C9EA77615203ADD5FAD1CA51,
	VcamExtraState_AddPointToDebugPath_m25FE360AFEA9767845BE4CDAC59B6B2F8D56388E,
	VcamExtraState_ApplyDistanceSmoothing_m85B2B823A475E616309658A6B39C98C0BE46130F,
	VcamExtraState_UpdateDistanceSmoothing_mB8870D8DE044245AE242C40FE1D13EA50FD6E403,
	VcamExtraState_ResetDistanceSmoothing_mF3F47145B31CB7C1B781AAEBA31323CEDE88D523,
	VcamExtraState__ctor_mE3C0FE1F0BE52C461B54A78E9641BC19C694101F,
	CinemachineExternalCamera_get_State_mB9BBC06A7316FA3994BF183E657082620AB7B17D,
	CinemachineExternalCamera_get_LookAt_m29D641BD9B316CFF2E9BD3F489E3224B5613AE93,
	CinemachineExternalCamera_set_LookAt_m6A1622CE9C8CE837FD0F7667A52499F7E605735D,
	CinemachineExternalCamera_get_Follow_mABD656E191F4BB8328850ADC813AD651499FEAC2,
	CinemachineExternalCamera_set_Follow_m9535B088DDDAAAD9998F11A6E4C6AD9408B34879,
	CinemachineExternalCamera_InternalUpdateCameraState_m0EBB0C3B8A5C443EE8A27FABE62E2EF8EAF43B63,
	CinemachineExternalCamera__ctor_m4EAD9E2BB6EF89B9C227022C1A1D03CDDE137FEC,
	CinemachineFollowZoom_Reset_m49A2632F3774FC14CD3D0CDA8FF781698B816AFD,
	CinemachineFollowZoom_OnValidate_m7DEEAB1CA2523E3D791D409AA94A811647731E34,
	CinemachineFollowZoom_GetMaxDampTime_mEB15BA1EA85DE68078F2AC91FAEA0541B45146E3,
	CinemachineFollowZoom_PostPipelineStageCallback_m5F4DE79DB5CC62625EB17ED360BF5E06B76C265F,
	CinemachineFollowZoom__ctor_mE45B17E38BE042F5C3A40220D767780650A6FE8B,
	VcamExtraState__ctor_m53A6B951FA3826ABB2840F70844E357FE54FD258,
	CinemachineFreeLookModifier_OnValidate_m1A72C3DA3B042F5AF357515EFCCB36930EE985D7,
	CinemachineFreeLookModifier_OnEnable_m12CC8B25642C736321CE811B9A2547F72A5C2CA1,
	NULL,
	CinemachineFreeLookModifier_RefreshComponentCache_m96902FCDC49F7E04C28FF754D458FB640E4C2589,
	CinemachineFreeLookModifier_HasValueSource_m9077489D377D563E9A1A6767F98F0CD17B9399FB,
	CinemachineFreeLookModifier_PrePipelineMutateCameraStateCallback_m3D24FB041AAA0B98F888189C5AA9A6C521364E4E,
	CinemachineFreeLookModifier_PostPipelineStageCallback_mC9A960642FB6C361AA774C5A843A7614310AB375,
	CinemachineFreeLookModifier__ctor_m3FD448FA2E1D020B44AA69471DDDC3E8307A2CA6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Modifier_Validate_m915EB25B59CC975EC035AB45270A5BE0B50597A9,
	Modifier_Reset_mA0DF9610739560B2FDD666384108782C03DA6AB5,
	Modifier_get_CachedComponentType_m6C716EFFF2F1C18C78A7E2DFAAECBB5A8DEB9F27,
	Modifier_get_HasRequiredComponent_m29AB56253FE92BBEBE69D634453D1B37F265A09C,
	Modifier_RefreshCache_m6F0E9BD2C0FBABDAB99658CF4FCA8C01BAB4B3AB,
	Modifier_BeforePipeline_m58AF8EC4A00F1924C88FFFF93E5BC86D0DCEA2BE,
	Modifier_AfterPipeline_m3067412D245E19393B82095BE4E0212FEE4E632B,
	Modifier__ctor_m7DBF334FC84EC6C3D028D7196EF2A39BAEEB5012,
	NULL,
	NULL,
	NULL,
	NULL,
	TiltModifier_Validate_m4B48B50FA68B545E7D9F62BEB2E20FC08A5CEB6D,
	TiltModifier_Reset_mE0C3B4EB6729910547BF2DD364649BEFA6D8E141,
	TiltModifier_AfterPipeline_m42776093F458F8527CCF18E792C4C504F1991B55,
	TiltModifier__ctor_mE3B33050DE46EE9C38FAEA17AD71A74BB54C3BF7,
	LensModifier_Validate_m0A5D2B5D8117A6B9893E6D72FAA7532F0A79B9A2,
	LensModifier_Reset_m2D874D2313516523E70E04727932D5A44FC75355,
	LensModifier_BeforePipeline_m3F8F0A3ED7BDDCDFF7FA0D9ED33C435E323B55AF,
	LensModifier__ctor_m47809E6AFB8291DEC8604F010C511392A0E30661,
	PositionDampingModifier_Validate_mABF021569EB81D99A6EA202805C33F2B230773D5,
	PositionDampingModifier_Reset_m1678DD8AE01D1CB71FAB6BF0E91DCB8BA5A7CB6B,
	PositionDampingModifier_BeforePipeline_m152B989DE2B2C86E3E17557DED4C83C30A44EA4A,
	PositionDampingModifier_AfterPipeline_mF54A94D6CFCC014B7381299F1F3B6F8B39B9D3CD,
	PositionDampingModifier__ctor_mA4B0F686E391F1740C4654FD74F9C1B7DB69AC47,
	CompositionModifier_Validate_m4A31AD91423975F1C9F5C34BCC10ABC0AA26B986,
	CompositionModifier_Reset_m2ED36533159313F79F2B0204E2DFDD6220D8B63F,
	CompositionModifier_BeforePipeline_m60778DB53118337059BDA1A067C8E60A89A4E60D,
	CompositionModifier_AfterPipeline_mCE5BB65E89062D941D5B29935A2D0017D4E4A1CA,
	CompositionModifier__ctor_mCD8608F30946269B1E696AEBF13ABB9DE40BD004,
	DistanceModifier_Validate_m6514387A0657540B7D9CED201002ECE5E3A3F931,
	DistanceModifier_Reset_m2845507D9BF465AE3FDC652F3314F96E57A4986D,
	DistanceModifier_BeforePipeline_mED614C93952E4BE24E4347BFEDE5BB4E0F1BD34A,
	DistanceModifier_AfterPipeline_m4D44BD0F71499099663D9C702CB15ADDDF727241,
	DistanceModifier__ctor_m08F9CD4D7E80145FA57F82AEEFBC44FFFB1C18AA,
	NoiseModifier_Reset_mE30E1C1BB8B27AD3FE18F7A42ADE3C79E39C148A,
	NoiseModifier_BeforePipeline_mB8EDF8F5528930200BF658623D8042DAFBC48FE3,
	NoiseModifier_AfterPipeline_mE83A72BC5AF6B130A58A7B15EDE354D7CE03DCAE,
	NoiseModifier__ctor_m3ABDB340A635C901EC122B291A098BB3D1B5BACD,
	CinemachineGroupFraming_OnValidate_m70D192B593D59DAFBC7786AC22C25D8673C98AD1,
	CinemachineGroupFraming_Reset_m514B037950ADF854C5ABB0E4B90B0116F791F183,
	CinemachineGroupFraming_GetMaxDampTime_mFC43BC951253CAADB1DDFEADFC72D6DE4076FB4D,
	CinemachineGroupFraming_PostPipelineStageCallback_mD760DA6EF0F4774B5AAACAA1298250E5C3E65B7C,
	CinemachineGroupFraming_OrthoFraming_m5602A3502F95650A4A8E36705976615B2EBC157B,
	CinemachineGroupFraming_PerspectiveFraming_m08749C1BF0F957327FA1000EB9D18494385E98E3,
	CinemachineGroupFraming_AdjustSize_mA74DCF74636624A2517EB5CE451E6233E764733F,
	CinemachineGroupFraming_ComputeCameraViewGroupBounds_m76AE668AF39F0948F758CD6C8835219C9B18FABA,
	CinemachineGroupFraming_GetFrameHeight_m9212B3B0324A10200D32AB91DDD4689318BEE7FA,
	CinemachineGroupFraming__ctor_m5A099F5F0CDA46331F0ACEA7915D6FE39F36656B,
	VcamExtraState_Reset_m7084A60FD1C3699F299914B313336DE411F06F2C,
	VcamExtraState__ctor_m7634780DA7DCB4D8FC9DD8BBFD391BB54A61ADDB,
	CinemachineMixingCamera_OnValidate_m6BD0F1F154CB7840BC6792DA79B9272F1DD4BC19,
	CinemachineMixingCamera_Reset_m691AF7CAD8A77A2778AAC9B2EAD3D1A0857C30CF,
	CinemachineMixingCamera_get_State_m2C0A93A3E9F84545CB28CEFD9EA0ADAF8BC46196,
	CinemachineMixingCamera_get_Description_m486E7F41A3FDC990119727D73075ACA43CE1DC6B,
	CinemachineMixingCamera_GetWeight_m6342A2A6786194F79566D0CE90F41F39DCD87FC4,
	CinemachineMixingCamera_SetWeight_m2F14E726D70A7A1BA23FEDE8D6558F49B7CDF041,
	CinemachineMixingCamera_GetWeight_m4D7ED9DB66C75DABD40689B13D1CBA4A4AEB0C4C,
	CinemachineMixingCamera_SetWeight_mEF30F10178B20AB416BA0D77788B9BB2C6A1C2E4,
	CinemachineMixingCamera_IsLiveChild_m8A51DBCB589F49DB6586E2CE1D40D1A687EA347D,
	CinemachineMixingCamera_UpdateCameraCache_m34FBD49D2076EFCFF184FE0D09DCCAF738AE76C2,
	CinemachineMixingCamera_OnTransitionFromCamera_m9CAE7A08549375F0DC1C7679C61E78B92ABCB60B,
	CinemachineMixingCamera_InternalUpdateCameraState_m75E16DCC12C064D9378B48BEEDECB3C15C0D37D3,
	CinemachineMixingCamera_ChooseCurrentCamera_m7AC2A49B3C931F95B91087588EB691252104860D,
	CinemachineMixingCamera__ctor_mEBBE5A0517647F70102A7D3B59D4A8A3C686D856,
	CinemachinePixelPerfect__ctor_mFF8E9674F21E57004F8AFD1EC2FA250DC05902D4,
	CinemachineRecomposer_Reset_mAE28A433363B38291C46E89A219A752012A25081,
	CinemachineRecomposer_OnValidate_m53F62BBA1DDDD29517CAA39B27FC0163DCCD635B,
	CinemachineRecomposer_PrePipelineMutateCameraStateCallback_mDA724A1F4AF400965BED7492B757BA3207DAEAE9,
	CinemachineRecomposer_PostPipelineStageCallback_m471B6879AF9BE8778D0968423A74B3CF681E6028,
	CinemachineRecomposer__ctor_m4E26C058E31CFD6607C1049A8F0C24906CC3778C,
	CinemachineSequencerCamera_Reset_mAEB898064F28FFEAA0F6E5C4E248719CC854068E,
	CinemachineSequencerCamera_OnValidate_mA89B672AC12628E8BE97E50C0F6ADC4478EE6647,
	CinemachineSequencerCamera_PerformLegacyUpgrade_m2E3B3C81B6EEAE56B4D14BC8757C9F8919A3D00A,
	CinemachineSequencerCamera_OnTransitionFromCamera_m32C8CC58AADA14FF27884E88E1D0E90054C1F472,
	CinemachineSequencerCamera_ChooseCurrentCamera_mFB1E878BCE6BE11173B83DAD309E9C29D701AF00,
	CinemachineSequencerCamera_LookupBlend_mF931E3FD3EE1059B2F89E431A4A2CA2F97FA7F4F,
	CinemachineSequencerCamera_UpdateCameraCache_m21B5E23F8331BFF057EB1DFB9A06629317B86098,
	CinemachineSequencerCamera_AdvanceCurrentInstruction_m0AE059FAE82B4637A7BCBFD58FD02D81813DBA77,
	CinemachineSequencerCamera__ctor_m5459039D8987D7DF01AB4B5D9331C89D227203F3,
	Instruction_Validate_m8178D703AE92163B2335C8EE46B8AB28EC01BF1F,
	CinemachineShotQualityEvaluator_OnValidate_mCAFB157FD133A321C0FDE5F37E559E401F359C06,
	CinemachineShotQualityEvaluator_Reset_mFDA7D6A70DFEF5A4DB5AFCE48F28290F941B644A,
	CinemachineShotQualityEvaluator_PostPipelineStageCallback_mEB54ED44A27ED48DBB40D66B2E58B61E3BBC431D,
	CinemachineShotQualityEvaluator_IsTargetObscured_m3FEC7C16D84B3C6CF63CE2C33024445369BE7152,
	CinemachineShotQualityEvaluator__ctor_m2193F109AA00BC06F1C5360F12383BCB4DA282C2,
	DistanceEvaluationSettings_get_Default_mC0F7F48A113DFA5D72617E0B2AC47D6B14672A5B,
	CinemachineSplineCart_get_SplineSettings_m72366F879C4A2244896873C0BAA4AE344A19A1B4,
	CinemachineSplineCart_get_Spline_m633FBE44C5BEC263F495816E2EEDE7710CDDAA07,
	CinemachineSplineCart_set_Spline_mF31E2D49618BBFCA89A6D991D1CD1DB98BDD7693,
	CinemachineSplineCart_get_SplinePosition_mD32F0C154B1700B51E0A4E93A038BAF443EBE6C0,
	CinemachineSplineCart_set_SplinePosition_mEBE0AA723B162FD0A21BE073BCA1DF88E5939EAF,
	CinemachineSplineCart_get_PositionUnits_mEF36459A01FCCEB09D8AC1CF8231A3284E4668F1,
	CinemachineSplineCart_set_PositionUnits_mB29823F8C464F39FDBD050E5905748AE19664D83,
	CinemachineSplineCart_PerformLegacyUpgrade_m881C0764B0B5F29A3A42E2872A12D44BEACFE9CB,
	CinemachineSplineCart_OnValidate_m37A7EAB55C7FE394AA6488C74E6CB61652820CDD,
	CinemachineSplineCart_Reset_mEA75007F36ACAA7A866D470A138764FAB5199B28,
	CinemachineSplineCart_OnEnable_m634A5FF67C2CD69A80FAEB583B291091E63AE535,
	CinemachineSplineCart_OnDisable_m72C47C821DFF2A31F5D2DB67A17D653D165D92EB,
	CinemachineSplineCart_FixedUpdate_mA998B8329EBFCEA04C19ED39643FCD6D6E3A48B5,
	CinemachineSplineCart_Update_m2FD5D4E89782B9C1DF23619BE98C6398D2FCE1FA,
	CinemachineSplineCart_LateUpdate_mD7EF44C407717D1EA18719147877CE838570B9DC,
	CinemachineSplineCart_UpdateCartPosition_mE24A3A920970FA3E0A38070E3F6D67AF82AE60DF,
	CinemachineSplineCart_SetCartPosition_m684C83C28E391BFCF8310B88891BFEB85387B16E,
	CinemachineSplineCart__ctor_mCCD5F463CA103D5DFF20408E3CA15A88A19D73E1,
	CinemachineSplineRoll_GetInterpolator_m4807776EDA34CF771B224E533D3D0EE938C9FE65,
	CinemachineSplineRoll_PerformLegacyUpgrade_m771FE9DCDDE8891EE6866506D48C3FC3A54950F6,
	CinemachineSplineRoll_Reset_m80B435280C86CFD3460E01F2EAB5BFE1B6B0E4A4,
	CinemachineSplineRoll_OnEnable_mA7DB6E34C30DDB7D6CAC5CEE6B68FA42306E1503,
	CinemachineSplineRoll_OnBeforeSerialize_m68D6042F750B63B6977201691AA40C5C9FB09184,
	CinemachineSplineRoll_OnAfterDeserialize_m57D91A088DAA6517A6E841D58EB7A1E94443E76A,
	CinemachineSplineRoll__ctor_mE26FA080055862294B2F52627D68CBA5E7944345,
	RollData_op_Implicit_m7D9C4F9E45500F8A006B798EB4B365A13881F072,
	RollData_op_Implicit_mD7E3EE70B693689BA5A6E1C4290A3E0A2D0FEC93,
	LerpRollData_Interpolate_mCB9BC66ADFB6B3E320ED43AB11A121DAE116134E,
	LerpRollDataWithEasing_Interpolate_m97056DDAB2AC587935B5F6010023E96592917FDC,
	RollCache_Refresh_m1570582AE45E77CE46A95CDEAECD004C568B072C,
	RollCache_GetSplineRoll_mF4DEFB86AD16D1E13A2A620FB4EC35512625905D,
	CinemachineStateDrivenCamera_SetParentHash_m31730475BD0DEEC5D7E3A7EF4105333CADD8E36E,
	CinemachineStateDrivenCamera_Reset_m87808F6C0D704DFFDA8B7A90FE270EF5BB0E3CBC,
	CinemachineStateDrivenCamera_PerformLegacyUpgrade_m5B9DAFCE1F55862D80CD85EFEDA6A618421FBF0E,
	CinemachineStateDrivenCamera_CreateFakeHash_mE3F38A51644B9FA04A21EFEAD371642BED7DE741,
	CinemachineStateDrivenCamera_LookupFakeHash_mB8256DA4192E72A0207E358F51DAECB79A1F57C9,
	CinemachineStateDrivenCamera_ValidateInstructions_mC0D386CC5169FA2FB3362E333552D8E99C14D8F0,
	CinemachineStateDrivenCamera_ChooseCurrentCamera_mF0BDF0B5F7FCCD4634FC8E17240D25A880A49E3B,
	CinemachineStateDrivenCamera_GetClipHash_m5CB5DDB21E924AF543287FE6D2785258DC0D7700,
	CinemachineStateDrivenCamera_CancelWait_m24682024A7EF1C081FF86142D27B3153A722628C,
	CinemachineStateDrivenCamera__ctor_m6DBB830F2B9BA5680411F1FDFA25B20D897751F2,
	CinemachineStoryboard_PostPipelineStageCallback_mF0AA2553CDDE871D8C53FB98D11682E8199E2048,
	CinemachineStoryboard_UpdateRenderCanvas_mBC1E21D4697A16F48B64D9C51F0624D7E34FD8F1,
	CinemachineStoryboard_ConnectToVcam_m3B3EDC5D5D5BB4F18604C2AA3E0960F6FF1C2023,
	CinemachineStoryboard_get_CanvasName_m2E44F5B07725E17A29ED6249D7AB0697F1584EF2,
	CinemachineStoryboard_CameraUpdatedCallback_m1CAF6968DAEDC81F985E7D1D3AE24E63338F3552,
	CinemachineStoryboard_LocateMyCanvas_mAA75A83FD787864A06C0253C003EC9570EEEE063,
	CinemachineStoryboard_CreateCanvas_m90E92A407F57C297C6374B005563E34A0B6D17F5,
	CinemachineStoryboard_DestroyCanvas_m97FFDF78A29ACC4631E6B0B1B0603CFE264266EC,
	CinemachineStoryboard_PlaceImage_m526CDD0913252A5411FFD304E9BB8BE5FA003FC1,
	CinemachineStoryboard_StaticBlendingHandler_mD291D42C1662EA2104780FC7ECB210CA00BFB7D4,
	CinemachineStoryboard_InitializeModule_mE4B9F42F69EC0A317AD0D2A7DAD6EC8A9970C88F,
	CinemachineStoryboard__ctor_mE67D2B79B39A11B920B2C9CF84EB4D8946E0526C,
	CanvasInfo__ctor_mEFE0004747AA15A1B20D277BAD33BE11A39F9C8F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CinemachineTargetGroup_OnValidate_mF2C1D2E4DEF776010A42B5FA3311755E2E3FA228,
	CinemachineTargetGroup_Reset_m2F6791E212A9512CEA0848F466EB4540F79AAFB8,
	CinemachineTargetGroup_Awake_mE55E59E16F272B330DCF01FFE4FCBD1F5F835E2C,
	CinemachineTargetGroup_get_m_Targets_m32340F88B61076E8B1BDA77D30D25DE8BAAD2EFE,
	CinemachineTargetGroup_set_m_Targets_mC6D0282C62CE27F4BFCB9CD66F7C25868A599D4A,
	CinemachineTargetGroup_get_Transform_mBDE866FC4227F0BA446EADDC5F40EB1C78F70632,
	CinemachineTargetGroup_get_IsValid_m223C458CE45331512728FC02134AE69B33D8A562,
	CinemachineTargetGroup_get_BoundingBox_m1268B62BB2F439ACB18E37B83113306DCBE71350,
	CinemachineTargetGroup_set_BoundingBox_mA09001A15B114A881BF41C3479BD95CDF6813B12,
	CinemachineTargetGroup_get_Sphere_mB3BB5A8D1B37754F91ED9408088A0845B9FC9196,
	CinemachineTargetGroup_set_Sphere_m991EE174F87AAAFF3203257ADCF09DB9E1CA898D,
	CinemachineTargetGroup_get_IsEmpty_m166ED3A5F9605EBF290C672DAFCCC1E14D91E9D0,
	CinemachineTargetGroup_AddMember_mF6D56BECACF14C0BF41916098A637D45F6B0C691,
	CinemachineTargetGroup_RemoveMember_m35176F5E563B6A5B2A874AD36125A8681C943174,
	CinemachineTargetGroup_FindMember_m298C1CC36765AC2FA9AF03AF24ECBC21F1D1F7F4,
	CinemachineTargetGroup_GetWeightedBoundsForMember_m818E7A6287719BE71A40C419215F13019D5A5E52,
	CinemachineTargetGroup_GetViewSpaceBoundingBox_m5B31EB4DD1CB99D21ADF6E2CDE6B748BF6430308,
	CinemachineTargetGroup_get_CachedCountIsValid_m6287B5B024F638C122911C7458D7ED5A6B820724,
	CinemachineTargetGroup_IndexIsValid_m0CE4FCA85CC84DE18C9E39DFFA4D62C1058E27D8,
	CinemachineTargetGroup_WeightedMemberBoundsForValidMember_m9A27A14DA906D1C3805A6E724A4B6993AAF13774,
	CinemachineTargetGroup_DoUpdate_m93C935D8CAA338EC7974A720C39E19180FB306C3,
	CinemachineTargetGroup_UpdateMemberValidity_m37A8026F8A7AA307E764C9DB56ACD0550FCC6B72,
	CinemachineTargetGroup_CalculateAveragePosition_m2D9D8717B1DF56CD92A88F4E46AED6D14F9D8101,
	CinemachineTargetGroup_CalculateBoundingBox_m4932CB90EF04123FBA880E60B7066A555A79108B,
	CinemachineTargetGroup_CalculateBoundingSphere_mBAE1C5D6D23E0A725FC4F3034DFDEFBE590016D4,
	CinemachineTargetGroup_CalculateAverageOrientation_mD820DCAE0ADE1474CE4C65B2DA39DE888001D050,
	CinemachineTargetGroup_FixedUpdate_mC87193CB9B9B01C5CA1F1F7C8850AF4BCBD0CE38,
	CinemachineTargetGroup_Update_m5A4540EB66DC6D2B19D6EC236964F139AEF316A0,
	CinemachineTargetGroup_LateUpdate_m043836C3D51FDA071A3CD57BA87A82DE0BD29D5A,
	CinemachineTargetGroup_GetViewSpaceAngularBounds_m90A281BFF37AFAD21384D1D75C4976854F79BB09,
	CinemachineTargetGroup__ctor_m30C1BD7106CB9BE2FFC8A963AC3B6A42EC482838,
	Target__ctor_m8EFD91AF3960885A2FC683A71EEA7D9648C394D5,
	CinemachineThirdPersonAim_get_AimTarget_m9103B6463CA79130E2475D77FAA6A26FB3F547AA,
	CinemachineThirdPersonAim_set_AimTarget_mBD1A0C839AF90D2EB32DAD6C84A2CCB8E9438A8F,
	CinemachineThirdPersonAim_OnValidate_mA835880733B2B9A767E1896C041BCDBFB7D5FDFC,
	CinemachineThirdPersonAim_Reset_m91C1A20A102D211F9DAF6BEDC10809C27E5455ED,
	CinemachineThirdPersonAim_PostPipelineStageCallback_mF46F5D5D910289C314984022AD5BF7DE723FE180,
	CinemachineThirdPersonAim_ComputeLookAtPoint_m691776F22E761C3E1A9BBFD10C1D2B821BC074FA,
	CinemachineThirdPersonAim_ComputeAimTarget_m63898BEC67A7E1AFCB7755928C95E53EF1390028,
	CinemachineThirdPersonAim__ctor_mD69897D961C8C233F8B33D1B7C68A1CE08796379,
	CinemachineBasicMultiChannelPerlin_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableNoise_get_NoiseAmplitudeFrequency_m7D7A7C68A1228965466D32B7DE7821CF0E0CCA4E,
	CinemachineBasicMultiChannelPerlin_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableNoise_set_NoiseAmplitudeFrequency_m97048546B5F4966D128706A920356CE35998D5C9,
	CinemachineBasicMultiChannelPerlin_get_IsValid_m25C0688BA4B3B9B3806C601A992DAAEBE62DA6C1,
	CinemachineBasicMultiChannelPerlin_get_Stage_m866F09298A21910B01D26D026798C242C43D1CD5,
	CinemachineBasicMultiChannelPerlin_MutateCameraState_mFDC1EB4DEA20742A7D15264A44F11EBEE4D06754,
	CinemachineBasicMultiChannelPerlin_ReSeed_mA0BFDF577E765DAFA724A0EA7E6D2D1C975339D2,
	CinemachineBasicMultiChannelPerlin_Initialize_m73F84575F8E407FFC9ED249AE162586DCC28A5E0,
	CinemachineBasicMultiChannelPerlin__ctor_m76EDF5931EC8A9F96F508F914CD0970EB28BF062,
	CinemachineFollow_OnValidate_mB3469B45F98984708FF2F98A0E3FC38C39ACB774,
	CinemachineFollow_Reset_m67720F86F5B19721DA87D77F8E322B1AEB0E7B12,
	CinemachineFollow_get_EffectiveOffset_m70F5D7452DE04987C2C153E2CEE104C1933AE6F3,
	CinemachineFollow_get_IsValid_m3A22BF868363036406830A238790A9D83F21B3F4,
	CinemachineFollow_get_Stage_m4D76AB5640F75EF697F85A6EBCF1E5EC4E59C529,
	CinemachineFollow_GetMaxDampTime_m4669AB43ED15E850CEE5870255D48DA32B500BA1,
	CinemachineFollow_MutateCameraState_mC228777857D00E50EE04227A43DCE4E25317E089,
	CinemachineFollow_OnTargetObjectWarped_m0639B382C2F788D92ADA2BF28C4A24EFD14F91BC,
	CinemachineFollow_ForceCameraPosition_m8ED6B0AF5380659E90C8479BEDB11700A1B2EFFA,
	CinemachineFollow_GetReferenceOrientation_mD584EF8C036D67AEFA66CE66676F94B32B8209EA,
	CinemachineFollow_GetDesiredCameraPosition_m3B9FBB76A5DA4B3EFC3DDF3D2A38BCF0A15F0D72,
	CinemachineFollow__ctor_m98BAC48388C8078A73CF902CB42739F2F0957661,
	CinemachineHardLockToTarget_get_IsValid_m47979DED6B2A7D729352804D893252E31F9D3CD3,
	CinemachineHardLockToTarget_get_Stage_m00DACB866508022BE66389E2CAE979985A77D9A0,
	CinemachineHardLockToTarget_GetMaxDampTime_m433125CF70BAB12F8B2F9CDC772733E74CAF57B5,
	CinemachineHardLockToTarget_MutateCameraState_m67AADB57632431C64876D778D314A679A5A73EBB,
	CinemachineHardLockToTarget__ctor_m0AE526F6F08204C6E3D5299EE83C05CE18950611,
	CinemachineHardLookAt_get_IsValid_mC7255977EBB871634BB27A71C5A633EA2804A0C7,
	CinemachineHardLookAt_get_Stage_m5CAB6F58DF56471DC06090EA0C035230FEE84B2A,
	CinemachineHardLookAt_get_CameraLooksAtTarget_m35E9CA9DD16AB96C5B8AD5070B36441905187444,
	CinemachineHardLookAt_Reset_m1FB92DD4E0D37A238F3F58B552324BCC2E2DB188,
	CinemachineHardLookAt_MutateCameraState_mFBDF5F7E426F9B7F5C90C32580A7CA33A8F71854,
	CinemachineHardLookAt__ctor_mBEBAFAA6A8D452348831119396DEE6C959B0CCF5,
	CinemachineOrbitalFollow_get_TrackedPoint_m65548F6BBD262026C1F5702C66C0A559E0D6CF45,
	CinemachineOrbitalFollow_set_TrackedPoint_m60B4059F0ACC55D79B28C7A0979425AD17359DF3,
	CinemachineOrbitalFollow_OnValidate_m2A4D33C1AC75E15BE6E3E59D2583159528BC2BC7,
	CinemachineOrbitalFollow_Reset_m0E9AB79CC4BE46A0EEB22ABDCB561911FB7FDC64,
	CinemachineOrbitalFollow_get_DefaultHorizontal_m34BE51A65EBB9705DEBF404818972CE3D320D834,
	CinemachineOrbitalFollow_get_DefaultVertical_m8CDA48B3983A268AB17EE05E4E3CBE1B625B1929,
	CinemachineOrbitalFollow_get_DefaultRadial_m7C92D94F8A70BD7DD1C2450F7D99F5A9323D4D8C,
	CinemachineOrbitalFollow_get_IsValid_m0C59DED31F494A51B224824D38ECE01201D2D568,
	CinemachineOrbitalFollow_get_Stage_m71B0C624768AA8ACFBD67AB9CB461B842FEC5450,
	CinemachineOrbitalFollow_GetMaxDampTime_mD3039B8BEE32EFE3AA39DE50CD4DE3DCFB862219,
	CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisOwner_GetInputAxes_m7BC81810798A50C2419ADAFDC3029AE6E23BB8E9,
	CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_RegisterResetHandler_mFA7D8161577D4DE66E1279008571C1528F0C56CB,
	CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_UnregisterResetHandler_mB04173C2B4F550E98D459D34D40F8FC95FFA8B10,
	CinemachineOrbitalFollow_Unity_Cinemachine_IInputAxisResetSource_get_HasResetHandler_m0909105F241DD8631390DB53BEBB71FEBA5F8FCB,
	CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m5F502F9B8B942D05C82B41DB02BA6307CA599908,
	CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m0DBADCF5A6E7E2EB82E1D2F62BC21AFECD06A949,
	CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_m2D910B8E44A476B0F9ECFCF5E7BA02C4A2077E85,
	CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m3D4950D3A947F8348E80EBB29AAF928FCDB24850,
	CinemachineOrbitalFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m5ADF76933C766EB851D100959DC2ED2CCF0113D8,
	CinemachineOrbitalFollow_GetCameraOffsetForNormalizedAxisValue_mA40C84E1011360E302CB71A156B1DE5E5F942E1E,
	CinemachineOrbitalFollow_GetCameraPoint_m424A9F6125D071C39217E3532807456E62DFF711,
	CinemachineOrbitalFollow_OnTransitionFromCamera_mC1959BEB633AD662FEC16100C1D4FE6620489EA6,
	CinemachineOrbitalFollow_ForceCameraPosition_m6B2AD4C4CC9C0D259D8A54C908CAE2E175C354D1,
	CinemachineOrbitalFollow_InferAxesFromPosition_Sphere_m8D82354301A3AF91FD6A77965856150002173269,
	CinemachineOrbitalFollow_InferAxesFromPosition_ThreeRing_m85E4D8FB57D3ED53D92EE59D983585CE538C6195,
	CinemachineOrbitalFollow_OnTargetObjectWarped_mC9B656DA211B1CE1C961BE5642D8901FDAE233D5,
	CinemachineOrbitalFollow_MutateCameraState_m7C533C0CA41DF74B6027BC925EE80BA0F81D0E6C,
	CinemachineOrbitalFollow_UpdateHorizontalCenter_m431290B907BB1E7EF18EC316EFF6778AA731FE74,
	CinemachineOrbitalFollow_GetReferenceOrientation_mE357A5CE25CCC8231D4017AF10CABEDC3F843016,
	CinemachineOrbitalFollow__ctor_mA5E7ACBA058B60854F7254E1BBF5BBCFAA1B9DC7,
	CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_0_mE9826AA0E8C845B8649CCCF5061C076A5FA7B9B5,
	CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_1_m323E571454861DEE22B4ED36A3EEA4E4BE7A9B67,
	CinemachineOrbitalFollow_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__32_2_m4E6F8635BA9CABC62036312C80F1172EF9F6A5DD,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__GetHorizontalAxisU7C50_0_m3A7CA91577381FF9B6A5660CB85A33ADB4EDCAFA,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__GetVerticalAxisClosestValueU7C50_1_mEC451AC3DDF3F4D5F00D57BEEB4BCE799A632E96,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__SteepestDescentU7C50_2_mA81C1BC91FCC28B033E6AC6D4EAC9C329BD7C6A4,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__AngleFunctionU7C50_4_m91FDB466E9C2DA50423665596030ACFD6346E687,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__SlopeOfAngleFunctionU7C50_5_mAAB6093C8E81C0B649D125BAF3790DE26E677557,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__InitialGuessU7C50_6_mAF30EFD7E56913C6D9B663DD185B44A36153D6F8,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__ChooseBestAngleU7C50_7_m8E53D917C5CC64B60A2A1F7631D6DCD34B487B30,
	CinemachineOrbitalFollow_U3CInferAxesFromPosition_ThreeRingU3Eg__MapTo01U7C50_3_m409AE10D70DB97CEDFDB544C5875CE5A70A85567,
	Settings_get_Default_mDA8AD63191BD95F7F563A6DB36A4DCCE70B3425E,
	OrbitSplineCache_SettingsChanged_mE93B6AD4D70694FC04BCCE02118447F278CEB151,
	OrbitSplineCache_UpdateOrbitCache_mEE095885509E49893CA6EAAEF08383EE05F9BEBD,
	OrbitSplineCache_SplineValue_m4EF5A34796ED0FF7210F0F7B0365070E7B2CEADD,
	CinemachinePanTilt_OnValidate_mEB368963E6D3D892FF2B159A4FC8D0F704422024,
	CinemachinePanTilt_Reset_m442EDD54D7FA2D1CCE7BA5FF1B9CDADBA3360D58,
	CinemachinePanTilt_get_DefaultPan_m1A2455E0CAB62E0F96764B8279B683D06EA3825E,
	CinemachinePanTilt_get_DefaultTilt_mCD80D869ECDA84B6B44D6DBB693F7469C762DFD5,
	CinemachinePanTilt_Unity_Cinemachine_IInputAxisOwner_GetInputAxes_mB2276A8C1072B69FD73DCB59C94EEB72DC6BFD21,
	CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_RegisterResetHandler_m4CACEE56391345AC84CBD11BCDC8DCF31F0BE386,
	CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_UnregisterResetHandler_m2D19067DC671DDABA3C22AF377CB07F46EB7C787,
	CinemachinePanTilt_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m1FB0285358F5A0D4EB6AE5E710582466341BA4BC,
	CinemachinePanTilt_Unity_Cinemachine_IInputAxisResetSource_get_HasResetHandler_mD8A83CD0632259A53A7E0EEE7274671C76A642E1,
	CinemachinePanTilt_get_IsValid_m3D4617517E5730C0220379EDFB18F68A403E6302,
	CinemachinePanTilt_get_Stage_mFAFA0EBA5E1ADABB0B34CDE836F8E2843BFF3028,
	CinemachinePanTilt_PrePipelineMutateCameraState_m911EB22321C98911810BCB30E1064CCA690D891A,
	CinemachinePanTilt_MutateCameraState_m97FA2F7A8C07870858C100A593F58FBC875F2791,
	CinemachinePanTilt_ForceCameraPosition_mFF8C80379DC5F9B991C4F76F81CF0EE3DA1899ED,
	CinemachinePanTilt_OnTransitionFromCamera_m4CE20ED429347F32177DEC99D26665BCE4E1BE3F,
	CinemachinePanTilt_SetAxesForRotation_mD657916690343AE8A81186FC2D78BF68AEDAB8CA,
	CinemachinePanTilt_GetReferenceFrame_m547364EB069E964DEDE9DC534809161B69A1D25B,
	CinemachinePanTilt_GetRecenterTarget_m3B0EE4AA6A1E76BABA668437068387903DABDC3B,
	CinemachinePanTilt__ctor_mBCB4F55B8DA59793DDC2B6143CE8FD5C9C9AB901,
	CinemachinePanTilt_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__14_0_m2841BF42AB2F8BE4B137EAC7793349149A248495,
	CinemachinePanTilt_U3CUnity_Cinemachine_IInputAxisOwner_GetInputAxesU3Eb__14_1_mDE23936E05BB421C0BCE477C77CFBAC7EFB19435,
	CinemachinePanTilt_U3CGetRecenterTargetU3Eg__NormalizeAngleU7C31_0_m78DD09531A73DBE3D54A65B31114B35B19505FA2,
	CinemachinePositionComposer_get_GetEffectiveComposition_mFAE433732C019BDD897D5A576CB3CB8C0743E52D,
	CinemachinePositionComposer_Reset_mC6AEE40D90C95CCB62BE5D0609FA721930903F7A,
	CinemachinePositionComposer_OnValidate_m77E9821579C07E376397DE92831384B3F2079F04,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_get_Composition_mF2032B6D352F9DE8887DF712B27094AB17651C3D,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_set_Composition_mFDF2E2BDC48D543C05BDFFEC2A5F3C5E0DAA1F1E,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m7887198B311A38FA5E631AAF8506BFAE74ADCAF4,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_m60C3DD73923916E54DF03C4D722B84CC220DDC3C,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_mB57A8472CA0ED611644EA0177A3743DCD89CEE90,
	CinemachinePositionComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m41741FF7B31941CCD752F12135F530E2F3702D1B,
	CinemachinePositionComposer_get_IsValid_m8860A24E2DFBCB96456828BDF91A838DFDB3961C,
	CinemachinePositionComposer_get_Stage_m025C2A298F01F4CCDBC76B7BB16EAC247ED874DE,
	CinemachinePositionComposer_get_BodyAppliesAfterAim_m67F09AE75D2E86FF22614245D554B2C51D86161F,
	CinemachinePositionComposer_get_TrackedPoint_mCCBF19E6DF8BE8C79DD1EA1E317807114435DF1E,
	CinemachinePositionComposer_set_TrackedPoint_m2F2F16E5D486BC430DEE0A456618787BBC268AF5,
	CinemachinePositionComposer_OnTargetObjectWarped_m5E985899CD4BC7F94CE104C1F03A7909A9297ADD,
	CinemachinePositionComposer_ForceCameraPosition_mB63489141190E4A74E1FB91201E3B2F37C3788D8,
	CinemachinePositionComposer_GetMaxDampTime_m2F2D5051F80BED023333782EBD97C31ED591DE13,
	CinemachinePositionComposer_OnTransitionFromCamera_m03AE0BDC812160B4256397286027E1A069FF9D3B,
	CinemachinePositionComposer_ScreenToOrtho_m362BCD4CB1541C2030745A0E62A86B9B13C2434D,
	CinemachinePositionComposer_OrthoOffsetToScreenBounds_m88F4177CC359D817CF71CF2C5F5697ACED90B766,
	CinemachinePositionComposer_MutateCameraState_m846EDCBC18AA6F774CBF3813B9CCD58E41D6E99B,
	CinemachinePositionComposer__ctor_m8BAD7685BB1C75FFF2D2EAFFA3F2ECD6C025D3F3,
	CinemachineRotateWithFollowTarget_get_IsValid_m011E2DCE96D7D800513B8A9C2C91B58EF71DC78E,
	CinemachineRotateWithFollowTarget_get_Stage_m8B1986DF8AF6D25CED00B4B62B64678294658605,
	CinemachineRotateWithFollowTarget_GetMaxDampTime_mC707D09D3F8F17CDE38226BCFC5615A9A8A3603E,
	CinemachineRotateWithFollowTarget_MutateCameraState_mC93BEBDEB3D717E490421A6BE55BABC9D3CCFB76,
	CinemachineRotateWithFollowTarget__ctor_m1D49E4E085497D7111F7B8FCA87B7CE45A7E0091,
	CinemachineRotationComposer_Reset_m9EB67C485D1906CA68AC257BA2948E57CF68015B,
	CinemachineRotationComposer_OnValidate_mBA676070BF8DFACD00449F349D5C4A71874DCB2E,
	CinemachineRotationComposer_get_IsValid_mF597D231A410FD6FEDBA4544B1673F69C8835BC5,
	CinemachineRotationComposer_get_Stage_m202BBFEC2632E3E33FB5F0868276905AC3DB4782,
	CinemachineRotationComposer_get_CameraLooksAtTarget_m40D45BAD5D0ECF224723EB1F89330CEF75DBB864,
	CinemachineRotationComposer_get_TrackedPoint_mE7BC076074745CED759DA5BA9A4663C4B1A6E40D,
	CinemachineRotationComposer_set_TrackedPoint_m0A6F781CA6271496E36D76E9F29E05A6C27C6CC9,
	CinemachineRotationComposer_get_GetEffectiveComposition_m3436B218B6C3C92AE0486BDFD6C481C73F34B370,
	CinemachineRotationComposer_GetLookAtPointAndSetTrackedPoint_mB6824FFBE79AB78587399A347ED3EF2593C4A2BF,
	CinemachineRotationComposer_OnTargetObjectWarped_m81C264C9D50A82C103F1C5F2E448365806C26295,
	CinemachineRotationComposer_ForceCameraPosition_mFDA90B8A897E826BDDB67F40AD1A2C27292082A2,
	CinemachineRotationComposer_GetMaxDampTime_mE998A5E72BB5B1379E1482CD619CD04E671BD78F,
	CinemachineRotationComposer_PrePipelineMutateCameraState_m853D661A776B9C43DF903FC0D3EFAEE3517AF411,
	CinemachineRotationComposer_MutateCameraState_m517B63B729A7FEEE5506C03587AED29C0FC35950,
	CinemachineRotationComposer_RotateToScreenBounds_mDCCCF46AE265D22CDD6996C8BC85D538D6C9F2DF,
	CinemachineRotationComposer_ClampVerticalBounds_mBB6B805C67C5C6D080981B434A6F13DD3E4544A2,
	CinemachineRotationComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_get_Composition_m077D0E3940C3625255B90E5C171BAF363AD6A7F0,
	CinemachineRotationComposer_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableComposition_set_Composition_m97C412BA759F528586F9DDFEE0E5264E07488416,
	CinemachineRotationComposer__ctor_m34E958BFFC94B45048F4230B170778B474A2A395,
	FovCache_UpdateCache_mBE2C2FD57DAD380D862B04A8374C6A3E6C60B99E,
	FovCache_ScreenToAngle_m697CC6600E25E343A92923A25F91567B3202669F,
	FovCache_DirectionFromScreen_mC6E97AA5783FD6A4FEABAAE42E3FA3C14C47144B,
	CinemachineSplineDolly_PerformLegacyUpgrade_mB15F1CA32A5F42E077ABAF8421A596834BE2F611,
	CinemachineSplineDolly_get_SplineSettings_m0FEAC219FAE29EFFF51B08F1EFC764ECAFB55B6A,
	CinemachineSplineDolly_get_Spline_m041CFE464883EA4B3D539FADC9FC4364EF81E826,
	CinemachineSplineDolly_set_Spline_m35075A8EF36AE412C8C5969462B37AB2D2DF9E47,
	CinemachineSplineDolly_get_CameraPosition_m6F48E4837D4564CC15C631B85403C86FD323841A,
	CinemachineSplineDolly_set_CameraPosition_mC738F980AAF2A9CAE106B5CB3C95562160A1DE16,
	CinemachineSplineDolly_get_PositionUnits_mED892ADBC97A52C3BFDBB26C37C20098EDE05296,
	CinemachineSplineDolly_set_PositionUnits_m6EE169F0C3BC0E5EE6B9D600D2D2FEEB50AE516A,
	CinemachineSplineDolly_OnValidate_mA2528278EBE9FE17ABA179B6602079AAF6260BBB,
	CinemachineSplineDolly_Reset_m1384CF250F6BBF7CC47CFAD2BABB1B750AF981F7,
	CinemachineSplineDolly_OnEnable_m6BDD3BBAFBCF134E9EAF7E310D4A6EE0C645982A,
	CinemachineSplineDolly_OnDisable_m471745B15C702AA5B32C5CC9D8E58ADDFDAC5586,
	CinemachineSplineDolly_get_IsValid_mF12C38EB43AF615EFE26384509FE45A0FD959F83,
	CinemachineSplineDolly_get_Stage_m4692BD68A2E147D517836D88DD056A8F142A4789,
	CinemachineSplineDolly_GetMaxDampTime_mEB4406D893E2A030CBD7E715F7D628432E0D4497,
	CinemachineSplineDolly_MutateCameraState_mC78B97FB6BAC9E9915C4EF9E7149B863293177B2,
	CinemachineSplineDolly_GetCameraRotationAtSplinePoint_mA347EFF7F38D8DF1434B20CBE329B0A82EDA9B42,
	CinemachineSplineDolly__ctor_m2E5B83CF4E777F536A8BEAE163DD2442C953FEF5,
	CinemachineSplineDollyLookAtTargets_Reset_mCF14582D038BFBD0D1767DE0E0D9FC62A7AEC71A,
	CinemachineSplineDollyLookAtTargets_get_IsValid_mAB051C2104A99D7A0B11B2C2293C3F9AF89F9951,
	CinemachineSplineDollyLookAtTargets_get_Stage_m4F0C28152881405C745C987BF1815AF5E2A3B1FE,
	CinemachineSplineDollyLookAtTargets_MutateCameraState_m2ACF028041E1155D302E5DE754F86DBFEE665500,
	CinemachineSplineDollyLookAtTargets_GetGetSplineAndDolly_m52C897647678C8951447444CADCBC688C5F61968,
	CinemachineSplineDollyLookAtTargets__ctor_mD7C03CB7A7A94DE5495B3FB388F01B0467B4E552,
	Item_get_WorldLookAt_m5FC326A00B241D52FC2C5C8152BCB0FE23BB1E36,
	Item_set_WorldLookAt_m8C05FC415D07807B4349FF9C858391BF04BBC24A,
	LerpItem_Interpolate_mC3591384873341D9D393C147FBA9F5EF94FA46BF,
	CinemachineThirdPersonFollow_get_CurrentObstacle_mEC20AA0E72B3CD01C2C4DCC7A68B98E6B01AC2A1,
	CinemachineThirdPersonFollow_set_CurrentObstacle_m8F10CB8B3C7DDBDFDEDE354731AEEB82A7617961,
	CinemachineThirdPersonFollow_OnValidate_mA5B3E521F0EDE8A201702BCC8BD3CB2CA2BB7814,
	CinemachineThirdPersonFollow_Reset_m629B27ED3919A314034D0F5AF94A53FEF9AB4859,
	CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m73294D7D610E05BA8A4A8856BFC569D8004B24EE,
	CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_m8649C980990CA3EF97A9563806008A829A058364,
	CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_mCF1D5DD1A43EF61778EC5484407F9C0C6942D943,
	CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m956BB974C0243663A50B4B01EF9A3464101084E4,
	CinemachineThirdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_m09966A3E4FF0926F1D8BD4273F1DD7B739C62836,
	CinemachineThirdPersonFollow_get_IsValid_m2295836C8CC389ABB098E1BB5F680EA3E95CD719,
	CinemachineThirdPersonFollow_get_Stage_m8BD02FAB60DC6CD855D22C2573469410E9E106B6,
	CinemachineThirdPersonFollow_GetMaxDampTime_m733CE61BD86CC854F2DAC2AF1A542C77BCB64A34,
	CinemachineThirdPersonFollow_MutateCameraState_mED66DEEC78719A9BA50680A982A87F5DFBBE86A8,
	CinemachineThirdPersonFollow_OnTargetObjectWarped_m3B4E74883C0DF3F66F5229ACCC4420A74CA4E7F9,
	CinemachineThirdPersonFollow_PositionCamera_mE85D97810EBD91108691EB77017211127C8162D4,
	CinemachineThirdPersonFollow_GetRigPositions_m9D2A508DBAB0080915D2E4644AB014AA2DFC775B,
	CinemachineThirdPersonFollow_GetHeading_m794B4EA4F31F8C92153C9511741FEA55E6A6E3FE,
	CinemachineThirdPersonFollow_GetRawRigPositions_mF6175B4E9EED134D5CFA18436335195C6B35A005,
	CinemachineThirdPersonFollow_ResolveCollisions_mE24DD4A21D7BF43B69113D323E43421FA8D0A7AE,
	CinemachineThirdPersonFollow__ctor_mC65B4D43E85565959938E8DAAFBF0BDEEF4B4B43,
	ObstacleSettings_get_Default_mAEA852450D1E9BECD0BBB0364291296790F392A4,
	BlendManager_OnEnable_m4E0000025C81A2130A6BDCA7E34E4C2F604AFC78,
	BlendManager_get_ActiveVirtualCamera_m29E032AB3726D10B07B9520B4F09DF6838BA710C,
	BlendManager_DeepCamBFromBlend_mBBF8734887273F41BCDDE8C117855D2565D2C582,
	BlendManager_get_ActiveBlend_mF44711D3574FBD5EDE5ACAAC8B4861A86B0F1391,
	BlendManager_set_ActiveBlend_mC83F9C7532D5DA3BDC61AC1773B40AA4682B0506,
	BlendManager_get_IsBlending_m6A397A4469F590EA64C3EDECE82EC6179D2B52C6,
	BlendManager_get_Description_mBD6C4BBBF57F7C6D96AA8E0217ADE371B53994E5,
	BlendManager_IsLiveInBlend_m3FA5E2D6620457DE488D30B2EBD0F1F3A62D0877,
	BlendManager_IsLive_mE23B9958ECA2AA6E3364BFE876FAA2AB86834521,
	BlendManager_get_CameraState_mE7815E7A60FFE3640F91A6A359F1B09ABB38C5E6,
	BlendManager_ComputeCurrentBlend_m7469CC25BAACB7538442D2EC4388156603802030,
	BlendManager_RefreshCurrentCameraState_m0587B0615F6BCDC9EB221AFD9956F732852236E0,
	BlendManager_ProcessActiveCamera_m6D03C5D02130A8C84635324771B6AB8744EF5B3F,
	BlendManager__ctor_m6D824F8C5F9C2A7FD2B7E7344C72C08B009237B6,
	BlendManager_U3CProcessActiveCameraU3Eg__CollectLiveCamerasU7C21_0_m3127987B812EF6925B8D2D5FD5F22EDE771C8B1A,
	NULL,
	NULL,
	NULL,
	CameraBlendStack_get_DefaultWorldUp_m66523230416DB99E62227B87B20FE97A996FF8FF,
	CameraBlendStack_SetCameraOverride_m83F5918402E4915D8E65504B7FDA5B27D9841645,
	CameraBlendStack_ReleaseCameraOverride_mCD593715F64C0D3850BFD7E02D36223B82FD58DF,
	CameraBlendStack_OnEnable_m02D77290D65AD9A09E81B960AF9BF2A93A748016,
	CameraBlendStack_OnDisable_mC17B1918ABBA1CC63E5AAA19F3A9EC33F8EB306A,
	CameraBlendStack_get_IsInitialized_mBEFB5CFD670C84DBAA98AE213138328D82D2AEA4,
	CameraBlendStack_get_LookupBlendDelegate_mA653E86DB58865B73CBD97915F7C789BBC5CB177,
	CameraBlendStack_set_LookupBlendDelegate_m7CE34F82C0F52DA5BF28CA8634501E52F74C6398,
	CameraBlendStack_ResetRootFrame_mDBEA39C701F6BEA5BB7523809061E486DE47381E,
	CameraBlendStack_UpdateRootFrame_m380E44AC8A016C16EF5C2A377E50B7923E290F53,
	CameraBlendStack_ProcessOverrideFrames_m81E7A82232BBAFF7B34E88822A03EC3F20361CC6,
	CameraBlendStack_SetRootBlend_m9EF7F3EF5C547768C70209C504B5B98844E2C4E0,
	CameraBlendStack_GetDeltaTimeOverride_m6751B96BCB998FF1FF81A8CC74A5B5AAA7195E75,
	CameraBlendStack__ctor_m692C21630EBD2FAA23FD6F075A9FA67EECB24CB0,
	CameraBlendStack__cctor_mFFB924A76892886F990E0E45D2F2D7EEB43B7BB0,
	CameraBlendStack_U3CSetCameraOverrideU3Eg__FindFrameU7C7_0_mC511F6E9D8830DD526D8FD82162157F4552A485F,
	CameraBlendStack_U3CUpdateRootFrameU3Eg__AdvanceBlendU7C18_0_m60DC36BF49C95A311722BE940D13B7CCBA72D46B,
	StackFrame__ctor_mFCF6C137B34EB76AC197F0857DD60BAA4E84E447,
	StackFrame_get_Active_m2BD2519893FFEF932DB31DAF621A3CC926D7538A,
	StackFrame_GetSnapshotIfAppropriate_m42DCE69A1562483EE8AE3A1DC984D063499DE5BE,
	SnapshotBlendSource_get_RemainingTimeInBlend_m6C1511FA87A6F5F068CE8C1B6A3FE3C2FB2C51F5,
	SnapshotBlendSource_set_RemainingTimeInBlend_mD4C63E7C8BA89930999890AAF7A5E268C0274664,
	SnapshotBlendSource__ctor_m25CF4762C2DD894BF0A8C7E0A9A0BCC8C7BBBEA5,
	SnapshotBlendSource_get_Name_mC0C3BA66648BCC5E7F587A901235824FC20C0D4A,
	SnapshotBlendSource_get_Description_mE595AEB80D5FDFFEC39450F188A53BEB75BC7343,
	SnapshotBlendSource_get_State_m2D12F08072357D64ED3A3B4FCFE5623B573C01FC,
	SnapshotBlendSource_get_IsValid_m8236EC54D50E9DD6893CE3681043D2FF1583CDD5,
	SnapshotBlendSource_get_ParentCamera_m108E2B4897F03D783E196E2D6ED0800CBE593548,
	SnapshotBlendSource_UpdateCameraState_mB819667A3FEE53972DDF63D174D7E3688A798281,
	SnapshotBlendSource_OnCameraActivated_m4568709D55A05987424408B3BA2778359AEE0C54,
	SnapshotBlendSource_TakeSnapshot_mBE81FB399C1B2B8FBFFFF481FCA3A745787FF458,
	CameraState_get_Default_mD69074AD6B6D491AE36193203D78F6A748A7BF19,
	CameraState_AddCustomBlendable_m0AA826256B3ED67F345FE5DBE2881830CC4CCCAB,
	CameraState_Lerp_mFD80713BCFBAA0B937D83BDFD1505706088D66DC,
	CameraState_InterpolateFOV_m5C09D707583A9EAC6A027CE93B49EA4A163C30BD,
	CameraState_ApplyPosBlendHint_m22B5EEC209158D78F3EC0C236F2B6DDD4A09613B,
	CameraState_ApplyRotBlendHint_m136A1971A3EF5367BFE064B483716B9973AAD705,
	CameraState_InterpolatePosition_m8A7447EE2D5FED3614D73F884B64B49A11CDB1D4,
	CameraState__cctor_mFFC4430D63CA26D3531FC5EA45290515ABB96B86,
	CameraStateExtensions_HasLookAt_m7A95F08DBF4242DA032710DB8A4C08FD90E3A6E3,
	CameraStateExtensions_GetCorrectedPosition_mB0FD3576F7A1173911B35598EF098C97CA0615FB,
	CameraStateExtensions_GetCorrectedOrientation_mF9C37FE55D80D50168F2E5A89A555A6EC8DF3D51,
	CameraStateExtensions_GetFinalPosition_m94D469582F60CF0117C2CE9B93DB574EDF152AB0,
	CameraStateExtensions_GetFinalOrientation_mEB5584BB2351BB42C2CA87CA5B5B7C89BDAC40F2,
	CameraStateExtensions_GetNumCustomBlendables_mE7DCAAE4567E31F80D40A924977F7C7AF2D7D524,
	CameraStateExtensions_GetCustomBlendable_mAF74CC00AB89208B005F0E720A1516B8112CCA17,
	CameraStateExtensions_FindCustomBlendable_m4B9B4BAC928137CB8E709894F21239F8EAC3B32F,
	CameraStateExtensions_IsTargetOffscreen_mE05237C31B634F74E2209924342A75647C077BC8,
	CameraUpdateManager_InitializeModule_m29CF9E2D4AF43477DBFF274A074AFEA513F49D72,
	CameraUpdateManager_get_VirtualCameraCount_m77560DFF4A30601542778B09E1311ED4211881BE,
	CameraUpdateManager_GetVirtualCamera_m2A7B000759CF468F2D277E7C4BE7C78243986B07,
	CameraUpdateManager_AddActiveCamera_mDE6D175910C2F74CC02192806330A31863619BF4,
	CameraUpdateManager_RemoveActiveCamera_m40EFC9E6018206FFB121C86C6F704E6D10F90823,
	CameraUpdateManager_CameraDestroyed_m80AEFAD29231C3840C7DFF90D3484AD33C102BF4,
	CameraUpdateManager_CameraEnabled_mF20ECF96288C42846FF384C1F9781AE5EF3B0F5F,
	CameraUpdateManager_CameraDisabled_m1ECB5CCE9D7796E71400C411B27C8BDA684FBAEC,
	CameraUpdateManager_ForgetContext_m0D126C54343308E15D14501B2A9BC6C6D95BDC96,
	CameraUpdateManager_UpdateAllActiveVirtualCameras_m19F9E944A70080E3FD0A02972FD33A4856C8FC6C,
	CameraUpdateManager_UpdateVirtualCamera_m86ECD688DFB565629940F27B614B06323F4417CC,
	CameraUpdateManager_GetUpdateTarget_m22B260DC6BEE6CC5D1DCBE003A712A86C93BE50C,
	CameraUpdateManager_GetVcamUpdateStatus_m98AA6A80F41B4158305106741EA48FE042546028,
	CameraUpdateManager__cctor_m287FCD6DC066A9C5ABCD3D89E154976D0808B63D,
	UpdateStatus__ctor_m20AF6E04E04D0D2AD5E7D6BF21BF680583391645,
	CinemachineBlend_get_BlendWeight_m97C082AA2A3AF1F8E75B3EA6DDA2AC7D18CDCDA2,
	CinemachineBlend_get_CustomBlender_m965AE257516C38F6C680C2BDD755B68710CC7569,
	CinemachineBlend_set_CustomBlender_m9A25BD8E5114EA22CC2C8222A32B12167C3BB29B,
	CinemachineBlend_get_IsValid_m5EE72C7A52D34FC083F2D6D6340C11D87CE55EB0,
	CinemachineBlend_get_IsComplete_m947FBEF655E91D49C1706F03CE14D683B6017A54,
	CinemachineBlend_get_Description_mAC7F69E790C47B95B56C8916C34ECAC5699D4B35,
	CinemachineBlend_Uses_m92123D6ACA37D0BF71A81A86C5956BA739CFB4F1,
	CinemachineBlend_CopyFrom_mBAA041B8980B173B783949EFDF8D29588B88D32A,
	CinemachineBlend_ClearBlend_m29C0F6784AB71F8ADE61FD3A32463BBB2156E5E8,
	CinemachineBlend_UpdateCameraState_m5D905501623CC3511AC3AE2DC69C0DAADC747432,
	CinemachineBlend_get_State_m3C3AD6FE6F78297DB4E1877F1C29621BDE94065F,
	CinemachineBlend__ctor_mDB183B3B9E68F4758B74D85E8D7BF30A36B62629,
	NULL,
	CinemachineBlendDefinition_get_BlendTime_mEAAA78C5DA24961202ED2E0ACA2BA3876F13F0CA,
	CinemachineBlendDefinition__ctor_m39FCAAA94DC44BAE3882C37D8BE9CC903B3C3D0C,
	CinemachineBlendDefinition_CreateStandardCurves_m03315670810AA1CBA01C39471C2A53B291771D10,
	CinemachineBlendDefinition_get_BlendCurve_m7613588597293BB3B062EA98D823D78C19AB9AB6,
	LookupBlendDelegate__ctor_mD71CB40CDDE5786EE133528BE84B962996C9CC20,
	LookupBlendDelegate_Invoke_m03596CD7B7B777D770B2EF6FBF9A959D562A7513,
	LookupBlendDelegate_BeginInvoke_mA48F98E8EF05AACD5B0929BE56A9CEAFCD45CE16,
	LookupBlendDelegate_EndInvoke_mCF87786030253AF8C591638DBE857255410E888F,
	NestedBlendSource__ctor_m7EBE3B4707F4EB471443E52272CAFECADB15B87D,
	NestedBlendSource_get_Blend_m4BDC8E79BC05FD3174233808BA8510B42EEF7F03,
	NestedBlendSource_set_Blend_mD011D3413328BF2F07AB14539B17349F818547E6,
	NestedBlendSource_get_Name_mD316266B336E7A2E0E5691F701A5437A906549D8,
	NestedBlendSource_get_Description_m3317DCBADF9A42E2C32087C6D3BC8A9828590A62,
	NestedBlendSource_get_State_mBA8354DC1527CD0162802C458D7AA72104346279,
	NestedBlendSource_set_State_m8BBB5BC3919A0D65FDE212BEEF504B5A6429B5E7,
	NestedBlendSource_get_IsValid_m7F8A87153A84B46E73E536A0BC3301C6B1ABB9A4,
	NestedBlendSource_get_ParentCamera_m61CC6C074634C351DC76384D8D90D86F85A2349E,
	NestedBlendSource_UpdateCameraState_mDD3893BA47D8507086C742F92AB2C06D94ADDA9D,
	NestedBlendSource_OnCameraActivated_m4B350183065B333349C64943A6F9705513372310,
	CinemachineBlenderSettings_GetBlendForVirtualCameras_m4951DD388E5E705A1D693B530F78E4C1FBC8FE06,
	CinemachineBlenderSettings_LookupBlend_m490C2A69812AEC37601C476F8DB1D0D2F6764E7B,
	CinemachineBlenderSettings__ctor_mA194685EDC721F1CFCAB403632688EB4775EC248,
	CinemachineCameraManagerBase_Reset_m3B5E5B6053111A23DF66DB9BFC13E255AEF31CD1,
	CinemachineCameraManagerBase_OnEnable_m4D75E25AC79AE356BC7023732820114D38D7365B,
	CinemachineCameraManagerBase_OnDisable_mAC1961839FDA3FAD744062BB5416F7DB4D6707CE,
	CinemachineCameraManagerBase_get_Description_m8615103557B6F9F6F2971FC187E241FFB4718021,
	CinemachineCameraManagerBase_get_State_m4B961BB28A6FE1BE1823666A3322EAA7990F46AD,
	CinemachineCameraManagerBase_IsLiveChild_mA6756878C0C6390536CEBB84BBB856BE819B635B,
	CinemachineCameraManagerBase_get_ChildCameras_mE90A6C9C1E20294423EC438B5758FBA262CE4DC2,
	CinemachineCameraManagerBase_get_PreviousStateIsValid_m515D79F64A71A2EF220F59BF26A608BED2325556,
	CinemachineCameraManagerBase_set_PreviousStateIsValid_m868DC9D6C56EB9204523985BB3434767CD035B43,
	CinemachineCameraManagerBase_get_IsBlending_m5DA9AD35898E14CD00DF93B97B47DB3C912D8741,
	CinemachineCameraManagerBase_get_ActiveBlend_m4E91A04AFE9DDA00AC8B5BAE81E48A5E65EBE0AF,
	CinemachineCameraManagerBase_set_ActiveBlend_m668AE15A189C7AF8762E0BA9A54492E3A36C3843,
	CinemachineCameraManagerBase_get_LiveChild_mFAED3CD4B6564F5C92A4AF94780D3E59C02C318E,
	CinemachineCameraManagerBase_get_LookAt_mCD330D46B5217DF5F92A94C8A873D2732B20493A,
	CinemachineCameraManagerBase_set_LookAt_m59FC8788902093A39417082E5C0B9BECD3701DA0,
	CinemachineCameraManagerBase_get_Follow_mAFB1722C56B048FDF07E5F8790A29E4F93716493,
	CinemachineCameraManagerBase_set_Follow_m13EC394E925A0A649CB128CEA8C8D613F3CADD47,
	CinemachineCameraManagerBase_InternalUpdateCameraState_m60D37331CE396C4A15710EB075921485DBE8E678,
	CinemachineCameraManagerBase_LookupBlend_m598C971C905EC6730E7AA4E6F8F52E5573B31A9D,
	NULL,
	CinemachineCameraManagerBase_OnTargetObjectWarped_mC15D3F6E8BE020D013BE39C9B5C425ACCFCE2506,
	CinemachineCameraManagerBase_ForceCameraPosition_m019E6DAF197702B5713FCB2B8DC7E7723B451762,
	CinemachineCameraManagerBase_OnTransitionFromCamera_mE20E05E85379E4D144AEB8096AFB8702C8E51A75,
	CinemachineCameraManagerBase_InvalidateCameraCache_m7608FC7337215720BA0C438F139F78BD28672CDA,
	CinemachineCameraManagerBase_UpdateCameraCache_m09FFD73FFD8E2FFC40BC341F243823F5B2CF9D9F,
	CinemachineCameraManagerBase_OnTransformChildrenChanged_m3324BF871DE74908088B87D457A8333B1DFFAA51,
	CinemachineCameraManagerBase_SetLiveChild_mE5A612156CEA0934BB0AEF667777A8DB82CD5E18,
	CinemachineCameraManagerBase_ResetLiveChild_mA1E777A316F7B4ACA4C644C3582A38BDBFE2C749,
	CinemachineCameraManagerBase_FinalizeCameraState_m4483D1231FC4B83A5C714D73C0E6688086CEFC5E,
	CinemachineCameraManagerBase__ctor_m6DFB3B858A5652EDFBFA12A75A01DF3D041859E0,
	CinemachineComponentBase_get_VirtualCamera_mD329B41E529512F6D6865C494250CE7752B3414D,
	CinemachineComponentBase_OnEnable_m26CDC97E5D8E23ACE8B57DA8D5D6FFDE05235825,
	CinemachineComponentBase_OnDisable_m850A44908937828074D3C7D350D8D85490E0D881,
	CinemachineComponentBase_get_FollowTarget_m5FD071F4CFC04A762C050C595FFA01AD65E7B735,
	CinemachineComponentBase_get_LookAtTarget_mDC040B0253537EA6D79FA2426130B15F56A85100,
	CinemachineComponentBase_get_FollowTargetAsGroup_m169DD0B2E175EF2FECEB53B351E4468C28E51561,
	CinemachineComponentBase_get_FollowTargetPosition_mEEC29F64B4A9F7212414B27CF89F2E072D76E93B,
	CinemachineComponentBase_get_FollowTargetRotation_m2D82372276FCD518DA193F0D43736AFA3120EABF,
	CinemachineComponentBase_get_LookAtTargetAsGroup_m086E4D6F3EE6C1EDC253D411FCFB57CD8DEE2C43,
	CinemachineComponentBase_get_LookAtTargetPosition_m53AEF0FF47E364639BD3DAC0C4350E99FAFB77C0,
	CinemachineComponentBase_get_LookAtTargetRotation_m1620D9099CCE378D4985A16F8A22496021BB5F07,
	CinemachineComponentBase_get_VcamState_m41D53E377042B0E17CF3DBD979A46887E25C79D9,
	NULL,
	CinemachineComponentBase_PrePipelineMutateCameraState_mE370BA3BA6DDD2AEFAEF6B2EC71E6C897040165B,
	NULL,
	CinemachineComponentBase_get_BodyAppliesAfterAim_m3063532CC85496B923F330031AACC8DD54A98F9B,
	NULL,
	CinemachineComponentBase_OnTransitionFromCamera_m713C04FB25D7B46B404C446F2ACFADC1D7479B7D,
	CinemachineComponentBase_OnTargetObjectWarped_m5CFEEE60A835CAE290E25D522C3B42D8E7785C38,
	CinemachineComponentBase_ForceCameraPosition_m9504C43B0710A0E9EAB145312CAEB8C7FAB07E89,
	CinemachineComponentBase_GetMaxDampTime_mCB28FC34A068661AC79C83D827D3DEE6096FF0D7,
	CinemachineComponentBase_get_CameraLooksAtTarget_m6942A1CF73E5D8D3AB7D451D91969B963DB4A372,
	CinemachineComponentBase__ctor_mEC1EA23D0F82607A6833FFEE570EFB625E91D235,
	CinemachineCore_get_CurrentUnscaledTime_m16A7A62D6D30E269D785C1A3BCFD455A3381AE31,
	CinemachineCore_SoloGUIColor_m4F7B067B3005757A6BB5ACC0E806C9A2A8BCA437,
	CinemachineCore_get_DeltaTime_m6935B26BF92AF69BCFC16FDB313AFD7E631AA5DE,
	CinemachineCore_get_CurrentTime_m4ACD45A4B881E0B14F287DEB646F1EED338F525C,
	CinemachineCore_get_CurrentUpdateFrame_m0CC2D4DD6910FCD993783BE5F8567D0A2858E9D1,
	CinemachineCore_set_CurrentUpdateFrame_m3BC501FD9EA12E62A4ACF2D5BC63E47F78BC54AB,
	CinemachineCore_get_VirtualCameraCount_m670A6FE41DD623612C81D3E7C6EF342FC3269DCE,
	CinemachineCore_GetVirtualCamera_mD033E8701101A28C451762C7F7FDAA68B88D2791,
	CinemachineCore_get_SoloCamera_mF61A59E26D24D08494589C8CEACE523A72CD06A1,
	CinemachineCore_set_SoloCamera_m97EE35F39E74F49573A209D8DF5D64CA77225D2D,
	CinemachineCore_IsLive_m507627FEA55003CCD1DC244E7BE3240C0D277733,
	CinemachineCore_IsLiveInBlend_m2B2C949DD1B63EC240072C4543960E8F86F58BAF,
	CinemachineCore_FindPotentialTargetBrain_mCE0C1074FB3C7977265C5670AAE2EBFB31B9C0C1,
	CinemachineCore_OnTargetObjectWarped_mA8DA334FF1B7D2BB31C963E3A1E7E9CF93876C39,
	CinemachineCore_ResetCameraState_m2143148A0E23473B023324C30FEF0530FFB5578E,
	CinemachineCore__cctor_m601115E2D1197CF6AA52A4144B9F3459DCDC5876,
	AxisInputDelegate__ctor_m464267CCDE013210DD9C8FEE30A1DE12998E1C67,
	AxisInputDelegate_Invoke_m7103ACD42DEEE2651E87DFCCC26B2B6DC332AC96,
	AxisInputDelegate_BeginInvoke_m61158065FB156DC4AE98ECAFF71420A56E5D50EF,
	AxisInputDelegate_EndInvoke_m8CD32DC1153BFE7CDA871B41EEEB341D53DF86C5,
	GetBlendOverrideDelegate__ctor_mD2DB6B6E201B9FB3980EC718A12CEAD330381F0C,
	GetBlendOverrideDelegate_Invoke_m9AF271245CD71AD1AAF666816908B00A70B349FA,
	GetBlendOverrideDelegate_BeginInvoke_mB72C85F9ABA04477E7E8246526D72B33F5080BEC,
	GetBlendOverrideDelegate_EndInvoke_m5A33D4DFA92034ADD1F0560DF7B6B8477D10B8B0,
	GetCustomBlenderDelegate__ctor_mDB68F1A676F155DE6E17DE6BD3E05A189D773CD5,
	GetCustomBlenderDelegate_Invoke_mEFBDEA65558C1C0AD43C7A5A2FACBBAAABA28738,
	GetCustomBlenderDelegate_BeginInvoke_m3B434A910D0A60EA582F54E7669532D06B57B1FC,
	GetCustomBlenderDelegate_EndInvoke_m21F9201FF108578C4B5457F57A6083129AF4C13B,
	CameraEvent__ctor_m0257DA39D426A0442931A0D76EDD10E06F57538C,
	BrainEvent__ctor_mFBF01AA3AEFB13F13B34F791D61C486D92C7540D,
	BlendEvent__ctor_mFC48D2E26EF801AA92207EFBA47461F876435622,
	CinemachineExtension_get_ComponentOwner_mB19806759148B6E4F01E610B107EC78B28877D65,
	CinemachineExtension_Awake_m074204694B512A9E90F2313B134DB557EFE92DD3,
	CinemachineExtension_OnDestroy_m889B537AF07ACB70B809AF2F91C23A021955CA14,
	CinemachineExtension_OnEnable_m0B2BF8A873C4FA3D3570220E01156892FDE5DB61,
	CinemachineExtension_EnsureStarted_m07FFB5E2F75E7325758F7F0691DF289C8D1D65B5,
	CinemachineExtension_ConnectToVcam_m14DC2CA35BC3C27890E8E7F231DDAC12C95EEC17,
	CinemachineExtension_PrePipelineMutateCameraStateCallback_m583F18A86EA1FC89B229AADCFDABF49B5F649C30,
	CinemachineExtension_InvokePostPipelineStageCallback_m84AC82A3131947ED9AD1CF5CE15BD7EBBB18BA78,
	CinemachineExtension_PostPipelineStageCallback_m07F9D2DC852DE7CCBC206130E6E963DEFCD5E356,
	CinemachineExtension_OnTargetObjectWarped_mDE6D3748839F61950F32C5E9A8FA7EF4EEC89899,
	CinemachineExtension_ForceCameraPosition_m58E817B4DA9D513E2480A2E338188BC7FB2C3401,
	CinemachineExtension_ForceCameraPosition_m05C8AAE813EC7A2B376D22DE8D672990F72E2DFC,
	CinemachineExtension_OnTransitionFromCamera_m194A0420605CE8A91DA7FB5D8FEB0E71925DB8BE,
	CinemachineExtension_GetMaxDampTime_m2ED79E27E33F6A78AF5413B8F3BD98E8CF96A691,
	NULL,
	NULL,
	CinemachineExtension__ctor_mCA69966AF11787D974CF8BDC0AD0640C6D522F33,
	VcamExtraStateBase__ctor_m533285976B0B646B1684CE8D7DA7B26C8AF3CD40,
	InputAxisNamePropertyAttribute__ctor_mA25741723DB3F3629EA763866A1F55D2C2357CC5,
	HideFoldoutAttribute__ctor_m911FAFF8C5ACF1EC23B1A19D113E0C6F2F205ABB,
	HideIfNoComponentAttribute__ctor_m0D4C7632F9054F165A2C55F5616592DBA6D67529,
	FoldoutWithEnabledButtonAttribute__ctor_mFDA196D781AA3808C11CBC0C70543ACADCE02B3A,
	EnabledPropertyAttribute__ctor_m5AF5629FC523CF4D93FDD223675BF12E5F81498B,
	RangeSliderAttribute__ctor_m29327AD56006418002FBC2F2A3E6FE30A9455A5F,
	MinMaxRangeSliderAttribute__ctor_m3644DDE8BE66D7B01168AC6CEF4848A1453B5B09,
	LensSettingsHideModeOverridePropertyAttribute__ctor_m55F57997292EEA24F873E04FE49DCBFC164CC82C,
	SensorSizePropertyAttribute__ctor_mEF509899C1DCA32660C83194E733DD37CD5FFDC9,
	TagFieldAttribute__ctor_m61C6F53F1F3A568C157AF57291DC5D9671D6FB7D,
	CinemachineEmbeddedAssetPropertyAttribute__ctor_m91E0DCE9A6C1A9A119CFEF51BED40A8D0B2B31C4,
	Vector2AsRangeAttribute__ctor_m2B72D311E1BC62ABD8D787969EC0BD7BE3A4A1D6,
	DelayedVectorAttribute__ctor_m9089F799241404BEA797144EC7F35C78810894BC,
	CameraPipelineAttribute_get_Stage_m4AEA2CCBA98F4AD9841920C816DB8C9A51AD6776,
	CameraPipelineAttribute_set_Stage_mB676D6070776D22A38CDC5BB4459433EC7CBCF5E,
	CameraPipelineAttribute__ctor_m280FFFB15246372A2FCF057BE91FD0C4D64C3012,
	RequiredTargetAttribute_get_RequiredTarget_mD1A2E2A9C3974853DEC8D46342381A9FA8E508FF,
	RequiredTargetAttribute_set_RequiredTarget_m6618F9EEB3ED26EE86ABE0143F536784BFDDC93F,
	RequiredTargetAttribute__ctor_m8E96FE95D2B1F037757077B97559335AED37FA67,
	ChildCameraPropertyAttribute__ctor_m45806417956F3B04999593459669AAB981E58147,
	EmbeddedBlenderSettingsPropertyAttribute__ctor_mE28735514D69AD992F020A0872E56C3980C636B2,
	CinemachineVirtualCameraBase_get_IsDprecated_m613885D06614FDC3439D6E3924D50B72E562B1D8,
	CinemachineVirtualCameraBase_PerformLegacyUpgrade_m8085B2C58C5E3B8F6D6FAE69FC6C46ED81977D62,
	CinemachineVirtualCameraBase_GetMaxDampTime_mD9CD7D72CDA2A8E4EF4CBAF378D69D9B90DEE48B,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_mC29470298D4619AF113007CED7665E4383EC6C74,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m0A2FB823A132EB8E051039559B27BC9B48A0FB9B,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m8B089604D8752F2559D41A428A5899A52E22067F,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m0C61AD23C6B627F433F89CCC77C9516343D9D05E,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m94A23EBBF40B410B5B7E62BC948D5200786F2235,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m5E89DE7F81D8633FB26ABF543D35E43829D8C94D,
	CinemachineVirtualCameraBase_AddExtension_m3BD9F04749A64A8927DEF686C4E2BC4F7F2FCE3B,
	CinemachineVirtualCameraBase_RemoveExtension_m8E64C77F8DA1E9C0DE78C1F34A767DC4AFAA74E2,
	CinemachineVirtualCameraBase_get_Extensions_mFDFF9C31CD292F5D3B52E5B8790C23738964DA18,
	CinemachineVirtualCameraBase_set_Extensions_m32A3850B56674A0792522B19B325B82D885E10B6,
	CinemachineVirtualCameraBase_InvokePostPipelineStageCallback_m308C5EA8BD881ACA6DE0AED007D4E867E568E2A6,
	CinemachineVirtualCameraBase_InvokePrePipelineMutateCameraStateCallback_m3B8747BAEC1265100B32B1BED7B43281B9FD03DA,
	CinemachineVirtualCameraBase_InvokeOnTransitionInExtensions_mF738122F63FAA369BB6D315FF52F2C131327E04E,
	CinemachineVirtualCameraBase_get_Name_m871E53110E7A7CEAA9B3FB4C7DA2FCA4CEBA8EF4,
	CinemachineVirtualCameraBase_get_Description_mCAE40209194E3B37A278C90B631903EC485843CA,
	CinemachineVirtualCameraBase_get_IsValid_m07F50E4AA48AD8725DC7946E61AD43CE0F92285E,
	NULL,
	CinemachineVirtualCameraBase_get_ParentCamera_m74F9284C7ADEE288B9CA32B868AC58C5A0AD79C5,
	NULL,
	NULL,
	NULL,
	NULL,
	CinemachineVirtualCameraBase_get_PreviousStateIsValid_mEC4F08FCD1B68E9BDA87E8A6C644AF1810E7CCE1,
	CinemachineVirtualCameraBase_set_PreviousStateIsValid_m3B16C7DF7E6204ED00717C698D82FAD764AFEBB6,
	CinemachineVirtualCameraBase_UpdateCameraState_m15B09CAA0B7B7C9C4BF21C3CB7EE8C381C17A2B2,
	NULL,
	CinemachineVirtualCameraBase_OnCameraActivated_m213854437800E918326AA65A83C6F954FC5020CE,
	CinemachineVirtualCameraBase_OnTransitionFromCamera_m967F7473B63B024076F068DD0F6121DF67A2D771,
	CinemachineVirtualCameraBase_EnsureStarted_mC27538EB83BF0880C70C5DB02DBA1AD9B6026950,
	CinemachineVirtualCameraBase_OnTransformParentChanged_m0BC7E299C926D756802D3E5B9AE7430C72EA3A78,
	CinemachineVirtualCameraBase_OnDestroy_mDF22FD418B1C6F6E396DE2ED91A41EB9ED5B7A8D,
	CinemachineVirtualCameraBase_Start_m77423323B924DDC4BA53429212FB9EBB6E63D941,
	CinemachineVirtualCameraBase_OnEnable_mF40447A683589D185774F668A336E4E4733483FB,
	CinemachineVirtualCameraBase_OnDisable_m3CCBEB61BC31AE740D17F1970151A78A1440FA69,
	CinemachineVirtualCameraBase_Update_mE773DD68C09FAE4B3D8E7417B4041BA4BC4C3362,
	CinemachineVirtualCameraBase_UpdateStatusAsChild_m79D5B6F938E972CB66CC5921725D0B533B349C64,
	CinemachineVirtualCameraBase_ResolveLookAt_m09C36D36693FB3C8F8B977250C0699D27F90DC03,
	CinemachineVirtualCameraBase_ResolveFollow_mC6034B56B6E59E7823CD6282487600D329D384CB,
	CinemachineVirtualCameraBase_UpdateVcamPoolStatus_m935D273B61760DD23EFFE49B4D98F1027CC46EF2,
	CinemachineVirtualCameraBase_MoveToTopOfPrioritySubqueue_mE32B50D0760F29F04290A0C152C4A27B335542F5,
	CinemachineVirtualCameraBase_Prioritize_m602C5C07F6D94E69AAA58C72D8028EDB862BCEF1,
	CinemachineVirtualCameraBase_OnTargetObjectWarped_m4B7CE07D5DCEDF2CF1F040E702FA8D9BDCBA4B82,
	CinemachineVirtualCameraBase_OnTargetObjectWarped_mD249AF7528240E89B96B4A8C11659984356A1230,
	CinemachineVirtualCameraBase_ForceCameraPosition_m957D2484B865D7747176068C5F413C3F9A3D9C45,
	CinemachineVirtualCameraBase_ForceCameraPosition_mA1E5EFDCD4CDB9F37B529EB133EF35412990ACF6,
	CinemachineVirtualCameraBase_PullStateFromVirtualCamera_m0FA7B4F6F570306E5B84B96791146EA02FF40AE3,
	CinemachineVirtualCameraBase_InvalidateCachedTargets_m876BC9A9C9A858AC645911683687A29B49F7E94E,
	CinemachineVirtualCameraBase_get_FollowTargetChanged_mD6319FA298724A1AC19C42E0E9143C2848B276D2,
	CinemachineVirtualCameraBase_set_FollowTargetChanged_m036680814C4B5C7E26382D133B8C5939E0FE10D5,
	CinemachineVirtualCameraBase_get_LookAtTargetChanged_m82BD81D2F6C0FB4202514520B6B4A521118301FC,
	CinemachineVirtualCameraBase_set_LookAtTargetChanged_mC2260BA382737B7F763D52288A2981CC24ABE5D0,
	CinemachineVirtualCameraBase_UpdateTargetCache_mE9C70E351F4FC628796B0C7C81E58CEBF7B4D162,
	CinemachineVirtualCameraBase_get_FollowTargetAsGroup_m00A6A7E9368F3FB19EBE03568559CB0495C741BF,
	CinemachineVirtualCameraBase_get_FollowTargetAsVcam_m9A9F1E6530607CB12ED58E1169D708314EE04048,
	CinemachineVirtualCameraBase_get_LookAtTargetAsGroup_m5DC6FDBBEC8FABB71620695BF1495A682357701C,
	CinemachineVirtualCameraBase_get_LookAtTargetAsVcam_mBDFB42FA8343B7343AFDA011228BF82263C365C2,
	CinemachineVirtualCameraBase_GetCinemachineComponent_m4008999DDB5B37F4B27F919A381120CC9F657305,
	CinemachineVirtualCameraBase_get_IsLive_mF74581E7EAD9D325DAA5830B8BE203D6726F7FF0,
	CinemachineVirtualCameraBase_IsParticipatingInBlend_m4628B6304130AF5AEDE432A9C578892EC6D20A24,
	CinemachineVirtualCameraBase_CancelDamping_m9266B95194ED49FC48D8DAF3990C2D18E778C102,
	CinemachineVirtualCameraBase__ctor_m54D6E829804A391FDCC9B80F99EBF79B081B32DF,
	ConfinerOven__ctor_m8E4DFA5A376CF2044613FF387B3CF8587090D215,
	ConfinerOven_GetBakedSolution_m8E157F43C4675432384387A93F5E7F91D4C96C5C,
	ConfinerOven_get_State_m9F261080E8975C6B12A13E57F8C511F9C78B62CC,
	ConfinerOven_set_State_mF2C4BCD19A0D0BE6EDEB120536CFAF44A85BAD04,
	ConfinerOven_Initialize_mEE7234B6AEA4DDE99864446B15FA825C9B9E8DDE,
	ConfinerOven_BakeConfiner_mFD48829388B5B12A9A2DA3E589A85B812E2E4745,
	ConfinerOven_U3CInitializeU3Eg__GetPolygonBoundingBoxU7C24_0_m82682694495C1BA133EAD6111F6AB8E65D695167,
	ConfinerOven_U3CInitializeU3Eg__MidPointOfIntRectU7C24_1_m3130A59D289EEB7D421CEDC30BF348DFE1BD8987,
	ConfinerOven_U3CBakeConfinerU3Eg__ComputeSkeletonU7C25_0_m321AB5A37B6FB1DE2A3B0FBC2F39C27C809083D1,
	FloatToIntScaler_FloatToInt_m2C8F87AC2231245186FF3E3DCACEBBB726ABC617,
	FloatToIntScaler_IntToFloat_mA505F9EBBC60E66115587256DE38F09C87FA14E8,
	FloatToIntScaler_get_ClipperEpsilon_mD87546D732EAE3830C6E78F06CB66A4D300E3583,
	FloatToIntScaler__ctor_m8088E3C77768B1CE534A7FDDFA5B912F4BBBCDB1,
	BakedSolution__ctor_mF821FC173A634C663606ADD16F19E962ACC26BFB,
	BakedSolution_IsValid_m262984864D41DD3745E300C7C1D4E9ED6E573122,
	BakedSolution_ConfinePoint_mB54B2B936BA204D2955AB7A50EC255747CF5A0D7,
	BakedSolution_FindIntersection_m4DDACC615C5FD803B8A327DB5C499602E1657C02,
	BakedSolution_U3CConfinePointU3Eg__IntPointLerpU7C9_0_m9EB77C25C2DBD2C3964460D7863ED496A3CBF90B,
	BakedSolution_U3CConfinePointU3Eg__IsInsideOriginalU7C9_1_m075371B86E76ADA99BD437AC809BEBC11D052F29,
	BakedSolution_U3CConfinePointU3Eg__ClosestPointOnSegmentU7C9_2_mB3960D474D34A35070E9567BCA209B78B898F1A1,
	BakedSolution_U3CConfinePointU3Eg__DoesIntersectOriginalU7C9_3_mA5BFA2AA066B746FF7A914E5AD1C3D6C1AC227D3,
	BakedSolution_U3CFindIntersectionU3Eg__IntPointDiffSqrMagnitudeU7C10_0_m92D483C95B9A39994A7A4A6B766D60270E14DC84,
	AspectStretcher_get_Aspect_m9E6B3A0AB70146498E1BAD185698573EC606F687,
	AspectStretcher__ctor_mFF295333F630563CEBB2E78810A0821F15AF4CD3,
	AspectStretcher_Stretch_m86BC52CD00CE835B7A1E8881877534E761E1A86B,
	AspectStretcher_Unstretch_m95AFEFCD91BAE10A23E6640BEA785C0082A327E0,
	PolygonSolution_StateChanged_mC389F789218AE923B31D6C0AA436436BCDD7CBCF,
	PolygonSolution_get_IsNull_m467E000AEC921A9EB76D814C0611F2BC9FB5AA15,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GaussianWindow1D_Vector3__ctor_m3EE3C181115DE9B21E26DC966C321F95D588971A,
	GaussianWindow1D_Vector3_Compute_m304EFF4256F3858789C38D0BAE6E179E749876F6,
	GaussianWindow1D_Quaternion__ctor_mE8D482808CEE826DEB5C00892D564B6531736EF2,
	GaussianWindow1D_Quaternion_Compute_m667A5E4A77FD4106BA958896296EEB1AFB8BF505,
	GaussianWindow1D_CameraRotation__ctor_m9E7724E984A051E3B006D29E736F7BDCE9DA3AE8,
	GaussianWindow1D_CameraRotation_Compute_m616882A244CCC5A8DA619DEDF0E80248E0ED27EE,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ActivationEvent__ctor_m3957EA43143C3F6745A6E55A7A3A33D6DE8E933D,
	NULL,
	NULL,
	AxisGetter__ctor_m034F15020098173B81CBCBE0426E38501DA6DAD0,
	AxisGetter_Invoke_m97C87897F65D4B47FB28D5262C0CFB450C9D4647,
	AxisGetter_BeginInvoke_mA522E78E9D646589CBD645037544B0EB0910C172,
	AxisGetter_EndInvoke_mEF4ECB882B8B8BF82E00230E23B0C62A3BC354E0,
	NULL,
	NULL,
	NULL,
	NULL,
	InputAxis_ClampValue_m9B998276127396874FBC7D934D22217A2B1F7983,
	InputAxis_GetNormalizedValue_m6A3EED70C1988D787B19B725D8071993F7EFD411,
	InputAxis_GetClampedValue_m8828F8C144F025EF8203C94F9A00FE2C26656013,
	InputAxis_Validate_mD5883DE4C3F177CB83F4554222480DEBD1B160A2,
	InputAxis_Reset_mFBC7BAF970104C4927D5F73C62BE55F889071DB9,
	InputAxis_get_DefaultMomentary_m26C9722370DAF0010279AA8ACCAF815F27061BDF,
	InputAxis_TrackValueChange_mCB6E35D0B19AEB110BC712F4B1075B13350FFCBF,
	InputAxis_SetValueAndLastValue_m0587AE4B73578F3144756FDCDA7DAE6DB7233067,
	InputAxis_UpdateRecentering_mA17553DCBBEBA694E54A12088A30EDE575DF5800,
	InputAxis_UpdateRecentering_m44844384DFA8B93AB3847B51B23B4F61223FD10F,
	InputAxis_TriggerRecentering_mDC766F564C495683B8F136D7A6E02C0360F710FA,
	InputAxis_CancelRecentering_m34D92FBFC0234058E6A7F68F10ABED0116F86558,
	RecenteringSettings_get_Default_m1DB630F62C13DA84B9675BB330F45830A6F3E0DD,
	RecenteringSettings_Validate_m6EEB2F6774899BDC39A2246C4D74AFEDC16D404D,
	RecenteringState_get_CurrentTime_mCF4B043C5A00951F3BAD6E1070657F481B6952FC,
	DefaultInputAxisDriver_Validate_m29DC58F7A6E8B2BD7C54C3FFDA88D602E7E59135,
	DefaultInputAxisDriver_get_Default_m5AD45D8E6860D5DBEC385009819B97FC64D59394,
	DefaultInputAxisDriver_ProcessInput_m942B6A2B5C55C8936D74BF2D62E872A1DC735A03,
	DefaultInputAxisDriver_Reset_m258D1F1BBAF2400C7449AEFD16C50BD5C536859D,
	NULL,
	InputAxisControllerManagerAttribute__ctor_mCF3A2BED03E1E31155D175DFA2C5DC128D228708,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LensSettings_get_Orthographic_m2AF510A849B57A9485FB4000835234532D83A2E9,
	LensSettings_get_IsPhysicalCamera_m2700AD42D56E90B57D18438C765F929E09CC6D10,
	LensSettings_get_Aspect_m24F38278056BAB1241945C9E75983B9A9FBDA177,
	LensSettings_get_Default_m81FA4439AD6CEAE550FFC9C9B95B7A1E391AF78E,
	LensSettings_FromCamera_m96A14A2FA3AF5787455A2E7034CFBF15B4F7BBAD,
	LensSettings_PullInheritedPropertiesFromCamera_mAC72C4DE1DA21BF3CE38AA8C318312D3EB8CA3F5,
	LensSettings_CopyCameraMode_m687969BF551F739E019BE706A6827F5E43D4BB76,
	LensSettings_Lerp_mB261958D7635251CC1350A7C8F37DBBDDC125BF6,
	LensSettings_Lerp_m17E59426452E33421D279C13D83BE1D9D64B37BD,
	LensSettings_Validate_mFE36866AB5B16F9B0405E410CACB999F29519265,
	LensSettings_AreEqual_m3354C383423AF6EBE6E8AB2A9CB99F167F45F31A,
	NoiseSettings_GetCombinedFilterResults_m36851B75C2834CDED986361FEAE884B0F07818D7,
	NoiseSettings_get_SignalDuration_m604F891E67C1AB1DA9B3C7FF6AF926B2FA52615F,
	NoiseSettings_GetSignal_m81854E60FED3D1F5A75EC9CA396A053D262D3D8A,
	NoiseSettings__ctor_m2AF765DD9D96EFCF99246FA051E7FBD5889419D4,
	NoiseParams_GetValueAt_m1B5D915FA8782DAD5230854FA7FFDA503A34D021,
	TransformNoiseParams_GetValueAt_m757A109B904184C5AAE5BD2B466DC80026474FF6,
	PositionPredictor_get_IsEmpty_m234E8623FD6865187578B480D0FA79B3E0F8AB05,
	PositionPredictor_get_CurrentPosition_m0736A2E1D68141B5BEC2BD9078B2624EB0005018,
	PositionPredictor_ApplyTransformDelta_mC33429167317BB4EE95AFF4EB66E341958463DA4,
	PositionPredictor_ApplyRotationDelta_m0EDD7BF0D0321D32668DD102887CF8F90CE6CA9D,
	PositionPredictor_Reset_m1C332992802B45BCD6677A1C04B54F9FE47962D8,
	PositionPredictor_AddPosition_m521CEDF019A2B4289EDBDF17B7478524B6CBB67F,
	PositionPredictor_PredictPositionDelta_m58018C09253901A9A4D1A39325684D1EB82CB882,
	Damper_DecayConstant_mECE09E43773ACC93199D7D3409F52435E5AC42B5,
	Damper_DecayedRemainder_mEF68D05AF9EBD9B88C177D6A201B92CD4E929672,
	Damper_Damp_m8B702D3084078F17BEE09AAC063D6F4C2AD1C176,
	Damper_Damp_mEC5B6A02F900E7E74103A42D1BECEBFC917285A3,
	Damper_Damp_m07CBB05E6E8B73B96260FAE384C12B46197D090A,
	Damper_StandardDamp_mCA3A60AA8AA9FEE0F54BC777803AA9000A9C8DC1,
	Damper_StableDamp_m044117749C70B505BE64CA5795C088B8AF087BCF,
	AverageFrameRateTracker_get_FPS_m3E937ABDD808857917AE4CBB2EF54864A9EF5015,
	AverageFrameRateTracker_set_FPS_m955C66FEB47D5160435325A50F09BF777BC3C019,
	AverageFrameRateTracker_get_DampTimeScale_m73BE3180C4D680E898689656044927E7FC98A33B,
	AverageFrameRateTracker_set_DampTimeScale_m7BDA8485592B7A10468008DD109315D7B4F78084,
	AverageFrameRateTracker_Initialize_mF98B5D72BB69E30D6B407100A46B54168D7350F2,
	AverageFrameRateTracker_OnSceneLoaded_m19A47294EE213FC24B4A1F6B481168ED2A9AC9D9,
	AverageFrameRateTracker_Reset_m26A9D5BF8F446765E7AD662CC8732AF4354C811F,
	AverageFrameRateTracker_Append_mAB6A06DF0ADA879BA57B48FB0A42C669B85D2668,
	AverageFrameRateTracker_SetDampTimeScale_m3DEA9974A8037A4F35B9124BE6E25B4639C9DD84,
	AverageFrameRateTracker__cctor_m1713DA0E413D248984A39A69B30549E5CC3957CB,
	PrioritySettings_get_Value_mD2377C4F969CCA1E306F3A188F78DA1C31CB5F3C,
	PrioritySettings_set_Value_m6C9D23382F5F5AE929899110938132552027B440,
	PrioritySettings_op_Implicit_m365AAF1AFE4BF159BABF41A0CE6B46B2B72F5FDB,
	PrioritySettings_op_Implicit_m70FEAAC6052DEB1F4FBF49DF618F99640E49B6E3,
	RuntimeUtility_DestroyObject_mA0B0FC6E7BBA370D6E580ED130E51E03380AE3C5,
	RuntimeUtility_IsPrefab_m6DC9033458A8E47262F121AF7680C633C3F91B48,
	RuntimeUtility_RaycastIgnoreTag_mB9C9C3736B95648C2FFD5C19588BFACAD5730A59,
	RuntimeUtility_SphereCastIgnoreTag_m276D5530B79F1752104575E1EC4A5EF7BF8E346E,
	RuntimeUtility_GetScratchCollider_mC8016F3A33DF0E369622045CE248807109F16D80,
	RuntimeUtility_DestroyScratchCollider_mD1BFC728D834B9FEE35DF2405E554BF2B7576C2A,
	RuntimeUtility_NormalizeCurve_mD9A17D430CC9591AAAD8406BA128F16BE6AD109E,
	RuntimeUtility__cctor_m7EFCCFDD999C9B16DDA4498ED6CCFBE9854A5B75,
	ScreenComposerSettings_Validate_mC2F21833234FFE27191159ADEE6A2CC6DAC38CCE,
	ScreenComposerSettings_get_EffectiveDeadZoneSize_m67B0C04C458112121CFE381069227FF96470EA61,
	ScreenComposerSettings_get_EffectiveHardLimitSize_mC4A9B50A42FD6446505DCC222EEEFCD688B8CE29,
	ScreenComposerSettings_get_DeadZoneRect_m411B6054F72D2D6CD33562F3970EF01E7A4D25E8,
	ScreenComposerSettings_set_DeadZoneRect_mB8DAA838A8727F3D9F52F92C8E37E0B2EAAB1991,
	ScreenComposerSettings_get_HardLimitsRect_mAAF8C4A2F753C614FEF2107191DFE3112C665743,
	ScreenComposerSettings_set_HardLimitsRect_m537078AB83AFB3298BDFEE946951D8628974E42C,
	ScreenComposerSettings_Lerp_m43E5EDDEA7F6AFA0C71BA8565720E9604C709F20,
	ScreenComposerSettings_Approximately_m4C15F068C20E333005A6B0EC55C2FA59CDC4D4FF,
	ScreenComposerSettings_get_Default_m553CB9FD1CE1817FE53DB24DF207EBE4AA299D08,
	NULL,
	NULL,
	NULL,
	NULL,
	SignalSourceAsset__ctor_mB4028F5F34A0DFFA7645D3A9E4135D46AC14A5F9,
	NULL,
	NULL,
	NULL,
	NULL,
	FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Validate_m74C78176F5772767ED80F04F3E247404A4C95040,
	FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Reset_mA6CDBC5DA8F6FD983BA2B8689D8590253533959B,
	FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_get_RequiresTrackingTarget_m5140437E08AAB7A0D171A2C04F91353E377EED5A,
	FixedSpeed_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_GetSplinePosition_mDAA9D3C6FA24CA31D8DCBED5D591B120E9336C8C,
	FixedSpeed__ctor_mA3B4AAC6F424ABA14E28F6A8E1980804666194AD,
	NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Validate_mDA5CEDE38EDA13DCAF878AE1DFB82E046DA1A56D,
	NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_Reset_mE3BA74818517653A88BA058FF5F2480B10D1A79C,
	NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_get_RequiresTrackingTarget_m1B57D3E66084390DCBB1271D022EECF7899D8FB3,
	NearestPointToTarget_Unity_Cinemachine_SplineAutoDolly_ISplineAutoDolly_GetSplinePosition_m98A3A8C1697C1501669ABC080E162FF202E03DF1,
	NearestPointToTarget__ctor_m5416A1E0BA75E8BC555716CD60641588419BE9C5,
	SplineContainerExtensions_IsValid_mD729728516744EA63643D0C3A80DA513D4B80139,
	SplineContainerExtensions_LocalEvaluateSplineWithRoll_m459BA6DDB25FABDC356F1799A0BB5D8F88AACD4D,
	SplineContainerExtensions_EvaluateSplineWithRoll_m5B03FC3538975CBD17D3C0410A14C3C804B6D6DD,
	SplineContainerExtensions_EvaluateSplinePosition_m0BB77C79547F3F45159C6642910744956663309D,
	SplineContainerExtensions_GetMaxPosition_mA82D00477FFB951A386CBCAF23334B3ADDC9146C,
	SplineContainerExtensions_StandardizePosition_m2758EAAE06875D8E2CF9DA156A12027945F88A11,
	SplineContainerExtensions_U3CLocalEvaluateSplineWithRollU3Eg__RollAroundForwardU7C1_0_m9BF64D75EC98C4763BF0F5BB53A2B140514281BC,
	SplineHelpers_Bezier3_m7F87EF920A07BB0720AC2EA2E44141D21062C5DC,
	SplineHelpers_BezierTangent3_mB410323048107D35E51789592EAB662D7A847E80,
	SplineHelpers_BezierTangentWeights3_mA00093AFE65074A33EAEE1E0DD85E92CC8F6C2CB,
	SplineHelpers_Bezier1_mE6E32E17731E3460E8D896F419E8D1C7550CD1C8,
	SplineHelpers_BezierTangent1_m9C8CD3661CF2D2C19D03D7E5EA947721C9C12FF5,
	SplineHelpers_ComputeSmoothControlPoints_mFCDECAA84A1F671B82A5AC8EC0AF5C187EC73630,
	SplineHelpers_ComputeSmoothControlPointsLooped_m44F3F79DF0F25AAF3593E1BDD7B83D7C1BFEEF94,
	SplineHelpers_ComputeSmoothControlPoints_m31F4B7535C0879B100AE10DA4A94EF5A80AC4366,
	SplineHelpers_ComputeSmoothControlPointsLooped_m2A824F8E1A547B9775D57D7B3C0354350B48C6A3,
	NULL,
	SplineSettings_ChangeUnitPreservePosition_m5BA3FA8B14F1E0F650D9C8605BC4E38D8EC62956,
	SplineSettings_GetCachedSpline_m413EFDEC0D7E7541E17A2D493371FA599D8AFFA1,
	SplineSettings_InvalidateCache_m99A3ADEEA8ABB0949C26A23C867A91DCC2F3BB88,
	CachedScaledSpline__ctor_mA369524A88F256C2FB0D34BA35FFF509DA71118F,
	CachedScaledSpline_Dispose_m6429258694491EECEEF5494B4D901A567472943B,
	CachedScaledSpline_IsCrudelyValid_m4DFADCA4BF37C994A63D212550F244D5C91643AA,
	CachedScaledSpline_KnotsAreValid_m3B0B6FD6F04827995F9880FC8F11EC41DA74A8BC,
	CachedScaledSpline_get_Item_m6F910A22DAE4709DD615B16665D66E0BE63EF3C3,
	CachedScaledSpline_get_Closed_m5D5CDD69F92593D7B75B6A893C5DB0976639E433,
	CachedScaledSpline_get_Count_m1CC3161473832424C73EF28BAF2EAC303A027260,
	CachedScaledSpline_GetCurve_m1AEEA0E3D177D87B71DCA29F8597ECF149CEA66A,
	CachedScaledSpline_GetCurveInterpolation_mB29A42613DE9D6C00ED17F9014634676DD566F3A,
	CachedScaledSpline_GetCurveLength_m13FB517E3656A50364C4A88A74D6129F860203F9,
	CachedScaledSpline_GetCurveUpVector_m2C9CF45C5C6CE5B2E505A10A5CA0D3F01BD086D1,
	CachedScaledSpline_GetEnumerator_m7E754D3593DE9207E0E54A7646025F763ADC7A74,
	CachedScaledSpline_GetLength_m17FBE003936998B1B641822316C35BD361416A0A,
	CachedScaledSpline_System_Collections_IEnumerable_GetEnumerator_m814F488A676EF28CBD29CA515C55F83A60081781,
	TargetPositionCache_get_CacheMode_m045C1D7C48006FE8F2167125AE517E7520BAD987,
	TargetPositionCache_set_CacheMode_mDC64D9C79153B9B3D7591A4AE9149F4B96CCD41F,
	TargetPositionCache_get_IsRecording_mB16D76FE556847B99334AEE28A2D6361C376D5CC,
	TargetPositionCache_get_CurrentPlaybackTimeValid_mEBC82C9F30FEC88B9EE726ADD2EEC0414FFE9CC1,
	TargetPositionCache_get_IsEmpty_mB02355A787E17C773C225FCE0C446C3C67558AFF,
	TargetPositionCache_get_CacheTimeRange_m44600940141B87875AFC6758676C553305FEDDB3,
	TargetPositionCache_get_HasCurrentTime_m3DC0F1E1E64AA355107519490672AD8DDFD15DC9,
	TargetPositionCache_ClearCache_mEAC1BADA50EB4F8C255577D677854FEF5007398A,
	TargetPositionCache_CreatePlaybackCurves_m234A8A18A44B60771384489180DB25D0925E0FB8,
	TargetPositionCache_GetTargetPosition_m56E56AE498B6762B222110307704C07651D39364,
	TargetPositionCache_GetTargetRotation_m34E682B381F4E781734BB027AE7C912EF2F743C1,
	TargetPositionCache__ctor_m030DA1B7921F25550F415CEB1053E0BB9D734135,
	CacheCurve_get_Count_mC1ABAF4434BF406623444D7EDD8CC37D7F0D0011,
	CacheCurve__ctor_m188804DE79B8AD04C9A40DAC5525F650C8D1D5BE,
	CacheCurve_Add_m6CD994E3DCFE5D0DB41B294C445CE63CEE47D82E,
	CacheCurve_AddUntil_m68BFB175DBB6BD27C30FA4CADB522E101750DA42,
	CacheCurve_Evaluate_m278E0F3E2E25A3B42D9A5E6F3357AECE4BC6AAD7,
	Item_Lerp_m823202A1C7D0027FDB52BB980BDE7C927D15BC1D,
	Item_get_Empty_mDB4BE921C2BB309083E40F1FCA190AA7FBF06284,
	CacheEntry_AddRawItem_mD06A71DA3CC675F4A139D0DA1A0FF47113D486B6,
	CacheEntry_CreateCurves_m56B64C6585A01F270335F7EE178682D4DF5D684C,
	CacheEntry__ctor_mA5627E1A79382A0606EDE21F3FD85254F0AE2D54,
	TimeRange_get_IsEmpty_m856A3BAB8551D7AE7EBECB42CE4FEB20F0EEFF24,
	TimeRange_Contains_m29397CAB8AB82D9C8C1510F37E5F86648B25E85F,
	TimeRange_get_Empty_mFCD38C47F562F5FE409AE83A4F6857386127B2F5,
	TimeRange_Include_mBFF0A417B53F24B32B5048D99E99EE9FD32BB103,
	UnityVectorExtensions_IsNaN_m969CADE1F5A725344E04DD5AC802D31DB1B8D79B,
	UnityVectorExtensions_IsNaN_mED320A7D2D91E180476D34DA16B49F46F54F1FE4,
	UnityVectorExtensions_ClosestPointOnSegment_mA34DB043BC4C8A159E4B83C34DA4170EF1646090,
	UnityVectorExtensions_ClosestPointOnSegment_m45E515427CD8561DEED42A4DE9675D86EB0E9195,
	UnityVectorExtensions_ProjectOntoPlane_m77B38EF796EB3B782B0776EEEA193E623A445ECE,
	UnityVectorExtensions_SquareNormalize_m3910DC5715713B7331CFF9BCAF2C0464B6D80073,
	UnityVectorExtensions_FindIntersection_m9830D118D76BDE138959B24D51A946D405C00289,
	UnityVectorExtensions_Cross_mEB2DDD692E0953048DFD57C8A48A020CA5876F61,
	UnityVectorExtensions_Abs_m84660BB13F23722B12A558CD56C81E7F9291BD34,
	UnityVectorExtensions_Abs_m953FE3119ADCE56D51AA2E26B1602D66D233955A,
	UnityVectorExtensions_IsUniform_mEAAB9C36B34502A0627A75C0C659356019A8FEC6,
	UnityVectorExtensions_IsUniform_m40B08DDF0E9EE9DF582EC84E82906969556441BE,
	UnityVectorExtensions_AlmostZero_m3DEF48A1216986914EDC18611380D48098B680E6,
	UnityVectorExtensions_ConservativeSetPositionAndRotation_mE464415AAC8D77CD5063AEC67872E9B09300AEBD,
	UnityVectorExtensions_Angle_m0AE04C65BFE17AECC8004BFA8111C5EAD9879FE4,
	UnityVectorExtensions_SignedAngle_m89B35E7B4423DDAD39D7B0B8870B284F8BF68B6A,
	UnityVectorExtensions_SafeFromToRotation_m2224E4AFA4D4690FA9A64F04CB2FCE67305379FE,
	UnityVectorExtensions_SlerpWithReferenceUp_mD396806ED54804F5F98F44AEFB49F7FC5C541358,
	UnityVectorExtensions_NormalizeAngle_m74253FF7FC9B096D66A73BDC5384E67AFF75F42B,
	UnityQuaternionExtensions_SlerpWithReferenceUp_mA259B0FBF1578FE283D1EABB1A955C3026699054,
	UnityQuaternionExtensions_GetCameraRotationToTarget_mAC945EFD2ADC7E99FE351F20933BCD77B84F87CE,
	UnityQuaternionExtensions_ApplyCameraRotation_m9B52A2FC0546F44A767DB8F57533FB5E617F0E4A,
	UnityRectExtensions_Inflated_m7EAF6244D1A316CBE7D88E71E83104116F9707A9,
	UpdateTracker_InitializeModule_m319C8F8C3961A8C8AA1019EDD79B69F8448582C7,
	UpdateTracker_UpdateTargets_m7F6B2C530C2A66D3849DE425B9955FA488F823F3,
	UpdateTracker_GetPreferredUpdate_mB656C104D1BA1843C031F997F18BBCC7315C5332,
	UpdateTracker_OnUpdate_m3839C252A196C7D01FE504B3A2144BD372A968F9,
	UpdateTracker_ForgetContext_mA8B5E5E8B8A72D0E6AAC0EE20CAABF0EBE2F43E8,
	UpdateTracker__ctor_mF8CDFBAC6B41A61ED8EE64570494676A03F1F295,
	UpdateTracker__cctor_mE94BDD134865DCD7BADD25A78274F03C3E46859F,
	UpdateStatus_get_PreferredUpdate_m0139ED8CDB6CF356E9BA0F69E3C0D54347FA33B1,
	UpdateStatus_set_PreferredUpdate_mF55CC6B2BE19C4C8CFF1889A534353F691E47A43,
	UpdateStatus__ctor_m2E41EA71CE4C03DE7623A29DEE62EBDF5E207B61,
	UpdateStatus_OnUpdate_mEB3A3378AE81829D0F38E2B13AB0CF93CD265F50,
	VirtualCameraRegistry_get_AllCamerasSortedByNestingLevel_mB8443929A6E1FDD13F160448DBFA132B3975523E,
	VirtualCameraRegistry_get_ActiveCameraCount_m0F6EBF82D74474F4A68AB0246A31FFF0477BD3F9,
	VirtualCameraRegistry_GetActiveCamera_m969F12A3F8618BE1AB5C1580DC4B3C8819192E09,
	VirtualCameraRegistry_AddActiveCamera_mAF49B92C4059EFE30EC65E74987ACB190883CED2,
	VirtualCameraRegistry_RemoveActiveCamera_mAC546C41967072CA13337EE5A936F7DF2CF851FE,
	VirtualCameraRegistry_CameraDestroyed_mF341421E602B220D976AFFF4D99FB526F52ACFE6,
	VirtualCameraRegistry_CameraEnabled_m6BEE51817F6B30DF69280AB447B850BEAC363BFB,
	VirtualCameraRegistry_CameraDisabled_m0B9CB64455F12B3F7DFD5E4E60FB6A1C36307B7E,
	VirtualCameraRegistry__ctor_mE08BE68203AD239DD0A9E3C00F288B466937D55B,
	U3CU3Ec__cctor_mE03791F29689884B39D0989EE68F6E4EEBAF1D8B,
	U3CU3Ec__ctor_mF1B46FD8D59C1E93AE5079A978A38502589E72BA,
	U3CU3Ec_U3CGetActiveCameraU3Eb__8_0_mD2F56E9122FBF90DB68E7930C072C6AFFFB62A5A,
	CinemachineDebug_SBFromPool_mD72B0073894675497D6C33D3CDD40E7C21E01316,
	CinemachineDebug_ReturnToPool_mDEC25F0380F48FA13289721CE5D5BFCD42A44634,
	AxisState__ctor_m23329A65D540AACEA3D12AD0E31EB3998A90514C,
	AxisState_Validate_m3B61D89177847EE19351A98751E603C7039ED1B8,
	AxisState_Reset_m09D45EA8AF1672C0AFB8C8186C2116970BEAE363,
	AxisState_SetInputAxisProvider_m9D63DCDEF7DBC2134E1240F0A181F65F212AF3EC,
	AxisState_get_HasInputProvider_m6E45E8D133FACCFD8447188FD32B43509FB925BC,
	AxisState_Update_mD7790DB75E0B27E7A684A1191A7D70E29AB707E6,
	AxisState_ClampValue_mECEF16C692361CF42EEA975781D7001F0AD3BA8D,
	AxisState_MaxSpeedUpdate_m712A58C8CBB77C9FE3218938A92CD4EA1CE73C6A,
	AxisState_GetMaxSpeed_m6F8A4B4AE39B234407D6CAA3A4456D2832727097,
	AxisState_get_ValueRangeLocked_mD7974E42395AB05026ECEC99828FBE7918E86368,
	AxisState_set_ValueRangeLocked_mC5ED4CAF1070E329AAE7B21894201F727550E3AB,
	AxisState_get_HasRecentering_m8470DD49BB3D804C165A68A035D70C0BCBF3BAB2,
	AxisState_set_HasRecentering_mA36545B79D4DC3862C5C33EC8724DCBF4FA23404,
	NULL,
	NULL,
	Recentering__ctor_m3B930E9904BC3388AA82FBEFC51E14011941CA5E,
	Recentering_Validate_mD19D0E03095F0E10845ACE1522A0DF6E491AB13C,
	Recentering_CopyStateFrom_mD5862CCAAD12BE76C99F664A3B8E7DD5046D75B0,
	Recentering_CancelRecentering_m1AF21DE017EAC0419747C0CAF3C366DFCC478075,
	Recentering_RecenterNow_m79D915974F8ED61A61E2A876EAD98CCC3AD0B41F,
	Recentering_DoRecentering_m3A4E02DD1E33E97FD311BC711C2C00E3AC541836,
	Recentering_LegacyUpgrade_m3ED8B90A073DCC80ECDEB056019F73656C89C539,
	Cinemachine3rdPersonFollow_OnValidate_m9F6C0D9BB847E0CA037E3BA785284E313258631B,
	Cinemachine3rdPersonFollow_Reset_m7C6DFBA16328009BF966A9119107BD280CDE0BDE,
	Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m0DBC0C005FD8C8AA8237A8BB6B9FD0EB68844320,
	Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_get_PositionDamping_mD7FF345DC7E06DE26F561159CFEF0BD316ED3066,
	Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiablePositionDamping_set_PositionDamping_mDDB3DFB3C48D921B33B52DF4928E5BE91FF9A2EC,
	Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_get_Distance_m711F1DC5434664F69050E246F07342F2D75218B9,
	Cinemachine3rdPersonFollow_Unity_Cinemachine_CinemachineFreeLookModifier_IModifiableDistance_set_Distance_mA27F08BBB1F4D18F5A56BFD5696A5A34D7B685D0,
	Cinemachine3rdPersonFollow_get_IsValid_m7071E49663EB128FE7C1D69129231D6FF1AECDDA,
	Cinemachine3rdPersonFollow_get_Stage_m20CA88F58548A2A4392B1026BFDE179308768FBE,
	Cinemachine3rdPersonFollow_GetMaxDampTime_m39E41649E32C236182C0F0F4B1F2EAE9DBEF2FEA,
	Cinemachine3rdPersonFollow_MutateCameraState_m23D1DDCC45F8CA37C0B1956AD7C9122F14538148,
	Cinemachine3rdPersonFollow_OnTargetObjectWarped_m41953DC07F4CF0E45AC1DACD73ADB6B0F09F41CE,
	Cinemachine3rdPersonFollow_PositionCamera_m87F615F422A2E02716B0E3FD5EA08E11FF473AFA,
	Cinemachine3rdPersonFollow_GetRigPositions_m2571B8C20E3C9498A8BB6499D752F5D5BF226EAA,
	Cinemachine3rdPersonFollow_GetHeading_m8CE3DE92A79041471AEFF16D9132C3A51309B206,
	Cinemachine3rdPersonFollow_GetRawRigPositions_m626519EC404F8B92BA2C8BCEF974FE66C52DEDA2,
	Cinemachine3rdPersonFollow_ResolveCollisions_mC466354440257439A82F60A3E795F6DAA4D701E6,
	Cinemachine3rdPersonFollow_UpgradeToCm3_mA87BA3939F4919A9D9BA48DE7DE82CCBC26592D0,
	Cinemachine3rdPersonFollow__ctor_m0B53B501194B1041012F40DAC36BE24267FF0C8F,
	CinemachineCollider_IsTargetObscured_mD20DE581C8C4636ECDF387A9475FE15187842B3F,
	CinemachineCollider_CameraWasDisplaced_m15065AC55D0DBFD7D8E6B14974FEB410DC6F444D,
	CinemachineCollider_GetCameraDisplacementDistance_m155C6CD9EC3247DF447DBB41AE9688CAB3BA0461,
	CinemachineCollider_OnValidate_mB72B9B243D1E0DD3CB7AC37C5161CB8B3224AEC6,
	CinemachineCollider_OnDestroy_mE44616F55BA4B9BBF4D8B7550287867D82027362,
	CinemachineCollider_get_DebugPaths_mC63AEBAB5FE270117FF3984D274C58115453C755,
	CinemachineCollider_GetMaxDampTime_mFB2BD485F4256A819977ABA0175AE82790DBAD41,
	CinemachineCollider_PostPipelineStageCallback_mAD4A444EAA6CA5CADB903EA004A9050A3CBB7038,
	CinemachineCollider_PreserveLineOfSight_mDA910A155CB6C165DB4460C111F2ACFB81C8D70A,
	CinemachineCollider_PullCameraInFrontOfNearestObstacle_m9BA69562C222452DF684B692DC6FC83F0DB8F890,
	CinemachineCollider_PushCameraBack_mC6036621C388D6795EC44B38BBC3D773B50A161D,
	CinemachineCollider_GetWalkingDirection_m500FD2E00E7C14EAE5728CC5939757F4CCF9B964,
	CinemachineCollider_GetPushBackDistance_m3B75E64A915E54B788963981B452BE9E3507C3B5,
	CinemachineCollider_ClampRayToBounds_m89AFAB24FB9A4E473081F776DDF59976B38F7615,
	CinemachineCollider_RespectCameraRadius_m6E2CDF18BA0299F9F28073C0BCA62571A3187A7B,
	CinemachineCollider_CheckForTargetObstructions_mAB8330FC7BB13D34AE808A537908D41399907FDB,
	CinemachineCollider_IsTargetOffscreen_m0D77F28C65AD30E489B3FC6E9DB7A1C3BBF01B64,
	CinemachineCollider_UpgradeToCm3_m131DC61A9DC9BDFBDDA04C862151832F2E802FBF,
	CinemachineCollider__ctor_m33504A6939C585E40E315ED21A0FEE3E1410600E,
	CinemachineCollider__cctor_mF5B31519C19F7C6AB66C3E2C1D9027AA64ABC7D1,
	VcamExtraState_AddPointToDebugPath_m2F14422ECB21D5AAF9CFD529C8068A1A2E95B6C9,
	VcamExtraState_ApplyDistanceSmoothing_mC5A5550408F64899458236636EAD1418598882A7,
	VcamExtraState_UpdateDistanceSmoothing_m9303D36B6F129A3A32F89F69B28505A25121685A,
	VcamExtraState_ResetDistanceSmoothing_m01F1110A923E433EB5777EEDEC104ACD7CC9FBDC,
	VcamExtraState__ctor_m3A3EFCC13C5242E1D3FE4ABA92D155E7A81C6EDA,
	CinemachineComposer_get_IsValid_m48B424C1F6EC85269987ECFD2AD1C4129D720AE1,
	CinemachineComposer_get_Stage_m33E24995C8957A1EE3002DBC32BA8E25D367AA32,
	CinemachineComposer_get_TrackedPoint_m17FA628CBAA4C2A9C8CC98652FD04538AAB4BF40,
	CinemachineComposer_set_TrackedPoint_m82C5CB9EF35902C59B954D592DC1DB4FB24CF949,
	CinemachineComposer_GetLookAtPointAndSetTrackedPoint_mF5563C01405F05E5852AC3A21666364088F26E97,
	CinemachineComposer_OnTargetObjectWarped_m56391CB3BAAE11B0912083EF703A504B13EBAAF2,
	CinemachineComposer_ForceCameraPosition_mFAC884E7992FED519A04A0A5BACE32127EDC0EEA,
	CinemachineComposer_GetMaxDampTime_mE24B53C789A44988C806891D8812CBD7AA08AB63,
	CinemachineComposer_PrePipelineMutateCameraState_m64D80C52C996B2070EC268B7CF3C9E51658C2C4A,
	CinemachineComposer_MutateCameraState_m8A4D21D4793E8F0D6C31A6EA1192B6380CB198CE,
	CinemachineComposer_get_SoftGuideRect_m508DA0621951A72CFAC6CC0F7BE523552062A433,
	CinemachineComposer_set_SoftGuideRect_mFF86B04741A25AFFB1ADB8F9D412FBD293AC5882,
	CinemachineComposer_get_HardGuideRect_m764D5861C1F9CD9529B02128CCE9EC3E2EF51C4F,
	CinemachineComposer_set_HardGuideRect_m3EEE252D72860A6D063936487B55FB15DCB80616,
	CinemachineComposer_RotateToScreenBounds_mC52D231D2CED0D0A467DAAD1AE91F2DDE4421C2C,
	CinemachineComposer_ClampVerticalBounds_m36F538FA96783C84D68AA0AC20AEEEF61CEB801D,
	CinemachineComposer_get_Composition_m93221C9589DDE8EE4CE8AB55FDD676C46B4A2AC7,
	CinemachineComposer_set_Composition_mE79927D911D5D3460F5AFAAA40DC5F8232037E7C,
	CinemachineComposer_UpgradeToCm3_m75D24B12D97695155AAB8E54805DF738A7404955,
	CinemachineComposer__ctor_m0B3196E312A2EC9255A45BC60D6D8732382DBD19,
	FovCache_UpdateCache_mD5487E1D88B365D823D207D4C18DBDC120481CE4,
	FovCache_ScreenToFOV_m7FC58E4F1B8DC1FECB4AF316428058A48ED7AFE8,
	CinemachineConfiner_CameraWasDisplaced_m5D18B778CA884292A3C4B0D530B407C4F3070F99,
	CinemachineConfiner_GetCameraDisplacementDistance_m68E820FD079FAE1F729541182B1158DD908CCEBD,
	CinemachineConfiner_Reset_mF3955188F3046A9341DCE61A243DB756FF37B618,
	CinemachineConfiner_OnValidate_m56702690476247E0D295124EE39380DDEF0CE096,
	CinemachineConfiner_ConnectToVcam_mF6D90F7C813E6E0A6ED254B2E861413293877B0F,
	CinemachineConfiner_get_IsValid_m9967184F23A332245BAD9B0A6BD1822ED7D972C2,
	CinemachineConfiner_GetMaxDampTime_mABC2BA69E53D07C4D1074DDB6BFEEDA1D3FE16BA,
	CinemachineConfiner_PostPipelineStageCallback_m7D915A3761FFB3542BA0B2234D42233CE29E6AAD,
	CinemachineConfiner_InvalidatePathCache_mA9E10FC549EF941F56833F5F433DD36796DCF107,
	CinemachineConfiner_InvalidateCache_m13504547336071A6BB0543126C3E29674276C844,
	CinemachineConfiner_ValidatePathCache_m11439C5FCA2C74D4EA43903A868E6B4F84ECE994,
	CinemachineConfiner_ConfinePoint_m94102EC70561D2CAEDAE1A2BD6C3F4FD828BD2B1,
	CinemachineConfiner_ConfineOrthoCameraToScreenEdges_m6667AF5C52440D7C6047AA4D67DF5351F8C60C00,
	CinemachineConfiner_UpgradeToCm3_GetTargetType_m814C33FD220CD68183BBE8B3AA83BC70471EC1DE,
	CinemachineConfiner_UpgradeToCm3_m19333C2758F346D7D34138767B3A90AD51AAC73C,
	CinemachineConfiner_UpgradeToCm3_mCC4ABA4A6B7364D415817B013BCA2D2D342F3D43,
	CinemachineConfiner__ctor_m996E0EF507917902647FB591B6E360029F620C9D,
	VcamExtraState__ctor_m2C9EED514335B9004BF926FC0F2AE35F02FE9253,
	CinemachineDollyCart_FixedUpdate_m6B014ACF8780CA4A65858F1F964614177A697D7E,
	CinemachineDollyCart_Update_m89F7DA17A6535C04AC92E059198A6C19614ABEE5,
	CinemachineDollyCart_LateUpdate_m32FB9B11366B1A4BDCCB3BC2D07D1DE363F9CAB2,
	CinemachineDollyCart_SetCartPosition_mB1F5E38A82198AF14B3F54EE6EFCF3F8E23B68AE,
	CinemachineDollyCart_UpgradeToCm3_m57A906704D3EBB9D3B4F5601129BEB070F133434,
	CinemachineDollyCart__ctor_mA47AA45EF8ED3E108C7C81D9462E927891D18562,
	CinemachineDoNotUpgrade__ctor_m2BCE8145D7D6BD8C0EE030D592FDE86D1F1DFEE3,
	CinemachineFramingTransposer_get_SoftGuideRect_m83D20AE6B97BFF1D18D87A7A815B78CDE8BECAD8,
	CinemachineFramingTransposer_set_SoftGuideRect_m243193E77EC03F978424812032587299BA0EADC2,
	CinemachineFramingTransposer_get_HardGuideRect_mE4FE9E3A32E01D0097AA3A359C72EE4034865710,
	CinemachineFramingTransposer_set_HardGuideRect_mFB7AE55FA001912BFE038C05DBB1DD19417DD53A,
	CinemachineFramingTransposer_OnValidate_m5CC34FC8001530400C820997C6BE49214175ACC8,
	CinemachineFramingTransposer_get_IsValid_m2D98BD599DCDDB47F69C7878A794971DD536124B,
	CinemachineFramingTransposer_get_Stage_mE1310804108A2AD0D2B48F3197869DD1CD453A1E,
	CinemachineFramingTransposer_get_BodyAppliesAfterAim_m0E09A60E5FA964DF453794687C8C6FA420592018,
	CinemachineFramingTransposer_get_TrackedPoint_m3D58B30506EB65466C961C0E64234A2928EF75B5,
	CinemachineFramingTransposer_set_TrackedPoint_mA0CA2C5597E3FE901D32B4CAD82D203D72A44932,
	CinemachineFramingTransposer_OnTargetObjectWarped_mA71D169CB700B4B8DF0A9F42D74C02E3DB308FFD,
	CinemachineFramingTransposer_ForceCameraPosition_m21EA6BFB3A42909D1F0506BB00A28B96D65A97BA,
	CinemachineFramingTransposer_GetMaxDampTime_m75D70D7C9FA158625947BCD0A9916A9041FC9589,
	CinemachineFramingTransposer_OnTransitionFromCamera_mE11245253C3ED9BF2274703A0A30561E631E56DE,
	CinemachineFramingTransposer_ScreenToOrtho_mEAD258A02DCFCAC034DE9A1B08F465BCD149001B,
	CinemachineFramingTransposer_OrthoOffsetToScreenBounds_m59D579AD56E8D75381A1BCD90521BEDE4F1554F0,
	CinemachineFramingTransposer_get_LastBounds_m4DCAEC33A4112B23DF3AAF140968641DF578995B,
	CinemachineFramingTransposer_set_LastBounds_m994C70A03147A0B93D5F1A4B2FBC4C93978CAA21,
	CinemachineFramingTransposer_get_LastBoundsMatrix_mF280BE6A460347767231AAF16ADDED82D0AF2F5B,
	CinemachineFramingTransposer_set_LastBoundsMatrix_m99EA9A7C087AC301B38B994AB8D5D970534C9380,
	CinemachineFramingTransposer_MutateCameraState_m5FCADAD8CBA3E14ADA2AEFF0A678CAF2719CF456,
	CinemachineFramingTransposer_GetTargetHeight_mDD73BF6EED9F4CD6B6140CA493DB95584FDB3045,
	CinemachineFramingTransposer_ComputeGroupBounds_mD8CCAEEB72A14DD3FCBDE59517339CD767F9CCAA,
	CinemachineFramingTransposer_GetScreenSpaceGroupBoundingBox_m8DD095FF48442391445B761C8ABD8ED638777CA3,
	CinemachineFramingTransposer_get_Composition_mB8762265D00E9E93670AC1F1F77807CE18943545,
	CinemachineFramingTransposer_set_Composition_mD1CB635C2C6E986A9ADDDB3EFF6320D583572D3E,
	CinemachineFramingTransposer_UpgradeToCm3_m9ED85E4E0EE5ABC5A980E7B921F2E7B15B22FED5,
	CinemachineFramingTransposer_UpgradeToCm3_m5F44DE244A4967781BFFE98B4D4D49A20DFCFA80,
	CinemachineFramingTransposer__ctor_m689FA8F6AE2046BDF39F05B44493BE6FC69D6643,
	CinemachineFreeLook_PerformLegacyUpgrade_m99CF898E574EE256857A0B46C3BFCDD407405E0E,
	CinemachineFreeLook_get_IsDprecated_m88D6AF54D6FF18608EC8527EF48D785A1FC3DBC6,
	CinemachineFreeLook_OnValidate_m44FFF11DF20F465A5E5203AC4CE6FA81BF68BA65,
	CinemachineFreeLook_GetRig_m2D4F355319EDB98C1594EEE46B6E9A4E3AF601AC,
	CinemachineFreeLook_get_RigsAreCreated_m9F577128696D888BFA47E78B01C156DB32076619,
	CinemachineFreeLook_get_RigNames_mBCA70FB931D76D68DE7AFADD4F87F654B617D5E4,
	CinemachineFreeLook_OnEnable_mC446431DC1DACABF912FA89E1F738E43E7DFB9BE,
	CinemachineFreeLook_UpdateInputAxisProvider_m577AF397745D46720008434566F2534F9720180E,
	CinemachineFreeLook_OnDestroy_mA17A0480999D5D6575F807A8B8FF126B020654EA,
	CinemachineFreeLook_OnTransformChildrenChanged_m7A0A30C8D7D5413793ECC45C1873C5A96AC3676D,
	CinemachineFreeLook_Reset_mCC7BBB47DBEB265AF277ABDE75D3EE54EF343259,
	CinemachineFreeLook_get_PreviousStateIsValid_m76B18F2666F83833C9E650726E3777D292D4D2D1,
	CinemachineFreeLook_set_PreviousStateIsValid_m74910407A4A3FE83EA70DD77037EA2EC736F1D77,
	CinemachineFreeLook_get_State_m1CAAC9038A74F1914E48675BBB03E2FE1050F51C,
	CinemachineFreeLook_get_LookAt_mFBD08797DBB4BFF5B36FD0D2F804E9AE6F6476DF,
	CinemachineFreeLook_set_LookAt_m4344AC345F753044980B0254E7696826A5468BF1,
	CinemachineFreeLook_get_Follow_m37DD23AD4BAF5D6ABA3D028587B5165E4118838D,
	CinemachineFreeLook_set_Follow_mE9D9D96D46D82195F28E9BEE0663DAA142FB3C5D,
	CinemachineFreeLook_IsLiveChild_m3938F6DFD7BBEFE7C0D2BCAE4D408FF3DDAD38DE,
	CinemachineFreeLook_OnTargetObjectWarped_m7F3AFFF4309D82E6B9B9FBE8083627159C2D4540,
	CinemachineFreeLook_ForceCameraPosition_mD1FA34EE978F7E56CBC9468FC5F33F4EE3AA7852,
	CinemachineFreeLook_InternalUpdateCameraState_mC860D45758E9846042B4CA5EEDA9E0498A33D1CB,
	CinemachineFreeLook_OnTransitionFromCamera_mB9170F481E881B7A256D6BAEA0E8FD1DBB8546E0,
	CinemachineFreeLook_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_mBDE8AF12357720E4A27E8369C0985371C95814E0,
	CinemachineFreeLook_GetYAxisClosestValue_m835524A5EFE21F7B4B0D99E299F5624416E3EC6E,
	CinemachineFreeLook_SteepestDescent_m1CF5877C82EA61C23596FE151C4ACDF165B8E92F,
	CinemachineFreeLook_InvalidateRigCache_m1415D4925274916549845A131DC1EF5815F152D9,
	CinemachineFreeLook_DestroyRigs_m316FE82C2B85E09EB6E740457B4A6ADD273680B8,
	CinemachineFreeLook_CreateRigs_m229E68545DDC9414FD86A817AA4F4EF8E2976507,
	CinemachineFreeLook_UpdateRigCache_m2BDA9449AC8C3C2A44F0D2D90C2E8F548F2F14DF,
	CinemachineFreeLook_LocateExistingRigs_m6D9ACA5FDCAE2C5892F1737355ACE03E94BAFAD4,
	CinemachineFreeLook_UpdateXAxisHeading_m67D7F9103A098968CA41FD0C46510F8740D3B1AF,
	CinemachineFreeLook_PushSettingsToRigs_mFAD40EBE27B8E8DEF7B1356F1277A9C3801CDA3B,
	CinemachineFreeLook_GetYAxisValue_m65488E6EB92D60E5F4838542850CC8E7C04F02C9,
	CinemachineFreeLook_CalculateNewState_m39CB4DEBDBD80B945236D220D103D4D63D32FDA6,
	CinemachineFreeLook_GetLocalPositionForCameraFromInput_mF494794AC4BE5B4EE72D14A38120DB39DF7085DD,
	CinemachineFreeLook_UpdateCachedSpline_m0C1C87A94BB544A9E4CF63AF0DA68E6D7FD3B3A4,
	CinemachineFreeLook__ctor_mC3F83BF9717B582F4284C2CA17AAE70DFB03DA9E,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__AngleFunctionU7C52_0_mC11699787EFA6293ED78BA3336E7580EB93C6235,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__SlopeOfAngleFunctionU7C52_1_mFCC93B209E9B6ADC8E738285B10F0A186AE6CAB9,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__InitialGuessU7C52_2_m43180E7CCF6AB49CAE1B8F39F952B8F5AD2CF0D0,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__ChooseBestAngleU7C52_3_mC5DB4A03A7DAB3B0306812DC68CE2415D04DAC39,
	Orbit__ctor_m8022DA2F7290722B44C38A6A7F4782B39A375CAE,
	CreateRigDelegate__ctor_m1BD652C55A6F078509ADC349A011C3E9EE2079DD,
	CreateRigDelegate_Invoke_m672111599A43B7D9E16A45332D8664FFDB31AE15,
	CreateRigDelegate_BeginInvoke_m9F4C0274EEF060272253F3663F2E3257A225CE36,
	CreateRigDelegate_EndInvoke_m1E54378187660830754E026AD010E5E101B345E0,
	DestroyRigDelegate__ctor_m1A1B2BF53220E8DA6044544CDD2BF4634DD66EA4,
	DestroyRigDelegate_Invoke_mF9B7611AFCA9EC5F7093CA0EE3EB52CEC043008B,
	DestroyRigDelegate_BeginInvoke_m63025A2220E79B6358BAD7D8414938655376C27A,
	DestroyRigDelegate_EndInvoke_m2A68D901C11E6A90382610F31386EB725B301B4C,
	CinemachineGroupComposer_OnValidate_m899426059E843FFBF5E84DB501F02AB4966E4C90,
	CinemachineGroupComposer_Reset_mA1F25FF12B96C1165963B74F08CF91B5D7E38641,
	CinemachineGroupComposer_get_LastBounds_mA82ADC5037A17285DC11F23C1B916DA2B4203598,
	CinemachineGroupComposer_set_LastBounds_m093A6FF6DE0E63C60BED961ED14CE47C6701D52E,
	CinemachineGroupComposer_get_LastBoundsMatrix_m6BB98740041EB9843976843164C5F6578F94343B,
	CinemachineGroupComposer_set_LastBoundsMatrix_m3CAC84879481A947A997DDFAD68330F2C664DD88,
	CinemachineGroupComposer_GetMaxDampTime_m9DD5656E4C0CB0BE1F661A25F4CC3BE9E8B9AA04,
	CinemachineGroupComposer_MutateCameraState_m386F6F9EF1479031B7AFE0FF3B03D44626B6867A,
	CinemachineGroupComposer_GetTargetHeight_mF17365ED88A00D8BC26281335D7C256C894463DE,
	CinemachineGroupComposer_GetScreenSpaceGroupBoundingBox_m00C34895DFA2273DCB4B069B6A61A45F9F2E671D,
	CinemachineGroupComposer_UpgradeToCm3_m3A9A5F360A86DE7D2034BD1102D812053674CD9C,
	CinemachineGroupComposer__ctor_mB46EF0A8B2A8D570A49438E0FC91DDD701D71003,
	AxisBase_Validate_m346100A07229E141A781DFA36B2F8B7A99AEAFB5,
	CinemachineInputAxisDriver_Validate_m84655856991F2EDC28367081279BF16A80009CAC,
	CinemachineInputAxisDriver_Update_m481B879CF4FBD76457792D1533DF505D014DE4B1,
	CinemachineInputAxisDriver_Update_mB5463E7B9F6C10C51903F3BF4D8A27263E259D2C,
	CinemachineInputAxisDriver_ClampValue_mABC231D2F9842EABC91604D26F296C5AE30F3F44,
	CinemachineInputProvider_GetAxisValue_m5709F61EC48799DB5302A1EBE27641BEFEC6A633,
	CinemachineInputProvider_ResolveForPlayer_mEBB6573C0F529AD528E19DFF3381EEA045D67E7C,
	CinemachineInputProvider_OnDisable_m2E86A211186A6439142619372870C25B3E3968E3,
	CinemachineInputProvider__ctor_mD89A2574141B577201A1A7E942005E38A855FE16,
	CinemachineInputProvider_U3CResolveForPlayerU3Eg__GetFirstMatchU7C7_0_mA04002433420500DEF76E611EC238F93DDCFB571,
	U3CU3Ec__DisplayClass7_0__ctor_m70B586881BA5FAD5260CEC933CEABF4FA39836FA,
	U3CU3Ec__DisplayClass7_0_U3CResolveForPlayerU3Eb__1_m7A714021553634D0DA8824E9C715C77EFA66BF7C,
	CinemachineInputProviderExtensions_GetInputAxisProvider_m75E65D3BF9AE2A9E46A5DBF5CD95D868EAD267BA,
	CinemachineLegacyCameraEvents_OnEnable_mC483821F77A7D41A8CF8C37F2203966AD7C1F071,
	CinemachineLegacyCameraEvents_OnDisable_mB90EA14C25A4D9E934B0FEF06EFF4D209AB53F2B,
	CinemachineLegacyCameraEvents_OnCameraActivated_m55959B8CF1A27842E79B1234548CFE748BD729BD,
	CinemachineLegacyCameraEvents__ctor_mFB89C2CAD5E404B16371238D26CB2229F880EDBE,
	OnCameraLiveEvent__ctor_mDD84F7682414813B9CB869B347152D6DC0F9A484,
	HeadingTracker__ctor_mE44ADB60804CBF308F06DABED0EBF109CEE43E35,
	HeadingTracker_get_FilterSize_mCD39231CE59B1CAA94ADE6C856547FBD6E7AF26E,
	HeadingTracker_ClearHistory_m9C4EC790785C4E5F28EB35E847079EB9C4FA9765,
	HeadingTracker_Decay_m6186639A5F462340F36B848D6A0C57917B87A2CA,
	HeadingTracker_Add_m59F20F9FC66ACE908B78692B2E2C48235714E247,
	HeadingTracker_PopBottom_m049C60BA33D0A37D858A3A323251C8300FE07D65,
	HeadingTracker_DecayHistory_m7A81A41460216AD7AE18BD8644866F7B152F9202,
	HeadingTracker_GetReliableHeading_m06B0753676E0EBEE99CDDB35289188F17E971799,
	CinemachineOrbitalTransposer_OnValidate_m27AFA1E76C2F1D1C50995D6D6EA5C8BF0CE57F16,
	CinemachineOrbitalTransposer_UpdateHeading_mAA765E33A9D8A6FFB87305F086067F359446F7F1,
	CinemachineOrbitalTransposer_UpdateHeading_mFD6E865B3766378E30315B47EF5ACEF2A04D9D8A,
	CinemachineOrbitalTransposer_OnEnable_m4039EAA60B25A82FF105FE8D70CD09DFB6742F49,
	CinemachineOrbitalTransposer_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m026B9333A2FA5CF8541147E4426C5EDA3BEA1193,
	CinemachineOrbitalTransposer_UpdateInputAxisProvider_mDA52E1EE2F28B186D6BBA52B2D860DA2DBE19314,
	CinemachineOrbitalTransposer_OnTargetObjectWarped_m8DBFF886B18E158EEC994CC5D719E7ABBA3F3254,
	CinemachineOrbitalTransposer_ForceCameraPosition_m1CC1AA49303299F6271EB1742F5F8455417BEB2C,
	CinemachineOrbitalTransposer_OnTransitionFromCamera_m9F0D6B6E7307748D3AC371C1054728F72D55973D,
	CinemachineOrbitalTransposer_GetAxisClosestValue_m80F69D95B306047CADC66162AFF7B7F581E412E9,
	CinemachineOrbitalTransposer_MutateCameraState_m71E4466F2087A6DF7CF42882F83DFCF5BC68DE66,
	CinemachineOrbitalTransposer_GetTargetCameraPosition_m0C205CFE791DB38B520AEA61BEE8458FB6477785,
	CinemachineOrbitalTransposer_GetTargetHeading_m2DEFA6F70A596EDB7AC3D67F819C7957E3C1308B,
	CinemachineOrbitalTransposer_UpgradeToCm3_mA27A7DA6C26B261A45DBE8FFC357EC271A719470,
	CinemachineOrbitalTransposer__ctor_mAACA858D22B8652838E12EB38A8E68DF60D57933,
	Heading__ctor_m5CBDDB066E6DF303F92BC1264BF5A34C96F16382,
	UpdateHeadingDelegate__ctor_m3D4109E08FE806FA2CBC36F4BF54EDDC82571490,
	UpdateHeadingDelegate_Invoke_mB27A3CF5BB09802E3E989991A77C14E672C37F6A,
	UpdateHeadingDelegate_BeginInvoke_m2637A0D71F166CC74CF5E45D6D42433DE378D776,
	UpdateHeadingDelegate_EndInvoke_mF444A6CF8BCF534CDDD3B14D34A4BEE8EF242219,
	U3CU3Ec__cctor_mE9E8806F7D554A77B9AD830A8A271AE7D9DF6BFE,
	U3CU3Ec__ctor_m93127D69718D1B675220C18FFB3F40033560B68A,
	U3CU3Ec_U3C_ctorU3Eb__31_0_m46FDBC41B1B996E59C57F1917160D58DA6D554F6,
	CinemachinePath_get_MinPos_m242BFD54E2EA15F3D42E73D0AA91EF20DB5A89FE,
	CinemachinePath_get_MaxPos_mF18C1A821F79473C474F203E2D242CF6812C5333,
	CinemachinePath_get_Looped_m09A52B423D4CC031C72489A682A68CAFDED10918,
	CinemachinePath_Reset_m46C889DCF2D757C25B2A8557878F8768951E11D5,
	CinemachinePath_OnValidate_m9D1457DC1FF88C8AA057CF8F88EE5CB7EF872CAF,
	CinemachinePath_get_DistanceCacheSampleStepsPerSegment_m22FC53EBD61CA6D4617C1ECB6E0842F6B1D3D74B,
	CinemachinePath_GetBoundingIndices_mE8E2B2FBE56850FF0A8373C99529BACF7C4A84EB,
	CinemachinePath_EvaluateLocalPosition_m2544E21A704BAC9FFC2851772025447E857D66BA,
	CinemachinePath_EvaluateLocalTangent_mEA2CA96D1DC561A72EDEBA49A475014687CF26AA,
	CinemachinePath_EvaluateLocalOrientation_mDAA63D623E64A8DA8434248D200ED6008FC0D786,
	CinemachinePath_GetRoll_mB03391DE6E23E2F89AD0F758671CA383D80C89F6,
	CinemachinePath_RollAroundForward_mFBA039EDB89A179D0CC17D673940CBA44EDF208D,
	CinemachinePath__ctor_m61372CA2207403085AA1A71FD2285F3B4F20E48C,
	NULL,
	NULL,
	NULL,
	CinemachinePathBase_StandardizePos_mC5BDA5E1CF9AFB0759D408B66742B64CCF19A536,
	CinemachinePathBase_EvaluatePosition_mECBDF202924BD8B139616E4DEBC2DE89C5F68D41,
	CinemachinePathBase_EvaluateTangent_m1ECF609F052EAF83E04CE80C00F59CCEBF4E5068,
	CinemachinePathBase_EvaluateOrientation_mD2B4B1E52A627DC73BDAA27B6FD646807B1B1E91,
	NULL,
	NULL,
	NULL,
	CinemachinePathBase_FindClosestPoint_m5408B5DBB0952F10EC1FEBA2057C9B8B1046CDEB,
	CinemachinePathBase_MinUnit_m471F2FF54F563F9A19DC32E9275A8467284A2424,
	CinemachinePathBase_MaxUnit_mB995170CFCA094A7C3E9B31B4F9D6A2553B1E56B,
	CinemachinePathBase_StandardizeUnit_m994E42C73E007A38D0F3039E1A99364DFA74211A,
	CinemachinePathBase_EvaluatePositionAtUnit_m47FDF0D57A865B4F6B5C6BC7460EB3BD5FFA5B65,
	CinemachinePathBase_EvaluateTangentAtUnit_mBD047A731AFCC51D5B145476B65A72C994651AA5,
	CinemachinePathBase_EvaluateOrientationAtUnit_m2185E6E2EC73BEF736EF7F6C2C4DF6EFE222843A,
	NULL,
	CinemachinePathBase_InvalidateDistanceCache_mEB69E50239A70868E9AB984172EA6DD5AA308944,
	CinemachinePathBase_DistanceCacheIsValid_m8BC6A6302FC87D6A1E3B5309FFA135E2255694CA,
	CinemachinePathBase_get_PathLength_m652EE55DF497D83E59E451A4D584A7A7BFB121F7,
	CinemachinePathBase_StandardizePathDistance_m4A401FCE67BEAA92F1C7864E89160507A3FEADDB,
	CinemachinePathBase_ToNativePathUnits_m9FE04A368DDED74F3175D4F4DB2090DB5FBC57F0,
	CinemachinePathBase_FromPathNativeUnits_mF9B0FD93F95C79AACC988AFB217607FBA3995104,
	CinemachinePathBase_OnEnable_m384DE1B42EAD98BE31690C78E3CBC4C9AAFA663D,
	CinemachinePathBase_ResamplePath_m8F0793AE39ACFB8BA515E0A973D7ADF9D7520FFD,
	CinemachinePathBase__ctor_mA5BC84DA8D48A1A3484EA17201F948E6980096A1,
	Appearance__ctor_mADFB8A11271BA7F20FA20CBD18DE7A471104009D,
	CinemachinePipeline__ctor_m588E6BB31B1C2345FC123CFA1C6C78C2B61EDD73,
	CinemachinePOV_Unity_Cinemachine_CinemachineFreeLookModifier_IModifierValueSource_get_NormalizedModifierValue_m2472F38919118BF5864AA29CF9F0F034DA9F871A,
	CinemachinePOV_get_IsValid_mC090D4DF48E39F5DDF2BBCEB708E39EAE653363E,
	CinemachinePOV_get_Stage_mEDD446A71EF0B1A7C0FF42691C79C11338B38DBC,
	CinemachinePOV_OnValidate_m5E388DF722E0FA9972964FAF2E734AC337B477E6,
	CinemachinePOV_OnEnable_m8A8AAA5F8FCA291F9180158C71BC90CA37C4E9FA,
	CinemachinePOV_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m6FCA40A576A2725E1A2CBE12774FD08F2B3084D9,
	CinemachinePOV_UpdateInputAxisProvider_m96B616E27C17568F5DD0997BBC9AF1042117C63A,
	CinemachinePOV_PrePipelineMutateCameraState_mBCEBE5E28593C8569C53FF3FD74F850AF429C805,
	CinemachinePOV_MutateCameraState_m5B7BBD55C148A5AF045FEB4C8D60C52503589238,
	CinemachinePOV_GetRecenterTarget_m2D7249FD4307B34464613479E0637C350B2E2A43,
	CinemachinePOV_NormalizeAngle_m120DAA70B95351E50F6910153F56F7068045FF8C,
	CinemachinePOV_ForceCameraPosition_mD86ECCBA35EC168C4109BBF72B11306D8456D7F2,
	CinemachinePOV_OnTransitionFromCamera_m26897DA1B83CFD88842E5229D8FB36F341902147,
	CinemachinePOV_SetAxesForRotation_m1A1EF7C7B3C50A8C873F1D6EFCAD65C7D7F71066,
	CinemachinePOV_UpgradeToCm3_m3F38965D6ED535BA573E340C03BCFB61D1A64313,
	CinemachinePOV__ctor_mB9B972066CE057058815A47745E15CF6C6C704E6,
	CinemachineSameAsFollowTarget_get_IsValid_mE53D0FA3AC6C62B6A199C95564728E9ACC202451,
	CinemachineSameAsFollowTarget_get_Stage_mF27DF24729542FFBB2CA1BF1FD19ADFFF26B8988,
	CinemachineSameAsFollowTarget_GetMaxDampTime_m4F7306DA606C90A8A7C72B72406C58155899D8D9,
	CinemachineSameAsFollowTarget_MutateCameraState_m0EF320BEDCF7D79A83F53C4A67A5EC291ADC1CD4,
	CinemachineSameAsFollowTarget_UpgradeToCm3_mCD0F2B20921A61E731496D2908BB7B65EF41709D,
	CinemachineSameAsFollowTarget__ctor_m443B13544B8F6B0430FC5DE711B4CDC6C2199D67,
	CinemachineSmoothPath_get_MinPos_m910524110AA6B88E68B458110ED67913856EC7C8,
	CinemachineSmoothPath_get_MaxPos_mA62F7994CF3F1C557297B8EF606264E80E30EF02,
	CinemachineSmoothPath_get_Looped_m39E0E3F8E867D3ADCA14B1B8F572631A748B637B,
	CinemachineSmoothPath_get_DistanceCacheSampleStepsPerSegment_m6E3FDF8570FE8B31793B1161F878B0E2EBA71E7B,
	CinemachineSmoothPath_OnValidate_m012C279042947B45690AE7AD51C7B736EA7106FF,
	CinemachineSmoothPath_Reset_mF848956AE8411201C675B8FAE376134D55419E9E,
	CinemachineSmoothPath_InvalidateDistanceCache_m86F480F4790B3D39FEAC2314A688CD69683526A9,
	CinemachineSmoothPath_UpdateControlPoints_m2C03B480AA4C475E74EB59A42061D6EBEAACD651,
	CinemachineSmoothPath_GetBoundingIndices_m263F00A9F76F4689E1ECB01530C4A86660298DE2,
	CinemachineSmoothPath_EvaluateLocalPosition_mEDB2FBFCC7B326E566986F2C8D5869F0F014B41B,
	CinemachineSmoothPath_EvaluateLocalTangent_m7AD1129C4FCCA5EA80C1CCB1758EACBC76D092D3,
	CinemachineSmoothPath_EvaluateLocalOrientation_mE5A8073F4864F88008828FA8FB0655844E9EC37C,
	CinemachineSmoothPath_RollAroundForward_m7313CE50FF501476FCF53C836E5E50D7AAB7A551,
	CinemachineSmoothPath__ctor_m78C8971A1BB3718FF626AB69225E17D096C4DE82,
	Waypoint_get_AsVector4_m358E9E0B3C5BF20F20ACB52873D9E9446D961E36,
	Waypoint_FromVector4_mD3CAE1774293C487073AE38DED4A1D4766ACBFC7,
	CinemachineTouchInputMapper_Start_m254207B98E7CE6FAD5993C8C65453EAD73BA9761,
	CinemachineTouchInputMapper_GetInputAxis_mCB3F8EDCA569CA229ECECE121479CBFBCA4ECC75,
	CinemachineTouchInputMapper__ctor_mFA32270DC73505DB6F1B437927C8842DD17D0403,
	CinemachineTrackedDolly_get_IsValid_m1403467E726A3EDD6C3C2527272B4F0EBDA1134D,
	CinemachineTrackedDolly_get_Stage_m0172E64081775C35E056FDD8F1B09A5EC75F0359,
	CinemachineTrackedDolly_GetMaxDampTime_m5C78894035F699CE3BAA512C13ADA292E966C237,
	CinemachineTrackedDolly_MutateCameraState_m7140B176C4FF84906A96B8B66F0921304FFAF741,
	CinemachineTrackedDolly_GetCameraOrientationAtPathPoint_m1949C9ED3DCD558D2A7F27E8CC075DD2065C013F,
	CinemachineTrackedDolly_get_AngularDamping_mB7A2F30451BB0C082D55D5437B679BDDA11A9310,
	CinemachineTrackedDolly_UpgradeToCm3_mB08A863F720B96FD34AAE5F07BC302C6815F72AC,
	CinemachineTrackedDolly__ctor_m4E026B5C47E38F9A87F014811AFD52A5DB81BB10,
	AutoDolly__ctor_m3B7C437D0F1F270E1FCE4710910A590FFD243CC0,
	CinemachineTransposer_get_TrackerSettings_m04830B82E096DF5E347A1256356D8BDE2643DD16,
	CinemachineTransposer_OnValidate_m606DDE20AE388161717BE364362463B2EE7EDBD4,
	CinemachineTransposer_get_HideOffsetInInspector_mF7329AB3746F04FA98FE6FF8949F52DFF9F538DA,
	CinemachineTransposer_set_HideOffsetInInspector_mF2D2D2D0B57F4869EE3E50B6B8278866B6CDBE4B,
	CinemachineTransposer_get_EffectiveOffset_m0B4B3AA79286EA6F5F16FA8401A1911257C18386,
	CinemachineTransposer_get_IsValid_m51029A45C7008AFCED66842E91E8D4B736BC5D1E,
	CinemachineTransposer_get_Stage_m9F4CAF5EFACE473B759C6FCBB707BBE96F86CAC2,
	CinemachineTransposer_GetMaxDampTime_mECB9E5D32672ADA5167A36D51634DE37FD113236,
	CinemachineTransposer_MutateCameraState_m54A04E7B15B3EEF997F3F7874AEEF13AA05198E1,
	CinemachineTransposer_OnTargetObjectWarped_m01A0FB117CC85A19CF21C0C24498683D5414BFD4,
	CinemachineTransposer_ForceCameraPosition_m5FD9FB370899D082FE20F26174B01CD67DEE2E48,
	CinemachineTransposer_GetReferenceOrientation_mE864F0CA360317576821F6D709D0872C8D2A663F,
	CinemachineTransposer_GetTargetCameraPosition_mC6F3BB4CC9DBF64A85A060B3475B4061DB901EDA,
	CinemachineTransposer_UpgradeToCm3_m3C11165E10A40E83B53158E87120A9BFAE0BE1E3,
	CinemachineTransposer__ctor_m747039A1923268409E4F0B0F3F4311BAC0286335,
	CinemachineVirtualCamera_PerformLegacyUpgrade_m75043A3568B73763172F08127B0188FEDC335C18,
	CinemachineVirtualCamera_get_IsDprecated_mADFA043287577B5B241915389DE62A61E17016D9,
	CinemachineVirtualCamera_get_State_m2B975126FF35A9433915EDCA7153495D74C16BC7,
	CinemachineVirtualCamera_get_LookAt_mD912826BA1F07E89B4C72E2E14271819E3698D70,
	CinemachineVirtualCamera_set_LookAt_m731B086636370206EF8A1B3A1E1CB7D5311EB6E1,
	CinemachineVirtualCamera_get_Follow_mB7F8F586CAD2DC03A489AD3A1B1D6C97C1B02348,
	CinemachineVirtualCamera_set_Follow_m4A371BEB0A4476F3337B00C2FCCDF3B91284FC64,
	CinemachineVirtualCamera_GetMaxDampTime_mA67B8DF7584E92325E6E231B3FCAAF4B536DD613,
	CinemachineVirtualCamera_InternalUpdateCameraState_m1013A42B5A8E93C00EED7698833BB6CBD722A118,
	CinemachineVirtualCamera_OnEnable_m76E86C78F8CE023500DBC47D9BB1A776C60E5837,
	CinemachineVirtualCamera_OnDestroy_mA2A2D126795D1F9ECB0CB3C76304041B7971BC93,
	CinemachineVirtualCamera_OnValidate_mA7930EEBEF017FDE84F8BB7C6F2B343475800B3E,
	CinemachineVirtualCamera_OnTransformChildrenChanged_m84112E4040D6C6F338AA2545989742BC9E2B600D,
	CinemachineVirtualCamera_Reset_m02AFCCA318CCA0BBC5FB9B627129E34E46F7E743,
	CinemachineVirtualCamera_DestroyPipeline_mDA5D57328A912657F0A29FC4D91ED472DA08D54D,
	CinemachineVirtualCamera_CreatePipeline_m540F432BECA028656FFD94DE9D9754BA589A008A,
	CinemachineVirtualCamera_InvalidateComponentPipeline_m74EFE071B3D4C73B2A5CDF73C817D59C21432438,
	CinemachineVirtualCamera_GetComponentOwner_mD1D86A1EEF34F6C79927A7EBED9C83CDE1E67FD1,
	CinemachineVirtualCamera_GetComponentPipeline_m55048017AD1951408530C306C849090F64570F54,
	CinemachineVirtualCamera_GetCinemachineComponent_m96FF29242A33A8929E9C60AA80959A8EDF001392,
	NULL,
	NULL,
	NULL,
	CinemachineVirtualCamera_UpdateComponentPipeline_m771722F8D819DE66779F541255A8C5D2158FAC29,
	CinemachineVirtualCamera_SetFlagsForHiddenChild_m8A58A93E7379050861BB6AD30D77A678C788CB43,
	CinemachineVirtualCamera_CalculateNewState_mAE96EFF8DE98E3B03B09A0EB19D27B8B8D287C90,
	CinemachineVirtualCamera_OnTargetObjectWarped_mA36AFF744413B299E89ED32A85C6CC03E1AF65E7,
	CinemachineVirtualCamera_ForceCameraPosition_mF1D429C0B71D6CA71AE3133048A204CC74FC2DE7,
	CinemachineVirtualCamera_SetStateRawPosition_m65A1315C334895AD27A32BA3454330DB09B33751,
	CinemachineVirtualCamera_OnTransitionFromCamera_m274BB20F96EDF8F4CE6E45B5B0535E6D5C3AED7A,
	CinemachineVirtualCamera_Unity_Cinemachine_AxisState_IRequiresInput_RequiresInput_m31DE0FEB413302BF606C23F711681D175925C71A,
	CinemachineVirtualCamera__ctor_m8ED96240A29D5C373D76210613D079B87F372C2F,
	CreatePipelineDelegate__ctor_m69643E8561A4E503B52314E0AB11EAEDA86EFCD8,
	CreatePipelineDelegate_Invoke_m6E6AC41C832AC1BC1F83261EBD9452EE4F1B26E5,
	CreatePipelineDelegate_BeginInvoke_m86D3EAD2BFEE25C736AE3DE997CDAA29B2D5A780,
	CreatePipelineDelegate_EndInvoke_m3BA5461CB30EF0F7C407A95D24A593C7BA42806A,
	DestroyPipelineDelegate__ctor_m417AAACDE7A3186B5B8BAA036A4E150485033164,
	DestroyPipelineDelegate_Invoke_m3109307C35DC548E3C0A26C39251A59E347BBD0B,
	DestroyPipelineDelegate_BeginInvoke_m8322F79EFC947602180CB5E6A19331550D1EBE2E,
	DestroyPipelineDelegate_EndInvoke_m592E51044B970A682BB9F3B88EF600F931535BFB,
	U3CU3Ec__cctor_m5902DA0D2693C77B4F2A389BEF0FD4658BD45FEE,
	U3CU3Ec__ctor_mB50F1566116B30BCBE2AB1A4FCB7970BB8EFEBA0,
	U3CU3Ec_U3CUpdateComponentPipelineU3Eb__44_0_m36914D634EE6E38683F6654DE8AFBA1882C1A400,
	VcamTargetPropertyAttribute__ctor_m59083A45573C38A071C24FD9E24585756C85536A,
	LegacyLensSettings_ToLensSettings_mB72D0B6673F19BC5697AB92AF1AAC5B9B7CB2CF6,
	LegacyLensSettings_SetFromLensSettings_m1EBBC2E3012094938CFE36A470A1B5709669DCAB,
	LegacyLensSettings_Validate_m7D021CE1F7DDDAB75E8DB632DE6C361F4F02D183,
	LegacyLensSettings_get_Default_m9DF19927BD4DA161D9B8164D408A9385DA83082B,
	CinemachineBrainEvents_GetMixer_mBF316A88CA46E6EF08AEDC85FC42F6B0558D39C2,
	CinemachineBrainEvents_OnEnable_m9FE0874E885DB802B54C83335223F866AF828B38,
	CinemachineBrainEvents_OnDisable_mA5FA42343B49BD087F61CEF9E387A8D9A01D083C,
	CinemachineBrainEvents_OnCameraUpdated_m5115CBA44936650F24A05365471F652E07BF2E5E,
	CinemachineBrainEvents__ctor_m300E6954E1A00E0664DE88A476609990E75A0E63,
	CinemachineCameraEvents_OnEnable_m5BDFC3FE94BCC51CC61092E0F964E3CFEB0E3FDD,
	CinemachineCameraEvents_OnDisable_mF18FBA269408ECABD01769165A7FC42006417AA9,
	CinemachineCameraEvents_OnCameraActivated_mD638E29F5EEFEDDAE452A203EFD44E2671E98B0D,
	CinemachineCameraEvents_OnBlendCreated_m713481243CE2C55D62CDD226280DC076BC44A51B,
	CinemachineCameraEvents_OnBlendFinished_mA205F6229D30B32FA6F6A2E80EA7DE48F12DB97E,
	CinemachineCameraEvents_OnCameraDeactivated_mFEF1435E8DBA146371BA55702C26571885582939,
	CinemachineCameraEvents__ctor_m2B67581C41DF90751D038D0991A806C4AE12F312,
	CinemachineCameraManagerEvents_GetMixer_m6A84ACEF0E88C37CD950B115A61469494D7E5793,
	CinemachineCameraManagerEvents_OnEnable_m88A6A5E799CE8168F0317F6302A75699518D7A7C,
	CinemachineCameraManagerEvents_OnDisable_mFD7D44DEA91481DEACE3D9FB0633E1F3889800FF,
	CinemachineCameraManagerEvents__ctor_m801A8DCC4D9401931807CEF9ADD53652CB4F31EB,
	CinemachineInputAxisController_Reset_mE68EBFDDE9609A0FA27A0814A790F7A072B992CB,
	CinemachineInputAxisController_InitializeControllerDefaultsForAxis_mB0B7561252EED6765132E7165939D599DF2D8246,
	CinemachineInputAxisController_Update_mEBAF2A94D290DAB75F240886010713CD999DE4DC,
	CinemachineInputAxisController__ctor_mF1D628CF0B067EADE1738570BDF0F807F96239AC,
	SetControlDefaultsForAxis__ctor_m2C7D27199774523F0B5B0790E3C8ECDC7717417A,
	SetControlDefaultsForAxis_Invoke_mA7751023CB446A7FD6AACB2C0A5C92F7F2F7E701,
	SetControlDefaultsForAxis_BeginInvoke_m0B485DAFB61FE48E586D36380C96656BDB7C1C41,
	SetControlDefaultsForAxis_EndInvoke_m16B348996D9FDD38C7ACED2FF570DFD54F2CD5B1,
	Reader_GetValue_m4ED97721BDC84F248FE3D085431EA3D921253F58,
	Reader_ResolveAndReadInputAction_m22A5F39EAFBEBA607C13A9EEDC632BF27DC224FA,
	Reader_ReadInput_m4DD490F8CC8AE41F05AAD07350B0136CFD3AD19C,
	Reader__ctor_m63F40D0157CF3F324637968E92334344948E5DA2,
	Reader_U3CResolveAndReadInputActionU3Eg__GetFirstMatchU7C8_0_mC0E1D59C4B0009C424D1A7E6FE19AAD5FA75B405,
	ControlValueReader__ctor_mD9F083553203BB2DE6CE0A1D209929AFD06BCCCC,
	ControlValueReader_Invoke_m17280C755ADD9E87BA7F5979351CCC0A7214A515,
	ControlValueReader_BeginInvoke_mDDF92D85CEF9EFB414FECB966C714B52614482BD,
	ControlValueReader_EndInvoke_mA537C17D10AE8ACF23E9B0EC3322F4375B623DAC,
	NULL,
	CinemachineMixerEventsBase_InstallHandlers_m15BDFBD31CDD12BD6F1C16F4496409D4F3791385,
	CinemachineMixerEventsBase_UninstallHandlers_mBBD2D828E9DA293919204C9783C80D88CBD4C72B,
	CinemachineMixerEventsBase_OnCameraActivated_mEECC261CE2941B6A2F8F25A12BACB1F924D1F000,
	CinemachineMixerEventsBase_OnCameraDeactivated_m972028AC75380825A01544E70C1F44916033FA86,
	CinemachineMixerEventsBase_OnBlendCreated_mEBC8A2118F728AE9439C333758220A51E8730018,
	CinemachineMixerEventsBase_OnBlendFinished_m5A88C2B351431E9B3A4C9A6490298B4EB21592D6,
	CinemachineMixerEventsBase__ctor_mE84DA33ADBDD40C137A40172D617EB332667DCAC,
	CinemachineSplineSmoother_SmoothSplineNow_mA0FF23D04880F9FAF17A75BBE35E874F81CF9DBF,
	CinemachineSplineSmoother__ctor_mD75416FD30F780E34C5B044C5A52732554B8FE89,
	CinemachineTriggerAction_Filter_m36D9BC246ACB12F21E9AFB318999FDD08D38F4C4,
	CinemachineTriggerAction_InternalDoTriggerEnter_m7CDCE62E612D8FEB1480FF8C359C115758AEF779,
	CinemachineTriggerAction_InternalDoTriggerExit_mB3543B8137F8F265F7D35BBDE3D79D6C19898B95,
	CinemachineTriggerAction_OnTriggerEnter_m08FDFB756EF4414156FEB61005A0F375B85D4C66,
	CinemachineTriggerAction_OnTriggerExit_m372A68390BFEACC424899B7322EDEC4C92F796BE,
	CinemachineTriggerAction_OnCollisionEnter_m07598D4C497DB457989AE93DC0FCA94BA8024EFD,
	CinemachineTriggerAction_OnCollisionExit_mD69340A4FCD852B90084A20003FD1A7B49497F57,
	CinemachineTriggerAction_OnTriggerEnter2D_m0D41DA8B265E668B4D43703BDEE1B3E948212938,
	CinemachineTriggerAction_OnTriggerExit2D_m1E682C95A166BE011B1F77159A515BE24D530AF2,
	CinemachineTriggerAction_OnCollisionEnter2D_m8DC667AA67E6624D781698D4668F10AA42443334,
	CinemachineTriggerAction_OnCollisionExit2D_mAAA808BFD294DC4EF8AE35BDA09B4DB0631ADD83,
	CinemachineTriggerAction_OnEnable_m738581AA42EC4664475981A86EDFDBA001E35038,
	CinemachineTriggerAction__ctor_m70E57ED2AA3C80E74C8C27E35221898BA6B838CD,
	ActionSettings__ctor_m634C3CD9EC38605901BA50D58E89EB27BC5F1A56,
	ActionSettings_Invoke_m0909B351B47486770E46521676789D6DCD11E6FE,
	TriggerEvent__ctor_mD8370E1F7E0912BD1DF377835495FC287990B169,
	GroupWeightManipulator_Start_mA1F0B126DAF266088946482651762E8E26148801,
	GroupWeightManipulator_OnValidate_m8103AB555F999250B55D11F968A0849310180643,
	GroupWeightManipulator_Update_mCEFEC124080ADCCDB357CEF20FE8D0DE8916A20A,
	GroupWeightManipulator_UpdateWeights_mAA06E7BA50BF0F83FBC83FCEC03B28E3D65736C5,
	GroupWeightManipulator__ctor_mBF62C9730629DD57A68A3CE94E2C76009D1C2FF7,
	CinemachineCollisionImpulseSource_Reset_m4C0BD9B25B7AC7BA8C2955E2221929969C18F51F,
	CinemachineCollisionImpulseSource_Start_mCA8DC740D676B26289D1A68C5F3EA94BA5EC9C39,
	CinemachineCollisionImpulseSource_OnEnable_m2B96C7209FBF654F99B26F91E238351EEA11669C,
	CinemachineCollisionImpulseSource_OnCollisionEnter_mD36ECDAD22B650FD448CED69127EA631E454AEF1,
	CinemachineCollisionImpulseSource_OnTriggerEnter_mC55CE5E757A6B7DBA6FE4A212B05DB1581836322,
	CinemachineCollisionImpulseSource_GetMassAndVelocity_m6D011C303EAE6502F3A55E29B419F2D2D9AD9E18,
	CinemachineCollisionImpulseSource_GenerateImpactEvent_m25CFB3D82355142D01FF5CDCD9E0309FA6A981E0,
	CinemachineCollisionImpulseSource_OnCollisionEnter2D_m3F9E3B5AED0371BF324A89D166C00DF6076A753B,
	CinemachineCollisionImpulseSource_OnTriggerEnter2D_mFCF9E09617B1F36E789BEE51586D63812CEAA921,
	CinemachineCollisionImpulseSource_GetMassAndVelocity2D_mFEED066CEF8B730CA1CA7F48A45737ABF1BCE3E6,
	CinemachineCollisionImpulseSource_GenerateImpactEvent2D_m9E350C0D8680E5D00789D2C92996EDBC7C66BF98,
	CinemachineCollisionImpulseSource__ctor_mA92E5BF4E42F12175524C6EB79EB02079E81BA6C,
	CinemachineFixedSignal_get_SignalDuration_m804E774BF6108692BC4EC742EEE07569BD591220,
	CinemachineFixedSignal_AxisDuration_m6A1BE86CB19EF1D84E44F2B6C66BAE1CC5E9F802,
	CinemachineFixedSignal_GetSignal_m16036EE6491457C8A7BACA89E2B257C4BBBC9ED7,
	CinemachineFixedSignal_AxisValue_m6A6D0895F7DB9E6DD28C2C698F3737E99F18E24E,
	CinemachineFixedSignal__ctor_mBFA1043F287A6E5E6C7BC5F2CA9C935450B1C544,
	CinemachineImpulseDefinition_OnValidate_m61EEB2D519F6D2BFCDA1449456AD12F7451E1C9D,
	CinemachineImpulseDefinition_CreateStandardShapes_m278EDDEC7FA48FB02874E2E3ADA877D732824521,
	CinemachineImpulseDefinition_GetStandardCurve_mC3C6534C54B283F4F0A99DCF6723A92D91ED2E1D,
	CinemachineImpulseDefinition_get_ImpulseCurve_m6829C232BCD8DFB595C028C6B85A151802BD27F0,
	CinemachineImpulseDefinition_CreateEvent_m5169990F921BB0D026C6E099EB965C09E4A1D45E,
	CinemachineImpulseDefinition_CreateAndReturnEvent_m5B2DD42B512C246F6C88768AD159DD12FE35261C,
	CinemachineImpulseDefinition_LegacyCreateAndReturnEvent_m4674B4481E5C8749D58034A0EA0B83602FFFEB1E,
	CinemachineImpulseDefinition__ctor_m0DF7DA7FF9F2C545FF621FD0482024C5924B4E30,
	SignalSource__ctor_mB47DBDB4C80B809FE4D4D14A82A1B39FDA2489D2,
	SignalSource_get_SignalDuration_m12D1741D975D3F72759A3B8EE376351AC4B0329E,
	SignalSource_GetSignal_mA630937F85C20FACD1C86A64B725C15880E07468,
	LegacySignalSource__ctor_m47E5D665D6EA8803261F56D4162B76E49944435D,
	LegacySignalSource_get_SignalDuration_mDCB9255ACFB20AC6A0C1A13E9C5CAB3D8AD79CA7,
	LegacySignalSource_GetSignal_mD04FECB2C2533D50EED31458CA710D96F1D9EC18,
	CinemachineImpulseListener_Reset_m96EA430B6241450795795B1397770C9BFCE3B168,
	CinemachineImpulseListener_PostPipelineStageCallback_m683B33008E9FA1A00A22D1FB37199BAFE24F2D7F,
	CinemachineImpulseListener__ctor_mA63E963CAC79E2C50550D22528C3D8B5625E7B64,
	ImpulseReaction_ReSeed_m80E36F4DD6AA9D7387796A4D20B73F1D530D0CD7,
	ImpulseReaction_GetReaction_m3DEF1931AAC8E9F2EB1BDF6A93DDEA0CE2E074C8,
	CinemachineImpulseChannelPropertyAttribute__ctor_mE648AB30E624A586DA267734AEF59D2C8F1A02F0,
	CinemachineImpulseManager__ctor_m346881347C73DCD5000686C9408FE6F3C0BE3E6E,
	CinemachineImpulseManager_get_Instance_m796E40ED8A795EAEF6828F1E78BEC548AE948311,
	CinemachineImpulseManager_InitializeModule_m5C60A823F87B7A975AA794120FCFF07B6779B1F9,
	CinemachineImpulseManager_EvaluateDissipationScale_m86364CA6D5E4CDAB1DCDF0BBD2D045DE909F1FF0,
	CinemachineImpulseManager_GetImpulseAt_mA009FA51EB3967996FBC3CE30AB092742058E377,
	CinemachineImpulseManager_GetStrongestImpulseAt_m8B6FEF2DB52B1CE2D97E6906773E16B4672E867F,
	CinemachineImpulseManager_get_CurrentTime_mE288B4778997FF9457672B6B6570408D3D5DF6DB,
	CinemachineImpulseManager_NewImpulseEvent_m8EF13DBA4E6CC92CD749D65DBA28BFB565F4F082,
	CinemachineImpulseManager_AddImpulseEvent_m2086866EF411B9D5C81CFD63B4EE72E2FAF457A3,
	CinemachineImpulseManager_Clear_m078DA8D56609BD863014346C37E39907E315DC86,
	EnvelopeDefinition_get_Default_mC645BD6D012F136B443ED09B518D2C80801B2E65,
	EnvelopeDefinition_get_Duration_m99ECD0F243185637F28F813DF6C7E4D8B8E22DA8,
	EnvelopeDefinition_GetValueAt_m321C9E091D2281616FB23F0217C448A1E748F8C5,
	EnvelopeDefinition_ChangeStopTime_mCD399ABC589C3A7085DF613BEF7D658D3F5F4537,
	EnvelopeDefinition_Clear_m9D7A2C7235329CDC066F74184252D5042A884307,
	EnvelopeDefinition_Validate_m3D386A841D7D70AF6BA8418C923105610ED5E243,
	ImpulseEvent_get_Expired_m678FF8A3DA2C847006E5B8B7F0C026AD8BB998F4,
	ImpulseEvent_Cancel_m9040AAA5618EF16159A4CE1055A4A8F8C8B40A8C,
	ImpulseEvent_DistanceDecay_mFE048B4DB8487B3B57F88F32559FBAA1C974360E,
	ImpulseEvent_GetDecayedSignal_m0C1E1BC9D9DBD3CF9502F2A78E9CE76C88CB1DC0,
	ImpulseEvent_Clear_mC722697D5FF4F4CCB6310785B3B0B2A0E942256E,
	ImpulseEvent__ctor_m928FFFDFEC1E724119D9E2A00A83ADEC7A123C56,
	CinemachineImpulseSource_OnValidate_m2EB54151674202C38E0214B275D66F11AC9824E5,
	CinemachineImpulseSource_Reset_m82EC4F453F875ABCD307D0C776D78191F7805F0A,
	CinemachineImpulseSource_GenerateImpulseAtPositionWithVelocity_mE36667217B861ECCDD200C561D35F8CE35179DD1,
	CinemachineImpulseSource_GenerateImpulseWithVelocity_mE371B43094774C20DE1B47CB1B07E7CEB8E09483,
	CinemachineImpulseSource_GenerateImpulseWithForce_mB1854F631FD1CB51EB28E04696BE1093E0B9CD2E,
	CinemachineImpulseSource_GenerateImpulse_m636EBFFB7F218AB71EC03630646C17FD7D1ABC52,
	CinemachineImpulseSource_GenerateImpulseAt_m849718CB5F4D37BC6C7A742672E06E4617708112,
	CinemachineImpulseSource_GenerateImpulse_mA2E8EB5EC05355CDD6FA14D472E7992D5018447F,
	CinemachineImpulseSource_GenerateImpulse_m2942BE53CBAB6BEFFF94DF5E95BCC03630474622,
	CinemachineImpulseSource__ctor_mA2237C72339A5405AA8B06F8761D67103D291C6B,
	CinemachineExternalImpulseListener_Reset_m9B690DFF8FEB9B1B23E12B266FA448B07985845E,
	CinemachineExternalImpulseListener_OnEnable_mE8ECB30D4E61A0C71671D3DAD15023B537980C41,
	CinemachineExternalImpulseListener_Update_mFCA27C65AC1A2BF05F5C55F6C52DBDA71B2A4B7F,
	CinemachineExternalImpulseListener_LateUpdate_m65350EDEDA526E465D508EF3E4E8BA85E646EA84,
	CinemachineExternalImpulseListener__ctor_m41E8BEF5EADA91C4A457DF9FDEE4C6B5E031B098,
	CinemachineAutoFocus_Reset_m96B425EE2326AED45EA64753CC3851879E7D0867,
	CinemachineAutoFocus_OnValidate_mA804D0D3FC44012D377F3113618AE5F33CCB9E79,
	CinemachineAutoFocus_PostPipelineStageCallback_mDBBFFE344F5CF3367F2E490C324CBCBDB6751C0B,
	CinemachineAutoFocus__ctor_mA74C9E0B3893D5739CFB28B42D5979119BF553DD,
	VcamExtraState__ctor_mFBFE5A0B1EFAAB4A2FF1B5BECDFF196D4E527669,
	CinemachinePostProcessing_get_CalculatedFocusDistance_mFA0B8E9F02700B7F2033132A4A6E666A1FBBF696,
	CinemachinePostProcessing_set_CalculatedFocusDistance_m8F46D8AF5C005DC67C867D8905A0E73F272E3427,
	CinemachinePostProcessing_get_IsValid_m685E482D072ABD987A1F368153EF76E1A06CFB78,
	CinemachinePostProcessing_OnValidate_m3C525BA4FBE5DC8119616B9D6D2A7E9DF43F273E,
	CinemachinePostProcessing_Reset_mC377941DEAE2331F0D77F79D7FAFFE859ED423A6,
	CinemachinePostProcessing_OnEnable_m57FE0912173FD4BABD56C3D2650B386EFC434207,
	CinemachinePostProcessing_OnDestroy_m9D19AB44DE33BA0C13FCD9F8A239386322A6223D,
	CinemachinePostProcessing_InvalidateCachedProfile_m5C8C9BA51D065B3EED70E5ADAA4513BC00EB5275,
	CinemachinePostProcessing_PostPipelineStageCallback_m44D91FDA3230310E1319DE146C0134A7945FF240,
	CinemachinePostProcessing_OnCameraCut_mFE200B8A8EE07C12B30A82D2DED264DAC48E9D9E,
	CinemachinePostProcessing_ApplyPostFX_mFCB3FAEA8EC0B2A1B9A52CD165A1FB50B667F798,
	CinemachinePostProcessing_GetDynamicBrainVolumes_m9833A144683205F63999571D688E40417B3E0541,
	CinemachinePostProcessing_GetPPLayer_m1ED3492C2E2E9835DE3ADCC1C9B4D97AFA769C2D,
	CinemachinePostProcessing_CleanupLookupTable_m7698E5BAEC4E368B001966C62F23F25AAA499081,
	CinemachinePostProcessing_InitializeModule_m037F8387DB48F3B21BF5E9E7B157E8DE8F7BC99A,
	CinemachinePostProcessing__ctor_m489BE10A349C4A8BE65B3D46C6178FBBEBA06376,
	CinemachinePostProcessing__cctor_mE83704D20B93369F35046DCC9301695BF1C5B3B8,
	VcamExtraState_CreateProfileCopy_m42717CA4C0D54ADF3A72793D7790855D4AE7C7E7,
	VcamExtraState_DestroyProfileCopy_m48BF732B6A064E8E305A8402A557389DDD2E73A9,
	VcamExtraState__ctor_m01F1D9B616E656157F683CC1B749D57E8CA0D8BC,
	U3CU3Ec__cctor_m279199F45F011561613351E8B1856EA36AAEE9DC,
	U3CU3Ec__ctor_mEA767480AB6B7036B7DA0B5F982B9E1D7FD9EF2E,
	U3CU3Ec_U3CInitializeModuleU3Eb__29_0_m8FA681BCDFDF5D3015A0CBF20C5987003C55D149,
	SaveDuringPlayAttribute__ctor_mF55C962144DC218836F6D7BD4EF05E3C89865D65,
	NoSaveDuringPlayAttribute__ctor_m5407A216DEEB663D7D97B748271A7DE91D0400DF,
	Point64__ctor_m5EB70A4258455C7BF2A8F9988906B13BEE9CBD33,
	Point64__ctor_m9A2A8514FA57059A1E35A472B12C5FAADB791175,
	Point64__ctor_m42E3840AD7E7CB739E24F994EC2A80AD9C82B6B5,
	Point64__ctor_m9E3602EF25ACD25C87C1E8F8A1DCEB6195583EF0,
	Point64__ctor_mB75675457F560A9A56BD0E83BF82BE4D0619D797,
	Point64__ctor_mA6D9ADE4B7E4E625A510BB2A38666035C2ADB439,
	Point64_op_Equality_m533566B7842BD7883D47B43A2B2367E4250E1FFD,
	Point64_op_Inequality_mB4B9424D7E64E33071DC3260A52399506316BBE1,
	Point64_op_Addition_m5267335FA0B03821E9F16775C5A3BB997225B109,
	Point64_op_Subtraction_m40F4346AA88B2F98969C96C88DFFC8334E11836C,
	Point64_ToString_mD638C7638543E53040AF9B8F5EBDBD1297CFA5C7,
	Point64_Equals_m9867809E23F9C6E38A9C016EA31DCCD959A9FA16,
	Point64_GetHashCode_m78A3E7E42A2DE0050262E6CA7AC488752DCBB557,
	PointD__ctor_mD7F71F13290EE30EC27140C79FAD97DBBB6AB693,
	PointD__ctor_m76B965465D81252C8CA9EA4AA9DB761DAABCACF6,
	PointD__ctor_m9F826315168AF7531D2B9B1B6BAC781F3B232845,
	PointD__ctor_m768C67DB1D245EEACC1C035DB6CCB6307E718D61,
	PointD__ctor_mC60B9B78B15396C7BF505A9E4CAFA7A28B2D97E2,
	PointD__ctor_mD6A84584FCB6FA3FCE7C369420AC192B55B0764A,
	PointD_ToString_mD6DD8937CFA667AB2E1993A6BA9B691CF01B4816,
	PointD_IsAlmostZero_m00428B2A16410BB43461565457CB8E5B6B9FA95A,
	PointD_op_Equality_mC15DC7291675D4B72B63C292A08C3EC8FDFDFA44,
	PointD_op_Inequality_m62912227B963100E672B5AFB05B0131ECF9C321E,
	PointD_Equals_mEE31449D9D892E12AD7DFBC4338083BCF2EA2C95,
	PointD_GetHashCode_mC3F108E35D20A3BC90FCB8D63D3960E4357016B8,
	Rect64__ctor_m45E1192E0667467D907B8EAC1DF491F87BE35AE6,
	Rect64__ctor_m5CC7EB6188A744E984EF75A80C475F69F8CC36C7,
	Rect64_get_Width_m14E5462BBF19831F4A1ECCC4AC774F742D6B9D79,
	Rect64_set_Width_m78CE851721CBFD86A6784DBD8E3CECF49AEF7CB2,
	Rect64_get_Height_mFA6F40B071095EFA741B587D48DFA2DF6B213A91,
	Rect64_set_Height_mDF2C950030E9DAA14F3982D4054C7E32B016A612,
	Rect64_IsEmpty_mA1909BCE253440E19D6FCF27BCB713B55CD0F1CA,
	Rect64_MidPoint_mBAD1B4C2A407D545A79D57DAF96A11643A116291,
	Rect64_Contains_m491E4B0656953BDFB487F127EB689BEA614DFB2F,
	Rect64_Contains_mEA0DF8A8952D68B91A659BAE6E1CA80F095C0C61,
	RectD__ctor_m16EAF9EACEA6C15525DC8DFFC4AC6F56D2232371,
	RectD__ctor_m97BDBE86BF2072754834BF40952F96D8F58EF569,
	RectD_get_Width_m36E3639CE7CAE7DBBE017C681ECC288D38D18F60,
	RectD_set_Width_m50EC47998EADEB1E8E754DF9A5DAF7490859EAE9,
	RectD_get_Height_m0954D6B12C7EA5D3100435F39F1DC9DA97A6AB72,
	RectD_set_Height_m57A967EDB839AF8E2AD356E31EB33C5D1D86B8D1,
	RectD_IsEmpty_mD5E0765FFEF030C9A4F800CFA082A1C7D08276A2,
	RectD_MidPoint_m86B304B65390BF7D14A0DAA466E000DF14B7FAAC,
	RectD_PtIsInside_m6ED1F239CFF91D99F828E9503ED112F802F4DB6E,
	InternalClipper_CrossProduct_m63AC99360F8F749E6D9B6D98D47F495AE2523204,
	InternalClipper_DotProduct_m8500EA30A859184F30DFB8F75448FCEAC2B5D517,
	InternalClipper_DotProduct_m5C89226B95B7774FCD9EA8D27AAA094CA2764A7F,
	InternalClipper_GetIntersectPoint_m60EB44C838395B5DB9E638A648061DF6C431204E,
	InternalClipper_SegmentsIntersect_mCE20DEDC3CFD906BB55717F83A275F68CB803DB3,
	InternalClipper_PointInPolygon_m52292F3C90E8F1A26AC475D101AE1B26B5677528,
	Clipper_Intersect_mF94C92319A0A709CB2CB256AD1F40000E456A82B,
	Clipper_Intersect_m4CE872D5CCF42BBDFF6073B2E4BE7BBB27B52E75,
	Clipper_Union_m0A5D385ED9CC99B042D378D08CFDD97921B0F361,
	Clipper_Union_m2361172BF012A55B36BE6BA104B03E352E479129,
	Clipper_Union_m16184FA4C5C7109C189CD27B73FFD2F623F9865F,
	Clipper_Union_m77329CF78CA8EDB92B70670919CD6A00241AEF83,
	Clipper_Difference_mD7007303D8A90017024CC78870DA6BCCBB2447D5,
	Clipper_Difference_m63F409BC748290A884237E908E75FCCB43899544,
	Clipper_Xor_m191AE38812AECE227B5777B508A31B11A6474A49,
	Clipper_Xor_m4A0760A6B1E973B5C5F41D27898C7216F4DEB193,
	Clipper_BooleanOp_m9D1DF67E67C6B736A02E08D85AC4379F6B6A96E0,
	Clipper_BooleanOp_m48E37ABA8A6A68A7D5954373D8D25087C83A9AB1,
	Clipper_InflatePaths_mAD15F1415A16D40F7AB9F5ABDDA397CB1F473B1C,
	Clipper_InflatePaths_mEF8773463F841C66671FC30E4C9A005D04B9FF4D,
	Clipper_MinkowskiSum_mDD1FE8F104D708FCBBCB5294C2260D907C70BF7C,
	Clipper_MinkowskiDiff_mE66C4B8A9AB90861744D427E2BD82BF96BCCA4B7,
	Clipper_Area_m9BA24ABC1B19BC5F0332D8D460BF5CA948B002CE,
	Clipper_Area_m0AAF4070E776868EFD019DAB10D7A9811D38EA64,
	Clipper_Area_mA48B13C9ABA02B3B0DB18E97D056DFFF1A3EF00E,
	Clipper_Area_mCA8AABB15DE439C9D4B6CCEEEF8C2A18CC25E2E0,
	Clipper_IsPositive_m50894DF274EBCFE6626CD24AB35C54D3736923F5,
	Clipper_IsPositive_m475B7AEA20A3DE6A89D4A92601857EA3B80B1FBD,
	Clipper_Path64ToString_m4E6A42606A4445DC8A06B439880E5B984FA6E0FA,
	Clipper_Paths64ToString_m641C0C89AE946FB1E4CD7BA0A2F7DBD3111CD03C,
	Clipper_PathDToString_m31719E09AC0CAE021B42503A6471E927FB3A37CC,
	Clipper_PathsDToString_m2B7A9EBDF254D1F1A8914A3DC127A18FA7C8EA54,
	Clipper_OffsetPath_m0CE176A4EE6A2DE38F50C89B640D0F485928F4EB,
	Clipper_ScalePoint64_mC0CF74302270EFA4148EE58A507D4BB583647423,
	Clipper_ScalePointD_m69EDCED4FE50FF270E40B257F11140C13E8BC309,
	Clipper_ScalePath_m8551F85D9E9B91E619954C35C296646584E69055,
	Clipper_ScalePaths_m99176F63BDBD6BCDFF3C8CDD06FB935E61F76659,
	Clipper_ScalePath_m48C4CEACA1D22578E87CF5E7621835A6AC26014C,
	Clipper_ScalePaths_m04C8A48A6E2F88B3B7CA81849969928E0226F545,
	Clipper_ScalePath64_mCFF8D4326636E6B8EB54E0EC6E7558A38D3B7955,
	Clipper_ScalePaths64_mB85B01BB3B1DC0D08BABB6CF29EF23B832574398,
	Clipper_ScalePathD_m2FB6CAE621463C3FB7089EE31195817E5C4B8BE6,
	Clipper_ScalePathsD_mB2834B99D7B27D4671227249101FED7EF9E8E554,
	Clipper_Path64_m0F4D3A81EDF7BB7DC5F5E3846468E3017C0DB715,
	Clipper_Paths64_mE360CA8F10558E9B3E2F4F2716E26D51F72E915D,
	Clipper_PathsD_mDDA011C7723F83F6F6A10F1BC146D4EA96C3950B,
	Clipper_PathD_mF3A22497B45643C6D934F4AA4C18DF95567DEDBC,
	Clipper_TranslatePath_mC95214554AF35F34FA29FEDFB46ECCD6F6518447,
	Clipper_TranslatePaths_m35B30F1CD9E8C1A820BC6FB928EA1EEEC4972CC4,
	Clipper_TranslatePath_mCAAF79D4C9B32CD8956B229F5FE960FF9BC76469,
	Clipper_TranslatePaths_m81ADA4C18BD4032046CFC4BE0E7AB52A023D38B6,
	Clipper_ReversePath_m4D70229BD383312F335D92B6C3CB76718E9DFBA4,
	Clipper_ReversePath_m66747DE2F39798988E23316BA336B7132CC9C447,
	Clipper_ReversePaths_mAA7E16CDBD26F32506E5719CE2C3A4B9114D6C81,
	Clipper_ReversePaths_m9F304C90B74905FDC781490540B790612CC77F87,
	Clipper_GetBounds_mA0B74D94F68141CB4961856B51407A2754813666,
	Clipper_GetBounds_m83E232ABBB124699E8D8EE8881BE1ABDFAE889AB,
	Clipper_MakePath_m285E9191B65BAAB0266E5B7C3C069DA704C12423,
	Clipper_MakePath_m1857FE7CA04BEFD50D8C5B234DF5056975999E03,
	Clipper_MakePath_m0CEE58E0491BFEED6CA779FA6835B5916DA0E502,
	Clipper_Sqr_mD546F7C4FDC2B7072B0327B4330EC82523B81A1C,
	Clipper_PointsNearEqual_m7893EAD949C5328933459DA488F6E171E48BAC35,
	Clipper_StripNearDuplicates_m37A1A924F73A544263556741217BD2B2979C5A19,
	Clipper_StripDuplicates_mBBDE77B1208D6591ED82AA122040AE02A2E453B6,
	Clipper_AddPolyNodeToPaths_m2CF75BC3FE843E01D20456262C6DAFE720CCE932,
	Clipper_PolyTreeToPaths64_m5A2F579CAC271943EC008F7D3782ACAB5CCAC07E,
	Clipper_AddPolyNodeToPathsD_mCAF9A5128E9CBCE9F0E478DEC8B6B22ADF2DC195,
	Clipper_PolyTreeToPathsD_m0C7B9B09C84E70A8E4D3BA67BFC3B7522B0152F6,
	Clipper_PerpendicDistFromLineSqrd_mC9D7F4A630B546FF1FCCB70C87D956CEC794D8FB,
	Clipper_PerpendicDistFromLineSqrd_m8A169591B07F12AF0AB29306C2A896F51631E53C,
	Clipper_RDP_m1D9DA0B586C7A42D83D28CE1C9E56E8BAFEA3467,
	Clipper_RamerDouglasPeucker_mB210EFB97FC834C4B51D0B5F490098924B42EA9C,
	Clipper_RamerDouglasPeucker_mC582E722B714F238E100524CBAA1B0F7E9D6E27C,
	Clipper_RDP_m809A525B2B3DAF7D797903F9B5993CF53490E80B,
	Clipper_RamerDouglasPeucker_m3D986575659388334F67F744ED39D492998BC610,
	Clipper_RamerDouglasPeucker_mE836CF9E56A361BE6992B4541E169202FE75709D,
	Clipper_TrimCollinear_m364C4AC3B1171FAF18904DB9CC708C652BF9B8AE,
	Clipper_TrimCollinear_m34D4351793CDE4FAC94038DB2BD5D4DF17CABB7F,
	Clipper_PointInPolygon_m6661346977C51B35D654E5887F4265800B51CE90,
	Clipper__cctor_mC8D7A6F74A5789A5D161E65A27131C5DEED19777,
	Vertex__ctor_mBED6850150FAF517F7AD9CF8E41BE0B4CD8BCFDF,
	LocalMinima__ctor_m5F6CBC2F2A77AF724F270687C77B9CB6C6DC72FC,
	LocalMinima_op_Equality_mFE4055A76B5EE542E678B40AF00EB987009A6D88,
	LocalMinima_op_Inequality_m49A7290CFA803184D0D35EB428356BB42D1EFB48,
	LocalMinima_Equals_m1277D897943A6BFD7E7D863054002C607077D985,
	LocalMinima_GetHashCode_mCD15DB133E348BE5D7EF4CB5F967A13734BE3981,
	IntersectNode__ctor_mF58FC6DC9D4776F4A5823130E7186E55DBA70269,
	LocMinSorter_Compare_mC60E39AE99F875F5459C66078950CF1AA50D67B7,
	OutPt__ctor_mA2A33D081F46CC410EF22AAC73F09F43D83C7275,
	OutRec__ctor_mAE8AC3021E727E86BDE4EDF6F539DDE53E8B0BA5,
	Joiner__ctor_m93DD3E66087A5A327F98CD391CAC4405ABF880B6,
	Active__ctor_m8A418DBC21CF856F68CCEF069C21E4CF0E81BEE7,
	ClipperBase_get_PreserveCollinear_m884F36E93A9CF2925D7A90A47D0D4879E9CBF170,
	ClipperBase_set_PreserveCollinear_m2EA8A84D4C894541ACD80670231B8768FB5A923D,
	ClipperBase_get_ReverseSolution_m274C0141939DE41147535AACFE4E7354C9F3738F,
	ClipperBase_set_ReverseSolution_m33289691FAC628FBAD82766199C09EEA6B7AD985,
	ClipperBase__ctor_m3377C3B7E8707CF9691174B115F0755B0860F64E,
	ClipperBase_IsOdd_m278170AD17D458CC5D12AADB4BE103778039530B,
	ClipperBase_IsHotEdge_mC676B22C061AFE833E03471FEFD88B350FC858E2,
	ClipperBase_IsOpen_m22951C2BE3620958046B18FB9E6A47733B0E5050,
	ClipperBase_IsOpenEnd_mA850FE8D0993EEDDF1F26146B6B3D76EF74B8EB8,
	ClipperBase_IsOpenEnd_mA637897E566D52FF15B8E999A7F8517AA577BB60,
	ClipperBase_GetPrevHotEdge_m996312E7DFA4F884B18866CA2B6EFADFBB114EBD,
	ClipperBase_IsFront_mD28DA953694FF6117E55E9D6933C133FDA54744D,
	ClipperBase_GetDx_m08AA79E73C3DAD89AC4DEB5486B91700EE323998,
	ClipperBase_TopX_m8A62D77000F391837552843AC07FE4ED150D11F0,
	ClipperBase_IsHorizontal_m1692D7867C953908856CF62030ABB4CB9DE75685,
	ClipperBase_IsHeadingRightHorz_mDB2204EA300302A0D408198ABF2AB897EA5129C3,
	ClipperBase_IsHeadingLeftHorz_mB84DBA5B7A37878D5CAC69B302BD4B74A67EA400,
	ClipperBase_SwapActives_m8A5C8081EEF70929E68496D8C986C5A116E56618,
	ClipperBase_GetPolyType_m741BB0E77410109734055D00175B59573BF503FE,
	ClipperBase_IsSamePolyType_mDE785DEF1C48563916A08C4A7D276ED8BF22F6E1,
	ClipperBase_GetIntersectPoint_m4AD57489985047E3EA9B9EA37774FBB3AB9D8BB8,
	ClipperBase_SetDx_mDF7110C172D48EA818545F25637C05E7CCD35C17,
	ClipperBase_NextVertex_mBED342F034C90AE536EDBCC45A0BB200E71EC9F7,
	ClipperBase_PrevPrevVertex_m8639FC76AD6D52E6527D4DF989F42011359CED88,
	ClipperBase_IsMaxima_m11E2EB952D562FD426061D1A37FE66DB62B6E2BD,
	ClipperBase_IsMaxima_mE23D8D08AAF2D240C9523F9F9131BE9C4431AFD2,
	ClipperBase_GetMaximaPair_m6B24637D9F9BAE18F78CC03D80257AAE21E64D49,
	ClipperBase_GetCurrYMaximaVertex_mC64924C09B518BC2D2D79F09407EFA6C1E3C74DF,
	ClipperBase_GetHorzMaximaPair_m3385748EE2C638B47280CF7E5806C9F162B03C7C,
	ClipperBase_SetSides_mB7AC14B9DBA135C2A193351634A85C157CF7AEF4,
	ClipperBase_SwapOutrecs_m04F635A703EC3533740CAD6FFAD906D87C339F25,
	ClipperBase_Area_mD42D1A1C6F68B13F4B4C60A05FB6127B5A305AB3,
	ClipperBase_AreaTriangle_m5AA07E4FF02A9897B91F5D659B0E3B14BA086B0E,
	ClipperBase_GetRealOutRec_m61E940B13D222A6A1D8AC76D17D00B224E6FF536,
	ClipperBase_UncoupleOutRec_m22890408939D336C1ABA042B8D7953FE4B908089,
	ClipperBase_OutrecIsAscending_m178DAD1F829ECA431F336727C1418E072D1B1BB5,
	ClipperBase_SwapFrontBackSides_mC1256FBEB4A1C15383A9CCC734C67BA3165439E8,
	ClipperBase_EdgesAdjacentInAEL_m0CB19434574D3B2098FBB68110FAC12026D5D9B5,
	ClipperBase_ClearSolution_m8EAEC4BA1F5DC802EC73B232855734F0029AD98D,
	ClipperBase_Clear_m233C5BA84AB1788555678E9BA228B2E8BB20561F,
	ClipperBase_Reset_m42AB438C536535E39288ED45BD0521938F0833E1,
	ClipperBase_InsertScanline_mFC659C51CC122A800C176F131581A7291A3B4D3E,
	ClipperBase_PopScanline_mB2C1A5CDC638F5F7294BD32B4D2B685A4EAFB873,
	ClipperBase_HasLocMinAtY_m7F529905CCFD4737E098323762F806C889E06C9E,
	ClipperBase_PopLocalMinima_mC71F5CEC5E4AE28AF14AF92A1DF95D3AA1576745,
	ClipperBase_AddLocMin_m731137148E1C27058E7BCF2C63D943B193041B84,
	ClipperBase_AddPathsToVertexList_m60AC0B010EBEE7F3B44A84A68798DA2D29E15364,
	ClipperBase_AddSubject_m1ADB44258541E8F9689EAD6A84AC472C3904B44E,
	ClipperBase_AddOpenSubject_mF9D1FF3935EC5272EAEED601E0EF2C469A9961D0,
	ClipperBase_AddClip_m0CE6DA2418F5178CF6243CA92826DEB334563986,
	ClipperBase_AddPath_mC229D797EED38746B7FE62EAD24FB8AEAC36B728,
	ClipperBase_AddPaths_mABEC70A7EE5FD36A491E41E81B7D498B93CC6011,
	ClipperBase_IsContributingClosed_m1352FB40656F72475E1439CC74BE1C399017439B,
	ClipperBase_IsContributingOpen_mD76C8A801BD4E2AF141A4FC5DB27E92CB9B8806F,
	ClipperBase_SetWindCountForClosedPathEdge_m9D6BC44B172B72DE8FE205C88690B6906DC4605C,
	ClipperBase_SetWindCountForOpenPathEdge_mBE168497A0DA50A5A651B1588103AC33011583B0,
	ClipperBase_IsValidAelOrder_mAB7B29B92A678F18A13EBADFCE4998C923036293,
	ClipperBase_InsertLeftEdge_mDBD6FB3F63CFE665418599B8CD37DABB33EBDD3A,
	ClipperBase_InsertRightEdge_m3BD5F4A587007979CF9FE9F2C686CAF45A81ED1F,
	ClipperBase_InsertLocalMinimaIntoAEL_mEEC6A55D2033DC46DCF7C242030FFE65B8F647D7,
	ClipperBase_PushHorz_m1827650F0B60C16A04B29DD61E3F1C383A8BA848,
	ClipperBase_PopHorz_m644DC7AA9B43A38727649817D484A4D989E2BDDA,
	ClipperBase_TestJoinWithPrev1_m5ABC4B433FC41FA962738EC5A21513A8ABEE2F8D,
	ClipperBase_TestJoinWithPrev2_m6D7D06FF7C96493CD844803AEBA96A2D332054B5,
	ClipperBase_TestJoinWithNext1_m6079D22ACD17904191CDADF68323F00A2341D614,
	ClipperBase_TestJoinWithNext2_m1EEE0E9451F97AAE1E3CC23ADB1D8BF32F87E077,
	ClipperBase_AddLocalMinPoly_m7EE4107F7C5A0192265A5969AC80781F0FCCF90F,
	ClipperBase_AddLocalMaxPoly_mC7ADDCFEAC60A745D30BC4ED674F18B619BEEE95,
	ClipperBase_JoinOutrecPaths_m20C9C5FBA4EEADEF857734A83AFC302293F56E35,
	ClipperBase_AddOutPt_mF63FF842B322347CC9EF7657B217DAF69E6F2DD4,
	ClipperBase_StartOpenPath_mB5491561FB23DAB237F04B58CF3FE7D74C2F9CE3,
	ClipperBase_UpdateEdgeIntoAEL_m37DA3969746C4FE39BA5EBF1D58A52BE9AE5BA87,
	ClipperBase_FindEdgeWithMatchingLocMin_m10F0397F43440BEFE3BB90CDAA5CC17EE93DEF20,
	ClipperBase_IntersectEdges_m782CE1843263C1B46A447B9AD7D22AC52F8658E0,
	ClipperBase_DeleteFromAEL_m6C3D2F623AD0405D571F21AB26887499487E7843,
	ClipperBase_AdjustCurrXAndCopyToSEL_mE1C8C6971748EB474A975F6572BEEA01D10B0068,
	ClipperBase_ExecuteInternal_mE2B20F005B3D1BBB64245576D0AD138C46388C21,
	ClipperBase_DoIntersections_m79E1D7039EBE16E83976330EB912E067FCB52B77,
	ClipperBase_DisposeIntersectNodes_m6BEA90C33EE3005FD9DA50BC4815D0564D61C022,
	ClipperBase_AddNewIntersectNode_m837BD1744A6942E571F42E6DAB8F1DE5407A22D9,
	ClipperBase_ExtractFromSEL_m5D22C9126471B9AC1345BBFB37BE7A69AB48C55F,
	ClipperBase_Insert1Before2InSEL_mF9CE36D808835BD0A0A7F245C9702E8F9F1BF9B9,
	ClipperBase_BuildIntersectList_mBC017D612CACD60B48A5D14E2A87FC5C34B3D6A7,
	ClipperBase_ProcessIntersectList_m3BB4D0746870EB2FFC947D0D0E221A4EB6A93DDD,
	ClipperBase_SwapPositionsInAEL_m73AB50026B0B13D36AB7D7999FF156E69F70CE16,
	ClipperBase_ResetHorzDirection_mD051239C59ED695641E44B5793E48C51AFEF794F,
	ClipperBase_HorzIsSpike_mE363A87C004376AE41FE89BFC0D646BBEF7A6C60,
	ClipperBase_TrimHorz_m96375B5B9BB8E36F05C55072166ADD081CC27F02,
	ClipperBase_DoHorizontal_m89D7E0E0D1BE0B52575E30ED668384A3382D74ED,
	ClipperBase_DoTopOfScanbeam_mB8542D876AF2351848FBDB064DD26819BB24009B,
	ClipperBase_DoMaxima_mBCD63C7B9D595BA8BAE6BBD02C15976C609D939B,
	ClipperBase_IsValidPath_m4A6A4261CD2656BBEA994974D744CC1970C6FF5B,
	ClipperBase_AreReallyClose_m0CB08A087791CDE2D6CBE06ABC59245505276AA0,
	ClipperBase_IsValidClosedPath_m9BDB793958F77047BC6943774941AA425E4D6BD1,
	ClipperBase_ValueBetween_mC7E79835A793785D52B917856834D5CE16011108,
	ClipperBase_ValueEqualOrBetween_m21550523E9ACF5F8E0DE003B01B4AAEF5162A334,
	ClipperBase_PointBetween_mBA693B2881DB0E9A63501D90012CAEC7C1C327E5,
	ClipperBase_CollinearSegsOverlap_m6A09AF2E2328DC8798F54A56821FCBCFACE81348,
	ClipperBase_HorzEdgesOverlap_m34C01213B2415C4F352EFC2A36CCA933CEE0457A,
	ClipperBase_GetHorzTrialParent_mF8585835AD44D92F939BEF2FFE3C8EB1C6620ABC,
	ClipperBase_OutPtInTrialHorzList_mA11AC0DAC904C71C55812C9B40224C67194696CA,
	ClipperBase_ValidateClosedPathEx_mF64A4DD8944AC26C1828F0D7A32D34DDCCD1BD2A,
	ClipperBase_InsertOp_mE917A3A84A18833FFBA1BD95153F9B70A1735D9D,
	ClipperBase_DisposeOutPt_mFEF5EB205D4C5A47B36ACB86411720F87CB06E9E,
	ClipperBase_SafeDisposeOutPts_mCC7278FF462786ECFB1F0299093027A32030C958,
	ClipperBase_SafeDeleteOutPtJoiners_m98AFF4E0A2FE970CA38243C711FF3EA5B9A6A00B,
	ClipperBase_AddTrialHorzJoin_mC14D3969C6AB0B939DE1282424011D42E468A492,
	ClipperBase_FindTrialJoinParent_m54FA0A5C86A2C5E139421E7CA4D25F249F9C6FDB,
	ClipperBase_DeleteTrialHorzJoin_m1C099923EEAB4223461739094D3DAF400D060CFB,
	ClipperBase_GetHorzExtendedHorzSeg_m4E7926788326203C937606CAD7697F1804FDBB2E,
	ClipperBase_ConvertHorzTrialsToJoins_mA177417AF153F1813350BF5A1BD764CFD4C77547,
	ClipperBase_AddJoin_mE74B403A7D3FCCF2F9FA1BDC84FF035BFC97D0E5,
	ClipperBase_FindJoinParent_mCC8C9A816C714BED301C9E64BC45878621554770,
	ClipperBase_DeleteJoin_mA50E94C741854BB15C89E22BED1C4C039BD6DE78,
	ClipperBase_ProcessJoinList_mCFDD120CD4D1B5B32BD30ADA58376B419E805BE0,
	ClipperBase_CheckDisposeAdjacent_mC20ED7A34CF29D813BF4E9514CC085C7C9828A2A,
	ClipperBase_DistanceFromLineSqrd_m8F139AE6DCC2087D43ABB784BC1F3A4391CBD10C,
	ClipperBase_DistanceSqr_m1E07B2BC535E14C93A3ECAC01CE2D537BD5170DE,
	ClipperBase_ProcessJoin_m1E0165E0A829ACF58B93A7BFD069111BD1186773,
	ClipperBase_UpdateOutrecOwner_mEBBFDB524821EDBA9347DBFE47A09304289F15F3,
	ClipperBase_CompleteSplit_mD6B84AF5B330CECD61E9F91192174E41858BA0FB,
	ClipperBase_CleanCollinear_mD6E96F28435321850F63DF37354B7B1F12967311,
	ClipperBase_DoSplitOp_mDD082A518BB662241DFD6DDF04E5B6EC437203AF,
	ClipperBase_FixSelfIntersects_m0480C143081AC01FAD037533163A021BF870AD5D,
	ClipperBase_BuildPath_mB2C8ABFB3EE94619AC8CD435B8CF7C9DE20C5FBA,
	ClipperBase_BuildPaths_m779EAFF6AB234617F6697E15F5B5FF1CE8060F5E,
	ClipperBase_Path1InsidePath2_m581D2B694D1BDC7F7781D46187F9EB2251DAB008,
	ClipperBase_GetBounds_mBE7C3E594E3057FF959BB035D2F5B9FE453B80A5,
	ClipperBase_DeepCheckOwner_m5CDFD90C49FB7F862D69C9E4B66F3C2D810CACD7,
	ClipperBase_BuildTree_mD803DF2B2ADAAB6CA91A272ACBC4D30B941C0583,
	ClipperBase_GetBounds_m1CA8845EB91981C3EB4C5535A463801446A82719,
	IntersectListSort_Compare_m8B6F16B72168215A62DCC94F47817F6C4A06AFD7,
	Clipper64__ctor_mD7F57490D5A0F55A0401F504F914FF044965B7BE,
	Clipper64_AddPath_m92239EE232CD5E2C6C7C38679EF14C2A97A9D7AD,
	Clipper64_AddPaths_m4E1BA351D175D464F244AD96F66C2BB642668505,
	Clipper64_AddSubject_m2D7944587E377D95D871F4F2CAA7DCA5DACD55DE,
	Clipper64_AddOpenSubject_m9A74F31933638F0EAA6A6695D9F2A49E7E16BF55,
	Clipper64_AddClip_m46DC4A4A6A9B54A3553F537A8C80F3461FDAAF99,
	Clipper64_Execute_mA5BD23FA79851FA3659B5012C257780F52857F8A,
	Clipper64_Execute_m6B9ECAA8BCCA3D11996F642C355F9E0660261A97,
	Clipper64_Execute_m7D08F40FB34AC98BCEFB7834FFC701D21303A901,
	Clipper64_Execute_m3E2E7B7E9B4244177C39A2C52EB44DD5697867D7,
	ClipperD__ctor_mE7B5A7CC43655E73F67E3373AC406594237A37DC,
	ClipperD_AddPath_m4F6E5CB18285E5144D5CE23DBB9BF7212DD09B94,
	ClipperD_AddPaths_m73F811E5AB517CD1A5090B5E92ECC27407ED239C,
	ClipperD_AddSubject_mAA6ACBE6283BFB950B00D5E106E88EBF7B9E9801,
	ClipperD_AddOpenSubject_mE681B20FD13647FCF902E3D4C3CD7C218EE493D8,
	ClipperD_AddClip_mECEA14DE0BD6066953DE3897FC16C350223C3FE9,
	ClipperD_AddSubject_m540EE1963865A6C17E1E9E44F7391A872096A132,
	ClipperD_AddOpenSubject_m4990A6FAC1F6D3B05A055CF73F3EF91E44F8E887,
	ClipperD_AddClip_mB34D456FDE8684862D7C398143BFBD26F5C36A88,
	ClipperD_Execute_m98CC3DEA8503C9E09D2916F1FE1C8633A4967D29,
	ClipperD_Execute_m4DFC11A0AB84A69EC7A9A18F65C8D43B1FD6CD5C,
	ClipperD_Execute_mD83952B879D3F86C0AFD1D45B2136E5E33BD672B,
	ClipperD_Execute_m4BF3F81176D9D89D37A21F92B3C23AE724FC7360,
	PolyPathBase_GetEnumerator_m757EA5B6B5C9CF33C793FC1688F806D6C6DDEBB2,
	PolyPathBase_System_Collections_IEnumerable_GetEnumerator_mCD919DAA12C9FD1C70E4268BD4CD13AEA7B026D4,
	PolyPathBase_get_IsHole_m4EF5FC1764C9E87C0B403A81417B298B89EDED3F,
	PolyPathBase__ctor_mC01752BBF2643B614C29EC03B2B1E71FB57FBBF6,
	PolyPathBase_GetIsHole_m83FFD8CC2F9FFDA80454B443B4D3C423F49C2017,
	PolyPathBase_get_Count_m2BE66946E74CF32A62DFD129F73ED99A9A1C8809,
	NULL,
	PolyPathBase_Clear_mA5401C1B473B24F03102CC5ECCF4FA775D75F198,
	PolyPathEnum__ctor_m301C275A82F48AD72F9B87B01CA72089822B69B9,
	PolyPathEnum_MoveNext_m365FE87B6526A651BBE1351BBA06A52CCD89219E,
	PolyPathEnum_Reset_m34E4F4E003B88BA05CD656412BE822ED2C8A6E5F,
	PolyPathEnum_get_Current_mA6856B935140CB345D8DE716388177273D0AE4C2,
	PolyPathEnum_System_Collections_IEnumerator_get_Current_m89CACC27AF0DA7E089DB77D83597002304CA4A5D,
	PolyPath64_get_Polygon_m09838F3009F102410EF6FD6BBC399D4591DA291C,
	PolyPath64_set_Polygon_m090CF4FBB804DD2A75FF8D672E210CA6F7A01BA1,
	PolyPath64__ctor_m280F23C97135E84896B5AA1787EA24D1196A228A,
	PolyPath64_AddChild_m4900DF946B026D77E9A5262E861CDA436D8FECAC,
	PolyPath64_get_Child_m128FE59E349F0FD2C8511D1505AA9CB0232A8667,
	PolyPath64_Area_m7776BA7AE19557FA4DCB252CE69145A339B562A8,
	PolyPathD_get_Scale_m6507D170709F8C498F979CCD7BFDAB456241242E,
	PolyPathD_set_Scale_m352A10AE02AB6C2B80ABAC34664F26577C696DFF,
	PolyPathD_get_Polygon_mCD84F613699E928FA14E65E77C3F94D395374C13,
	PolyPathD_set_Polygon_mCAD9925D6C69BA47BB32995377AC0A53148FE54A,
	PolyPathD__ctor_mCABCA9798C5C95038F016C4CAF4E94066FB4E1CE,
	PolyPathD_AddChild_m275F1CEA2ED7CAFC2B9B18EBDFAB33AE38611765,
	PolyPathD_get_Child_m0225B909AFDDF9CD705E83F168873AC7035C669C,
	PolyPathD_Area_mC63C7A5233A3EEEC5E2B49D61A6CCB5A54002642,
	PolyTree64__ctor_m5C532F826C31E7C7439DB5C2CFA8151E2B245026,
	PolyTreeD_get_Scale_m8B360EBAC6EC68D9A29D0EF471AB6680BF49D35E,
	PolyTreeD__ctor_mF18D57DEAF88C2C2F5C80F1561856732C3266704,
	ClipperLibException__ctor_m9462A5848E7FCF39A4B0D917DC1378FD9046E2D9,
	Minkowski_MinkowskiInternal_m977C74D953AD031768A97C2D44D602F74FEAC8EA,
	Minkowski_Sum_m8A8E42B41873740061A51103B15FF88E64C32144,
	Minkowski_Sum_mA27EEC41FBBCEB5781B3937B0C35637DE6180FBD,
	Minkowski_Diff_mD501FE629DF34294AED49942F9296C8856FB43FB,
	Minkowski_Diff_m1693CC4319EE8F9933412039B01B331E012A1831,
	Minkowski__ctor_m6D3531CE8C2F80DA0354E69AC5FC8770D23EB5F7,
	PathGroup__ctor_mB491E5FAB07A99A82FDD1765AFF45BB5867C6269,
	ClipperOffset_get_ArcTolerance_mD01E65F73E83F39DBF0820E69EBB71CA767F66C3,
	ClipperOffset_set_ArcTolerance_m417ED66D7BAF4FFF26377DE261C3357881190609,
	ClipperOffset_get_MergeGroups_m085EBEC3841F64B2B5AA25063553CA77A65ED861,
	ClipperOffset_set_MergeGroups_m84D491969A57C312C9EF7CAE3B18017FB1D22007,
	ClipperOffset_get_MiterLimit_mEE41076816ED8D7E9B95C366AC3E844665C0EB36,
	ClipperOffset_set_MiterLimit_m0265DDAEAF8FAF787BE72E0684473894D73774E9,
	ClipperOffset_get_PreserveCollinear_m0D204F10652EAD0F5EBED348D1426B284CF343FD,
	ClipperOffset_set_PreserveCollinear_m6E40EFB4F540B828ADB03C42E890AFC07720D5E2,
	ClipperOffset_get_ReverseSolution_m323515AFCE89F5DE51B807CDF8DCC75CE9C0556A,
	ClipperOffset_set_ReverseSolution_m6704E4F2CDC2B5DD686D83F3D125046CD46AD911,
	ClipperOffset__ctor_mC2B401F9DCCC0D75FC04A3BE296CB5C218765117,
	ClipperOffset_Clear_m5566299BC22F28883A844B2D14EF028BB254363A,
	ClipperOffset_AddPath_m969306160BBAF29DB547230952D82A8DBDACC438,
	ClipperOffset_AddPaths_m432691C01083D6BF2B50C159034EEBCEF52C3198,
	ClipperOffset_AddPath_mAE2FA2AEB1D9818F69787A1AF184D08D3200D75B,
	ClipperOffset_AddPaths_mF739CD8C0649037DDB0D1DF17A9F7C492EA65820,
	ClipperOffset_Execute_m19AFCEF180405E790405E5C88D667CCFFEB38E11,
	ClipperOffset_GetUnitNormal_mD87821DCE28C5287D8E2C7AE876631F13472222E,
	ClipperOffset_GetLowestPolygonIdx_m812E5758CF1130A319743FFB4B23AAAF635293C0,
	ClipperOffset_TranslatePoint_mBAABFDF6DDD3C74DB61777EDAF9FF3CE2C6FD46B,
	ClipperOffset_ReflectPoint_m98FFDF2A94DD3D82EF4D84832183FA220B3227E1,
	ClipperOffset_AlmostZero_m3FA32FE92D06B3D825C5A23C7C276CF8B0AD2384,
	ClipperOffset_Hypotenuse_mBF6C34BAF86DB86331E6570F69E1D95CE21ABE55,
	ClipperOffset_NormalizeVector_m1C3295296135C9A10C16DBBB259531286CDFCB61,
	ClipperOffset_GetAvgUnitVector_mC9D0378C1997F1046E681797571DCFBFE7BD5828,
	ClipperOffset_IntersectPoint_m4B59C25C54631781D081D22D2178736AB80D1B9C,
	ClipperOffset_DoSquare_m83408508052C282F823959CADE1E0E1D5E54060D,
	ClipperOffset_DoMiter_m49DFD6F57BD35879AB6BB63D68085D9F81FE0FFD,
	ClipperOffset_DoRound_m31B75D3953B4C6CC2D84BA5D10D2E97C8CA2DEE4,
	ClipperOffset_BuildNormals_mB1AC7D53EF4066BD95311730430D04E3A5382056,
	ClipperOffset_OffsetPoint_mEA7B92FB9B0B2AE00AB6670AC4686F657A2FF47A,
	ClipperOffset_OffsetPolygon_m085C68F71521D7963FD4CEAEEB02053CF9CB6324,
	ClipperOffset_OffsetOpenJoined_m2260AB1498D47577582BE78F9A7489811626F2A2,
	ClipperOffset_OffsetOpenPath_m613FDC55390337609EDD574C84927587C1F29D13,
	ClipperOffset_IsFullyOpenEndType_mBF88D37022D48398D9FE55E9E94F7E38657C2F98,
	ClipperOffset_DoGroupOffset_mE9AB8D44249456BB7FD8B6129247869DB92017CA,
	CinemachinePlayableMixer_OnPlayableDestroy_m028A80CB5B9851B584BA738583715CB43CDCAC8E,
	CinemachinePlayableMixer_PrepareFrame_mC5BDD90E9383712485D4E8C8E2C1B7463AA39DA3,
	CinemachinePlayableMixer_ProcessFrame_m85F1A2389BC43CFBBB6FEE5880C87F509E43F749,
	CinemachinePlayableMixer_GetDeltaTime_m9C79B5A40F18ED60D898D8DA0398DA7569C0F4F4,
	CinemachinePlayableMixer__ctor_mF9D8F32BAD80EAB2BDD5E77B8C404F8D7D89873B,
	MasterDirectorDelegate__ctor_m178AAB7DC100B6474C6F6FF1EE578251EED56E2F,
	MasterDirectorDelegate_Invoke_mB16645851739385543DFEBF6C2C63F3CB816C82D,
	MasterDirectorDelegate_BeginInvoke_m8C6CCF365354890AA3F4E1AAD314A4F727657ED5,
	MasterDirectorDelegate_EndInvoke_mB0EC894637D6F53EE9F26E78586EC17518D31AE6,
	CinemachineShot_CreatePlayable_mEAB6AA6FF023EA6B752A730737E422FB221F0A79,
	CinemachineShot_GatherProperties_m9EAC8210F7BEA1CE76745B07C885C7FA4D6FB18D,
	CinemachineShot__ctor_m1A3DBD1AAFF2E06D85E28F827AC34522100B08B0,
	CinemachineShotPlayable_get_IsValid_m7A5EF626BEF91096CC497B752F54FB119AA6DC3C,
	CinemachineShotPlayable__ctor_m4C6F8B9615410D6954ECDA93A8880C5786FCDDA4,
	CinemachineTrack_CreateTrackMixer_mF847D3583C1E75108DD71CAC50130351ED77719E,
	CinemachineTrack__ctor_m19999D2D7FCAC28D93F1E62694C5FE7DAF86B6B3,
	TrackerSettings_get_Default_m8E415BB3D0AD83EABCABB98E592C1C14D70062FE,
	TrackerSettings_Validate_m39F755B16D1D9850129598FFDD642263727B5FBE,
	TrackerSettingsExtensions_GetMaxDampTime_m623C441E60820B00C91B790CD1C4D489CFE2405A,
	TrackerSettingsExtensions_GetEffectivePositionDamping_mBFE2AB354F87DA056A1E0131C5DBC3A935AF6643,
	TrackerSettingsExtensions_GetEffectiveRotationDamping_mCC4F77628859B407EC524006B4CECCDDE865C102,
	Tracker_get_PreviousTargetPosition_mF5F405294137CDA1A119EF5B756882AB84E62520,
	Tracker_set_PreviousTargetPosition_m4BDFFF69C93C6535B2EA145D84A3B965C9653CD4,
	Tracker_get_PreviousReferenceOrientation_m225BDC07837F9BA115290A0AEC57F14AE3A1C8D9,
	Tracker_set_PreviousReferenceOrientation_m1FAF567E391E866E6737253EBDEADDBAAE231905,
	Tracker_InitStateInfo_mF66FEF8AD511965D882D267C455527461F1E7A7A,
	Tracker_GetReferenceOrientation_m787693044C37713DC332241FB2108373C12FD40F,
	Tracker_TrackTarget_m8CAD3274FAD87F564E9D8D749229041EEEA07D9A,
	Tracker_GetOffsetForMinimumTargetDistance_m10279516242F65DF2226B6A5C02AEF36B7431419,
	Tracker_OnTargetObjectWarped_m7E42DEF855CEAEE2A19BD5A670A1798715EB66D3,
	Tracker_OnForceCameraPosition_m952EC37B25CD2FA1E1306FB1AD6A41363FA830C8,
};
extern void ShapeCache_Invalidate_m7EAE6FF99DB3AD5D61BEB9156C14E6BD0DE1AF3E_AdjustorThunk (void);
extern void ShapeCache_ValidateCache_m9B20BC43138C2439DB9F4499BC3E9338504B640B_AdjustorThunk (void);
extern void ShapeCache_IsValid_m29BFC1989D49FB1151484B62E53A003D95A9AB81_AdjustorThunk (void);
extern void ShapeCache_CalculateDeltaTransformationMatrix_m8E975270968FEC0D915DA7EBDB6459FF46A4EE6F_AdjustorThunk (void);
extern void Instruction_Validate_m8178D703AE92163B2335C8EE46B8AB28EC01BF1F_AdjustorThunk (void);
extern void LerpRollData_Interpolate_mCB9BC66ADFB6B3E320ED43AB11A121DAE116134E_AdjustorThunk (void);
extern void LerpRollDataWithEasing_Interpolate_m97056DDAB2AC587935B5F6010023E96592917FDC_AdjustorThunk (void);
extern void RollCache_Refresh_m1570582AE45E77CE46A95CDEAECD004C568B072C_AdjustorThunk (void);
extern void RollCache_GetSplineRoll_mF4DEFB86AD16D1E13A2A620FB4EC35512625905D_AdjustorThunk (void);
extern void OrbitSplineCache_SettingsChanged_mE93B6AD4D70694FC04BCCE02118447F278CEB151_AdjustorThunk (void);
extern void OrbitSplineCache_UpdateOrbitCache_mEE095885509E49893CA6EAAEF08383EE05F9BEBD_AdjustorThunk (void);
extern void OrbitSplineCache_SplineValue_m4EF5A34796ED0FF7210F0F7B0365070E7B2CEADD_AdjustorThunk (void);
extern void FovCache_UpdateCache_mBE2C2FD57DAD380D862B04A8374C6A3E6C60B99E_AdjustorThunk (void);
extern void FovCache_ScreenToAngle_m697CC6600E25E343A92923A25F91567B3202669F_AdjustorThunk (void);
extern void FovCache_DirectionFromScreen_mC6E97AA5783FD6A4FEABAAE42E3FA3C14C47144B_AdjustorThunk (void);
extern void Item_get_WorldLookAt_m5FC326A00B241D52FC2C5C8152BCB0FE23BB1E36_AdjustorThunk (void);
extern void Item_set_WorldLookAt_m8C05FC415D07807B4349FF9C858391BF04BBC24A_AdjustorThunk (void);
extern void LerpItem_Interpolate_mC3591384873341D9D393C147FBA9F5EF94FA46BF_AdjustorThunk (void);
extern void CameraState_AddCustomBlendable_m0AA826256B3ED67F345FE5DBE2881830CC4CCCAB_AdjustorThunk (void);
extern void CinemachineBlendDefinition_get_BlendTime_mEAAA78C5DA24961202ED2E0ACA2BA3876F13F0CA_AdjustorThunk (void);
extern void CinemachineBlendDefinition__ctor_m39FCAAA94DC44BAE3882C37D8BE9CC903B3C3D0C_AdjustorThunk (void);
extern void CinemachineBlendDefinition_CreateStandardCurves_m03315670810AA1CBA01C39471C2A53B291771D10_AdjustorThunk (void);
extern void CinemachineBlendDefinition_get_BlendCurve_m7613588597293BB3B062EA98D823D78C19AB9AB6_AdjustorThunk (void);
extern void AspectStretcher_get_Aspect_m9E6B3A0AB70146498E1BAD185698573EC606F687_AdjustorThunk (void);
extern void AspectStretcher__ctor_mFF295333F630563CEBB2E78810A0821F15AF4CD3_AdjustorThunk (void);
extern void AspectStretcher_Stretch_m86BC52CD00CE835B7A1E8881877534E761E1A86B_AdjustorThunk (void);
extern void AspectStretcher_Unstretch_m95AFEFCD91BAE10A23E6640BEA785C0082A327E0_AdjustorThunk (void);
extern void PolygonSolution_StateChanged_mC389F789218AE923B31D6C0AA436436BCDD7CBCF_AdjustorThunk (void);
extern void PolygonSolution_get_IsNull_m467E000AEC921A9EB76D814C0611F2BC9FB5AA15_AdjustorThunk (void);
extern void InputAxis_ClampValue_m9B998276127396874FBC7D934D22217A2B1F7983_AdjustorThunk (void);
extern void InputAxis_GetNormalizedValue_m6A3EED70C1988D787B19B725D8071993F7EFD411_AdjustorThunk (void);
extern void InputAxis_GetClampedValue_m8828F8C144F025EF8203C94F9A00FE2C26656013_AdjustorThunk (void);
extern void InputAxis_Validate_mD5883DE4C3F177CB83F4554222480DEBD1B160A2_AdjustorThunk (void);
extern void InputAxis_Reset_mFBC7BAF970104C4927D5F73C62BE55F889071DB9_AdjustorThunk (void);
extern void InputAxis_TrackValueChange_mCB6E35D0B19AEB110BC712F4B1075B13350FFCBF_AdjustorThunk (void);
extern void InputAxis_SetValueAndLastValue_m0587AE4B73578F3144756FDCDA7DAE6DB7233067_AdjustorThunk (void);
extern void InputAxis_UpdateRecentering_mA17553DCBBEBA694E54A12088A30EDE575DF5800_AdjustorThunk (void);
extern void InputAxis_UpdateRecentering_m44844384DFA8B93AB3847B51B23B4F61223FD10F_AdjustorThunk (void);
extern void InputAxis_TriggerRecentering_mDC766F564C495683B8F136D7A6E02C0360F710FA_AdjustorThunk (void);
extern void InputAxis_CancelRecentering_m34D92FBFC0234058E6A7F68F10ABED0116F86558_AdjustorThunk (void);
extern void RecenteringSettings_Validate_m6EEB2F6774899BDC39A2246C4D74AFEDC16D404D_AdjustorThunk (void);
extern void DefaultInputAxisDriver_Validate_m29DC58F7A6E8B2BD7C54C3FFDA88D602E7E59135_AdjustorThunk (void);
extern void DefaultInputAxisDriver_ProcessInput_m942B6A2B5C55C8936D74BF2D62E872A1DC735A03_AdjustorThunk (void);
extern void DefaultInputAxisDriver_Reset_m258D1F1BBAF2400C7449AEFD16C50BD5C536859D_AdjustorThunk (void);
extern void LensSettings_get_Orthographic_m2AF510A849B57A9485FB4000835234532D83A2E9_AdjustorThunk (void);
extern void LensSettings_get_IsPhysicalCamera_m2700AD42D56E90B57D18438C765F929E09CC6D10_AdjustorThunk (void);
extern void LensSettings_get_Aspect_m24F38278056BAB1241945C9E75983B9A9FBDA177_AdjustorThunk (void);
extern void LensSettings_PullInheritedPropertiesFromCamera_mAC72C4DE1DA21BF3CE38AA8C318312D3EB8CA3F5_AdjustorThunk (void);
extern void LensSettings_CopyCameraMode_m687969BF551F739E019BE706A6827F5E43D4BB76_AdjustorThunk (void);
extern void LensSettings_Lerp_m17E59426452E33421D279C13D83BE1D9D64B37BD_AdjustorThunk (void);
extern void LensSettings_Validate_mFE36866AB5B16F9B0405E410CACB999F29519265_AdjustorThunk (void);
extern void NoiseParams_GetValueAt_m1B5D915FA8782DAD5230854FA7FFDA503A34D021_AdjustorThunk (void);
extern void TransformNoiseParams_GetValueAt_m757A109B904184C5AAE5BD2B466DC80026474FF6_AdjustorThunk (void);
extern void PositionPredictor_get_IsEmpty_m234E8623FD6865187578B480D0FA79B3E0F8AB05_AdjustorThunk (void);
extern void PositionPredictor_get_CurrentPosition_m0736A2E1D68141B5BEC2BD9078B2624EB0005018_AdjustorThunk (void);
extern void PositionPredictor_ApplyTransformDelta_mC33429167317BB4EE95AFF4EB66E341958463DA4_AdjustorThunk (void);
extern void PositionPredictor_ApplyRotationDelta_m0EDD7BF0D0321D32668DD102887CF8F90CE6CA9D_AdjustorThunk (void);
extern void PositionPredictor_Reset_m1C332992802B45BCD6677A1C04B54F9FE47962D8_AdjustorThunk (void);
extern void PositionPredictor_AddPosition_m521CEDF019A2B4289EDBDF17B7478524B6CBB67F_AdjustorThunk (void);
extern void PositionPredictor_PredictPositionDelta_m58018C09253901A9A4D1A39325684D1EB82CB882_AdjustorThunk (void);
extern void PrioritySettings_get_Value_mD2377C4F969CCA1E306F3A188F78DA1C31CB5F3C_AdjustorThunk (void);
extern void PrioritySettings_set_Value_m6C9D23382F5F5AE929899110938132552027B440_AdjustorThunk (void);
extern void ScreenComposerSettings_Validate_mC2F21833234FFE27191159ADEE6A2CC6DAC38CCE_AdjustorThunk (void);
extern void ScreenComposerSettings_get_EffectiveDeadZoneSize_m67B0C04C458112121CFE381069227FF96470EA61_AdjustorThunk (void);
extern void ScreenComposerSettings_get_EffectiveHardLimitSize_mC4A9B50A42FD6446505DCC222EEEFCD688B8CE29_AdjustorThunk (void);
extern void ScreenComposerSettings_get_DeadZoneRect_m411B6054F72D2D6CD33562F3970EF01E7A4D25E8_AdjustorThunk (void);
extern void ScreenComposerSettings_set_DeadZoneRect_mB8DAA838A8727F3D9F52F92C8E37E0B2EAAB1991_AdjustorThunk (void);
extern void ScreenComposerSettings_get_HardLimitsRect_mAAF8C4A2F753C614FEF2107191DFE3112C665743_AdjustorThunk (void);
extern void ScreenComposerSettings_set_HardLimitsRect_m537078AB83AFB3298BDFEE946951D8628974E42C_AdjustorThunk (void);
extern void SplineSettings_ChangeUnitPreservePosition_m5BA3FA8B14F1E0F650D9C8605BC4E38D8EC62956_AdjustorThunk (void);
extern void SplineSettings_GetCachedSpline_m413EFDEC0D7E7541E17A2D493371FA599D8AFFA1_AdjustorThunk (void);
extern void SplineSettings_InvalidateCache_m99A3ADEEA8ABB0949C26A23C867A91DCC2F3BB88_AdjustorThunk (void);
extern void TimeRange_get_IsEmpty_m856A3BAB8551D7AE7EBECB42CE4FEB20F0EEFF24_AdjustorThunk (void);
extern void TimeRange_Contains_m29397CAB8AB82D9C8C1510F37E5F86648B25E85F_AdjustorThunk (void);
extern void TimeRange_Include_mBFF0A417B53F24B32B5048D99E99EE9FD32BB103_AdjustorThunk (void);
extern void AxisState__ctor_m23329A65D540AACEA3D12AD0E31EB3998A90514C_AdjustorThunk (void);
extern void AxisState_Validate_m3B61D89177847EE19351A98751E603C7039ED1B8_AdjustorThunk (void);
extern void AxisState_Reset_m09D45EA8AF1672C0AFB8C8186C2116970BEAE363_AdjustorThunk (void);
extern void AxisState_SetInputAxisProvider_m9D63DCDEF7DBC2134E1240F0A181F65F212AF3EC_AdjustorThunk (void);
extern void AxisState_get_HasInputProvider_m6E45E8D133FACCFD8447188FD32B43509FB925BC_AdjustorThunk (void);
extern void AxisState_Update_mD7790DB75E0B27E7A684A1191A7D70E29AB707E6_AdjustorThunk (void);
extern void AxisState_ClampValue_mECEF16C692361CF42EEA975781D7001F0AD3BA8D_AdjustorThunk (void);
extern void AxisState_MaxSpeedUpdate_m712A58C8CBB77C9FE3218938A92CD4EA1CE73C6A_AdjustorThunk (void);
extern void AxisState_GetMaxSpeed_m6F8A4B4AE39B234407D6CAA3A4456D2832727097_AdjustorThunk (void);
extern void AxisState_get_ValueRangeLocked_mD7974E42395AB05026ECEC99828FBE7918E86368_AdjustorThunk (void);
extern void AxisState_set_ValueRangeLocked_mC5ED4CAF1070E329AAE7B21894201F727550E3AB_AdjustorThunk (void);
extern void AxisState_get_HasRecentering_m8470DD49BB3D804C165A68A035D70C0BCBF3BAB2_AdjustorThunk (void);
extern void AxisState_set_HasRecentering_mA36545B79D4DC3862C5C33EC8724DCBF4FA23404_AdjustorThunk (void);
extern void Recentering__ctor_m3B930E9904BC3388AA82FBEFC51E14011941CA5E_AdjustorThunk (void);
extern void Recentering_Validate_mD19D0E03095F0E10845ACE1522A0DF6E491AB13C_AdjustorThunk (void);
extern void Recentering_CopyStateFrom_mD5862CCAAD12BE76C99F664A3B8E7DD5046D75B0_AdjustorThunk (void);
extern void Recentering_CancelRecentering_m1AF21DE017EAC0419747C0CAF3C366DFCC478075_AdjustorThunk (void);
extern void Recentering_RecenterNow_m79D915974F8ED61A61E2A876EAD98CCC3AD0B41F_AdjustorThunk (void);
extern void Recentering_DoRecentering_m3A4E02DD1E33E97FD311BC711C2C00E3AC541836_AdjustorThunk (void);
extern void Recentering_LegacyUpgrade_m3ED8B90A073DCC80ECDEB056019F73656C89C539_AdjustorThunk (void);
extern void FovCache_UpdateCache_mD5487E1D88B365D823D207D4C18DBDC120481CE4_AdjustorThunk (void);
extern void FovCache_ScreenToFOV_m7FC58E4F1B8DC1FECB4AF316428058A48ED7AFE8_AdjustorThunk (void);
extern void Orbit__ctor_m8022DA2F7290722B44C38A6A7F4782B39A375CAE_AdjustorThunk (void);
extern void AxisBase_Validate_m346100A07229E141A781DFA36B2F8B7A99AEAFB5_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Validate_m84655856991F2EDC28367081279BF16A80009CAC_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Update_m481B879CF4FBD76457792D1533DF505D014DE4B1_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Update_mB5463E7B9F6C10C51903F3BF4D8A27263E259D2C_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_ClampValue_mABC231D2F9842EABC91604D26F296C5AE30F3F44_AdjustorThunk (void);
extern void Heading__ctor_m5CBDDB066E6DF303F92BC1264BF5A34C96F16382_AdjustorThunk (void);
extern void Waypoint_get_AsVector4_m358E9E0B3C5BF20F20ACB52873D9E9446D961E36_AdjustorThunk (void);
extern void AutoDolly__ctor_m3B7C437D0F1F270E1FCE4710910A590FFD243CC0_AdjustorThunk (void);
extern void LegacyLensSettings_ToLensSettings_mB72D0B6673F19BC5697AB92AF1AAC5B9B7CB2CF6_AdjustorThunk (void);
extern void LegacyLensSettings_SetFromLensSettings_m1EBBC2E3012094938CFE36A470A1B5709669DCAB_AdjustorThunk (void);
extern void LegacyLensSettings_Validate_m7D021CE1F7DDDAB75E8DB632DE6C361F4F02D183_AdjustorThunk (void);
extern void ActionSettings__ctor_m634C3CD9EC38605901BA50D58E89EB27BC5F1A56_AdjustorThunk (void);
extern void ActionSettings_Invoke_m0909B351B47486770E46521676789D6DCD11E6FE_AdjustorThunk (void);
extern void ImpulseReaction_ReSeed_m80E36F4DD6AA9D7387796A4D20B73F1D530D0CD7_AdjustorThunk (void);
extern void ImpulseReaction_GetReaction_m3DEF1931AAC8E9F2EB1BDF6A93DDEA0CE2E074C8_AdjustorThunk (void);
extern void EnvelopeDefinition_get_Duration_m99ECD0F243185637F28F813DF6C7E4D8B8E22DA8_AdjustorThunk (void);
extern void EnvelopeDefinition_GetValueAt_m321C9E091D2281616FB23F0217C448A1E748F8C5_AdjustorThunk (void);
extern void EnvelopeDefinition_ChangeStopTime_mCD399ABC589C3A7085DF613BEF7D658D3F5F4537_AdjustorThunk (void);
extern void EnvelopeDefinition_Clear_m9D7A2C7235329CDC066F74184252D5042A884307_AdjustorThunk (void);
extern void EnvelopeDefinition_Validate_m3D386A841D7D70AF6BA8418C923105610ED5E243_AdjustorThunk (void);
extern void Point64__ctor_m5EB70A4258455C7BF2A8F9988906B13BEE9CBD33_AdjustorThunk (void);
extern void Point64__ctor_m9A2A8514FA57059A1E35A472B12C5FAADB791175_AdjustorThunk (void);
extern void Point64__ctor_m42E3840AD7E7CB739E24F994EC2A80AD9C82B6B5_AdjustorThunk (void);
extern void Point64__ctor_m9E3602EF25ACD25C87C1E8F8A1DCEB6195583EF0_AdjustorThunk (void);
extern void Point64__ctor_mB75675457F560A9A56BD0E83BF82BE4D0619D797_AdjustorThunk (void);
extern void Point64__ctor_mA6D9ADE4B7E4E625A510BB2A38666035C2ADB439_AdjustorThunk (void);
extern void Point64_ToString_mD638C7638543E53040AF9B8F5EBDBD1297CFA5C7_AdjustorThunk (void);
extern void Point64_Equals_m9867809E23F9C6E38A9C016EA31DCCD959A9FA16_AdjustorThunk (void);
extern void Point64_GetHashCode_m78A3E7E42A2DE0050262E6CA7AC488752DCBB557_AdjustorThunk (void);
extern void PointD__ctor_mD7F71F13290EE30EC27140C79FAD97DBBB6AB693_AdjustorThunk (void);
extern void PointD__ctor_m76B965465D81252C8CA9EA4AA9DB761DAABCACF6_AdjustorThunk (void);
extern void PointD__ctor_m9F826315168AF7531D2B9B1B6BAC781F3B232845_AdjustorThunk (void);
extern void PointD__ctor_m768C67DB1D245EEACC1C035DB6CCB6307E718D61_AdjustorThunk (void);
extern void PointD__ctor_mC60B9B78B15396C7BF505A9E4CAFA7A28B2D97E2_AdjustorThunk (void);
extern void PointD__ctor_mD6A84584FCB6FA3FCE7C369420AC192B55B0764A_AdjustorThunk (void);
extern void PointD_ToString_mD6DD8937CFA667AB2E1993A6BA9B691CF01B4816_AdjustorThunk (void);
extern void PointD_Equals_mEE31449D9D892E12AD7DFBC4338083BCF2EA2C95_AdjustorThunk (void);
extern void PointD_GetHashCode_mC3F108E35D20A3BC90FCB8D63D3960E4357016B8_AdjustorThunk (void);
extern void Rect64__ctor_m45E1192E0667467D907B8EAC1DF491F87BE35AE6_AdjustorThunk (void);
extern void Rect64__ctor_m5CC7EB6188A744E984EF75A80C475F69F8CC36C7_AdjustorThunk (void);
extern void Rect64_get_Width_m14E5462BBF19831F4A1ECCC4AC774F742D6B9D79_AdjustorThunk (void);
extern void Rect64_set_Width_m78CE851721CBFD86A6784DBD8E3CECF49AEF7CB2_AdjustorThunk (void);
extern void Rect64_get_Height_mFA6F40B071095EFA741B587D48DFA2DF6B213A91_AdjustorThunk (void);
extern void Rect64_set_Height_mDF2C950030E9DAA14F3982D4054C7E32B016A612_AdjustorThunk (void);
extern void Rect64_IsEmpty_mA1909BCE253440E19D6FCF27BCB713B55CD0F1CA_AdjustorThunk (void);
extern void Rect64_MidPoint_mBAD1B4C2A407D545A79D57DAF96A11643A116291_AdjustorThunk (void);
extern void Rect64_Contains_m491E4B0656953BDFB487F127EB689BEA614DFB2F_AdjustorThunk (void);
extern void Rect64_Contains_mEA0DF8A8952D68B91A659BAE6E1CA80F095C0C61_AdjustorThunk (void);
extern void RectD__ctor_m16EAF9EACEA6C15525DC8DFFC4AC6F56D2232371_AdjustorThunk (void);
extern void RectD__ctor_m97BDBE86BF2072754834BF40952F96D8F58EF569_AdjustorThunk (void);
extern void RectD_get_Width_m36E3639CE7CAE7DBBE017C681ECC288D38D18F60_AdjustorThunk (void);
extern void RectD_set_Width_m50EC47998EADEB1E8E754DF9A5DAF7490859EAE9_AdjustorThunk (void);
extern void RectD_get_Height_m0954D6B12C7EA5D3100435F39F1DC9DA97A6AB72_AdjustorThunk (void);
extern void RectD_set_Height_m57A967EDB839AF8E2AD356E31EB33C5D1D86B8D1_AdjustorThunk (void);
extern void RectD_IsEmpty_mD5E0765FFEF030C9A4F800CFA082A1C7D08276A2_AdjustorThunk (void);
extern void RectD_MidPoint_m86B304B65390BF7D14A0DAA466E000DF14B7FAAC_AdjustorThunk (void);
extern void RectD_PtIsInside_m6ED1F239CFF91D99F828E9503ED112F802F4DB6E_AdjustorThunk (void);
extern void LocalMinima__ctor_m5F6CBC2F2A77AF724F270687C77B9CB6C6DC72FC_AdjustorThunk (void);
extern void LocalMinima_Equals_m1277D897943A6BFD7E7D863054002C607077D985_AdjustorThunk (void);
extern void LocalMinima_GetHashCode_mCD15DB133E348BE5D7EF4CB5F967A13734BE3981_AdjustorThunk (void);
extern void IntersectNode__ctor_mF58FC6DC9D4776F4A5823130E7186E55DBA70269_AdjustorThunk (void);
extern void LocMinSorter_Compare_mC60E39AE99F875F5459C66078950CF1AA50D67B7_AdjustorThunk (void);
extern void IntersectListSort_Compare_m8B6F16B72168215A62DCC94F47817F6C4A06AFD7_AdjustorThunk (void);
extern void TrackerSettings_Validate_m39F755B16D1D9850129598FFDD642263727B5FBE_AdjustorThunk (void);
extern void Tracker_get_PreviousTargetPosition_mF5F405294137CDA1A119EF5B756882AB84E62520_AdjustorThunk (void);
extern void Tracker_set_PreviousTargetPosition_m4BDFFF69C93C6535B2EA145D84A3B965C9653CD4_AdjustorThunk (void);
extern void Tracker_get_PreviousReferenceOrientation_m225BDC07837F9BA115290A0AEC57F14AE3A1C8D9_AdjustorThunk (void);
extern void Tracker_set_PreviousReferenceOrientation_m1FAF567E391E866E6737253EBDEADDBAAE231905_AdjustorThunk (void);
extern void Tracker_InitStateInfo_mF66FEF8AD511965D882D267C455527461F1E7A7A_AdjustorThunk (void);
extern void Tracker_GetReferenceOrientation_m787693044C37713DC332241FB2108373C12FD40F_AdjustorThunk (void);
extern void Tracker_TrackTarget_m8CAD3274FAD87F564E9D8D749229041EEEA07D9A_AdjustorThunk (void);
extern void Tracker_GetOffsetForMinimumTargetDistance_m10279516242F65DF2226B6A5C02AEF36B7431419_AdjustorThunk (void);
extern void Tracker_OnTargetObjectWarped_m7E42DEF855CEAEE2A19BD5A670A1798715EB66D3_AdjustorThunk (void);
extern void Tracker_OnForceCameraPosition_m952EC37B25CD2FA1E1306FB1AD6A41363FA830C8_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[172] = 
{
	{ 0x0600006C, ShapeCache_Invalidate_m7EAE6FF99DB3AD5D61BEB9156C14E6BD0DE1AF3E_AdjustorThunk },
	{ 0x0600006D, ShapeCache_ValidateCache_m9B20BC43138C2439DB9F4499BC3E9338504B640B_AdjustorThunk },
	{ 0x0600006E, ShapeCache_IsValid_m29BFC1989D49FB1151484B62E53A003D95A9AB81_AdjustorThunk },
	{ 0x0600006F, ShapeCache_CalculateDeltaTransformationMatrix_m8E975270968FEC0D915DA7EBDB6459FF46A4EE6F_AdjustorThunk },
	{ 0x0600011A, Instruction_Validate_m8178D703AE92163B2335C8EE46B8AB28EC01BF1F_AdjustorThunk },
	{ 0x0600013C, LerpRollData_Interpolate_mCB9BC66ADFB6B3E320ED43AB11A121DAE116134E_AdjustorThunk },
	{ 0x0600013D, LerpRollDataWithEasing_Interpolate_m97056DDAB2AC587935B5F6010023E96592917FDC_AdjustorThunk },
	{ 0x0600013E, RollCache_Refresh_m1570582AE45E77CE46A95CDEAECD004C568B072C_AdjustorThunk },
	{ 0x0600013F, RollCache_GetSplineRoll_mF4DEFB86AD16D1E13A2A620FB4EC35512625905D_AdjustorThunk },
	{ 0x060001CF, OrbitSplineCache_SettingsChanged_mE93B6AD4D70694FC04BCCE02118447F278CEB151_AdjustorThunk },
	{ 0x060001D0, OrbitSplineCache_UpdateOrbitCache_mEE095885509E49893CA6EAAEF08383EE05F9BEBD_AdjustorThunk },
	{ 0x060001D1, OrbitSplineCache_SplineValue_m4EF5A34796ED0FF7210F0F7B0365070E7B2CEADD_AdjustorThunk },
	{ 0x06000216, FovCache_UpdateCache_mBE2C2FD57DAD380D862B04A8374C6A3E6C60B99E_AdjustorThunk },
	{ 0x06000217, FovCache_ScreenToAngle_m697CC6600E25E343A92923A25F91567B3202669F_AdjustorThunk },
	{ 0x06000218, FovCache_DirectionFromScreen_mC6E97AA5783FD6A4FEABAAE42E3FA3C14C47144B_AdjustorThunk },
	{ 0x06000231, Item_get_WorldLookAt_m5FC326A00B241D52FC2C5C8152BCB0FE23BB1E36_AdjustorThunk },
	{ 0x06000232, Item_set_WorldLookAt_m8C05FC415D07807B4349FF9C858391BF04BBC24A_AdjustorThunk },
	{ 0x06000233, LerpItem_Interpolate_mC3591384873341D9D393C147FBA9F5EF94FA46BF_AdjustorThunk },
	{ 0x0600027B, CameraState_AddCustomBlendable_m0AA826256B3ED67F345FE5DBE2881830CC4CCCAB_AdjustorThunk },
	{ 0x060002A7, CinemachineBlendDefinition_get_BlendTime_mEAAA78C5DA24961202ED2E0ACA2BA3876F13F0CA_AdjustorThunk },
	{ 0x060002A8, CinemachineBlendDefinition__ctor_m39FCAAA94DC44BAE3882C37D8BE9CC903B3C3D0C_AdjustorThunk },
	{ 0x060002A9, CinemachineBlendDefinition_CreateStandardCurves_m03315670810AA1CBA01C39471C2A53B291771D10_AdjustorThunk },
	{ 0x060002AA, CinemachineBlendDefinition_get_BlendCurve_m7613588597293BB3B062EA98D823D78C19AB9AB6_AdjustorThunk },
	{ 0x0600038E, AspectStretcher_get_Aspect_m9E6B3A0AB70146498E1BAD185698573EC606F687_AdjustorThunk },
	{ 0x0600038F, AspectStretcher__ctor_mFF295333F630563CEBB2E78810A0821F15AF4CD3_AdjustorThunk },
	{ 0x06000390, AspectStretcher_Stretch_m86BC52CD00CE835B7A1E8881877534E761E1A86B_AdjustorThunk },
	{ 0x06000391, AspectStretcher_Unstretch_m95AFEFCD91BAE10A23E6640BEA785C0082A327E0_AdjustorThunk },
	{ 0x06000392, PolygonSolution_StateChanged_mC389F789218AE923B31D6C0AA436436BCDD7CBCF_AdjustorThunk },
	{ 0x06000393, PolygonSolution_get_IsNull_m467E000AEC921A9EB76D814C0611F2BC9FB5AA15_AdjustorThunk },
	{ 0x060003BA, InputAxis_ClampValue_m9B998276127396874FBC7D934D22217A2B1F7983_AdjustorThunk },
	{ 0x060003BB, InputAxis_GetNormalizedValue_m6A3EED70C1988D787B19B725D8071993F7EFD411_AdjustorThunk },
	{ 0x060003BC, InputAxis_GetClampedValue_m8828F8C144F025EF8203C94F9A00FE2C26656013_AdjustorThunk },
	{ 0x060003BD, InputAxis_Validate_mD5883DE4C3F177CB83F4554222480DEBD1B160A2_AdjustorThunk },
	{ 0x060003BE, InputAxis_Reset_mFBC7BAF970104C4927D5F73C62BE55F889071DB9_AdjustorThunk },
	{ 0x060003C0, InputAxis_TrackValueChange_mCB6E35D0B19AEB110BC712F4B1075B13350FFCBF_AdjustorThunk },
	{ 0x060003C1, InputAxis_SetValueAndLastValue_m0587AE4B73578F3144756FDCDA7DAE6DB7233067_AdjustorThunk },
	{ 0x060003C2, InputAxis_UpdateRecentering_mA17553DCBBEBA694E54A12088A30EDE575DF5800_AdjustorThunk },
	{ 0x060003C3, InputAxis_UpdateRecentering_m44844384DFA8B93AB3847B51B23B4F61223FD10F_AdjustorThunk },
	{ 0x060003C4, InputAxis_TriggerRecentering_mDC766F564C495683B8F136D7A6E02C0360F710FA_AdjustorThunk },
	{ 0x060003C5, InputAxis_CancelRecentering_m34D92FBFC0234058E6A7F68F10ABED0116F86558_AdjustorThunk },
	{ 0x060003C7, RecenteringSettings_Validate_m6EEB2F6774899BDC39A2246C4D74AFEDC16D404D_AdjustorThunk },
	{ 0x060003C9, DefaultInputAxisDriver_Validate_m29DC58F7A6E8B2BD7C54C3FFDA88D602E7E59135_AdjustorThunk },
	{ 0x060003CB, DefaultInputAxisDriver_ProcessInput_m942B6A2B5C55C8936D74BF2D62E872A1DC735A03_AdjustorThunk },
	{ 0x060003CC, DefaultInputAxisDriver_Reset_m258D1F1BBAF2400C7449AEFD16C50BD5C536859D_AdjustorThunk },
	{ 0x060003E7, LensSettings_get_Orthographic_m2AF510A849B57A9485FB4000835234532D83A2E9_AdjustorThunk },
	{ 0x060003E8, LensSettings_get_IsPhysicalCamera_m2700AD42D56E90B57D18438C765F929E09CC6D10_AdjustorThunk },
	{ 0x060003E9, LensSettings_get_Aspect_m24F38278056BAB1241945C9E75983B9A9FBDA177_AdjustorThunk },
	{ 0x060003EC, LensSettings_PullInheritedPropertiesFromCamera_mAC72C4DE1DA21BF3CE38AA8C318312D3EB8CA3F5_AdjustorThunk },
	{ 0x060003ED, LensSettings_CopyCameraMode_m687969BF551F739E019BE706A6827F5E43D4BB76_AdjustorThunk },
	{ 0x060003EF, LensSettings_Lerp_m17E59426452E33421D279C13D83BE1D9D64B37BD_AdjustorThunk },
	{ 0x060003F0, LensSettings_Validate_mFE36866AB5B16F9B0405E410CACB999F29519265_AdjustorThunk },
	{ 0x060003F6, NoiseParams_GetValueAt_m1B5D915FA8782DAD5230854FA7FFDA503A34D021_AdjustorThunk },
	{ 0x060003F7, TransformNoiseParams_GetValueAt_m757A109B904184C5AAE5BD2B466DC80026474FF6_AdjustorThunk },
	{ 0x060003F8, PositionPredictor_get_IsEmpty_m234E8623FD6865187578B480D0FA79B3E0F8AB05_AdjustorThunk },
	{ 0x060003F9, PositionPredictor_get_CurrentPosition_m0736A2E1D68141B5BEC2BD9078B2624EB0005018_AdjustorThunk },
	{ 0x060003FA, PositionPredictor_ApplyTransformDelta_mC33429167317BB4EE95AFF4EB66E341958463DA4_AdjustorThunk },
	{ 0x060003FB, PositionPredictor_ApplyRotationDelta_m0EDD7BF0D0321D32668DD102887CF8F90CE6CA9D_AdjustorThunk },
	{ 0x060003FC, PositionPredictor_Reset_m1C332992802B45BCD6677A1C04B54F9FE47962D8_AdjustorThunk },
	{ 0x060003FD, PositionPredictor_AddPosition_m521CEDF019A2B4289EDBDF17B7478524B6CBB67F_AdjustorThunk },
	{ 0x060003FE, PositionPredictor_PredictPositionDelta_m58018C09253901A9A4D1A39325684D1EB82CB882_AdjustorThunk },
	{ 0x06000410, PrioritySettings_get_Value_mD2377C4F969CCA1E306F3A188F78DA1C31CB5F3C_AdjustorThunk },
	{ 0x06000411, PrioritySettings_set_Value_m6C9D23382F5F5AE929899110938132552027B440_AdjustorThunk },
	{ 0x0600041C, ScreenComposerSettings_Validate_mC2F21833234FFE27191159ADEE6A2CC6DAC38CCE_AdjustorThunk },
	{ 0x0600041D, ScreenComposerSettings_get_EffectiveDeadZoneSize_m67B0C04C458112121CFE381069227FF96470EA61_AdjustorThunk },
	{ 0x0600041E, ScreenComposerSettings_get_EffectiveHardLimitSize_mC4A9B50A42FD6446505DCC222EEEFCD688B8CE29_AdjustorThunk },
	{ 0x0600041F, ScreenComposerSettings_get_DeadZoneRect_m411B6054F72D2D6CD33562F3970EF01E7A4D25E8_AdjustorThunk },
	{ 0x06000420, ScreenComposerSettings_set_DeadZoneRect_mB8DAA838A8727F3D9F52F92C8E37E0B2EAAB1991_AdjustorThunk },
	{ 0x06000421, ScreenComposerSettings_get_HardLimitsRect_mAAF8C4A2F753C614FEF2107191DFE3112C665743_AdjustorThunk },
	{ 0x06000422, ScreenComposerSettings_set_HardLimitsRect_m537078AB83AFB3298BDFEE946951D8628974E42C_AdjustorThunk },
	{ 0x0600044A, SplineSettings_ChangeUnitPreservePosition_m5BA3FA8B14F1E0F650D9C8605BC4E38D8EC62956_AdjustorThunk },
	{ 0x0600044B, SplineSettings_GetCachedSpline_m413EFDEC0D7E7541E17A2D493371FA599D8AFFA1_AdjustorThunk },
	{ 0x0600044C, SplineSettings_InvalidateCache_m99A3ADEEA8ABB0949C26A23C867A91DCC2F3BB88_AdjustorThunk },
	{ 0x06000471, TimeRange_get_IsEmpty_m856A3BAB8551D7AE7EBECB42CE4FEB20F0EEFF24_AdjustorThunk },
	{ 0x06000472, TimeRange_Contains_m29397CAB8AB82D9C8C1510F37E5F86648B25E85F_AdjustorThunk },
	{ 0x06000474, TimeRange_Include_mBFF0A417B53F24B32B5048D99E99EE9FD32BB103_AdjustorThunk },
	{ 0x060004A5, AxisState__ctor_m23329A65D540AACEA3D12AD0E31EB3998A90514C_AdjustorThunk },
	{ 0x060004A6, AxisState_Validate_m3B61D89177847EE19351A98751E603C7039ED1B8_AdjustorThunk },
	{ 0x060004A7, AxisState_Reset_m09D45EA8AF1672C0AFB8C8186C2116970BEAE363_AdjustorThunk },
	{ 0x060004A8, AxisState_SetInputAxisProvider_m9D63DCDEF7DBC2134E1240F0A181F65F212AF3EC_AdjustorThunk },
	{ 0x060004A9, AxisState_get_HasInputProvider_m6E45E8D133FACCFD8447188FD32B43509FB925BC_AdjustorThunk },
	{ 0x060004AA, AxisState_Update_mD7790DB75E0B27E7A684A1191A7D70E29AB707E6_AdjustorThunk },
	{ 0x060004AB, AxisState_ClampValue_mECEF16C692361CF42EEA975781D7001F0AD3BA8D_AdjustorThunk },
	{ 0x060004AC, AxisState_MaxSpeedUpdate_m712A58C8CBB77C9FE3218938A92CD4EA1CE73C6A_AdjustorThunk },
	{ 0x060004AD, AxisState_GetMaxSpeed_m6F8A4B4AE39B234407D6CAA3A4456D2832727097_AdjustorThunk },
	{ 0x060004AE, AxisState_get_ValueRangeLocked_mD7974E42395AB05026ECEC99828FBE7918E86368_AdjustorThunk },
	{ 0x060004AF, AxisState_set_ValueRangeLocked_mC5ED4CAF1070E329AAE7B21894201F727550E3AB_AdjustorThunk },
	{ 0x060004B0, AxisState_get_HasRecentering_m8470DD49BB3D804C165A68A035D70C0BCBF3BAB2_AdjustorThunk },
	{ 0x060004B1, AxisState_set_HasRecentering_mA36545B79D4DC3862C5C33EC8724DCBF4FA23404_AdjustorThunk },
	{ 0x060004B4, Recentering__ctor_m3B930E9904BC3388AA82FBEFC51E14011941CA5E_AdjustorThunk },
	{ 0x060004B5, Recentering_Validate_mD19D0E03095F0E10845ACE1522A0DF6E491AB13C_AdjustorThunk },
	{ 0x060004B6, Recentering_CopyStateFrom_mD5862CCAAD12BE76C99F664A3B8E7DD5046D75B0_AdjustorThunk },
	{ 0x060004B7, Recentering_CancelRecentering_m1AF21DE017EAC0419747C0CAF3C366DFCC478075_AdjustorThunk },
	{ 0x060004B8, Recentering_RecenterNow_m79D915974F8ED61A61E2A876EAD98CCC3AD0B41F_AdjustorThunk },
	{ 0x060004B9, Recentering_DoRecentering_m3A4E02DD1E33E97FD311BC711C2C00E3AC541836_AdjustorThunk },
	{ 0x060004BA, Recentering_LegacyUpgrade_m3ED8B90A073DCC80ECDEB056019F73656C89C539_AdjustorThunk },
	{ 0x060004FB, FovCache_UpdateCache_mD5487E1D88B365D823D207D4C18DBDC120481CE4_AdjustorThunk },
	{ 0x060004FC, FovCache_ScreenToFOV_m7FC58E4F1B8DC1FECB4AF316428058A48ED7AFE8_AdjustorThunk },
	{ 0x0600055D, Orbit__ctor_m8022DA2F7290722B44C38A6A7F4782B39A375CAE_AdjustorThunk },
	{ 0x06000572, AxisBase_Validate_m346100A07229E141A781DFA36B2F8B7A99AEAFB5_AdjustorThunk },
	{ 0x06000573, CinemachineInputAxisDriver_Validate_m84655856991F2EDC28367081279BF16A80009CAC_AdjustorThunk },
	{ 0x06000574, CinemachineInputAxisDriver_Update_m481B879CF4FBD76457792D1533DF505D014DE4B1_AdjustorThunk },
	{ 0x06000575, CinemachineInputAxisDriver_Update_mB5463E7B9F6C10C51903F3BF4D8A27263E259D2C_AdjustorThunk },
	{ 0x06000576, CinemachineInputAxisDriver_ClampValue_mABC231D2F9842EABC91604D26F296C5AE30F3F44_AdjustorThunk },
	{ 0x0600059B, Heading__ctor_m5CBDDB066E6DF303F92BC1264BF5A34C96F16382_AdjustorThunk },
	{ 0x060005F1, Waypoint_get_AsVector4_m358E9E0B3C5BF20F20ACB52873D9E9446D961E36_AdjustorThunk },
	{ 0x060005FE, AutoDolly__ctor_m3B7C437D0F1F270E1FCE4710910A590FFD243CC0_AdjustorThunk },
	{ 0x0600063A, LegacyLensSettings_ToLensSettings_mB72D0B6673F19BC5697AB92AF1AAC5B9B7CB2CF6_AdjustorThunk },
	{ 0x0600063B, LegacyLensSettings_SetFromLensSettings_m1EBBC2E3012094938CFE36A470A1B5709669DCAB_AdjustorThunk },
	{ 0x0600063C, LegacyLensSettings_Validate_m7D021CE1F7DDDAB75E8DB632DE6C361F4F02D183_AdjustorThunk },
	{ 0x06000676, ActionSettings__ctor_m634C3CD9EC38605901BA50D58E89EB27BC5F1A56_AdjustorThunk },
	{ 0x06000677, ActionSettings_Invoke_m0909B351B47486770E46521676789D6DCD11E6FE_AdjustorThunk },
	{ 0x060006A0, ImpulseReaction_ReSeed_m80E36F4DD6AA9D7387796A4D20B73F1D530D0CD7_AdjustorThunk },
	{ 0x060006A1, ImpulseReaction_GetReaction_m3DEF1931AAC8E9F2EB1BDF6A93DDEA0CE2E074C8_AdjustorThunk },
	{ 0x060006AE, EnvelopeDefinition_get_Duration_m99ECD0F243185637F28F813DF6C7E4D8B8E22DA8_AdjustorThunk },
	{ 0x060006AF, EnvelopeDefinition_GetValueAt_m321C9E091D2281616FB23F0217C448A1E748F8C5_AdjustorThunk },
	{ 0x060006B0, EnvelopeDefinition_ChangeStopTime_mCD399ABC589C3A7085DF613BEF7D658D3F5F4537_AdjustorThunk },
	{ 0x060006B1, EnvelopeDefinition_Clear_m9D7A2C7235329CDC066F74184252D5042A884307_AdjustorThunk },
	{ 0x060006B2, EnvelopeDefinition_Validate_m3D386A841D7D70AF6BA8418C923105610ED5E243_AdjustorThunk },
	{ 0x060006E6, Point64__ctor_m5EB70A4258455C7BF2A8F9988906B13BEE9CBD33_AdjustorThunk },
	{ 0x060006E7, Point64__ctor_m9A2A8514FA57059A1E35A472B12C5FAADB791175_AdjustorThunk },
	{ 0x060006E8, Point64__ctor_m42E3840AD7E7CB739E24F994EC2A80AD9C82B6B5_AdjustorThunk },
	{ 0x060006E9, Point64__ctor_m9E3602EF25ACD25C87C1E8F8A1DCEB6195583EF0_AdjustorThunk },
	{ 0x060006EA, Point64__ctor_mB75675457F560A9A56BD0E83BF82BE4D0619D797_AdjustorThunk },
	{ 0x060006EB, Point64__ctor_mA6D9ADE4B7E4E625A510BB2A38666035C2ADB439_AdjustorThunk },
	{ 0x060006F0, Point64_ToString_mD638C7638543E53040AF9B8F5EBDBD1297CFA5C7_AdjustorThunk },
	{ 0x060006F1, Point64_Equals_m9867809E23F9C6E38A9C016EA31DCCD959A9FA16_AdjustorThunk },
	{ 0x060006F2, Point64_GetHashCode_m78A3E7E42A2DE0050262E6CA7AC488752DCBB557_AdjustorThunk },
	{ 0x060006F3, PointD__ctor_mD7F71F13290EE30EC27140C79FAD97DBBB6AB693_AdjustorThunk },
	{ 0x060006F4, PointD__ctor_m76B965465D81252C8CA9EA4AA9DB761DAABCACF6_AdjustorThunk },
	{ 0x060006F5, PointD__ctor_m9F826315168AF7531D2B9B1B6BAC781F3B232845_AdjustorThunk },
	{ 0x060006F6, PointD__ctor_m768C67DB1D245EEACC1C035DB6CCB6307E718D61_AdjustorThunk },
	{ 0x060006F7, PointD__ctor_mC60B9B78B15396C7BF505A9E4CAFA7A28B2D97E2_AdjustorThunk },
	{ 0x060006F8, PointD__ctor_mD6A84584FCB6FA3FCE7C369420AC192B55B0764A_AdjustorThunk },
	{ 0x060006F9, PointD_ToString_mD6DD8937CFA667AB2E1993A6BA9B691CF01B4816_AdjustorThunk },
	{ 0x060006FD, PointD_Equals_mEE31449D9D892E12AD7DFBC4338083BCF2EA2C95_AdjustorThunk },
	{ 0x060006FE, PointD_GetHashCode_mC3F108E35D20A3BC90FCB8D63D3960E4357016B8_AdjustorThunk },
	{ 0x060006FF, Rect64__ctor_m45E1192E0667467D907B8EAC1DF491F87BE35AE6_AdjustorThunk },
	{ 0x06000700, Rect64__ctor_m5CC7EB6188A744E984EF75A80C475F69F8CC36C7_AdjustorThunk },
	{ 0x06000701, Rect64_get_Width_m14E5462BBF19831F4A1ECCC4AC774F742D6B9D79_AdjustorThunk },
	{ 0x06000702, Rect64_set_Width_m78CE851721CBFD86A6784DBD8E3CECF49AEF7CB2_AdjustorThunk },
	{ 0x06000703, Rect64_get_Height_mFA6F40B071095EFA741B587D48DFA2DF6B213A91_AdjustorThunk },
	{ 0x06000704, Rect64_set_Height_mDF2C950030E9DAA14F3982D4054C7E32B016A612_AdjustorThunk },
	{ 0x06000705, Rect64_IsEmpty_mA1909BCE253440E19D6FCF27BCB713B55CD0F1CA_AdjustorThunk },
	{ 0x06000706, Rect64_MidPoint_mBAD1B4C2A407D545A79D57DAF96A11643A116291_AdjustorThunk },
	{ 0x06000707, Rect64_Contains_m491E4B0656953BDFB487F127EB689BEA614DFB2F_AdjustorThunk },
	{ 0x06000708, Rect64_Contains_mEA0DF8A8952D68B91A659BAE6E1CA80F095C0C61_AdjustorThunk },
	{ 0x06000709, RectD__ctor_m16EAF9EACEA6C15525DC8DFFC4AC6F56D2232371_AdjustorThunk },
	{ 0x0600070A, RectD__ctor_m97BDBE86BF2072754834BF40952F96D8F58EF569_AdjustorThunk },
	{ 0x0600070B, RectD_get_Width_m36E3639CE7CAE7DBBE017C681ECC288D38D18F60_AdjustorThunk },
	{ 0x0600070C, RectD_set_Width_m50EC47998EADEB1E8E754DF9A5DAF7490859EAE9_AdjustorThunk },
	{ 0x0600070D, RectD_get_Height_m0954D6B12C7EA5D3100435F39F1DC9DA97A6AB72_AdjustorThunk },
	{ 0x0600070E, RectD_set_Height_m57A967EDB839AF8E2AD356E31EB33C5D1D86B8D1_AdjustorThunk },
	{ 0x0600070F, RectD_IsEmpty_mD5E0765FFEF030C9A4F800CFA082A1C7D08276A2_AdjustorThunk },
	{ 0x06000710, RectD_MidPoint_m86B304B65390BF7D14A0DAA466E000DF14B7FAAC_AdjustorThunk },
	{ 0x06000711, RectD_PtIsInside_m6ED1F239CFF91D99F828E9503ED112F802F4DB6E_AdjustorThunk },
	{ 0x06000763, LocalMinima__ctor_m5F6CBC2F2A77AF724F270687C77B9CB6C6DC72FC_AdjustorThunk },
	{ 0x06000766, LocalMinima_Equals_m1277D897943A6BFD7E7D863054002C607077D985_AdjustorThunk },
	{ 0x06000767, LocalMinima_GetHashCode_mCD15DB133E348BE5D7EF4CB5F967A13734BE3981_AdjustorThunk },
	{ 0x06000768, IntersectNode__ctor_mF58FC6DC9D4776F4A5823130E7186E55DBA70269_AdjustorThunk },
	{ 0x06000769, LocMinSorter_Compare_mC60E39AE99F875F5459C66078950CF1AA50D67B7_AdjustorThunk },
	{ 0x060007F1, IntersectListSort_Compare_m8B6F16B72168215A62DCC94F47817F6C4A06AFD7_AdjustorThunk },
	{ 0x06000864, TrackerSettings_Validate_m39F755B16D1D9850129598FFDD642263727B5FBE_AdjustorThunk },
	{ 0x06000868, Tracker_get_PreviousTargetPosition_mF5F405294137CDA1A119EF5B756882AB84E62520_AdjustorThunk },
	{ 0x06000869, Tracker_set_PreviousTargetPosition_m4BDFFF69C93C6535B2EA145D84A3B965C9653CD4_AdjustorThunk },
	{ 0x0600086A, Tracker_get_PreviousReferenceOrientation_m225BDC07837F9BA115290A0AEC57F14AE3A1C8D9_AdjustorThunk },
	{ 0x0600086B, Tracker_set_PreviousReferenceOrientation_m1FAF567E391E866E6737253EBDEADDBAAE231905_AdjustorThunk },
	{ 0x0600086C, Tracker_InitStateInfo_mF66FEF8AD511965D882D267C455527461F1E7A7A_AdjustorThunk },
	{ 0x0600086D, Tracker_GetReferenceOrientation_m787693044C37713DC332241FB2108373C12FD40F_AdjustorThunk },
	{ 0x0600086E, Tracker_TrackTarget_m8CAD3274FAD87F564E9D8D749229041EEEA07D9A_AdjustorThunk },
	{ 0x0600086F, Tracker_GetOffsetForMinimumTargetDistance_m10279516242F65DF2226B6A5C02AEF36B7431419_AdjustorThunk },
	{ 0x06000870, Tracker_OnTargetObjectWarped_m7E42DEF855CEAEE2A19BD5A670A1798715EB66D3_AdjustorThunk },
	{ 0x06000871, Tracker_OnForceCameraPosition_m952EC37B25CD2FA1E1306FB1AD6A41363FA830C8_AdjustorThunk },
};
static const int32_t s_InvokerIndices[2161] = 
{
	10870,
	8468,
	8627,
	8468,
	3649,
	16420,
	10870,
	16448,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	4727,
	8691,
	10870,
	10698,
	260,
	8568,
	10861,
	10698,
	10698,
	10538,
	10537,
	10698,
	4790,
	8860,
	2804,
	16321,
	15705,
	10698,
	8627,
	10698,
	10698,
	10870,
	10537,
	10698,
	8627,
	6166,
	6166,
	4321,
	10870,
	8568,
	10870,
	7629,
	4321,
	10698,
	3054,
	8700,
	8445,
	10870,
	16420,
	8568,
	10870,
	10537,
	10698,
	10870,
	10698,
	10870,
	10870,
	10538,
	10698,
	8627,
	10698,
	8627,
	4685,
	4789,
	10781,
	2226,
	4790,
	3050,
	10870,
	10537,
	7481,
	10870,
	7481,
	10870,
	10870,
	1480,
	10870,
	10870,
	8568,
	2226,
	10870,
	3560,
	15710,
	10870,
	16420,
	10870,
	3360,
	10870,
	10870,
	10781,
	2203,
	10870,
	10870,
	10870,
	10537,
	2817,
	1480,
	1981,
	610,
	14423,
	10870,
	10870,
	10870,
	785,
	1630,
	10870,
	15451,
	6166,
	7634,
	10870,
	10870,
	10537,
	10781,
	2203,
	1480,
	7794,
	1946,
	10870,
	10870,
	10870,
	10870,
	10870,
	10781,
	2225,
	1480,
	3653,
	1334,
	3663,
	616,
	10870,
	16420,
	1939,
	10870,
	16420,
	10870,
	3195,
	6166,
	6166,
	7634,
	10870,
	10870,
	10870,
	10870,
	4638,
	10781,
	2203,
	2225,
	1480,
	1678,
	1974,
	1345,
	119,
	801,
	1335,
	13376,
	3663,
	6022,
	10870,
	16420,
	16425,
	16426,
	4788,
	3606,
	8700,
	8700,
	10870,
	10538,
	10698,
	8627,
	10698,
	8627,
	4790,
	10870,
	10870,
	10870,
	10781,
	1480,
	10870,
	10870,
	10870,
	10870,
	-1,
	10870,
	10537,
	2140,
	1480,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	8627,
	8627,
	10698,
	10537,
	8627,
	1438,
	1438,
	10870,
	-1,
	-1,
	-1,
	-1,
	8627,
	8627,
	1438,
	10870,
	8627,
	8627,
	1438,
	10870,
	8627,
	8627,
	1438,
	1438,
	10870,
	8627,
	8627,
	1438,
	1438,
	10870,
	8627,
	8627,
	1438,
	1438,
	10870,
	8627,
	1438,
	1438,
	10870,
	10870,
	10870,
	10781,
	1480,
	696,
	696,
	350,
	1436,
	3608,
	10870,
	10870,
	10870,
	10870,
	10870,
	10538,
	10698,
	7632,
	4321,
	7634,
	4651,
	2804,
	10537,
	2226,
	4790,
	3560,
	10870,
	10870,
	10870,
	10870,
	2140,
	1480,
	10870,
	10870,
	10870,
	8568,
	2226,
	3560,
	3054,
	10537,
	8700,
	10870,
	10870,
	10870,
	10870,
	1480,
	6022,
	10870,
	16428,
	10515,
	10698,
	8627,
	10781,
	8700,
	10637,
	8568,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	8700,
	10870,
	10698,
	8568,
	10870,
	10870,
	10870,
	10870,
	10870,
	15845,
	16029,
	2313,
	2313,
	8627,
	7489,
	8627,
	10870,
	8568,
	14187,
	3197,
	10870,
	3560,
	3197,
	10870,
	10870,
	1480,
	10870,
	8468,
	10698,
	8627,
	3528,
	8627,
	10870,
	4651,
	15983,
	16420,
	10870,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10870,
	10870,
	10870,
	10698,
	8627,
	10698,
	10537,
	10534,
	8466,
	10533,
	8465,
	10537,
	2217,
	8627,
	7062,
	5487,
	2604,
	10537,
	6112,
	13000,
	10870,
	10870,
	10861,
	10534,
	10533,
	10729,
	10870,
	10870,
	10870,
	1432,
	10870,
	10870,
	10861,
	8783,
	10870,
	10870,
	1480,
	1981,
	3661,
	10870,
	10507,
	8438,
	10537,
	10637,
	3790,
	10870,
	10870,
	10870,
	10870,
	10870,
	10861,
	10537,
	10637,
	10781,
	3790,
	4685,
	4789,
	7564,
	7794,
	10870,
	10537,
	10637,
	10781,
	3790,
	10870,
	10537,
	10637,
	10537,
	10870,
	3790,
	10870,
	10861,
	8783,
	10870,
	10870,
	16316,
	16316,
	16316,
	10537,
	10637,
	10781,
	8627,
	8627,
	8627,
	10537,
	10781,
	10861,
	8783,
	10781,
	8700,
	7792,
	10863,
	1707,
	4789,
	2282,
	2282,
	4685,
	3790,
	8656,
	10729,
	10870,
	10515,
	10515,
	10515,
	7628,
	3587,
	3609,
	1938,
	1938,
	3587,
	1568,
	13377,
	16424,
	5983,
	8445,
	7801,
	10870,
	10870,
	16316,
	16316,
	8627,
	8627,
	8627,
	10781,
	10537,
	10537,
	10637,
	3790,
	3790,
	4789,
	1707,
	8656,
	7564,
	10859,
	10870,
	10515,
	10515,
	15835,
	10771,
	10870,
	10870,
	10771,
	8692,
	10861,
	8783,
	10781,
	8700,
	10537,
	10637,
	10537,
	10861,
	8783,
	4685,
	4789,
	10781,
	1707,
	1906,
	3662,
	3790,
	10870,
	10537,
	10637,
	10781,
	3790,
	10870,
	10870,
	10870,
	10537,
	10637,
	10537,
	10861,
	8783,
	10771,
	1983,
	4685,
	4789,
	10781,
	3790,
	3790,
	288,
	12197,
	10771,
	8692,
	10870,
	1372,
	7784,
	7793,
	10870,
	10515,
	10698,
	8627,
	10781,
	8700,
	10637,
	8568,
	10870,
	10870,
	10870,
	10870,
	10537,
	10637,
	10781,
	3790,
	1903,
	10870,
	10870,
	10537,
	10637,
	3790,
	2675,
	10870,
	10861,
	8783,
	2311,
	10698,
	8627,
	10870,
	10870,
	10781,
	10861,
	8783,
	10781,
	8700,
	10537,
	10637,
	10781,
	3790,
	4685,
	3790,
	2012,
	14389,
	729,
	615,
	10870,
	16429,
	10870,
	10698,
	15710,
	10698,
	8627,
	10537,
	10698,
	6166,
	6166,
	10538,
	10870,
	4790,
	1844,
	10870,
	14793,
	-1,
	-1,
	-1,
	10861,
	260,
	8568,
	10870,
	10870,
	10537,
	10698,
	8627,
	10870,
	1532,
	3783,
	8627,
	10781,
	10870,
	16420,
	3195,
	13997,
	10870,
	10537,
	3537,
	10781,
	8700,
	4651,
	10698,
	10698,
	10538,
	10537,
	10698,
	4790,
	8860,
	8627,
	16292,
	8969,
	13085,
	11848,
	11448,
	11436,
	11299,
	16420,
	15442,
	15950,
	15778,
	15950,
	15778,
	15571,
	14912,
	14180,
	15442,
	16420,
	16321,
	15705,
	15983,
	15983,
	15983,
	15983,
	15983,
	15983,
	12838,
	13702,
	15710,
	15583,
	16420,
	10870,
	10781,
	10698,
	8627,
	10537,
	10537,
	10698,
	6166,
	8627,
	10870,
	4790,
	10538,
	10870,
	-1,
	10781,
	4321,
	10870,
	10698,
	4630,
	3054,
	1304,
	6512,
	8627,
	10698,
	8627,
	10698,
	10698,
	10538,
	8469,
	10537,
	10698,
	4790,
	8860,
	1748,
	11573,
	10870,
	10870,
	10870,
	10870,
	10698,
	10538,
	2804,
	10698,
	10537,
	8468,
	10537,
	10698,
	8627,
	10698,
	10698,
	8627,
	10698,
	8627,
	4790,
	3054,
	-1,
	4685,
	4789,
	2226,
	10870,
	10537,
	10870,
	2226,
	10870,
	8700,
	10870,
	10698,
	10870,
	10870,
	10698,
	10698,
	10698,
	10861,
	10729,
	10698,
	10861,
	10729,
	10538,
	-1,
	3790,
	-1,
	10537,
	-1,
	1707,
	4685,
	4789,
	10781,
	10537,
	10870,
	16379,
	16294,
	16379,
	16379,
	16321,
	15979,
	16321,
	15705,
	16341,
	15983,
	15451,
	15451,
	15710,
	14821,
	16420,
	16420,
	4630,
	7634,
	1836,
	7634,
	4630,
	806,
	275,
	6512,
	4630,
	3533,
	1304,
	7489,
	10870,
	10870,
	10870,
	10698,
	10870,
	10870,
	10870,
	10870,
	8468,
	2140,
	1480,
	1480,
	2203,
	4789,
	2225,
	1707,
	10781,
	-1,
	-1,
	10870,
	10870,
	10870,
	10870,
	8627,
	8627,
	4638,
	4735,
	4735,
	10870,
	10870,
	10870,
	8468,
	10870,
	10870,
	10637,
	8568,
	8568,
	10637,
	8568,
	8568,
	10870,
	10870,
	10537,
	8568,
	10781,
	1940,
	1983,
	1982,
	1940,
	1983,
	1982,
	8627,
	8627,
	10698,
	8627,
	1480,
	2140,
	1707,
	10698,
	10698,
	10537,
	-1,
	10698,
	-1,
	-1,
	-1,
	-1,
	10537,
	8468,
	4790,
	-1,
	8860,
	2226,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	7489,
	7489,
	10870,
	10870,
	10870,
	4685,
	2203,
	4789,
	2225,
	3051,
	10870,
	10537,
	8468,
	10537,
	8468,
	10870,
	10698,
	10698,
	10698,
	10698,
	7481,
	10537,
	10537,
	8468,
	10870,
	1359,
	7493,
	10637,
	8568,
	1359,
	8700,
	15786,
	15758,
	8445,
	7636,
	7633,
	10781,
	8662,
	359,
	10537,
	7779,
	11576,
	13343,
	6185,
	1935,
	2836,
	14130,
	10781,
	4735,
	7784,
	7784,
	5983,
	10537,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	4733,
	7790,
	4733,
	7560,
	4733,
	7781,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10870,
	-1,
	-1,
	4630,
	10515,
	3533,
	5451,
	-1,
	-1,
	-1,
	-1,
	7636,
	10781,
	10781,
	10870,
	10870,
	16316,
	10537,
	8700,
	4732,
	2249,
	10870,
	10870,
	16434,
	10870,
	16379,
	10870,
	16301,
	2025,
	8445,
	-1,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10537,
	10537,
	10781,
	16334,
	15646,
	8627,
	8445,
	13238,
	3790,
	10870,
	13900,
	13416,
	10781,
	2246,
	10870,
	3606,
	3659,
	10537,
	10861,
	8783,
	8656,
	10870,
	4790,
	7792,
	14442,
	13377,
	13377,
	13419,
	13417,
	13377,
	13377,
	16379,
	15991,
	16379,
	15991,
	16420,
	14835,
	16420,
	16420,
	15991,
	16420,
	10637,
	8568,
	15585,
	15771,
	15983,
	15451,
	11559,
	11370,
	16341,
	16420,
	13280,
	16420,
	10870,
	10859,
	10859,
	10738,
	8662,
	10738,
	8662,
	13361,
	13900,
	16376,
	-1,
	-1,
	-1,
	-1,
	10870,
	-1,
	-1,
	-1,
	-1,
	10870,
	10870,
	10537,
	282,
	10870,
	10870,
	10870,
	10537,
	282,
	10870,
	15451,
	11555,
	11367,
	13415,
	14432,
	12652,
	15781,
	11856,
	11856,
	11331,
	11848,
	11848,
	13498,
	13498,
	13498,
	13498,
	-1,
	8568,
	10698,
	10870,
	2189,
	10870,
	2813,
	2813,
	5478,
	10537,
	10637,
	5477,
	3591,
	7632,
	4823,
	10698,
	10781,
	10698,
	16321,
	15979,
	16291,
	16291,
	16291,
	16442,
	16291,
	16420,
	16420,
	15951,
	15779,
	10870,
	10637,
	2254,
	8983,
	2302,
	9255,
	13755,
	16454,
	2248,
	10870,
	10870,
	10537,
	6240,
	16442,
	8700,
	15465,
	15466,
	13380,
	13379,
	14522,
	15943,
	11575,
	14444,
	15943,
	15955,
	15465,
	15466,
	15466,
	13701,
	14445,
	13380,
	13350,
	12673,
	15835,
	12645,
	13410,
	13348,
	14407,
	16420,
	15979,
	15583,
	14703,
	15983,
	10870,
	16420,
	10637,
	8568,
	4250,
	2099,
	10698,
	10637,
	7481,
	8627,
	8627,
	8627,
	8627,
	8627,
	10870,
	16420,
	10870,
	3245,
	16341,
	15983,
	74,
	10870,
	10870,
	4265,
	10537,
	6240,
	7636,
	2869,
	10781,
	10537,
	8468,
	10537,
	8468,
	-1,
	-1,
	2042,
	10870,
	8445,
	10870,
	10870,
	2025,
	2675,
	10870,
	10870,
	10781,
	10861,
	8783,
	10781,
	8700,
	10537,
	10637,
	10781,
	3790,
	4685,
	3790,
	2012,
	14389,
	729,
	615,
	8627,
	10870,
	6166,
	6166,
	7634,
	10870,
	10870,
	10698,
	10781,
	1480,
	3652,
	1345,
	119,
	801,
	1335,
	13376,
	3663,
	6022,
	15442,
	8627,
	10870,
	16420,
	8783,
	3606,
	8700,
	8700,
	10870,
	10537,
	10637,
	10861,
	8783,
	1983,
	4685,
	4789,
	10781,
	3790,
	3790,
	10738,
	8662,
	10738,
	8662,
	177,
	755,
	10771,
	8692,
	8627,
	10870,
	1431,
	1327,
	6166,
	7634,
	10870,
	10870,
	8468,
	10537,
	10781,
	1480,
	10870,
	10870,
	10537,
	7794,
	7789,
	10698,
	8627,
	8627,
	10870,
	10870,
	10870,
	10870,
	10870,
	8700,
	8627,
	10870,
	10870,
	10738,
	8662,
	10738,
	8662,
	10870,
	10537,
	10637,
	10537,
	10861,
	8783,
	4685,
	4789,
	10781,
	1707,
	1906,
	3662,
	10534,
	8466,
	10681,
	8609,
	3790,
	7638,
	3653,
	13001,
	10771,
	8692,
	8627,
	8627,
	10870,
	8568,
	10537,
	10870,
	7481,
	10537,
	16341,
	10870,
	10870,
	10870,
	10870,
	10870,
	10537,
	8468,
	10538,
	10698,
	8627,
	10698,
	8627,
	2804,
	4685,
	4789,
	4790,
	2226,
	10537,
	3610,
	7639,
	10870,
	10870,
	7489,
	10537,
	7472,
	1933,
	10870,
	10781,
	3052,
	7792,
	10870,
	10870,
	3602,
	3602,
	7628,
	2246,
	4735,
	4630,
	1836,
	596,
	7489,
	4630,
	8627,
	1836,
	8627,
	10870,
	10870,
	10534,
	8466,
	10681,
	8609,
	10781,
	3790,
	7638,
	13002,
	8627,
	10870,
	10870,
	10870,
	2868,
	2868,
	3589,
	7632,
	3515,
	10870,
	10870,
	14282,
	10870,
	6166,
	15710,
	10870,
	10870,
	8860,
	10870,
	10870,
	8568,
	10637,
	10870,
	15835,
	8783,
	10870,
	10870,
	10861,
	10870,
	1941,
	609,
	10870,
	10537,
	10870,
	4685,
	4789,
	1707,
	3610,
	3790,
	7794,
	3605,
	8627,
	10870,
	2101,
	4630,
	1933,
	598,
	7634,
	16420,
	10870,
	1933,
	10781,
	10781,
	10537,
	10870,
	10870,
	10637,
	1938,
	7792,
	7792,
	7563,
	1921,
	15781,
	10870,
	-1,
	-1,
	-1,
	7636,
	7792,
	7792,
	7563,
	-1,
	-1,
	-1,
	1337,
	7632,
	7632,
	3603,
	3658,
	3658,
	3575,
	-1,
	10870,
	10537,
	10781,
	7636,
	3603,
	3603,
	10870,
	8568,
	10870,
	10870,
	10870,
	10781,
	10537,
	10637,
	10870,
	10870,
	10537,
	10870,
	3790,
	3790,
	10859,
	15835,
	4789,
	1707,
	8656,
	8627,
	10870,
	10537,
	10637,
	10781,
	3790,
	8627,
	10870,
	10781,
	10781,
	10537,
	10637,
	10870,
	10870,
	10870,
	10870,
	1938,
	7792,
	7792,
	7563,
	15781,
	10870,
	10863,
	16028,
	10870,
	7634,
	10870,
	10537,
	10637,
	10781,
	3790,
	3574,
	10861,
	8627,
	10870,
	1386,
	10844,
	10870,
	10537,
	8468,
	10861,
	10537,
	10637,
	10781,
	3790,
	4685,
	4789,
	7564,
	7794,
	8627,
	10870,
	8568,
	10537,
	10538,
	10698,
	8627,
	10698,
	8627,
	10781,
	4790,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	7489,
	10870,
	10698,
	10698,
	7481,
	-1,
	-1,
	-1,
	10870,
	15983,
	3052,
	4685,
	4789,
	8783,
	2226,
	10537,
	10870,
	4630,
	1836,
	596,
	7489,
	4630,
	8627,
	1836,
	8627,
	16420,
	10870,
	3245,
	10870,
	10659,
	8589,
	10870,
	16332,
	10698,
	10870,
	10870,
	8627,
	10870,
	10870,
	10870,
	8860,
	8815,
	4638,
	4638,
	10870,
	10698,
	10870,
	10870,
	10870,
	10870,
	3786,
	10870,
	10870,
	4630,
	3771,
	1274,
	2015,
	3596,
	3596,
	1332,
	10870,
	14282,
	4630,
	1332,
	274,
	7634,
	-1,
	8627,
	10870,
	8860,
	4638,
	8815,
	4638,
	10870,
	10870,
	10870,
	6166,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	10870,
	10870,
	8568,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	8627,
	8627,
	3593,
	4685,
	8627,
	8627,
	3593,
	4685,
	10870,
	10781,
	7634,
	2246,
	3598,
	10870,
	10870,
	16420,
	15705,
	10698,
	4791,
	3561,
	3561,
	10870,
	4685,
	10781,
	2246,
	4685,
	10781,
	2246,
	10870,
	1480,
	10870,
	10870,
	797,
	10870,
	10870,
	16341,
	16420,
	14442,
	390,
	390,
	10781,
	10698,
	8627,
	10870,
	16427,
	10781,
	7636,
	4732,
	10870,
	10870,
	10537,
	4732,
	7636,
	800,
	10870,
	10870,
	10870,
	10870,
	4791,
	8783,
	8700,
	10870,
	4791,
	8783,
	8700,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	1480,
	10870,
	10870,
	10781,
	8700,
	10537,
	10870,
	10870,
	10870,
	10870,
	10870,
	1480,
	15998,
	15983,
	13299,
	15710,
	16420,
	16420,
	10870,
	16420,
	8627,
	10870,
	10870,
	16420,
	10870,
	8691,
	10870,
	10870,
	8645,
	4560,
	3850,
	8646,
	4707,
	4709,
	14011,
	14011,
	14379,
	14379,
	10698,
	6166,
	10637,
	8646,
	8645,
	4709,
	4707,
	4560,
	3850,
	10698,
	15444,
	14012,
	14012,
	6166,
	10637,
	1426,
	8663,
	10638,
	8569,
	10638,
	8569,
	10537,
	10718,
	6185,
	6202,
	1395,
	8664,
	10571,
	8504,
	10571,
	8504,
	10537,
	10719,
	6186,
	13109,
	13109,
	14131,
	11558,
	12232,
	14207,
	13299,
	13299,
	14312,
	13299,
	14312,
	13299,
	13299,
	13299,
	13299,
	13299,
	12537,
	11783,
	11788,
	11418,
	13298,
	13298,
	15513,
	15513,
	15513,
	15513,
	15451,
	15451,
	15710,
	15710,
	15710,
	15710,
	13295,
	14378,
	14380,
	14309,
	14309,
	14309,
	14309,
	14309,
	14309,
	14309,
	14309,
	15710,
	15710,
	15710,
	15710,
	13295,
	13295,
	13288,
	13288,
	15710,
	15710,
	15710,
	15710,
	15790,
	15791,
	15710,
	15710,
	15710,
	15508,
	13070,
	13287,
	14307,
	14812,
	15710,
	14812,
	15710,
	13110,
	13109,
	12118,
	14309,
	14309,
	12118,
	14309,
	14309,
	14307,
	13291,
	14207,
	16420,
	2235,
	2155,
	13981,
	13981,
	6166,
	10637,
	2236,
	3216,
	4708,
	10870,
	1527,
	10870,
	10537,
	8468,
	10537,
	8468,
	10870,
	15447,
	15451,
	15451,
	15451,
	15451,
	15710,
	15451,
	14130,
	14237,
	15451,
	15451,
	15451,
	14672,
	15583,
	13993,
	14377,
	15983,
	15710,
	7489,
	15451,
	15451,
	7489,
	15710,
	14317,
	13681,
	14812,
	15513,
	13109,
	15710,
	15983,
	6166,
	15983,
	15450,
	10870,
	10870,
	10870,
	8569,
	5983,
	6113,
	10669,
	2155,
	2155,
	8627,
	8627,
	8627,
	2155,
	2155,
	6166,
	6166,
	8627,
	8627,
	2813,
	8627,
	4638,
	8569,
	8627,
	5983,
	2810,
	2815,
	2810,
	2815,
	1306,
	1838,
	4638,
	3534,
	3534,
	8627,
	7489,
	1838,
	8627,
	8569,
	4217,
	8569,
	10870,
	2190,
	7489,
	4638,
	6113,
	10870,
	4638,
	781,
	6166,
	2804,
	8627,
	8569,
	7489,
	15451,
	14011,
	15451,
	13033,
	13033,
	13069,
	12232,
	12203,
	7489,
	6166,
	5983,
	14327,
	15710,
	8445,
	8627,
	8627,
	14282,
	8627,
	2675,
	10870,
	4638,
	14317,
	8627,
	10870,
	13022,
	13109,
	14130,
	7489,
	15983,
	2193,
	8627,
	3499,
	8445,
	772,
	2813,
	2813,
	7577,
	2813,
	2813,
	10739,
	3208,
	10870,
	2155,
	2155,
	8627,
	8627,
	8627,
	765,
	1654,
	765,
	1654,
	8568,
	2155,
	2155,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	765,
	1654,
	765,
	1654,
	10698,
	10698,
	10537,
	8627,
	10537,
	10637,
	-1,
	10870,
	8627,
	10537,
	10870,
	10698,
	10698,
	10698,
	8627,
	8627,
	7489,
	7481,
	10571,
	10571,
	8504,
	10698,
	8627,
	8627,
	7489,
	7481,
	10571,
	10870,
	10571,
	10870,
	8627,
	12570,
	13298,
	12571,
	13298,
	12571,
	10870,
	2160,
	10571,
	8504,
	10537,
	8468,
	10571,
	8504,
	10537,
	8468,
	10537,
	8468,
	1394,
	10870,
	2160,
	2160,
	2160,
	2160,
	7474,
	14381,
	7062,
	1900,
	3571,
	2710,
	3063,
	7543,
	3571,
	1323,
	1519,
	692,
	707,
	8627,
	1517,
	4638,
	4638,
	2189,
	6112,
	4622,
	8639,
	4702,
	2230,
	7636,
	10870,
	4630,
	10698,
	3533,
	7489,
	3569,
	4638,
	10870,
	10537,
	10870,
	1898,
	10870,
	16408,
	10870,
	15836,
	15953,
	15953,
	10861,
	8783,
	10729,
	8656,
	1543,
	1324,
	139,
	283,
	8783,
	2149,
};
static const Il2CppTokenRangePair s_rgctxIndices[11] = 
{
	{ 0x0200002E, { 3, 5 } },
	{ 0x020000B7, { 14, 10 } },
	{ 0x020000CC, { 24, 20 } },
	{ 0x020000CE, { 44, 14 } },
	{ 0x020000CF, { 58, 1 } },
	{ 0x060000BB, { 0, 3 } },
	{ 0x0600031F, { 8, 2 } },
	{ 0x06000320, { 10, 4 } },
	{ 0x06000622, { 59, 1 } },
	{ 0x06000623, { 60, 2 } },
	{ 0x06000624, { 62, 1 } },
};
extern const uint32_t g_rgctx_TU26_t460548773C8D569A7F0F5231987758762EAEF0A5;
extern const uint32_t g_rgctx_Component_TryGetComponent_TisT_t64E57A14030624C678319AC427DB4DC7473B6978_m801C6D0D5ECFEFA9C1A134B1BA051B5FD0931E30;
extern const uint32_t g_rgctx_T_t64E57A14030624C678319AC427DB4DC7473B6978;
extern const uint32_t g_rgctx_ComponentModifier_1_t6279ACD66A7E42590BA5DEFB2113511F1EF86A4A;
extern const uint32_t g_rgctx_T_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837;
extern const uint32_t g_rgctx_T_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837;
extern const uint32_t g_rgctx_CinemachineFreeLookModifier_TryGetVcamComponent_TisT_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837_m6E22366A85A3C5EBE4957E30DBAE1FBAE9B7E045;
extern const uint32_t g_rgctx_TU26_tE55B4CEB64C29AF8A201A56E019E05CDC05DC79E;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t734ADFB3B22AF75D0995816492270FAC608C190F_mF6227A02F4CA6D6BC03A739E50A5DAAC5BEF3D3D;
extern const uint32_t g_rgctx_T_t734ADFB3B22AF75D0995816492270FAC608C190F;
extern const uint32_t g_rgctx_List_1_tD8F10793751AEA9033A8A7429AF16DCBC8E47E7C;
extern const uint32_t g_rgctx_List_1_Clear_m7F884906A4073D8F51E53CBAE6198958E7217867;
extern const uint32_t g_rgctx_T_t857F7A374E1F082F958E6A5A3D87A808B33F96CA;
extern const uint32_t g_rgctx_List_1_Add_m2392B6CE007D7F674B24E2C9F35A4E077D6C9305;
extern const uint32_t g_rgctx_GaussianWindow1d_1_tA5DD82D5B93E920C1B658CCAA05C6E95EB49180E;
extern const uint32_t g_rgctx_GaussianWindow1d_1_set_Sigma_mB2BD65D0E303D46945206DFB8C37126DD931C27D;
extern const uint32_t g_rgctx_GaussianWindow1d_1_GenerateKernel_mB54E618B87BF27B0FA6F53939E5B774BE9F8D1E2;
extern const uint32_t g_rgctx_GaussianWindow1d_1_get_KernelSize_m16C0FAF6ADC9BA41C41CABE88EC29E40EBE35496;
extern const uint32_t g_rgctx_TU5BU5D_t8C54891A4B29984B90493DF56EC883C4BBCC27D5;
extern const uint32_t g_rgctx_TU5BU5D_t8C54891A4B29984B90493DF56EC883C4BBCC27D5;
extern const uint32_t g_rgctx_T_t0B9A0A92CD413A1C36CC3E252872CE5E0D2EE88A;
extern const uint32_t g_rgctx_GaussianWindow1d_1_AddValue_m30A141E3507C880BB10010FA88C1513977470FC1;
extern const uint32_t g_rgctx_GaussianWindow1d_1_Value_mB50477592C69BFC648FA0652632D6EF309D2534A;
extern const uint32_t g_rgctx_GaussianWindow1d_1_Compute_mC03D17A5AC2854941F629E8BF2A79B50C74330F1;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_t22B1279063C7A606BE2BDBAF3FDBFEFF6CF0072A;
extern const uint32_t g_rgctx_List_1_t062416C3E12A58DEF4CCBFC3E61CF36ACA74E6BB;
extern const uint32_t g_rgctx_List_1_get_Item_m42BABC5CD7BB6CD5FD685ED0C6B626FBB7485485;
extern const uint32_t g_rgctx_Controller_t9B88B7CD3519FEE4DBF360A6782D4DAD7D59D41B;
extern const uint32_t g_rgctx_List_1_get_Count_m4FA41E894556FD0753EB6B162FD37ED50A089D6D;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_OnResetInput_m034F5A0575C34F0B82EE259BC2A681098B3A2D21;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_OnDisable_m433BD57096C4AE56DC81C854BD1FB0CEABD38C1B;
extern const uint32_t g_rgctx_List_1_Clear_m48E458B0D40643E5978E737939B5E1BCEEBAEAE6;
extern const uint32_t g_rgctx_List_1_RemoveAt_m718FA1EEBAE2A8AD86C6E864BD2F3F0B7922398B;
extern const uint32_t g_rgctx_List_1__ctor_m198F73DD03E448F37CB13F543ABB03883D40A87C;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_U3CCreateControllersU3Eg__GetControllerIndexU7C9_0_m8CF747DF38FC2EA3309B09DBF526168848777F0B;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_t22B1279063C7A606BE2BDBAF3FDBFEFF6CF0072A;
extern const uint32_t g_rgctx_Controller__ctor_mFA573307DBE1CAB7C321A85F38FF92B2B39F52DB;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69_m2AD723050359545CA252C0EDA1F20D96215D68AB;
extern const uint32_t g_rgctx_T_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69;
extern const uint32_t g_rgctx_DefaultInitializer_t3A12CD5AD87373C75E1A8BE078336D0FD2CDA753;
extern const uint32_t g_rgctx_DefaultInitializer_Invoke_m06F66753FC45A49663079BED12570671625F38BD;
extern const uint32_t g_rgctx_List_1_Add_m720948BBAF39D04B4C207317641C8E9D8E0547A4;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_RegisterResetHandlers_mB803D8C1451CE0B8BB617B5536584EE2A62BEB5F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69_IInputAxisReader_GetValue_m21238D3F9C8BFE52F10920694FC5EF7C769F5839;
extern const uint32_t g_rgctx_InputAxisControllerBase_1_tC91CDD4CC5CF8C2ACD2B857098A5955362F34B04;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_tED7593EA97A2578389AAD46858AAE9ABE252F17D;
extern const uint32_t g_rgctx_List_1_tF0F701A1677B6C6FA11821D8329A599F8314F00F;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_Validate_m7D1E6144AA74E0F37F62971C5DE13F999977EDDA;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_Reset_mBBC238F561A91F35E617FC14766343A1310F8B10;
extern const uint32_t g_rgctx_InputAxisControllerBase_1_SynchronizeControllers_mEC0DBB46E5C2FF8DFE681557BD6F3C6369221B69;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_OnDisable_m28BE4561474297C70B3433E940FC962B6B8DA694;
extern const uint32_t g_rgctx_InputAxisControllerBase_1_InitializeControllerDefaultsForAxis_m2A6F03E08A0BAE8F992B34955DF947AE80FC4D87;
extern const uint32_t g_rgctx_DefaultInitializer_t4C35A747D72811037045FA5750D1FD5FAE6F2EEE;
extern const uint32_t g_rgctx_DefaultInitializer__ctor_m1386BE5F42907252280717546B47875CD5910BF8;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_CreateControllers_mC3F2634622810E5E0719B7DDA9A6F7218466A534;
extern const uint32_t g_rgctx_InputAxisControllerBase_1_UpdateControllers_m48BB06C606410D04E640340E6CA7797FA5F3E769;
extern const uint32_t g_rgctx_InputAxisControllerManager_1_UpdateControllers_mE00D30C85E15B4BD9CEF8D9E9587D7C02838E610;
extern const uint32_t g_rgctx_InputAxisControllerManager_1__ctor_m8FC416B14D9CDD1F27589CBD5CFB1AB338EF3C1D;
extern const uint32_t g_rgctx_Controller_t25F86301801156CD5D0EC258745F5EF200069A60;
extern const uint32_t g_rgctx_T_tA5B64852E14E4B55AC3BB45E7055482E4FC41E1D;
extern const uint32_t g_rgctx_T_t65572E6ABF87D32A3F668BCF210C7E3AA7BB1820;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t65572E6ABF87D32A3F668BCF210C7E3AA7BB1820_m108C5888B464C41F8DEF2D355E4D9A05AB95EF57;
extern const uint32_t g_rgctx_T_tCA692D35E3694152B4E29721D62AD3622727A0EE;
static const Il2CppRGCTXDefinition s_rgctxValues[63] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t460548773C8D569A7F0F5231987758762EAEF0A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_TryGetComponent_TisT_t64E57A14030624C678319AC427DB4DC7473B6978_m801C6D0D5ECFEFA9C1A134B1BA051B5FD0931E30 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t64E57A14030624C678319AC427DB4DC7473B6978 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ComponentModifier_1_t6279ACD66A7E42590BA5DEFB2113511F1EF86A4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CinemachineFreeLookModifier_TryGetVcamComponent_TisT_t1B3E17CB3F7CFDFB8FA61654FAEC1F61F1938837_m6E22366A85A3C5EBE4957E30DBAE1FBAE9B7E045 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE55B4CEB64C29AF8A201A56E019E05CDC05DC79E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t734ADFB3B22AF75D0995816492270FAC608C190F_mF6227A02F4CA6D6BC03A739E50A5DAAC5BEF3D3D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t734ADFB3B22AF75D0995816492270FAC608C190F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tD8F10793751AEA9033A8A7429AF16DCBC8E47E7C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m7F884906A4073D8F51E53CBAE6198958E7217867 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t857F7A374E1F082F958E6A5A3D87A808B33F96CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m2392B6CE007D7F674B24E2C9F35A4E077D6C9305 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GaussianWindow1d_1_tA5DD82D5B93E920C1B658CCAA05C6E95EB49180E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_set_Sigma_mB2BD65D0E303D46945206DFB8C37126DD931C27D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_GenerateKernel_mB54E618B87BF27B0FA6F53939E5B774BE9F8D1E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_get_KernelSize_m16C0FAF6ADC9BA41C41CABE88EC29E40EBE35496 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t8C54891A4B29984B90493DF56EC883C4BBCC27D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t8C54891A4B29984B90493DF56EC883C4BBCC27D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0B9A0A92CD413A1C36CC3E252872CE5E0D2EE88A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_AddValue_m30A141E3507C880BB10010FA88C1513977470FC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_Value_mB50477592C69BFC648FA0652632D6EF309D2534A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_Compute_mC03D17A5AC2854941F629E8BF2A79B50C74330F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputAxisControllerManager_1_t22B1279063C7A606BE2BDBAF3FDBFEFF6CF0072A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t062416C3E12A58DEF4CCBFC3E61CF36ACA74E6BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m42BABC5CD7BB6CD5FD685ED0C6B626FBB7485485 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Controller_t9B88B7CD3519FEE4DBF360A6782D4DAD7D59D41B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m4FA41E894556FD0753EB6B162FD37ED50A089D6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_OnResetInput_m034F5A0575C34F0B82EE259BC2A681098B3A2D21 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_OnDisable_m433BD57096C4AE56DC81C854BD1FB0CEABD38C1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m48E458B0D40643E5978E737939B5E1BCEEBAEAE6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_m718FA1EEBAE2A8AD86C6E864BD2F3F0B7922398B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m198F73DD03E448F37CB13F543ABB03883D40A87C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_U3CCreateControllersU3Eg__GetControllerIndexU7C9_0_m8CF747DF38FC2EA3309B09DBF526168848777F0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputAxisControllerManager_1_t22B1279063C7A606BE2BDBAF3FDBFEFF6CF0072A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Controller__ctor_mFA573307DBE1CAB7C321A85F38FF92B2B39F52DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69_m2AD723050359545CA252C0EDA1F20D96215D68AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DefaultInitializer_t3A12CD5AD87373C75E1A8BE078336D0FD2CDA753 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DefaultInitializer_Invoke_m06F66753FC45A49663079BED12570671625F38BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m720948BBAF39D04B4C207317641C8E9D8E0547A4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_RegisterResetHandlers_mB803D8C1451CE0B8BB617B5536584EE2A62BEB5F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t23DCB70DDC351FE43D4E1C3EA51AA8099884FC69_IInputAxisReader_GetValue_m21238D3F9C8BFE52F10920694FC5EF7C769F5839 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputAxisControllerBase_1_tC91CDD4CC5CF8C2ACD2B857098A5955362F34B04 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputAxisControllerManager_1_tED7593EA97A2578389AAD46858AAE9ABE252F17D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tF0F701A1677B6C6FA11821D8329A599F8314F00F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_Validate_m7D1E6144AA74E0F37F62971C5DE13F999977EDDA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_Reset_mBBC238F561A91F35E617FC14766343A1310F8B10 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerBase_1_SynchronizeControllers_mEC0DBB46E5C2FF8DFE681557BD6F3C6369221B69 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_OnDisable_m28BE4561474297C70B3433E940FC962B6B8DA694 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerBase_1_InitializeControllerDefaultsForAxis_m2A6F03E08A0BAE8F992B34955DF947AE80FC4D87 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DefaultInitializer_t4C35A747D72811037045FA5750D1FD5FAE6F2EEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DefaultInitializer__ctor_m1386BE5F42907252280717546B47875CD5910BF8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_CreateControllers_mC3F2634622810E5E0719B7DDA9A6F7218466A534 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerBase_1_UpdateControllers_m48BB06C606410D04E640340E6CA7797FA5F3E769 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1_UpdateControllers_mE00D30C85E15B4BD9CEF8D9E9587D7C02838E610 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputAxisControllerManager_1__ctor_m8FC416B14D9CDD1F27589CBD5CFB1AB338EF3C1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Controller_t25F86301801156CD5D0EC258745F5EF200069A60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA5B64852E14E4B55AC3BB45E7055482E4FC41E1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t65572E6ABF87D32A3F668BCF210C7E3AA7BB1820 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t65572E6ABF87D32A3F668BCF210C7E3AA7BB1820_m108C5888B464C41F8DEF2D355E4D9A05AB95EF57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCA692D35E3694152B4E29721D62AD3622727A0EE },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Cinemachine_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Cinemachine_CodeGenModule = 
{
	"Unity.Cinemachine.dll",
	2161,
	s_methodPointers,
	172,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	11,
	s_rgctxIndices,
	63,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
