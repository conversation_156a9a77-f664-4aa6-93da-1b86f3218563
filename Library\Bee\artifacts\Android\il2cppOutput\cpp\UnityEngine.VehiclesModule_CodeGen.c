﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (void);
extern void WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (void);
extern void WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (void);
extern void WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (void);
extern void WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (void);
extern void WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (void);
extern void WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F (void);
extern void WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184 (void);
extern void WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F (void);
extern void WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348 (void);
extern void WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543 (void);
extern void WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7 (void);
extern void WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB (void);
extern void WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E (void);
extern void WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505 (void);
extern void WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0 (void);
extern void WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510 (void);
extern void WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8 (void);
extern void WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8 (void);
extern void WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F (void);
extern void WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8 (void);
extern void WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF (void);
extern void WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB (void);
extern void WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5 (void);
extern void WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3 (void);
extern void WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327 (void);
extern void WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52 (void);
extern void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (void);
extern void WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2 (void);
extern void WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0 (void);
extern void WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989 (void);
extern void WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F (void);
extern void WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C (void);
extern void WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20 (void);
extern void WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6 (void);
extern void WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC (void);
extern void WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF (void);
extern void WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC (void);
extern void WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A (void);
extern void WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0 (void);
extern void WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73 (void);
extern void WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1 (void);
extern void WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F (void);
extern void WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2 (void);
extern void WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7 (void);
extern void WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2 (void);
extern void WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714 (void);
extern void WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20 (void);
extern void WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4 (void);
extern void WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F (void);
extern void WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE (void);
extern void WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB (void);
static Il2CppMethodPointer s_methodPointers[52] = 
{
	WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D,
	WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C,
	WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162,
	WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E,
	WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE,
	WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B,
	WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F,
	WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184,
	WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F,
	WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348,
	WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543,
	WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7,
	WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB,
	WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E,
	WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505,
	WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0,
	WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510,
	WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8,
	WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8,
	WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F,
	WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8,
	WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF,
	WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB,
	WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5,
	WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3,
	WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327,
	WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52,
	WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890,
	WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2,
	WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0,
	WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989,
	WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F,
	WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C,
	WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20,
	WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6,
	WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC,
	WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF,
	WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC,
	WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A,
	WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0,
	WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73,
	WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1,
	WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F,
	WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2,
	WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7,
	WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2,
	WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714,
	WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20,
	WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4,
	WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F,
	WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE,
	WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB,
};
extern void WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk (void);
extern void WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk (void);
extern void WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk (void);
extern void WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk (void);
extern void WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk (void);
extern void WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[6] = 
{
	{ 0x06000001, WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk },
	{ 0x06000002, WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk },
	{ 0x06000003, WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk },
	{ 0x06000004, WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk },
	{ 0x06000005, WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk },
	{ 0x06000006, WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[52] = 
{
	10698,
	10861,
	10861,
	10781,
	10781,
	10781,
	10781,
	8700,
	10781,
	8700,
	10648,
	8579,
	8700,
	8700,
	8700,
	10872,
	8791,
	10872,
	8791,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	10537,
	10781,
	3771,
	5983,
	15831,
	14715,
	15831,
	14715,
	14708,
	14708,
	14715,
	14715,
	14715,
	14708,
	14708,
	14708,
	14708,
	15831,
	14715,
	15831,
	14715,
	15831,
	14715,
	15449,
	15831,
	13611,
	13969,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule = 
{
	"UnityEngine.VehiclesModule.dll",
	52,
	s_methodPointers,
	6,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
