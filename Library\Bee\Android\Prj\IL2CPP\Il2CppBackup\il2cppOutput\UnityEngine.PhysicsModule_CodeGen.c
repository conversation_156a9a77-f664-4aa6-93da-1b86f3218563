﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208 (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6 (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6 (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B (void);
extern void Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D (void);
extern void Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0 (void);
extern void Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B (void);
extern void Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246 (void);
extern void Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D (void);
extern void Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E (void);
extern void Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1 (void);
extern void Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150 (void);
extern void Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473 (void);
extern void Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912 (void);
extern void Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7 (void);
extern void Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C (void);
extern void Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD (void);
extern void Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292 (void);
extern void JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604 (void);
extern void JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5 (void);
extern void WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274 (void);
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E (void);
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34 (void);
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E (void);
extern void WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755 (void);
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C (void);
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F (void);
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43 (void);
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6 (void);
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5 (void);
extern void BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7 (void);
extern void BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0 (void);
extern void BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E (void);
extern void BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A (void);
extern void BoxCollider_get_center_Injected_m8DB81B1CFC1FB8FB4040BBACAD9EAA7AB5094855 (void);
extern void BoxCollider_set_center_Injected_mF45B8F9D3C124C4AD7BA370715F542B6AA24BF08 (void);
extern void BoxCollider_get_size_Injected_m7DCE0DAAB04E18055D954D944A07B16B840C88B6 (void);
extern void BoxCollider_set_size_Injected_m7F9B55933C41D7733F88A47AD0D728A6ED98DCCE (void);
extern void CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2 (void);
extern void CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5 (void);
extern void CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24 (void);
extern void CapsuleCollider_get_center_Injected_m13CFD076EB23DD354C06A7D555B3C02265E1A39F (void);
extern void CapsuleCollider_get_radius_Injected_mFD3AB35080826BE2C362AAE718514CDA803F4F83 (void);
extern void CapsuleCollider_get_height_Injected_m31ED5345D908D193A2AB0947EBC50E901454FEF4 (void);
extern void Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B (void);
extern void Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70 (void);
extern void Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD (void);
extern void Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87 (void);
extern void Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78 (void);
extern void Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB (void);
extern void Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4 (void);
extern void Collider_get_excludeLayers_m2535780746E624E84F10034B003B6327B1038A34 (void);
extern void Collider_set_excludeLayers_mA1C66C269BE2E8B22D39D20287D93D0EF1051C38 (void);
extern void Collider_get_sharedMaterial_m436FD38D7193330BE4FA630AB7F184BE952FDFFE (void);
extern void Collider_set_sharedMaterial_mA67DC663BEEBE9315F3B91A0B74B31D33579CD7D (void);
extern void Collider_set_material_m2E6C7BEE5FE88DD839FD0BBA83DD501E38B85499 (void);
extern void Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C (void);
extern void Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938 (void);
extern void Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E (void);
extern void Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952 (void);
extern void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (void);
extern void Collider_get_enabled_Injected_mE8FB3B18306335574123BDAC8CD64B85252FC410 (void);
extern void Collider_set_enabled_Injected_m9F39776AD11DCF70D4B251636107544CF56E3CC2 (void);
extern void Collider_get_attachedRigidbody_Injected_mFDBCE8D40EF92923428F133D7D2B38A6793EE317 (void);
extern void Collider_get_isTrigger_Injected_mCA6BD3736CB8FC95F6BD3100CF978D4C4436F8DD (void);
extern void Collider_set_isTrigger_Injected_m55B9E744938E11F684A13DCE07B85691B6F07DD3 (void);
extern void Collider_ClosestPoint_Injected_m95F62FF6C5A90AAF6163B1EA7F48F353E077D3FB (void);
extern void Collider_get_bounds_Injected_m35E8DE744FEA299744B566169FA85275977E266A (void);
extern void Collider_get_excludeLayers_Injected_mFE8762C780D84F94B6D6345CF4AF1B062B42C4C5 (void);
extern void Collider_set_excludeLayers_Injected_mCA60676B46FD5B282FA9AFAB9339EC4C57A351D7 (void);
extern void Collider_get_sharedMaterial_Injected_m4493FBC7D4252D669383D85899186DCAC8173146 (void);
extern void Collider_set_sharedMaterial_Injected_m7634C6A0AAB405A76AB21E2D3ED8B1FE9FA9145F (void);
extern void Collider_set_material_Injected_mE7116C7A74F7B0BFA011D5E2B21DF99E2798597F (void);
extern void Collider_Raycast_Injected_m165289A4749E303FF6D97066FD4831D83BF4C225 (void);
extern void Collider_Internal_ClosestPointOnBounds_Injected_mF341BF5A204F29856315B25FDB152635FDB1C8E5 (void);
extern void ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334 (void);
extern void ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC (void);
extern void ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005 (void);
extern void ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563 (void);
extern void ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF (void);
extern void ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8 (void);
extern void ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258 (void);
extern void ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5 (void);
extern void ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F (void);
extern void ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7 (void);
extern void ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C (void);
extern void ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B (void);
extern void ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A (void);
extern void ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E (void);
extern void ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD (void);
extern void ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A (void);
extern void ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE (void);
extern void ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294 (void);
extern void ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47 (void);
extern void ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08 (void);
extern void ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B (void);
extern void ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70 (void);
extern void ConfigurableJoint_get_angularYZDrive_m0308E706F0FBDB08A0D4C4DF7A8879C7710E4CB8 (void);
extern void ConfigurableJoint_set_angularYZDrive_mA9F165594FD53A2100E17D2E854DD967B91066EB (void);
extern void ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450 (void);
extern void ConfigurableJoint_get_xMotion_Injected_m8243F389A623170940FD5F3A1A57179A7A9FC4E8 (void);
extern void ConfigurableJoint_set_xMotion_Injected_m45A3A10F26AF07F6EAAF4655136AC54796770BFF (void);
extern void ConfigurableJoint_get_yMotion_Injected_m692445DFD076FFFDD097231CC20A74EE357805E7 (void);
extern void ConfigurableJoint_set_yMotion_Injected_mAD20A230DFAACEB63ADCD0CEE2B59C499D671D49 (void);
extern void ConfigurableJoint_get_zMotion_Injected_m65658EA4BD5C36F49D4B5B0BE9ED333573B3BF70 (void);
extern void ConfigurableJoint_set_zMotion_Injected_m3DDAA95E427DA9CE5067B03E7265175DE8978971 (void);
extern void ConfigurableJoint_get_angularXMotion_Injected_m6146F1CD08D98388B050D4DFA2E03D2F72EAACDF (void);
extern void ConfigurableJoint_set_angularXMotion_Injected_mDCDE4A33DA6A50D1A55089CE33F1D9745EBA64B0 (void);
extern void ConfigurableJoint_get_angularYMotion_Injected_m4A5A19075786EBE55525EBF3BE3EE41D989F38C7 (void);
extern void ConfigurableJoint_set_angularYMotion_Injected_m0B9182CD1A0B935FFC57E04B2856C8BE0852BB52 (void);
extern void ConfigurableJoint_get_angularZMotion_Injected_mD174F4EAE3291E4980A695C3342A8EBF26537493 (void);
extern void ConfigurableJoint_set_angularZMotion_Injected_m3F9D838E388EF63DBA4EFDD8D9376D974E3747E5 (void);
extern void ConfigurableJoint_get_linearLimit_Injected_m68D39D82611F16C7C41705D43006C904DF9A56C6 (void);
extern void ConfigurableJoint_set_linearLimit_Injected_mDCCEF13C848A216E91428073EE4071B24E78AAA4 (void);
extern void ConfigurableJoint_get_lowAngularXLimit_Injected_mFA755677AA804B45D818D274E6A87C52F9CD7258 (void);
extern void ConfigurableJoint_set_lowAngularXLimit_Injected_m0259C875B7C44AF543C9EB6772E7CE4345699D2D (void);
extern void ConfigurableJoint_get_highAngularXLimit_Injected_m415B04901E05BBD838BA3D94C29F9213BBFC95B5 (void);
extern void ConfigurableJoint_set_highAngularXLimit_Injected_mA8189D4B4EFF652E4C6AFAA1FD35CEA77C6F7A13 (void);
extern void ConfigurableJoint_get_angularYLimit_Injected_mE1B95F8BBF58AAD4FC23446680134C944390CC9F (void);
extern void ConfigurableJoint_set_angularYLimit_Injected_m3A9B2C3CFCD4F03A67B6E5AAB9FF616829331E0A (void);
extern void ConfigurableJoint_get_angularZLimit_Injected_mF59682561B5BC6C742AB36F01636C15CCF95CDC5 (void);
extern void ConfigurableJoint_set_angularZLimit_Injected_m817EAC8200909166E57F482EA6D1B0E50672B7EF (void);
extern void ConfigurableJoint_get_angularYZDrive_Injected_m08A7D75E3854715A10FF2A20E16429DC1AB38114 (void);
extern void ConfigurableJoint_set_angularYZDrive_Injected_m4DF8D9F76B95C8131AE562FF0AB9D33B5B5A71D6 (void);
extern void ConfigurableJoint_set_configuredInWorldSpace_Injected_mDD41CD5065EAF95009F4355CB8A62E0873D9C007 (void);
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
extern void Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE (void);
extern void Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8 (void);
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
extern void Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925 (void);
extern void Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6 (void);
extern void Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D (void);
extern void Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C (void);
extern void Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05 (void);
extern void Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7 (void);
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
extern void Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522 (void);
extern void Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214 (void);
extern void Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE (void);
extern void Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B (void);
extern void Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318 (void);
extern void Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69 (void);
extern void Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541 (void);
extern void Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88 (void);
extern void Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF (void);
extern void Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83 (void);
extern void Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C (void);
extern void Physics_OverlapCapsuleNonAlloc_mD13F4F0604878062489892A77D92A161681DB167 (void);
extern void Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220 (void);
extern void Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725 (void);
extern void Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144 (void);
extern void Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590 (void);
extern void Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7 (void);
extern void Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A (void);
extern void Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D (void);
extern void Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E (void);
extern void Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A (void);
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
extern void Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754 (void);
extern void Physics_Query_CapsuleCastAll_Injected_m509C782F8F40FFDEC8FB30DF7FB39E8E37FE3BF9 (void);
extern void Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B (void);
extern void Physics_Query_ComputePenetration_Injected_mE00633236C768B6C34999907C898FD3CBA451FA2 (void);
extern void Physics_GetColliderByInstanceID_Injected_m450D0994A6A8609B16027C585D392343F6211B8B (void);
extern void Physics_GetBodyByInstanceID_Injected_mB05F094BD1E39D3BB58ACFB771DA941EAB0B20D8 (void);
extern void Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960 (void);
extern void Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C (void);
extern void Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71 (void);
extern void ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE (void);
extern void ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769 (void);
extern void Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59 (void);
extern void Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4 (void);
extern void Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C (void);
extern void Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800 (void);
extern void Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D (void);
extern void Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C (void);
extern void Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90 (void);
extern void Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02 (void);
extern void Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D (void);
extern void Joint_get_connectedBody_Injected_m47A30AC63A3E4E4ACD454CDFAB75DC6A729AD3D6 (void);
extern void Joint_set_connectedBody_Injected_mD03274116872F87427E56A33AD43576B52AE56EB (void);
extern void Joint_get_axis_Injected_mD5B956924208B58BE5C172CC3AEBF38F1E2D6844 (void);
extern void Joint_set_axis_Injected_m39005E261739B0FD547BBADC9F5075F18D6006F4 (void);
extern void Joint_get_anchor_Injected_m4B8B3B8D3FA953AA6BD12A698B671A6E9ACE779E (void);
extern void Joint_set_anchor_Injected_m582DC57A32C30626D45316DA46F389F7A6C9622D (void);
extern void Joint_set_connectedAnchor_Injected_m6B8E5AB547A3BBC4CC89EF0CC135EB1C7C494386 (void);
extern void Joint_set_autoConfigureConnectedAnchor_Injected_mB2463DB1FCB480A9D4999293F4EBA033294DBE9B (void);
extern void Joint_set_connectedMassScale_Injected_m0F9D9C865AFFCD554D905A293AFF7935573719EE (void);
extern void MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E (void);
extern void MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C (void);
extern void MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1 (void);
extern void MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1 (void);
extern void MeshCollider_get_sharedMesh_Injected_m945CF7271B705EC6A69DDA92F456957A51DE288C (void);
extern void MeshCollider_set_sharedMesh_Injected_m56E1364C6A3A642BAD70564CBDE87C31B489D471 (void);
extern void MeshCollider_get_convex_Injected_mDB779FFF6A5E30B46B8C34B8B34C048FEA371312 (void);
extern void MeshCollider_set_convex_Injected_m37E82AA6100020879406A8A6CE8C5F50394F1556 (void);
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8 (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09 (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27 (void);
extern void ContactPair_get_colliderInstanceID_m3F76A3CD55912A4D758F95F97936AF6AAB0C6E55 (void);
extern void ContactPair_get_otherColliderInstanceID_mF665E8CB3D2B43E61CBCA41EC5F1D4EB487DCCDE (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8 (void);
extern void ContactPair_get_impulseSum_mD0F611BAE18DDC9370E43A00EE56EF6EAF73D94C (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1 (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1 (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8 (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454 (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565 (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10 (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A (void);
extern void PhysicsMaterial__ctor_m43CF5D49C73297C52A4EFC327E7FD29137E75FC7 (void);
extern void PhysicsMaterial_Internal_CreateDynamicsMaterial_m6766ACF352256A5DEA2DF928958006AAA4136389 (void);
extern void PhysicsMaterial_set_dynamicFriction_m59BE25A0D523F1C16E8E5EA3605D5D9C068D6AAE (void);
extern void PhysicsMaterial_set_staticFriction_m67DE5F3AD796C8AB92871E3A0B7338862BB27B9B (void);
extern void PhysicsMaterial_set_frictionCombine_m5095E28E658737F90616372006D6FA093DA875DF (void);
extern void PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected_m81FBE209E2CE0CC055F3D5CE695AD1B951EA2FF0 (void);
extern void PhysicsMaterial_set_dynamicFriction_Injected_mA206F4C8595D78CDD49B7502C085A671138B873E (void);
extern void PhysicsMaterial_set_staticFriction_Injected_mC15C61162DDC0DF3060AF13F0ABDD0E92203F1C6 (void);
extern void PhysicsMaterial_set_frictionCombine_Injected_mD50FF725DAC858C568C694790335C4AA09D4F66F (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_m7A25A75ED0EC93A9B68B87EFEEE16713B5F78B3D (void);
extern void PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545 (void);
extern void PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B (void);
extern void PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4 (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1 (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01 (void);
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934 (void);
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_mF8B5563CB6D620B1269EF5D2D7127F252D2CB358 (void);
extern void PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_Injected_m45784435E2FC65978531CB91B8D3F51773EBC78C (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737 (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11 (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155 (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005 (void);
extern void Rigidbody_get_linearVelocity_m367F5415641E5FB968440A91BAE2FCB8CAEA2C8C (void);
extern void Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985 (void);
extern void Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8 (void);
extern void Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0 (void);
extern void Rigidbody_get_linearDamping_m9A75A439BEDC39A14ADDDBEA9BF3BE08805DA7F2 (void);
extern void Rigidbody_set_linearDamping_m42BB8ADA5D26250A11256502D08BDC2DAB980242 (void);
extern void Rigidbody_get_angularDamping_mEC4083A962F81EA9BD610CC259951DA1DEC9C6DC (void);
extern void Rigidbody_set_angularDamping_m2763171B779080FC724173D87C34015ABED51671 (void);
extern void Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054 (void);
extern void Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B (void);
extern void Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389 (void);
extern void Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1 (void);
extern void Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED (void);
extern void Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3 (void);
extern void Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343 (void);
extern void Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188 (void);
extern void Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF (void);
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
extern void Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1 (void);
extern void Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D (void);
extern void Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C (void);
extern void Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6 (void);
extern void Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9 (void);
extern void Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D (void);
extern void Rigidbody_Sleep_m9826BDFCF078DF00223011B3F0FA7F4894F8F4CA (void);
extern void Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01 (void);
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
extern void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (void);
extern void Rigidbody_AddForce_mFD97FC9DA828C1952D46D28C50B1D994B19895F6 (void);
extern void Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543 (void);
extern void Rigidbody_AddRelativeForce_m16FBD1F3B609D21930E73B6E9474D8917D3918E6 (void);
extern void Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937 (void);
extern void Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233 (void);
extern void Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311 (void);
extern void Rigidbody_AddRelativeTorque_m117DF8F7B92DECCB2C6A57F3C6747E5237FEC89D (void);
extern void Rigidbody_AddRelativeTorque_mF1533E306ACDDBB49564E7761A887897966F4705 (void);
extern void Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C (void);
extern void Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965 (void);
extern void Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402 (void);
extern void Rigidbody_AddExplosionForce_mD8FF6CAA6FF6749259FB95762B2A521CF8483163 (void);
extern void Rigidbody_get_linearVelocity_Injected_m0230F76BD25355A9762C9C5E32C60BF8249F4F6D (void);
extern void Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467 (void);
extern void Rigidbody_get_angularVelocity_Injected_m8F8207351352DBE8B7103C3A703E03A25C3046CB (void);
extern void Rigidbody_set_angularVelocity_Injected_m214B4B9E3C8DCA28990EE9D4BD3402474C32E939 (void);
extern void Rigidbody_get_linearDamping_Injected_mA004E2EC6EA059DABF7E65E2AC5751BE82D6D544 (void);
extern void Rigidbody_set_linearDamping_Injected_mE9170235C451E464E568531097B082444080A306 (void);
extern void Rigidbody_get_angularDamping_Injected_m0AD025510E043C767E42F6C6D51E9E0E30CD4EB3 (void);
extern void Rigidbody_set_angularDamping_Injected_m3859D5E1B39760EB29ECB9A333DA18DAA467DF03 (void);
extern void Rigidbody_get_mass_Injected_m8664DF1CE25F4732818AEAE600C5451ECABB41BB (void);
extern void Rigidbody_set_mass_Injected_m19C3B6F0F2C8AE43E22EAD4A4A28315DB2C63880 (void);
extern void Rigidbody_set_useGravity_Injected_mE1C763DD42795192ABDEF9C9CD758ACA6B91EBBC (void);
extern void Rigidbody_get_isKinematic_Injected_m14C47E2023F4EE3554769F07DD6917B3C62D2212 (void);
extern void Rigidbody_set_isKinematic_Injected_m5613C03722F001F12F373B8AAB0C3CFF5512A78D (void);
extern void Rigidbody_set_constraints_Injected_mA1081EE2C1BEAB06BF087BBAF1981722371B820F (void);
extern void Rigidbody_set_collisionDetectionMode_Injected_m9A3A96B9DFDB40B727373F91B25D4E952D87D33F (void);
extern void Rigidbody_set_centerOfMass_Injected_m903ED712C17D74F6005C12BC6EAFC095B4CC58B0 (void);
extern void Rigidbody_set_detectCollisions_Injected_m4DBCBCAC10A4B747B6FDB966536BB94EA6C131A2 (void);
extern void Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E (void);
extern void Rigidbody_get_rotation_Injected_m185864060C2B33EDDEDE270E78A6C45F1AAF1FC1 (void);
extern void Rigidbody_set_rotation_Injected_m7E13C37597294859ACA960465BA6EC37177D1F5C (void);
extern void Rigidbody_set_interpolation_Injected_mD053C1B95C26E2D682A2281D9C07E3C5E1573D76 (void);
extern void Rigidbody_set_maxAngularVelocity_Injected_mF4082418439687734EE8227223047B8E353CE939 (void);
extern void Rigidbody_MovePosition_Injected_m1FBFE36CBE00546E08E32E304FEB6EEA5EE34E3E (void);
extern void Rigidbody_MoveRotation_Injected_m45E52DDE97339CE0552EBF4169DFBBC87D3F02D5 (void);
extern void Rigidbody_Sleep_Injected_m5950FFE8F57AA31DABC7D4A25A8B44241C75AC2E (void);
extern void Rigidbody_ResetInertiaTensor_Injected_mF07B48F4DA68E0F775B85CBF08B26C330DCC4507 (void);
extern void Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152 (void);
extern void Rigidbody_AddRelativeForce_Injected_mCC0BF054007C25A61656A6D8B8663E8288C31A9F (void);
extern void Rigidbody_AddTorque_Injected_m53C26367D5511FFDCC214CB10A2DEB920CE55F59 (void);
extern void Rigidbody_AddRelativeTorque_Injected_m7571FFF6A7AA8AE5C0C472D30378AC8D7579EE34 (void);
extern void Rigidbody_AddForceAtPosition_Injected_m0A062DE66B0FE396C994FC40EC85D318AF527AA6 (void);
extern void Rigidbody_AddExplosionForce_Injected_mABD06DF246F338199E69A50D5440C5D97C4F86F0 (void);
extern void SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E (void);
extern void SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697 (void);
extern void SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF (void);
extern void SphereCollider_get_center_Injected_m092FEA1F41E394E8C2E25921DFD2A1425F8874E9 (void);
extern void SphereCollider_get_radius_Injected_mF96901A391F7DBD16C5895D85C4853B65CD033A0 (void);
extern void SphereCollider_set_radius_Injected_m1A4885A34095D7CE22CE2001BA6D47022F387747 (void);
static Il2CppMethodPointer s_methodPointers[370] = 
{
	ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208,
	ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6,
	ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6,
	ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B,
	Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D,
	Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0,
	Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B,
	Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246,
	Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D,
	Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E,
	Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1,
	Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150,
	Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473,
	Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912,
	Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7,
	Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C,
	Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD,
	Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292,
	JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604,
	JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5,
	WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274,
	WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E,
	WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34,
	WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E,
	WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755,
	WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C,
	WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F,
	WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43,
	WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6,
	WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5,
	BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7,
	BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0,
	BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E,
	BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A,
	BoxCollider_get_center_Injected_m8DB81B1CFC1FB8FB4040BBACAD9EAA7AB5094855,
	BoxCollider_set_center_Injected_mF45B8F9D3C124C4AD7BA370715F542B6AA24BF08,
	BoxCollider_get_size_Injected_m7DCE0DAAB04E18055D954D944A07B16B840C88B6,
	BoxCollider_set_size_Injected_m7F9B55933C41D7733F88A47AD0D728A6ED98DCCE,
	CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2,
	CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5,
	CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24,
	CapsuleCollider_get_center_Injected_m13CFD076EB23DD354C06A7D555B3C02265E1A39F,
	CapsuleCollider_get_radius_Injected_mFD3AB35080826BE2C362AAE718514CDA803F4F83,
	CapsuleCollider_get_height_Injected_m31ED5345D908D193A2AB0947EBC50E901454FEF4,
	Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B,
	Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70,
	Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD,
	Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87,
	Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78,
	Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB,
	Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4,
	Collider_get_excludeLayers_m2535780746E624E84F10034B003B6327B1038A34,
	Collider_set_excludeLayers_mA1C66C269BE2E8B22D39D20287D93D0EF1051C38,
	Collider_get_sharedMaterial_m436FD38D7193330BE4FA630AB7F184BE952FDFFE,
	Collider_set_sharedMaterial_mA67DC663BEEBE9315F3B91A0B74B31D33579CD7D,
	Collider_set_material_m2E6C7BEE5FE88DD839FD0BBA83DD501E38B85499,
	Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C,
	Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938,
	Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E,
	Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952,
	Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA,
	Collider_get_enabled_Injected_mE8FB3B18306335574123BDAC8CD64B85252FC410,
	Collider_set_enabled_Injected_m9F39776AD11DCF70D4B251636107544CF56E3CC2,
	Collider_get_attachedRigidbody_Injected_mFDBCE8D40EF92923428F133D7D2B38A6793EE317,
	Collider_get_isTrigger_Injected_mCA6BD3736CB8FC95F6BD3100CF978D4C4436F8DD,
	Collider_set_isTrigger_Injected_m55B9E744938E11F684A13DCE07B85691B6F07DD3,
	Collider_ClosestPoint_Injected_m95F62FF6C5A90AAF6163B1EA7F48F353E077D3FB,
	Collider_get_bounds_Injected_m35E8DE744FEA299744B566169FA85275977E266A,
	Collider_get_excludeLayers_Injected_mFE8762C780D84F94B6D6345CF4AF1B062B42C4C5,
	Collider_set_excludeLayers_Injected_mCA60676B46FD5B282FA9AFAB9339EC4C57A351D7,
	Collider_get_sharedMaterial_Injected_m4493FBC7D4252D669383D85899186DCAC8173146,
	Collider_set_sharedMaterial_Injected_m7634C6A0AAB405A76AB21E2D3ED8B1FE9FA9145F,
	Collider_set_material_Injected_mE7116C7A74F7B0BFA011D5E2B21DF99E2798597F,
	Collider_Raycast_Injected_m165289A4749E303FF6D97066FD4831D83BF4C225,
	Collider_Internal_ClosestPointOnBounds_Injected_mF341BF5A204F29856315B25FDB152635FDB1C8E5,
	ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334,
	ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC,
	ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005,
	ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563,
	ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF,
	ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8,
	ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258,
	ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5,
	ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F,
	ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7,
	ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C,
	ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B,
	ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A,
	ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E,
	ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD,
	ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A,
	ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE,
	ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294,
	ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47,
	ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08,
	ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B,
	ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70,
	ConfigurableJoint_get_angularYZDrive_m0308E706F0FBDB08A0D4C4DF7A8879C7710E4CB8,
	ConfigurableJoint_set_angularYZDrive_mA9F165594FD53A2100E17D2E854DD967B91066EB,
	ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450,
	ConfigurableJoint_get_xMotion_Injected_m8243F389A623170940FD5F3A1A57179A7A9FC4E8,
	ConfigurableJoint_set_xMotion_Injected_m45A3A10F26AF07F6EAAF4655136AC54796770BFF,
	ConfigurableJoint_get_yMotion_Injected_m692445DFD076FFFDD097231CC20A74EE357805E7,
	ConfigurableJoint_set_yMotion_Injected_mAD20A230DFAACEB63ADCD0CEE2B59C499D671D49,
	ConfigurableJoint_get_zMotion_Injected_m65658EA4BD5C36F49D4B5B0BE9ED333573B3BF70,
	ConfigurableJoint_set_zMotion_Injected_m3DDAA95E427DA9CE5067B03E7265175DE8978971,
	ConfigurableJoint_get_angularXMotion_Injected_m6146F1CD08D98388B050D4DFA2E03D2F72EAACDF,
	ConfigurableJoint_set_angularXMotion_Injected_mDCDE4A33DA6A50D1A55089CE33F1D9745EBA64B0,
	ConfigurableJoint_get_angularYMotion_Injected_m4A5A19075786EBE55525EBF3BE3EE41D989F38C7,
	ConfigurableJoint_set_angularYMotion_Injected_m0B9182CD1A0B935FFC57E04B2856C8BE0852BB52,
	ConfigurableJoint_get_angularZMotion_Injected_mD174F4EAE3291E4980A695C3342A8EBF26537493,
	ConfigurableJoint_set_angularZMotion_Injected_m3F9D838E388EF63DBA4EFDD8D9376D974E3747E5,
	ConfigurableJoint_get_linearLimit_Injected_m68D39D82611F16C7C41705D43006C904DF9A56C6,
	ConfigurableJoint_set_linearLimit_Injected_mDCCEF13C848A216E91428073EE4071B24E78AAA4,
	ConfigurableJoint_get_lowAngularXLimit_Injected_mFA755677AA804B45D818D274E6A87C52F9CD7258,
	ConfigurableJoint_set_lowAngularXLimit_Injected_m0259C875B7C44AF543C9EB6772E7CE4345699D2D,
	ConfigurableJoint_get_highAngularXLimit_Injected_m415B04901E05BBD838BA3D94C29F9213BBFC95B5,
	ConfigurableJoint_set_highAngularXLimit_Injected_mA8189D4B4EFF652E4C6AFAA1FD35CEA77C6F7A13,
	ConfigurableJoint_get_angularYLimit_Injected_mE1B95F8BBF58AAD4FC23446680134C944390CC9F,
	ConfigurableJoint_set_angularYLimit_Injected_m3A9B2C3CFCD4F03A67B6E5AAB9FF616829331E0A,
	ConfigurableJoint_get_angularZLimit_Injected_mF59682561B5BC6C742AB36F01636C15CCF95CDC5,
	ConfigurableJoint_set_angularZLimit_Injected_m817EAC8200909166E57F482EA6D1B0E50672B7EF,
	ConfigurableJoint_get_angularYZDrive_Injected_m08A7D75E3854715A10FF2A20E16429DC1AB38114,
	ConfigurableJoint_set_angularYZDrive_Injected_m4DF8D9F76B95C8131AE562FF0AB9D33B5B5A71D6,
	ConfigurableJoint_set_configuredInWorldSpace_Injected_mDD41CD5065EAF95009F4355CB8A62E0873D9C007,
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE,
	Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925,
	Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6,
	Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D,
	Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C,
	Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05,
	Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522,
	Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214,
	Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE,
	Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B,
	Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318,
	Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69,
	Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541,
	Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88,
	Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF,
	Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83,
	Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C,
	Physics_OverlapCapsuleNonAlloc_mD13F4F0604878062489892A77D92A161681DB167,
	Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220,
	Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725,
	Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144,
	Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590,
	Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7,
	Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A,
	Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D,
	Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E,
	Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754,
	Physics_Query_CapsuleCastAll_Injected_m509C782F8F40FFDEC8FB30DF7FB39E8E37FE3BF9,
	Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B,
	Physics_Query_ComputePenetration_Injected_mE00633236C768B6C34999907C898FD3CBA451FA2,
	Physics_GetColliderByInstanceID_Injected_m450D0994A6A8609B16027C585D392343F6211B8B,
	Physics_GetBodyByInstanceID_Injected_mB05F094BD1E39D3BB58ACFB771DA941EAB0B20D8,
	Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960,
	Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C,
	Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71,
	ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE,
	ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769,
	Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59,
	Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4,
	Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C,
	Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800,
	Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D,
	Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C,
	Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90,
	Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02,
	Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D,
	Joint_get_connectedBody_Injected_m47A30AC63A3E4E4ACD454CDFAB75DC6A729AD3D6,
	Joint_set_connectedBody_Injected_mD03274116872F87427E56A33AD43576B52AE56EB,
	Joint_get_axis_Injected_mD5B956924208B58BE5C172CC3AEBF38F1E2D6844,
	Joint_set_axis_Injected_m39005E261739B0FD547BBADC9F5075F18D6006F4,
	Joint_get_anchor_Injected_m4B8B3B8D3FA953AA6BD12A698B671A6E9ACE779E,
	Joint_set_anchor_Injected_m582DC57A32C30626D45316DA46F389F7A6C9622D,
	Joint_set_connectedAnchor_Injected_m6B8E5AB547A3BBC4CC89EF0CC135EB1C7C494386,
	Joint_set_autoConfigureConnectedAnchor_Injected_mB2463DB1FCB480A9D4999293F4EBA033294DBE9B,
	Joint_set_connectedMassScale_Injected_m0F9D9C865AFFCD554D905A293AFF7935573719EE,
	MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E,
	MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C,
	MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1,
	MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1,
	MeshCollider_get_sharedMesh_Injected_m945CF7271B705EC6A69DDA92F456957A51DE288C,
	MeshCollider_set_sharedMesh_Injected_m56E1364C6A3A642BAD70564CBDE87C31B489D471,
	MeshCollider_get_convex_Injected_mDB779FFF6A5E30B46B8C34B8B34C048FEA371312,
	MeshCollider_set_convex_Injected_m37E82AA6100020879406A8A6CE8C5F50394F1556,
	ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8,
	ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A,
	ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A,
	ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09,
	ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27,
	ContactPair_get_colliderInstanceID_m3F76A3CD55912A4D758F95F97936AF6AAB0C6E55,
	ContactPair_get_otherColliderInstanceID_mF665E8CB3D2B43E61CBCA41EC5F1D4EB487DCCDE,
	ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C,
	ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8,
	ContactPair_get_impulseSum_mD0F611BAE18DDC9370E43A00EE56EF6EAF73D94C,
	ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1,
	ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1,
	ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8,
	ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA,
	ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC,
	ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454,
	ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565,
	ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10,
	ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E,
	ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF,
	ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A,
	PhysicsMaterial__ctor_m43CF5D49C73297C52A4EFC327E7FD29137E75FC7,
	PhysicsMaterial_Internal_CreateDynamicsMaterial_m6766ACF352256A5DEA2DF928958006AAA4136389,
	PhysicsMaterial_set_dynamicFriction_m59BE25A0D523F1C16E8E5EA3605D5D9C068D6AAE,
	PhysicsMaterial_set_staticFriction_m67DE5F3AD796C8AB92871E3A0B7338862BB27B9B,
	PhysicsMaterial_set_frictionCombine_m5095E28E658737F90616372006D6FA093DA875DF,
	PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected_m81FBE209E2CE0CC055F3D5CE695AD1B951EA2FF0,
	PhysicsMaterial_set_dynamicFriction_Injected_mA206F4C8595D78CDD49B7502C085A671138B873E,
	PhysicsMaterial_set_staticFriction_Injected_mC15C61162DDC0DF3060AF13F0ABDD0E92203F1C6,
	PhysicsMaterial_set_frictionCombine_Injected_mD50FF725DAC858C568C694790335C4AA09D4F66F,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_m7A25A75ED0EC93A9B68B87EFEEE16713B5F78B3D,
	PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545,
	PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B,
	PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF,
	PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4,
	PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F,
	PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F,
	PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1,
	PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_mF8B5563CB6D620B1269EF5D2D7127F252D2CB358,
	PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A,
	PhysicsScene_Internal_SphereCastNonAlloc_Injected_m45784435E2FC65978531CB91B8D3F51773EBC78C,
	PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC,
	RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155,
	RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005,
	Rigidbody_get_linearVelocity_m367F5415641E5FB968440A91BAE2FCB8CAEA2C8C,
	Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985,
	Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8,
	Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0,
	Rigidbody_get_linearDamping_m9A75A439BEDC39A14ADDDBEA9BF3BE08805DA7F2,
	Rigidbody_set_linearDamping_m42BB8ADA5D26250A11256502D08BDC2DAB980242,
	Rigidbody_get_angularDamping_mEC4083A962F81EA9BD610CC259951DA1DEC9C6DC,
	Rigidbody_set_angularDamping_m2763171B779080FC724173D87C34015ABED51671,
	Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054,
	Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B,
	Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389,
	Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1,
	Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED,
	Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3,
	Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343,
	Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188,
	Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1,
	Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D,
	Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C,
	Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6,
	Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9,
	Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D,
	Rigidbody_Sleep_m9826BDFCF078DF00223011B3F0FA7F4894F8F4CA,
	Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198,
	Rigidbody_AddForce_mFD97FC9DA828C1952D46D28C50B1D994B19895F6,
	Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543,
	Rigidbody_AddRelativeForce_m16FBD1F3B609D21930E73B6E9474D8917D3918E6,
	Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937,
	Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233,
	Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311,
	Rigidbody_AddRelativeTorque_m117DF8F7B92DECCB2C6A57F3C6747E5237FEC89D,
	Rigidbody_AddRelativeTorque_mF1533E306ACDDBB49564E7761A887897966F4705,
	Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C,
	Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965,
	Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402,
	Rigidbody_AddExplosionForce_mD8FF6CAA6FF6749259FB95762B2A521CF8483163,
	Rigidbody_get_linearVelocity_Injected_m0230F76BD25355A9762C9C5E32C60BF8249F4F6D,
	Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467,
	Rigidbody_get_angularVelocity_Injected_m8F8207351352DBE8B7103C3A703E03A25C3046CB,
	Rigidbody_set_angularVelocity_Injected_m214B4B9E3C8DCA28990EE9D4BD3402474C32E939,
	Rigidbody_get_linearDamping_Injected_mA004E2EC6EA059DABF7E65E2AC5751BE82D6D544,
	Rigidbody_set_linearDamping_Injected_mE9170235C451E464E568531097B082444080A306,
	Rigidbody_get_angularDamping_Injected_m0AD025510E043C767E42F6C6D51E9E0E30CD4EB3,
	Rigidbody_set_angularDamping_Injected_m3859D5E1B39760EB29ECB9A333DA18DAA467DF03,
	Rigidbody_get_mass_Injected_m8664DF1CE25F4732818AEAE600C5451ECABB41BB,
	Rigidbody_set_mass_Injected_m19C3B6F0F2C8AE43E22EAD4A4A28315DB2C63880,
	Rigidbody_set_useGravity_Injected_mE1C763DD42795192ABDEF9C9CD758ACA6B91EBBC,
	Rigidbody_get_isKinematic_Injected_m14C47E2023F4EE3554769F07DD6917B3C62D2212,
	Rigidbody_set_isKinematic_Injected_m5613C03722F001F12F373B8AAB0C3CFF5512A78D,
	Rigidbody_set_constraints_Injected_mA1081EE2C1BEAB06BF087BBAF1981722371B820F,
	Rigidbody_set_collisionDetectionMode_Injected_m9A3A96B9DFDB40B727373F91B25D4E952D87D33F,
	Rigidbody_set_centerOfMass_Injected_m903ED712C17D74F6005C12BC6EAFC095B4CC58B0,
	Rigidbody_set_detectCollisions_Injected_m4DBCBCAC10A4B747B6FDB966536BB94EA6C131A2,
	Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E,
	Rigidbody_get_rotation_Injected_m185864060C2B33EDDEDE270E78A6C45F1AAF1FC1,
	Rigidbody_set_rotation_Injected_m7E13C37597294859ACA960465BA6EC37177D1F5C,
	Rigidbody_set_interpolation_Injected_mD053C1B95C26E2D682A2281D9C07E3C5E1573D76,
	Rigidbody_set_maxAngularVelocity_Injected_mF4082418439687734EE8227223047B8E353CE939,
	Rigidbody_MovePosition_Injected_m1FBFE36CBE00546E08E32E304FEB6EEA5EE34E3E,
	Rigidbody_MoveRotation_Injected_m45E52DDE97339CE0552EBF4169DFBBC87D3F02D5,
	Rigidbody_Sleep_Injected_m5950FFE8F57AA31DABC7D4A25A8B44241C75AC2E,
	Rigidbody_ResetInertiaTensor_Injected_mF07B48F4DA68E0F775B85CBF08B26C330DCC4507,
	Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152,
	Rigidbody_AddRelativeForce_Injected_mCC0BF054007C25A61656A6D8B8663E8288C31A9F,
	Rigidbody_AddTorque_Injected_m53C26367D5511FFDCC214CB10A2DEB920CE55F59,
	Rigidbody_AddRelativeTorque_Injected_m7571FFF6A7AA8AE5C0C472D30378AC8D7579EE34,
	Rigidbody_AddForceAtPosition_Injected_m0A062DE66B0FE396C994FC40EC85D318AF527AA6,
	Rigidbody_AddExplosionForce_Injected_mABD06DF246F338199E69A50D5440C5D97C4F86F0,
	SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E,
	SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697,
	SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF,
	SphereCollider_get_center_Injected_m092FEA1F41E394E8C2E25921DFD2A1425F8874E9,
	SphereCollider_get_radius_Injected_mF96901A391F7DBD16C5895D85C4853B65CD033A0,
	SphereCollider_set_radius_Injected_m1A4885A34095D7CE22CE2001BA6D47022F387747,
};
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk (void);
extern void JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604_AdjustorThunk (void);
extern void JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5_AdjustorThunk (void);
extern void WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk (void);
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk (void);
extern void WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk (void);
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk (void);
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk (void);
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk (void);
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk (void);
extern void ContactPair_get_colliderInstanceID_m3F76A3CD55912A4D758F95F97936AF6AAB0C6E55_AdjustorThunk (void);
extern void ContactPair_get_otherColliderInstanceID_mF665E8CB3D2B43E61CBCA41EC5F1D4EB487DCCDE_AdjustorThunk (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk (void);
extern void ContactPair_get_impulseSum_mD0F611BAE18DDC9370E43A00EE56EF6EAF73D94C_AdjustorThunk (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[57] = 
{
	{ 0x06000001, ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk },
	{ 0x06000002, ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk },
	{ 0x06000003, ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk },
	{ 0x06000004, ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk },
	{ 0x06000013, JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604_AdjustorThunk },
	{ 0x06000014, JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5_AdjustorThunk },
	{ 0x06000015, WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274_AdjustorThunk },
	{ 0x06000016, WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk },
	{ 0x06000017, WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk },
	{ 0x06000018, WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk },
	{ 0x06000019, WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755_AdjustorThunk },
	{ 0x0600001A, WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk },
	{ 0x0600001B, WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk },
	{ 0x0600001C, WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk },
	{ 0x0600001D, WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk },
	{ 0x0600001E, WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk },
	{ 0x060000E4, ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk },
	{ 0x060000E5, ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk },
	{ 0x060000E6, ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk },
	{ 0x060000E7, ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk },
	{ 0x060000E8, ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk },
	{ 0x060000E9, ContactPair_get_colliderInstanceID_m3F76A3CD55912A4D758F95F97936AF6AAB0C6E55_AdjustorThunk },
	{ 0x060000EA, ContactPair_get_otherColliderInstanceID_mF665E8CB3D2B43E61CBCA41EC5F1D4EB487DCCDE_AdjustorThunk },
	{ 0x060000EB, ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk },
	{ 0x060000EC, ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk },
	{ 0x060000ED, ContactPair_get_impulseSum_mD0F611BAE18DDC9370E43A00EE56EF6EAF73D94C_AdjustorThunk },
	{ 0x060000EE, ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk },
	{ 0x060000EF, ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk },
	{ 0x060000F0, ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk },
	{ 0x060000F1, ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk },
	{ 0x060000F2, ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk },
	{ 0x060000F3, ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk },
	{ 0x060000F4, ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk },
	{ 0x060000F5, ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk },
	{ 0x060000F6, ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk },
	{ 0x060000F7, ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk },
	{ 0x060000F8, ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk },
	{ 0x06000102, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x06000103, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x06000104, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x06000105, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x06000106, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x06000108, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x0600010A, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x0600010D, PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545_AdjustorThunk },
	{ 0x06000110, PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk },
	{ 0x06000112, PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk },
	{ 0x06000114, PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk },
	{ 0x0600011C, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x0600011D, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x0600011E, RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk },
	{ 0x0600011F, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x06000120, RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk },
	{ 0x06000121, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
	{ 0x06000122, RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk },
	{ 0x06000123, RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk },
	{ 0x06000124, RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk },
};
static const int32_t s_InvokerIndices[370] = 
{
	10861,
	10861,
	10698,
	369,
	10861,
	10861,
	10698,
	10698,
	10698,
	10698,
	8468,
	10637,
	10698,
	10870,
	2013,
	3771,
	6534,
	7062,
	8700,
	8700,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	10861,
	8783,
	10861,
	8783,
	14708,
	14708,
	14708,
	14708,
	10861,
	10781,
	10781,
	14708,
	15831,
	15831,
	10537,
	8468,
	10698,
	10537,
	8468,
	7794,
	10534,
	10652,
	8583,
	10698,
	8627,
	8627,
	1904,
	1716,
	2273,
	7794,
	10870,
	15449,
	14709,
	15625,
	15449,
	14709,
	13611,
	14708,
	14708,
	14708,
	15625,
	14713,
	14713,
	12026,
	12742,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10783,
	8702,
	10783,
	8702,
	10783,
	8702,
	10783,
	8702,
	10783,
	8702,
	10647,
	8578,
	8468,
	15581,
	14712,
	15581,
	14712,
	15581,
	14712,
	15581,
	14712,
	15581,
	14712,
	15581,
	14712,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14708,
	14709,
	12831,
	12831,
	16291,
	16345,
	11570,
	12244,
	13084,
	14076,
	11374,
	11569,
	12243,
	13082,
	12234,
	13073,
	14019,
	15455,
	11560,
	12233,
	13072,
	14018,
	11568,
	12242,
	11267,
	11373,
	11371,
	11561,
	11835,
	11839,
	12641,
	13336,
	14354,
	12632,
	13324,
	14329,
	15711,
	11766,
	12491,
	13198,
	14209,
	11392,
	11769,
	12494,
	13202,
	11228,
	11296,
	11435,
	11836,
	12640,
	14353,
	16291,
	11200,
	11200,
	11768,
	11273,
	11393,
	15705,
	15705,
	14812,
	14812,
	14812,
	13707,
	15969,
	13246,
	16420,
	15971,
	11464,
	11175,
	11776,
	11198,
	15623,
	15623,
	14714,
	14714,
	14714,
	4630,
	4701,
	10698,
	8627,
	10861,
	8783,
	10861,
	8783,
	8783,
	8468,
	8700,
	15625,
	14713,
	14708,
	14708,
	14708,
	14708,
	14708,
	14709,
	14715,
	10698,
	8627,
	10537,
	8468,
	15625,
	14713,
	15449,
	14709,
	10698,
	10698,
	10537,
	5449,
	5449,
	10637,
	10637,
	10698,
	10698,
	10861,
	10537,
	10537,
	10537,
	10537,
	3242,
	5449,
	5449,
	10861,
	10781,
	10861,
	10861,
	10870,
	14812,
	8700,
	8700,
	8568,
	14793,
	14715,
	14715,
	14712,
	10698,
	10637,
	6166,
	6176,
	391,
	11557,
	257,
	11369,
	265,
	11388,
	11272,
	266,
	11202,
	11201,
	166,
	11208,
	169,
	11389,
	575,
	11527,
	11340,
	11378,
	11268,
	11194,
	11204,
	11379,
	10698,
	10861,
	8783,
	10861,
	8783,
	10781,
	8700,
	10698,
	10698,
	10861,
	8783,
	10861,
	8783,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	8468,
	10537,
	8468,
	8568,
	8568,
	8783,
	8468,
	10861,
	10729,
	8656,
	8568,
	8700,
	8783,
	8656,
	10870,
	10870,
	4787,
	8783,
	2254,
	4787,
	8783,
	4787,
	4787,
	8783,
	1575,
	2254,
	2286,
	4791,
	724,
	1578,
	14708,
	14708,
	14708,
	14708,
	15831,
	14715,
	15831,
	14715,
	15831,
	14715,
	14709,
	15449,
	14709,
	14712,
	14712,
	14708,
	14709,
	14708,
	14708,
	14708,
	14712,
	14715,
	14708,
	14708,
	15981,
	15981,
	13613,
	13613,
	13613,
	13613,
	12743,
	11488,
	10861,
	10781,
	8700,
	14708,
	15831,
	14715,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	370,
	s_methodPointers,
	57,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
