<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="AdvancedHelicopterControllerwithShooting.BulletScript" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.FollowTargetCamera" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.GameCanvas" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.Gasoline" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.GunController" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.GyroscopeViewController" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.HelicopterController" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.HelicopterSystemManager" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.HeliRotorController" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.MissileScript" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.RadarSystem" preserve="nothing"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.SimpleJoystick" preserve="nothing"/>
		<type fullname="AirplaneControl" preserve="nothing"/>
		<type fullname="AirplaneCrash" preserve="nothing"/>
		<type fullname="allcanvasfalse" preserve="nothing"/>
		<type fullname="BikeAnimation" preserve="nothing"/>
		<type fullname="BikeCamera" preserve="nothing"/>
		<type fullname="BikeControl" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.BoatController" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.BoatEngineSound" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.BoatSystemManager" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.BulletScript" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.FollowTargetCamera" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.GameCanvas" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.Gasoline" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.GunController" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.MissileScript" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.RadarSystem" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.SimpleJoystick" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.SpeedometerScript" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.WaterEffect" preserve="nothing"/>
		<type fullname="BoatControllerwithShooting.WaterSplashScript" preserve="nothing"/>
		<type fullname="CameraManager" preserve="nothing"/>
		<type fullname="Drivevehicleenterexit" preserve="nothing"/>
		<type fullname="Dronecamera" preserve="nothing"/>
		<type fullname="Dronecameraactivedeactive" preserve="nothing"/>
		<type fullname="FixedJoystick" preserve="nothing"/>
		<type fullname="GizmoObject" preserve="nothing"/>
		<type fullname="Invector.Utils.vComment" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonController" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonInput" preserve="nothing"/>
		<type fullname="OtherVehicleEnterexit" preserve="nothing"/>
		<type fullname="RCC_AssetPaths" preserve="nothing"/>
		<type fullname="RCC_Camera" preserve="nothing"/>
		<type fullname="RCC_CarControllerV4" preserve="nothing"/>
		<type fullname="RCC_ChangableWheels" preserve="nothing"/>
		<type fullname="RCC_CinematicCamera" preserve="nothing"/>
		<type fullname="RCC_ColorPickerBySliders" preserve="nothing"/>
		<type fullname="RCC_CustomizationSetups" preserve="nothing"/>
		<type fullname="RCC_Customizer" preserve="nothing"/>
		<type fullname="RCC_Customizer_Brake" preserve="nothing"/>
		<type fullname="RCC_Customizer_CustomizationManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_Decal" preserve="nothing"/>
		<type fullname="RCC_Customizer_DecalManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_Engine" preserve="nothing"/>
		<type fullname="RCC_Customizer_Handling" preserve="nothing"/>
		<type fullname="RCC_Customizer_Neon" preserve="nothing"/>
		<type fullname="RCC_Customizer_NeonManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_Paint" preserve="nothing"/>
		<type fullname="RCC_Customizer_PaintManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_Siren" preserve="nothing"/>
		<type fullname="RCC_Customizer_SirenManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_Speed" preserve="nothing"/>
		<type fullname="RCC_Customizer_Spoiler" preserve="nothing"/>
		<type fullname="RCC_Customizer_SpoilerManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_UpgradeManager" preserve="nothing"/>
		<type fullname="RCC_Customizer_WheelManager" preserve="nothing"/>
		<type fullname="RCC_DashboardColors" preserve="nothing"/>
		<type fullname="RCC_DashboardInputs" preserve="nothing"/>
		<type fullname="RCC_DashboardObjects" preserve="nothing"/>
		<type fullname="RCC_Demo" preserve="nothing"/>
		<type fullname="RCC_DemoMaterials" preserve="nothing"/>
		<type fullname="RCC_DemoVehicles" preserve="nothing"/>
		<type fullname="RCC_DetachablePart" preserve="nothing"/>
		<type fullname="RCC_Exhaust" preserve="nothing"/>
		<type fullname="RCC_FOVForCinematicCamera" preserve="nothing"/>
		<type fullname="RCC_GroundMaterials" preserve="nothing"/>
		<type fullname="RCC_HoodCamera" preserve="nothing"/>
		<type fullname="RCC_InfoLabel" preserve="nothing"/>
		<type fullname="RCC_InitialSettings" preserve="nothing"/>
		<type fullname="RCC_Light" preserve="nothing"/>
		<type fullname="RCC_Mirror" preserve="nothing"/>
		<type fullname="RCC_MobileButtons" preserve="nothing"/>
		<type fullname="RCC_PoliceSiren" preserve="nothing"/>
		<type fullname="RCC_Records" preserve="nothing"/>
		<type fullname="RCC_SceneManager" preserve="nothing"/>
		<type fullname="RCC_Settings" preserve="nothing"/>
		<type fullname="RCC_Skidmarks" preserve="nothing"/>
		<type fullname="RCC_SkidmarksManager" preserve="nothing"/>
		<type fullname="RCC_Telemetry" preserve="nothing"/>
		<type fullname="RCC_Teleporter" preserve="nothing"/>
		<type fullname="RCC_TrailerAttachPoint" preserve="nothing"/>
		<type fullname="RCC_TruckTrailer" preserve="nothing"/>
		<type fullname="RCC_UI_Canvas_Customizer" preserve="nothing"/>
		<type fullname="RCC_UI_Color" preserve="nothing"/>
		<type fullname="RCC_UI_Controller" preserve="nothing"/>
		<type fullname="RCC_UI_CustomizationSlider" preserve="nothing"/>
		<type fullname="RCC_UI_DashboardButton" preserve="nothing"/>
		<type fullname="RCC_UI_DashboardDisplay" preserve="nothing"/>
		<type fullname="RCC_UI_Decal" preserve="nothing"/>
		<type fullname="RCC_UI_DecalSetLocation" preserve="nothing"/>
		<type fullname="RCC_UI_Drag" preserve="nothing"/>
		<type fullname="RCC_UI_Joystick" preserve="nothing"/>
		<type fullname="RCC_UI_Neon" preserve="nothing"/>
		<type fullname="RCC_UI_ShowroomCameraDrag" preserve="nothing"/>
		<type fullname="RCC_UI_Siren" preserve="nothing"/>
		<type fullname="RCC_UI_SliderTextReader" preserve="nothing"/>
		<type fullname="RCC_UI_Spoiler" preserve="nothing"/>
		<type fullname="RCC_UI_SteeringWheelController" preserve="nothing"/>
		<type fullname="RCC_UI_Upgrade" preserve="nothing"/>
		<type fullname="RCC_UI_Wheel" preserve="nothing"/>
		<type fullname="RCC_Useless" preserve="nothing"/>
		<type fullname="RCC_WheelCamera" preserve="nothing"/>
		<type fullname="RCC_WheelCollider" preserve="nothing"/>
		<type fullname="RearLookAt" preserve="nothing"/>
		<type fullname="SMR_Theme" preserve="nothing"/>
		<type fullname="TrainInputSettings" preserve="nothing"/>
		<type fullname="truckrcccam" preserve="nothing"/>
		<type fullname="VehiclePlayerPosition" preserve="nothing"/>
		<type fullname="vThirdPersonCamera" preserve="nothing"/>
		<type fullname="WheelManager" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.DemoUI_v3" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.PassengerTags" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.RailSensor" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.RouteManager" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainAttachPassenger" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainCarCoupler" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainController_v3" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainDoor" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainDoorsController" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainPlayerInput" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainSpawner" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainSpeedMonitor" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainStationController" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainSuspension" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainWheel_v3" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.TrainWheelsTruck" preserve="nothing"/>
		<type fullname="WSMGameStudio.RailroadSystem.Wagon_v3" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SMR_GeneratedCollider" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SMR_GeneratedMesh" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SMR_IgnoredObject" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SMR_MeshCapTag" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SMR_MeshGenerationProfile" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.Spline" preserve="nothing"/>
		<type fullname="WSMGameStudio.Splines.SplineMeshRenderer" preserve="nothing"/>
	</assembly>
	<assembly fullname="Assembly-CSharp-firstpass">
		<type fullname="DG.Tweening.DOTweenAnimation" preserve="nothing"/>
	</assembly>
	<assembly fullname="DOTween">
		<type fullname="DG.Tweening.Core.DOTweenSettings" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.Cinemachine">
		<type fullname="Unity.Cinemachine.CinemachineBrain" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineCamera" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineDeoccluder" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineFollow" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineFreeLookModifier" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineInputAxisController" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineRotationComposer" preserve="nothing"/>
		<type fullname="Unity.Cinemachine.CinemachineThirdPersonFollow" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputActionAsset" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputActionReference" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSettings" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSystemObject" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.RemoteInputPlayerConnection" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.UI.InputSystemUIInputModule" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.LensFlareDataSRP" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerBitField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerColor" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerContainer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerHBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerMessageBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObject" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectList" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerProgressBar" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRow" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValue" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValueTuple" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector2" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector3" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector4" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.UIFoldout" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.TextMeshProUGUI" preserve="nothing"/>
		<type fullname="TMPro.TMP_ColorGradient" preserve="nothing"/>
		<type fullname="TMPro.TMP_FontAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_Settings" preserve="nothing"/>
		<type fullname="TMPro.TMP_SpriteAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_StyleSheet" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.Timeline">
		<type fullname="UnityEngine.Timeline.AnimationTrack" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.TimelineAsset" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.Animations.AnimatorController" preserve="nothing"/>
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.UnityConnectSettings" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.AnimationClip" preserve="nothing"/>
		<type fullname="UnityEngine.Animator" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerSnapshot" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioResource" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioClip" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.AudioSource" preserve="nothing"/>
		<type fullname="UnityEngine.Avatar" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.BoxCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Canvas" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasGroup" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="nothing"/>
		<type fullname="UnityEngine.CharacterJoint" preserve="nothing"/>
		<type fullname="UnityEngine.Collider" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.ConfigurableJoint" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Flare" preserve="nothing"/>
		<type fullname="UnityEngine.FlareLayer" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.HingeJoint" preserve="nothing"/>
		<type fullname="UnityEngine.Joint" preserve="nothing"/>
		<type fullname="UnityEngine.LensFlare" preserve="nothing"/>
		<type fullname="UnityEngine.Light" preserve="nothing"/>
		<type fullname="UnityEngine.LightingSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightProbes" preserve="nothing"/>
		<type fullname="UnityEngine.LODGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MeshCollider" preserve="nothing"/>
		<type fullname="UnityEngine.MeshFilter" preserve="nothing"/>
		<type fullname="UnityEngine.MeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Motion" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystem" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystemRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.PhysicsMaterial" preserve="nothing"/>
		<type fullname="UnityEngine.Playables.PlayableDirector" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.RectTransform" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rigidbody" preserve="nothing"/>
		<type fullname="UnityEngine.RuntimeAnimatorController" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.SphereCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.TrailRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
		<type fullname="UnityEngine.Video.VideoClip" preserve="nothing"/>
		<type fullname="UnityEngine.WheelCollider" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.IMGUIModule">
		<type fullname="UnityEngine.GUISkin" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ContentSizeFitter" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Dropdown" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.LayoutElement" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Mask" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Outline" preserve="nothing"/>
		<type fullname="UnityEngine.UI.RawImage" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Scrollbar" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ScrollRect" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Shadow" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Slider" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="nothing"/>
		<type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="nothing"/>
	</assembly>
</linker>
