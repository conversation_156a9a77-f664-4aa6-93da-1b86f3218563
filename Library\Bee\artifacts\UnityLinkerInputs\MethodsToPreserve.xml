<linker>
	<assembly fullname="Assembly-CSharp" ignoreIfMissing="1">
		<type fullname="AdvancedHelicopterControllerwithShooting.GameCanvas">
			<method name="Click_Button_CameraSwitch"/>
			<method name="Click_Button_Guns_Up"/>
			<method name="Click_Button_Guns_Up"/>
			<method name="Click_Button_MachineGun_Down"/>
			<method name="Click_Button_Missle_Down"/>
			<method name="Propeller_Height_Update"/>
		</type>
		<type fullname="BoatControllerwithShooting.GameCanvas">
			<method name="Click_Button_CameraSwitch"/>
			<method name="Click_Button_Guns_Up"/>
			<method name="Click_Button_Guns_Up"/>
			<method name="Click_Button_MachineGun_Down"/>
			<method name="Click_Button_Missle_Down"/>
		</type>
		<type fullname="CameraManager">
			<method name="ToggleCamera"/>
		</type>
		<type fullname="Drivevehicleenterexit">
			<method name="Entervehicle"/>
			<method name="Exitvehicle"/>
		</type>
		<type fullname="Dronecameraactivedeactive">
			<method name="PlayDrone"/>
			<method name="StopDrone"/>
		</type>
		<type fullname="OtherVehicleEnterexit">
			<method name="EnterAirplane"/>
			<method name="EnterBoat"/>
			<method name="EnterHelicopter"/>
			<method name="EnterMotorbike"/>
			<method name="EnterTrain"/>
			<method name="ExitAirplane"/>
			<method name="ExitBoat"/>
			<method name="ExitHelicopter"/>
			<method name="ExitMotorbike"/>
			<method name="ExitTrain"/>
		</type>
		<type fullname="RCC_UI_Canvas_Customizer">
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
			<method name="ChooseClass"/>
		</type>
		<type fullname="RCC_UI_CustomizationSlider">
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
			<method name="OnSlider"/>
		</type>
		<type fullname="RCC_UI_Decal">
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
		</type>
		<type fullname="RCC_UI_DecalSetLocation">
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
		</type>
		<type fullname="RCC_UI_Neon">
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
			<method name="Upgrade"/>
		</type>
		<type fullname="RCC_UI_Upgrade">
			<method name="OnClick"/>
			<method name="OnClick"/>
		</type>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime" ignoreIfMissing="1">
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton">
			<method name="OnAction"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField">
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel">
			<method name="OnScrollbarClicked"/>
			<method name="OnScrollbarClicked"/>
			<method name="ResetDebugManager"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.GameObject">
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
		</type>
	</assembly>
</linker>
