{ "pid": 11572, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11572, "tid": 1, "ts": 1752056628772214, "dur": 3725, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752056628775943, "dur": 140917, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752056628916863, "dur": 365611, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629720853, "dur": 17, "ph": "X", "name": "", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628772168, "dur": 30968, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803137, "dur": 917088, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803149, "dur": 38, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803189, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803192, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803502, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803558, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628803568, "dur": 5311, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628808886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628808889, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628808960, "dur": 3, "ph": "X", "name": "ProcessMessages 462", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628808965, "dur": 79, "ph": "X", "name": "ReadAsync 462", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809049, "dur": 3, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809053, "dur": 67, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809125, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809129, "dur": 83, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809217, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809220, "dur": 54, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809279, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809283, "dur": 54, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809340, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809344, "dur": 56, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809404, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809407, "dur": 52, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809464, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809467, "dur": 56, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809527, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809531, "dur": 52, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809587, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809590, "dur": 54, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809649, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809653, "dur": 48, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809705, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809709, "dur": 45, "ph": "X", "name": "ReadAsync 162", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809760, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809814, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809817, "dur": 52, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809874, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809877, "dur": 50, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809931, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809934, "dur": 48, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809986, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628809990, "dur": 52, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810046, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810049, "dur": 48, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810101, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810104, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810157, "dur": 2, "ph": "X", "name": "ProcessMessages 273", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810161, "dur": 53, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810218, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810222, "dur": 54, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810284, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810290, "dur": 53, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810352, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810355, "dur": 56, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810416, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810419, "dur": 61, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810484, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810488, "dur": 58, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810550, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810553, "dur": 46, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810604, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810607, "dur": 39, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810653, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810708, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810712, "dur": 48, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810763, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810766, "dur": 50, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810822, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810826, "dur": 48, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810878, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810884, "dur": 51, "ph": "X", "name": "ReadAsync 311", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810939, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628810943, "dur": 54, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811001, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811005, "dur": 50, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811059, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811063, "dur": 61, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811128, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811131, "dur": 51, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811186, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811190, "dur": 48, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811243, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811247, "dur": 52, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811302, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811307, "dur": 51, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811362, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811366, "dur": 49, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811419, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811423, "dur": 50, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811477, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811480, "dur": 56, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811541, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811544, "dur": 46, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811594, "dur": 3, "ph": "X", "name": "ProcessMessages 147", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811599, "dur": 56, "ph": "X", "name": "ReadAsync 147", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811660, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811664, "dur": 49, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811718, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811722, "dur": 50, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811776, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811780, "dur": 56, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811842, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811846, "dur": 58, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811908, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811912, "dur": 52, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811969, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628811972, "dur": 57, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812034, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812038, "dur": 52, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812094, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812097, "dur": 58, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812161, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812165, "dur": 51, "ph": "X", "name": "ReadAsync 538", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812221, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812225, "dur": 51, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812281, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812284, "dur": 46, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812334, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812338, "dur": 46, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812392, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812451, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812454, "dur": 58, "ph": "X", "name": "ReadAsync 520", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812517, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812521, "dur": 53, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812578, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812581, "dur": 50, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812636, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812640, "dur": 49, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812694, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812697, "dur": 49, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812751, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812758, "dur": 56, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812818, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812822, "dur": 72, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812899, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812903, "dur": 55, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812962, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628812968, "dur": 49, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813021, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813025, "dur": 45, "ph": "X", "name": "ReadAsync 249", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813074, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813077, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813127, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813129, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813187, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813192, "dur": 53, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813249, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813253, "dur": 52, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813309, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813313, "dur": 47, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813365, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813368, "dur": 51, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813424, "dur": 2, "ph": "X", "name": "ProcessMessages 226", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813428, "dur": 50, "ph": "X", "name": "ReadAsync 226", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813482, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813486, "dur": 47, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813537, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813541, "dur": 77, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813624, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813627, "dur": 61, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813693, "dur": 3, "ph": "X", "name": "ProcessMessages 655", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813697, "dur": 52, "ph": "X", "name": "ReadAsync 655", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813753, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813757, "dur": 47, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813808, "dur": 4, "ph": "X", "name": "ProcessMessages 254", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813814, "dur": 52, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813870, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813875, "dur": 48, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813927, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813930, "dur": 51, "ph": "X", "name": "ReadAsync 74", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813987, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628813991, "dur": 48, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814043, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814047, "dur": 52, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814105, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814108, "dur": 53, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814165, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814170, "dur": 50, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814224, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814228, "dur": 49, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814281, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814285, "dur": 142, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814434, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814491, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814495, "dur": 50, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814549, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814553, "dur": 53, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814610, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814613, "dur": 50, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814669, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814672, "dur": 51, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814728, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814732, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814781, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814785, "dur": 49, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814840, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814843, "dur": 61, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814909, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814913, "dur": 50, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814967, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628814971, "dur": 50, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815025, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815028, "dur": 65, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815097, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815102, "dur": 47, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815154, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815159, "dur": 44, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815207, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815210, "dur": 55, "ph": "X", "name": "ReadAsync 69", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815269, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815273, "dur": 53, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815330, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815334, "dur": 48, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815389, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815393, "dur": 54, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815451, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815455, "dur": 57, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815517, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815520, "dur": 49, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815574, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815577, "dur": 52, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815634, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815637, "dur": 51, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815692, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815696, "dur": 42, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815742, "dur": 2, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815746, "dur": 44, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815794, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815797, "dur": 56, "ph": "X", "name": "ReadAsync 137", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815858, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815861, "dur": 54, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815920, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815924, "dur": 52, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815981, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628815985, "dur": 47, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816036, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816040, "dur": 47, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816092, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816096, "dur": 85, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816186, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816191, "dur": 73, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816268, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816273, "dur": 54, "ph": "X", "name": "ReadAsync 730", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816331, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816335, "dur": 47, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816386, "dur": 2, "ph": "X", "name": "ProcessMessages 153", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816391, "dur": 52, "ph": "X", "name": "ReadAsync 153", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816448, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816451, "dur": 52, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816509, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816513, "dur": 53, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816570, "dur": 2, "ph": "X", "name": "ProcessMessages 213", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816574, "dur": 57, "ph": "X", "name": "ReadAsync 213", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816635, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816639, "dur": 52, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816695, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816699, "dur": 48, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816756, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816762, "dur": 59, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816826, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816830, "dur": 52, "ph": "X", "name": "ReadAsync 418", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816887, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816891, "dur": 51, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816946, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628816951, "dur": 54, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817009, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817013, "dur": 52, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817070, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817074, "dur": 45, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817124, "dur": 2, "ph": "X", "name": "ProcessMessages 79", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817127, "dur": 48, "ph": "X", "name": "ReadAsync 79", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817180, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817184, "dur": 56, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817244, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817247, "dur": 46, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817298, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817302, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817363, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817367, "dur": 51, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817424, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817429, "dur": 47, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817481, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817485, "dur": 56, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817546, "dur": 4, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817551, "dur": 61, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817616, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817621, "dur": 51, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817676, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817680, "dur": 50, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817737, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817741, "dur": 52, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817797, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817801, "dur": 46, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817851, "dur": 2, "ph": "X", "name": "ProcessMessages 81", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817855, "dur": 54, "ph": "X", "name": "ReadAsync 81", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817913, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817916, "dur": 51, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817971, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628817976, "dur": 46, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818026, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818029, "dur": 64, "ph": "X", "name": "ReadAsync 225", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818100, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818104, "dur": 50, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818159, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818162, "dur": 45, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818211, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818214, "dur": 54, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818273, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818277, "dur": 52, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818333, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818337, "dur": 52, "ph": "X", "name": "ReadAsync 542", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818393, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818397, "dur": 53, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818454, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818458, "dur": 50, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818512, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818516, "dur": 50, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818571, "dur": 2, "ph": "X", "name": "ProcessMessages 133", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818575, "dur": 54, "ph": "X", "name": "ReadAsync 133", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818633, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818637, "dur": 48, "ph": "X", "name": "ReadAsync 606", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818689, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818693, "dur": 53, "ph": "X", "name": "ReadAsync 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818750, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818754, "dur": 57, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818816, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818820, "dur": 58, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818883, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818887, "dur": 53, "ph": "X", "name": "ReadAsync 650", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818945, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628818948, "dur": 55, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819008, "dur": 2, "ph": "X", "name": "ProcessMessages 619", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819011, "dur": 55, "ph": "X", "name": "ReadAsync 619", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819070, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819074, "dur": 53, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819132, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819136, "dur": 50, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819191, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819194, "dur": 43, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819243, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819247, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819301, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819305, "dur": 48, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819357, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819360, "dur": 50, "ph": "X", "name": "ReadAsync 601", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819414, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819418, "dur": 62, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819484, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819488, "dur": 58, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819550, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819555, "dur": 47, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819606, "dur": 2, "ph": "X", "name": "ProcessMessages 221", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819610, "dur": 56, "ph": "X", "name": "ReadAsync 221", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819670, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819675, "dur": 60, "ph": "X", "name": "ReadAsync 640", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819739, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819742, "dur": 55, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819801, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819805, "dur": 51, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819861, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819864, "dur": 54, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819923, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819926, "dur": 44, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819974, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628819979, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820039, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820042, "dur": 53, "ph": "X", "name": "ReadAsync 535", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820099, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820102, "dur": 53, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820159, "dur": 3, "ph": "X", "name": "ProcessMessages 420", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820166, "dur": 53, "ph": "X", "name": "ReadAsync 420", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820223, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820228, "dur": 52, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820284, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820287, "dur": 50, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820341, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820345, "dur": 51, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820401, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820406, "dur": 59, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820472, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820476, "dur": 53, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820533, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820537, "dur": 52, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820593, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820597, "dur": 46, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820647, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820649, "dur": 58, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820711, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820715, "dur": 50, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820770, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820774, "dur": 59, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820837, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820841, "dur": 56, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820901, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820905, "dur": 54, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820965, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628820969, "dur": 53, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821026, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821030, "dur": 54, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821087, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821091, "dur": 59, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821155, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821159, "dur": 56, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821219, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821222, "dur": 53, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821280, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821284, "dur": 51, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821341, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821345, "dur": 50, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821401, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821460, "dur": 4, "ph": "X", "name": "ProcessMessages 531", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821466, "dur": 52, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821522, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821525, "dur": 60, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821589, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821593, "dur": 57, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821654, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821658, "dur": 55, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821718, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821721, "dur": 50, "ph": "X", "name": "ReadAsync 545", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821776, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821779, "dur": 53, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821837, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821841, "dur": 53, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821898, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821901, "dur": 60, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821965, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628821969, "dur": 51, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822025, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822029, "dur": 51, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822084, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822087, "dur": 52, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822144, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822148, "dur": 56, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822208, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822211, "dur": 51, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822266, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822270, "dur": 58, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822333, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822338, "dur": 67, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822410, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822414, "dur": 53, "ph": "X", "name": "ReadAsync 614", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822472, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822476, "dur": 58, "ph": "X", "name": "ReadAsync 535", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822539, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822543, "dur": 57, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822605, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822609, "dur": 59, "ph": "X", "name": "ReadAsync 419", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822673, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822677, "dur": 66, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822750, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822754, "dur": 66, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822825, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822829, "dur": 50, "ph": "X", "name": "ReadAsync 589", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822886, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822889, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822949, "dur": 2, "ph": "X", "name": "ProcessMessages 522", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628822953, "dur": 53, "ph": "X", "name": "ReadAsync 522", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823011, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823015, "dur": 51, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823070, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823074, "dur": 55, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823134, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823137, "dur": 53, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823196, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823200, "dur": 53, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823257, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823261, "dur": 53, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823317, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823321, "dur": 299, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823625, "dur": 5, "ph": "X", "name": "ProcessMessages 2162", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823631, "dur": 79, "ph": "X", "name": "ReadAsync 2162", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823715, "dur": 7, "ph": "X", "name": "ProcessMessages 610", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823725, "dur": 63, "ph": "X", "name": "ReadAsync 610", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823793, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823798, "dur": 60, "ph": "X", "name": "ReadAsync 575", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823864, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823869, "dur": 56, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823929, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823933, "dur": 56, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823994, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628823997, "dur": 51, "ph": "X", "name": "ReadAsync 570", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824055, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824059, "dur": 53, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824116, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824120, "dur": 52, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824176, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824182, "dur": 50, "ph": "X", "name": "ReadAsync 481", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824236, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824240, "dur": 57, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824301, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824304, "dur": 37, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824344, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824348, "dur": 41, "ph": "X", "name": "ReadAsync 187", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824394, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824397, "dur": 100, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824506, "dur": 4, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824513, "dur": 87, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824603, "dur": 3, "ph": "X", "name": "ProcessMessages 1022", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824608, "dur": 53, "ph": "X", "name": "ReadAsync 1022", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824666, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824670, "dur": 50, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824724, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824728, "dur": 75, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824807, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824810, "dur": 53, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824868, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824872, "dur": 50, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824927, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824930, "dur": 50, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824985, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628824988, "dur": 53, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825048, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825052, "dur": 49, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825105, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825109, "dur": 51, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825164, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825170, "dur": 49, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825223, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825227, "dur": 62, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825293, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825297, "dur": 53, "ph": "X", "name": "ReadAsync 594", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825354, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825358, "dur": 52, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825414, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825417, "dur": 51, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825472, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825477, "dur": 52, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825533, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825537, "dur": 55, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825598, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825602, "dur": 49, "ph": "X", "name": "ReadAsync 446", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825655, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825659, "dur": 50, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825714, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825718, "dur": 50, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825772, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825775, "dur": 52, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825832, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825836, "dur": 51, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825890, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825896, "dur": 53, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825953, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628825956, "dur": 49, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826011, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826014, "dur": 49, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826068, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826071, "dur": 52, "ph": "X", "name": "ReadAsync 155", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826127, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826130, "dur": 50, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826184, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826189, "dur": 49, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826243, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826248, "dur": 52, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826304, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826308, "dur": 50, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826363, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826366, "dur": 66, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826436, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826440, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826495, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826498, "dur": 50, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826553, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826558, "dur": 55, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826617, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826620, "dur": 52, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826677, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826680, "dur": 50, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826734, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826738, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826786, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628826790, "dur": 393, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827191, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827283, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827288, "dur": 49, "ph": "X", "name": "ReadAsync 829", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827339, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827343, "dur": 50, "ph": "X", "name": "ReadAsync 604", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827398, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827402, "dur": 53, "ph": "X", "name": "ReadAsync 190", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827461, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827465, "dur": 82, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827552, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827556, "dur": 70, "ph": "X", "name": "ReadAsync 464", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827631, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827635, "dur": 50, "ph": "X", "name": "ReadAsync 585", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827690, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827695, "dur": 52, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827752, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827755, "dur": 50, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827810, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827813, "dur": 53, "ph": "X", "name": "ReadAsync 260", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827871, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827875, "dur": 51, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827930, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827933, "dur": 48, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827985, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628827989, "dur": 56, "ph": "X", "name": "ReadAsync 198", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828048, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828052, "dur": 54, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828111, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828115, "dur": 42, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828161, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828164, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828227, "dur": 3, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828232, "dur": 51, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828287, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828291, "dur": 55, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828350, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828355, "dur": 56, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828415, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828418, "dur": 53, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828476, "dur": 5, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828483, "dur": 46, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828534, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828537, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828597, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828600, "dur": 54, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828659, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828663, "dur": 47, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828715, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828718, "dur": 54, "ph": "X", "name": "ReadAsync 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828776, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828780, "dur": 51, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828836, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828839, "dur": 43, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828887, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828890, "dur": 70, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828964, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628828968, "dur": 52, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829024, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829027, "dur": 53, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829085, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829090, "dur": 51, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829145, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829150, "dur": 51, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829205, "dur": 2, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829209, "dur": 51, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829264, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829267, "dur": 53, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829324, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829328, "dur": 53, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829386, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829391, "dur": 45, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829440, "dur": 2, "ph": "X", "name": "ProcessMessages 158", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829444, "dur": 59, "ph": "X", "name": "ReadAsync 158", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829507, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829511, "dur": 46, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829561, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829567, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829624, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829628, "dur": 56, "ph": "X", "name": "ReadAsync 498", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829690, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829693, "dur": 52, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829750, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829754, "dur": 53, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829812, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829816, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829869, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829872, "dur": 43, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829923, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829982, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628829986, "dur": 50, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830041, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830044, "dur": 52, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830100, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830104, "dur": 50, "ph": "X", "name": "ReadAsync 175", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830158, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830164, "dur": 54, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830222, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830225, "dur": 50, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830280, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830284, "dur": 48, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830336, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830340, "dur": 45, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830393, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830396, "dur": 60, "ph": "X", "name": "ReadAsync 121", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830461, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830465, "dur": 59, "ph": "X", "name": "ReadAsync 557", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830529, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830533, "dur": 51, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830588, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830592, "dur": 50, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830646, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830649, "dur": 49, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830703, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830706, "dur": 55, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830766, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830771, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830825, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830829, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830889, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830949, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628830953, "dur": 50, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831008, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831012, "dur": 47, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831063, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831067, "dur": 108, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831182, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831239, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831243, "dur": 43, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831291, "dur": 2, "ph": "X", "name": "ProcessMessages 94", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831294, "dur": 53, "ph": "X", "name": "ReadAsync 94", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831353, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831357, "dur": 42, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831404, "dur": 4, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831410, "dur": 74, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831491, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831569, "dur": 4, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831579, "dur": 69, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831652, "dur": 3, "ph": "X", "name": "ProcessMessages 636", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831658, "dur": 82, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831744, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831747, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831815, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831819, "dur": 51, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831874, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831878, "dur": 47, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831929, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628831933, "dur": 89, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832026, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832029, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832094, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832098, "dur": 49, "ph": "X", "name": "ReadAsync 537", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832152, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832156, "dur": 49, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832210, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832213, "dur": 149, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832366, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832369, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832427, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832431, "dur": 45, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832481, "dur": 2, "ph": "X", "name": "ProcessMessages 87", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832484, "dur": 52, "ph": "X", "name": "ReadAsync 87", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832540, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832545, "dur": 44, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832593, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832596, "dur": 137, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832740, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832789, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832819, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832894, "dur": 3, "ph": "X", "name": "ProcessMessages 1279", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628832899, "dur": 130, "ph": "X", "name": "ReadAsync 1279", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833037, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833095, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833099, "dur": 48, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833151, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833154, "dur": 50, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833208, "dur": 3, "ph": "X", "name": "ProcessMessages 297", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833213, "dur": 41, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833258, "dur": 2, "ph": "X", "name": "ProcessMessages 58", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833261, "dur": 117, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833385, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833443, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833446, "dur": 47, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833497, "dur": 2, "ph": "X", "name": "ProcessMessages 174", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833501, "dur": 51, "ph": "X", "name": "ReadAsync 174", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833556, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833560, "dur": 48, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833611, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833616, "dur": 113, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833737, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833796, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833800, "dur": 42, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833847, "dur": 2, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833851, "dur": 58, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833913, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833916, "dur": 42, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833963, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628833967, "dur": 132, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834107, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834166, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834170, "dur": 54, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834229, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834233, "dur": 53, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834290, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834294, "dur": 41, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834340, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834343, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834464, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834525, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834529, "dur": 51, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834583, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834587, "dur": 50, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834641, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834645, "dur": 44, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834693, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834697, "dur": 120, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834825, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834881, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834885, "dur": 54, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834943, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628834946, "dur": 49, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835000, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835004, "dur": 53, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835061, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835066, "dur": 107, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835180, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835245, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835248, "dur": 45, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835297, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835301, "dur": 53, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835359, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835363, "dur": 46, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835413, "dur": 2, "ph": "X", "name": "ProcessMessages 142", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835417, "dur": 107, "ph": "X", "name": "ReadAsync 142", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835532, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835595, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835598, "dur": 49, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835652, "dur": 4, "ph": "X", "name": "ProcessMessages 331", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835658, "dur": 51, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835714, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835718, "dur": 42, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835764, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835768, "dur": 93, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835865, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835870, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835927, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835930, "dur": 48, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835982, "dur": 2, "ph": "X", "name": "ProcessMessages 202", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628835985, "dur": 53, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836043, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836047, "dur": 45, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836096, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836099, "dur": 97, "ph": "X", "name": "ReadAsync 136", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836203, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836259, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836266, "dur": 51, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836322, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836327, "dur": 58, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836390, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836394, "dur": 42, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836440, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836444, "dur": 88, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836538, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836592, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836595, "dur": 58, "ph": "X", "name": "ReadAsync 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836661, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836714, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836718, "dur": 48, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836770, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836774, "dur": 47, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836825, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836829, "dur": 105, "ph": "X", "name": "ReadAsync 225", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628836943, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837001, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837005, "dur": 48, "ph": "X", "name": "ReadAsync 526", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837057, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837061, "dur": 54, "ph": "X", "name": "ReadAsync 229", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837119, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837122, "dur": 43, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837170, "dur": 4, "ph": "X", "name": "ProcessMessages 67", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837176, "dur": 96, "ph": "X", "name": "ReadAsync 67", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837278, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837337, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837340, "dur": 54, "ph": "X", "name": "ReadAsync 496", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837399, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837402, "dur": 54, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837460, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837463, "dur": 43, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837511, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837514, "dur": 89, "ph": "X", "name": "ReadAsync 62", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837610, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837669, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837673, "dur": 49, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837726, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837729, "dur": 49, "ph": "X", "name": "ReadAsync 205", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837785, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837789, "dur": 47, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837841, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837844, "dur": 57, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837906, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837909, "dur": 71, "ph": "X", "name": "ReadAsync 233", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837989, "dur": 5, "ph": "X", "name": "ProcessMessages 369", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628837998, "dur": 80, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838085, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838089, "dur": 49, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838142, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838146, "dur": 60, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838213, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838219, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838312, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838371, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838374, "dur": 51, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838429, "dur": 2, "ph": "X", "name": "ProcessMessages 185", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838433, "dur": 68, "ph": "X", "name": "ReadAsync 185", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838505, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838508, "dur": 51, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838564, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838567, "dur": 49, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838620, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838623, "dur": 51, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838679, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838683, "dur": 53, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838741, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838745, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838798, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838802, "dur": 93, "ph": "X", "name": "ReadAsync 157", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838902, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838955, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628838958, "dur": 91, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839056, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839112, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839116, "dur": 48, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839170, "dur": 3, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839174, "dur": 206, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839392, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839451, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628839455, "dur": 770, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840235, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840239, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840311, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840317, "dur": 60, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840384, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840390, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840461, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840467, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840540, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840546, "dur": 67, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840619, "dur": 5, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840626, "dur": 61, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840693, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840700, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840776, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840782, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840834, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840839, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840892, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840900, "dur": 61, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840967, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628840973, "dur": 51, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841029, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841032, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841111, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841115, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841184, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841191, "dur": 61, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841261, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841268, "dur": 62, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841337, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841343, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841428, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841435, "dur": 59, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841499, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841505, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841568, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841574, "dur": 71, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841653, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841660, "dur": 80, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841746, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841752, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841826, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841833, "dur": 62, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841900, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841910, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841985, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628841991, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842056, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842062, "dur": 61, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842129, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842134, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842202, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842207, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842277, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842283, "dur": 61, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842350, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842355, "dur": 70, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842432, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842440, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842518, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842526, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842589, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842596, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842666, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842670, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842728, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842732, "dur": 63, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842801, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842808, "dur": 78, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842895, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842902, "dur": 66, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842974, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628842980, "dur": 66, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843052, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843059, "dur": 62, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843130, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843136, "dur": 62, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843205, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843211, "dur": 71, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843290, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843298, "dur": 73, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843380, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843387, "dur": 66, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843460, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843467, "dur": 88, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843565, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843573, "dur": 68, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843648, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843657, "dur": 73, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843739, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843746, "dur": 70, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843820, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843824, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843897, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843904, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843973, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628843978, "dur": 73, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844057, "dur": 5, "ph": "X", "name": "ProcessMessages 172", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844064, "dur": 65, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844134, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844140, "dur": 51, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844195, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844200, "dur": 49, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844256, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844264, "dur": 54, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844324, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844330, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844400, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844404, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844459, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628844462, "dur": 13419, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628857891, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628857896, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628857965, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628857971, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858024, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858027, "dur": 385, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858421, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858497, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628858502, "dur": 781, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859291, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859335, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859338, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859386, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859394, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859461, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859464, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859679, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859729, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859732, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859782, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859785, "dur": 177, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628859969, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860016, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860019, "dur": 491, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860518, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860567, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860570, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860624, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860671, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860676, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860721, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860767, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860770, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860820, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860823, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860890, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860937, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860940, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860993, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628860996, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861044, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861047, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861096, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861100, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861155, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861227, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861232, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861301, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861357, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861512, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628861566, "dur": 478, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862049, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862051, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862114, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862119, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862172, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862174, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862253, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862298, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862301, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862346, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862352, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862396, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862399, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862530, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862576, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862579, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628862998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863001, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863047, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863050, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863096, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863099, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863207, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863249, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863253, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863301, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863345, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863348, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863393, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863396, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863491, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863493, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863540, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863543, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863621, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863624, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863669, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863677, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863831, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863839, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863906, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628863911, "dur": 223, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864141, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864188, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864190, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864235, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864238, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864283, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864286, "dur": 117, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864410, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864454, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864460, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864503, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864505, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864550, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864553, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864776, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864778, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864832, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864836, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864939, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864984, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628864987, "dur": 186, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865180, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865223, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865226, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865271, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865273, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865326, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865370, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865373, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865414, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865417, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865461, "dur": 514, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628865983, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866035, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866038, "dur": 182, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866227, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866271, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866274, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866615, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866662, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866668, "dur": 166, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866840, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866889, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628866892, "dur": 156, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867054, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867101, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867104, "dur": 583, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867692, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867695, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867757, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628867760, "dur": 812, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628868577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628868580, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628868654, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628868659, "dur": 95035, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628963713, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628963721, "dur": 171, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628963902, "dur": 51, "ph": "X", "name": "ProcessMessages 206", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628963956, "dur": 24622, "ph": "X", "name": "ReadAsync 206", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988595, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988605, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988669, "dur": 8, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988680, "dur": 63, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988754, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988763, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988855, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628988861, "dur": 900, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628989771, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628989778, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628989830, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628989836, "dur": 311, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990156, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990161, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990233, "dur": 7, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990245, "dur": 77, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990328, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990336, "dur": 275, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990620, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990628, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990672, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628990680, "dur": 586, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991285, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991293, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991388, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991396, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991688, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991694, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991790, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628991800, "dur": 588, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992398, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992476, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992485, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992590, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992599, "dur": 86, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992691, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992696, "dur": 270, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628992973, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993033, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993036, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993100, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993105, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993171, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993176, "dur": 56, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993240, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993245, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993330, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993385, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993387, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993464, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993466, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993516, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993634, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993639, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993714, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993719, "dur": 104, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993835, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993904, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628993909, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628994001, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628994006, "dur": 70, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628994083, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056628994088, "dur": 5989, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000084, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000088, "dur": 286, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000385, "dur": 12, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000402, "dur": 172, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000585, "dur": 80, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000669, "dur": 142, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000824, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000832, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000905, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629000913, "dur": 168, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001088, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001092, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001192, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001235, "dur": 154, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001399, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001505, "dur": 54, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001567, "dur": 92, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001668, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001677, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001801, "dur": 48, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001855, "dur": 82, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629001945, "dur": 77, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002029, "dur": 127, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002167, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002178, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002278, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002288, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002512, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002597, "dur": 45, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002648, "dur": 71, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002728, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002738, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002892, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002899, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629002997, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003042, "dur": 128, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003180, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003187, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003303, "dur": 23, "ph": "X", "name": "ProcessMessages 95", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003328, "dur": 69, "ph": "X", "name": "ReadAsync 95", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003402, "dur": 20, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003424, "dur": 212, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003647, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003721, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003727, "dur": 140, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003879, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629003975, "dur": 40, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004019, "dur": 85, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004113, "dur": 37, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004155, "dur": 387, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004553, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004560, "dur": 171, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004743, "dur": 15, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004765, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004887, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629004894, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005001, "dur": 50, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005056, "dur": 97, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005159, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005165, "dur": 761, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005936, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629005944, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629006053, "dur": 35, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629006093, "dur": 22758, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629028869, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629028877, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629028931, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629028940, "dur": 330, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029277, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029282, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029360, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029365, "dur": 71, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029443, "dur": 61, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029512, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029604, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029678, "dur": 68, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029751, "dur": 54, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029811, "dur": 29, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029843, "dur": 53, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029903, "dur": 21, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629029927, "dur": 101, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030034, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030038, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030094, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030116, "dur": 57, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030178, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030184, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030311, "dur": 23, "ph": "X", "name": "ProcessMessages 86", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030338, "dur": 57, "ph": "X", "name": "ReadAsync 86", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030402, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030408, "dur": 356, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030771, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030775, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030837, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030863, "dur": 42, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030910, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030916, "dur": 58, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629030981, "dur": 21, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031005, "dur": 106, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031120, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031188, "dur": 36, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031228, "dur": 172, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031410, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031475, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031501, "dur": 61, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031568, "dur": 25, "ph": "X", "name": "ProcessMessages 82", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031596, "dur": 52, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031653, "dur": 28, "ph": "X", "name": "ProcessMessages 10", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031688, "dur": 201, "ph": "X", "name": "ReadAsync 10", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031902, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629031969, "dur": 34, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032007, "dur": 310, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032326, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032390, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032413, "dur": 426, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032847, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629032988, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033009, "dur": 52, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033067, "dur": 23, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033094, "dur": 68, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033168, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033173, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033241, "dur": 30, "ph": "X", "name": "ProcessMessages 10", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033274, "dur": 90, "ph": "X", "name": "ReadAsync 10", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033374, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033436, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033463, "dur": 45, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033513, "dur": 42, "ph": "X", "name": "ProcessMessages 138", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033558, "dur": 226, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033796, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033832, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629033855, "dur": 175, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034036, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034041, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034096, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034119, "dur": 104, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034233, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034282, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034303, "dur": 507, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034820, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034885, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629034908, "dur": 6508, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629041428, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629041435, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629041517, "dur": 48, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629041570, "dur": 633960, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629675547, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629675553, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629675680, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629675689, "dur": 1958, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629677657, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629677662, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629677732, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752056629677735, "dur": 42481, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629720874, "dur": 3749, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 60129542144, "ts": 1752056628772092, "dur": 510414, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ts": 1752056629282508, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ts": 1752056629282512, "dur": 95, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629724627, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11572, "tid": 55834574848, "ts": 1752056628769052, "dur": 951236, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 55834574848, "ts": 1752056628769208, "dur": 2831, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 55834574848, "ts": 1752056629720301, "dur": 105, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 55834574848, "ts": 1752056629720331, "dur": 37, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 55834574848, "ts": 1752056629720408, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629724648, "dur": 24, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752056628803527, "dur":2773, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628806320, "dur":1301, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628807713, "dur":136, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1752056628807850, "dur":1337, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628809320, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809387, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809509, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809602, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809660, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809716, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809772, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809831, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628809971, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810198, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810309, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810416, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810472, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810663, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810720, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810776, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810867, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628810924, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811090, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811200, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811257, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811365, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811508, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811566, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811675, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811732, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811851, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628811909, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628812024, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628813266, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628813329, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628813694, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628814080, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628814139, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628814247, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628814374, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628814483, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628814540, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628814868, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628814979, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628815410, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628815469, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628815692, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628815825, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628815882, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628816228, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628816618, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628817208, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628817932, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628818286, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628818652, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628819002, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628820125, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628821075, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628823201, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628823917, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628824090, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628824149, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628824962, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825050, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825108, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825164, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825260, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825316, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825423, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628825964, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628826322, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628826497, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628826886, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056628826991, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628827048, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628827627, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628827724, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628827836, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628827899, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056628827991, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628828968, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628829869, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_A27EB21DA167B07C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1752056628829998, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056628830059, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628830354, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628830421, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628830844, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628832549, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/8048311097819667973.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628832804, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628832926, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/3398277663090063864.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628833469, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628833527, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628833822, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628833930, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10441134206345068177.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628834167, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628834287, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/9766718788248844585.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628834540, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628834600, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628834656, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/11295223868419055766.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628834898, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Splines.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628834956, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Splines.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628835258, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628835322, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628835614, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628835743, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/981101458197481158.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628835962, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628836026, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628836085, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10610811180600194915.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628836302, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628836363, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628836423, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13249999676030392445.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628836636, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628836694, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Cinemachine.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628837097, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628837376, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628837435, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628837497, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/11897712654715090967.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628837712, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628837772, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628837833, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/16778236131868972292.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628838044, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628838104, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628838356, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1752056628838452, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628838745, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056628838807, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1752056628838868, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1752056628838933, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056628839127, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752056628839492, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":0, "ts":1752056628809237, "dur":30310, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628839565, "dur":836402, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629675974, "dur":1142, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629677117, "dur":70, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629677230, "dur":62, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629677720, "dur":233, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629678003, "dur":23512, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752056628808785, "dur":30814, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628839633, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628839786, "dur":766, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628840553, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628840643, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628840778, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628840875, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628840969, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628841155, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628841266, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628841638, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628841851, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628842005, "dur":127, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628842136, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628842254, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628842429, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628842534, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628842696, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628842797, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628842883, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1752056628843093, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628843187, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628843254, "dur":150, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056628843406, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752056628843467, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628843557, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628843620, "dur":87, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056628843710, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628843788, "dur":396, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056628844202, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628844310, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628844465, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628844551, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/3398277663090063864.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752056628844637, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628844718, "dur":974, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628845693, "dur":1917, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628847611, "dur":2906, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628850518, "dur":604, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628851123, "dur":594, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628852057, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Audio\\AudioTrack.cs" }}
,{ "pid":12345, "tid":1, "ts":1752056628853309, "dur":978, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Attributes\\TrackColorAttribute.cs" }}
,{ "pid":12345, "tid":1, "ts":1752056628851718, "dur":4118, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628855836, "dur":653, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628856490, "dur":1498, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628858225, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\InputControlLayout.cs" }}
,{ "pid":12345, "tid":1, "ts":1752056628857989, "dur":2343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628860332, "dur":4028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628864360, "dur":123958, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628988319, "dur":924, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628989259, "dur":1315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628990599, "dur":11593, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.pdb" }}
,{ "pid":12345, "tid":1, "ts":1752056629002203, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056629002481, "dur":31733, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Splines.pdb" }}
,{ "pid":12345, "tid":1, "ts":1752056629034217, "dur":641762, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628808898, "dur":30748, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628839651, "dur":892, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628840545, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628840648, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628840815, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628840956, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628841057, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628841151, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628841260, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628841568, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628841702, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628841793, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628841972, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628842030, "dur":274, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628842308, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5E63226C05E60A63.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628842504, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628842805, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_67F60D54A1E44C7C.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628842949, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843047, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843174, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843256, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056628843337, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":2, "ts":1752056628843404, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843498, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843613, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843676, "dur":136, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1752056628843828, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628843915, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628844049, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628844162, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628844232, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp" }}
,{ "pid":12345, "tid":2, "ts":1752056628844296, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628844382, "dur":183, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp" }}
,{ "pid":12345, "tid":2, "ts":1752056628844567, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13249999676030392445.rsp" }}
,{ "pid":12345, "tid":2, "ts":1752056628844663, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628844742, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13249999676030392445.rsp" }}
,{ "pid":12345, "tid":2, "ts":1752056628844820, "dur":2139, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628846960, "dur":2301, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628849431, "dur":1270, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Helpers\\CinemachineSplineSmoother.cs" }}
,{ "pid":12345, "tid":2, "ts":1752056628852096, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\CinemachineTouchInputMapper.cs" }}
,{ "pid":12345, "tid":2, "ts":1752056628852804, "dur":977, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\CinemachineSmoothPath.cs" }}
,{ "pid":12345, "tid":2, "ts":1752056628849262, "dur":5385, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628854647, "dur":2131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628856779, "dur":1918, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628858698, "dur":816, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628859514, "dur":356, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628859871, "dur":4487, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628864359, "dur":119028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628983401, "dur":10702, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1752056628994106, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628994250, "dur":6242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056629000539, "dur":139, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056629000682, "dur":2884, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":2, "ts":1752056629003574, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056629003708, "dur":28357, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":2, "ts":1752056629032068, "dur":643929, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628808831, "dur":30789, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628839636, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628839789, "dur":889, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628840680, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628840970, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628841060, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628841213, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628841531, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628841610, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628841715, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628841799, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628841888, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628841972, "dur":437, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628842424, "dur":278, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628842703, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628842799, "dur":15275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1752056628858076, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628858282, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628858387, "dur":356, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628858744, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628858819, "dur":2367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1752056628861188, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628861401, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628861460, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628861701, "dur":685, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1752056628862387, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628862534, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628862762, "dur":740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1752056628863503, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628863666, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1752056628863903, "dur":653, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1752056628864557, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628864662, "dur":118740, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628983421, "dur":6343, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1752056628989767, "dur":4440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628994233, "dur":6171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056629000557, "dur":92, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056629000655, "dur":5682, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":3, "ts":1752056629006352, "dur":22806, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056629033841, "dur":60, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056629029211, "dur":4706, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":3, "ts":1752056629033920, "dur":642152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628808883, "dur":30750, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628839638, "dur":895, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628840534, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628840689, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628840865, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628841042, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628841160, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628841342, "dur":211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628841554, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628841663, "dur":113, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628841779, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628841885, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628842031, "dur":251, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628842285, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628842419, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628842607, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628842701, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628842773, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628842870, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843013, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628843101, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843301, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843367, "dur":53, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1752056628843425, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843664, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1752056628843745, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843831, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.rsp" }}
,{ "pid":12345, "tid":4, "ts":1752056628843884, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628843976, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628844105, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628844184, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628844253, "dur":291, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1752056628844546, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10610811180600194915.rsp" }}
,{ "pid":12345, "tid":4, "ts":1752056628844654, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628844808, "dur":2185, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628848242, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRun\\Tasks\\Platform\\PlatformSpecificCleanupTask.cs" }}
,{ "pid":12345, "tid":4, "ts":1752056628846994, "dur":2355, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628849350, "dur":2449, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628852036, "dur":788, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Activation\\ActivationPlayableAsset.cs" }}
,{ "pid":12345, "tid":4, "ts":1752056628853215, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\SplineSlice.cs" }}
,{ "pid":12345, "tid":4, "ts":1752056628853863, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\SplineRange.cs" }}
,{ "pid":12345, "tid":4, "ts":1752056628851800, "dur":3990, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628855791, "dur":607, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628856537, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\UI\\StandaloneInputModuleEditor.cs" }}
,{ "pid":12345, "tid":4, "ts":1752056628856399, "dur":2905, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628859305, "dur":548, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628859872, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1752056628860079, "dur":681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1752056628860760, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628860915, "dur":3480, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628864395, "dur":118949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628983357, "dur":7199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1752056628990558, "dur":3144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628993716, "dur":6734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056629030107, "dur":65, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056629000504, "dur":30544, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629031052, "dur":10803, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056629041929, "dur":338267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629380237, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629380459, "dur":346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629380829, "dur":619, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629381470, "dur":559, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629382052, "dur":1269, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629383348, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629383477, "dur":914, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629384418, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629384534, "dur":257, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Postprocessing.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629384822, "dur":418, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629385263, "dur":966, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629386253, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629386414, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629386522, "dur":294, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629386841, "dur":34346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629421220, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Splines.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629421463, "dur":250907, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629672426, "dur":377, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629672844, "dur":1185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629674068, "dur":811, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629674917, "dur":191, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629675143, "dur":588, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1752056629041857, "dur":633897, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":4, "ts":1752056629675756, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628808956, "dur":30703, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628839663, "dur":1110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628840775, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628840854, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628840910, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628841098, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628841175, "dur":144, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628841322, "dur":225, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628841548, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628841627, "dur":97, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628841727, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628841841, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628841939, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628842004, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628842106, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628842224, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628842348, "dur":84, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628842434, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628842548, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628842849, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1752056628843020, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628843309, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628843661, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628843753, "dur":451, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.rsp2" }}
,{ "pid":12345, "tid":5, "ts":1752056628844206, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp" }}
,{ "pid":12345, "tid":5, "ts":1752056628844268, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628844398, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/9766718788248844585.rsp" }}
,{ "pid":12345, "tid":5, "ts":1752056628844469, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628844528, "dur":169, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/9766718788248844585.rsp" }}
,{ "pid":12345, "tid":5, "ts":1752056628844699, "dur":1037, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628845737, "dur":2014, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628847752, "dur":1483, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628852007, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Timeline\\CinemachineTrack.cs" }}
,{ "pid":12345, "tid":5, "ts":1752056628849236, "dur":3762, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628852999, "dur":2577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628855577, "dur":565, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628856143, "dur":2260, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628858404, "dur":1291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628859703, "dur":172, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628859875, "dur":4488, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628864364, "dur":118991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628983364, "dur":10559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Cinemachine.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1752056628993925, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628994028, "dur":6416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056629000468, "dur":100, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056629029659, "dur":131, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056629000574, "dur":29690, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":5, "ts":1752056629030358, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056629030694, "dur":3190, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":5, "ts":1752056629033888, "dur":642142, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628809007, "dur":30665, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628839676, "dur":935, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628840612, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628840697, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628840811, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628840899, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628840995, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628841107, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628841223, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628841548, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628841655, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628841748, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628841848, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628841998, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628842106, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628842217, "dur":150, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628842370, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628842451, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628842619, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628842711, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628842861, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628843013, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628843562, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_67A92BD346026DED.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628843660, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628843762, "dur":208, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_67A92BD346026DED.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1752056628843975, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628844050, "dur":432, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":6, "ts":1752056628844533, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628844648, "dur":947, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628845596, "dur":2343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628847940, "dur":970, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628848911, "dur":2749, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628852143, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\ILayerable.cs" }}
,{ "pid":12345, "tid":6, "ts":1752056628852926, "dur":1736, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Extensions\\TrackExtensions.cs" }}
,{ "pid":12345, "tid":6, "ts":1752056628851661, "dur":4678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628856340, "dur":2286, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628858627, "dur":1679, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628860306, "dur":4095, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628864401, "dur":118970, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628983373, "dur":4900, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1752056628988274, "dur":4661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628992974, "dur":7486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056629029664, "dur":204, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056629000612, "dur":30511, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1752056629031126, "dur":251897, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056629286299, "dur":614, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":6, "ts":1752056629286914, "dur":2948, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":6, "ts":1752056629289863, "dur":132, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":6, "ts":1752056629283025, "dur":6975, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056629290001, "dur":386052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628809047, "dur":30659, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628839712, "dur":958, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628840671, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628840784, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628840873, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628841068, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628841209, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628841328, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628841544, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628841631, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628841801, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628841893, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628842017, "dur":144, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1752056628842164, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628842311, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628842443, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628842517, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628842628, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628842953, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628843018, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628843211, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628843271, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628843416, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628843487, "dur":177, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628843668, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628843748, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1752056628843827, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628843881, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628843938, "dur":581, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628844520, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/981101458197481158.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628844610, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628844681, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/981101458197481158.rsp" }}
,{ "pid":12345, "tid":7, "ts":1752056628844769, "dur":2401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628847171, "dur":2304, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628851999, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\ConfinerOven.cs" }}
,{ "pid":12345, "tid":7, "ts":1752056628849476, "dur":3336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628855450, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Debugging\\DebugUpdater.cs" }}
,{ "pid":12345, "tid":7, "ts":1752056628852813, "dur":3232, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628856046, "dur":671, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628856718, "dur":1888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628858607, "dur":1237, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628859860, "dur":2646, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628862508, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628862635, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628862694, "dur":607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1752056628863301, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628863460, "dur":894, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628864355, "dur":2945, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628867302, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1752056628867438, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628867499, "dur":115982, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628983484, "dur":10802, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1752056628994289, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628994381, "dur":6082, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056629000478, "dur":274, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056629029999, "dur":55, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056629000760, "dur":29652, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":7, "ts":1752056629030422, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056629030580, "dur":2994, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":7, "ts":1752056629033577, "dur":642478, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628809103, "dur":30616, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628839725, "dur":812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628840538, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628840630, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628840781, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628840877, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628840983, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628841060, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628841120, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628841246, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628841533, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628841699, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628841768, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628841850, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_C9E7F9647D79AD26.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628841916, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628842018, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628842124, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628842238, "dur":274, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_661F12A898C05437.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628842513, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628842677, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628842779, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628842868, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628842990, "dur":797, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628843813, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628843995, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628844103, "dur":15406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1752056628859511, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628859710, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628859841, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628860071, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628860154, "dur":1296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1752056628861452, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628861643, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628861839, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628861910, "dur":708, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1752056628862619, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628862771, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628862938, "dur":1204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1752056628864143, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628864349, "dur":235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1752056628864585, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628864669, "dur":918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1752056628865588, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628865806, "dur":117623, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628983441, "dur":8123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1752056628991566, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628991664, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628992069, "dur":10040, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1752056629002119, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056629002294, "dur":29261, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":8, "ts":1752056629031558, "dur":644415, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628809150, "dur":30583, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628839739, "dur":1012, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628840752, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628840867, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628840983, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628841335, "dur":222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628841558, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628841694, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628841786, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628841863, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":9, "ts":1752056628841943, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628842009, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628842113, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628842206, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628842311, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628842436, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628842564, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628842649, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628842754, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628842834, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628842931, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628843237, "dur":114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628843367, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628843444, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1752056628843510, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628843602, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628843653, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628843748, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp" }}
,{ "pid":12345, "tid":9, "ts":1752056628843827, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628843892, "dur":93, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp" }}
,{ "pid":12345, "tid":9, "ts":1752056628843988, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628844067, "dur":362, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.rsp2" }}
,{ "pid":12345, "tid":9, "ts":1752056628844432, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/8048311097819667973.rsp" }}
,{ "pid":12345, "tid":9, "ts":1752056628844540, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628844621, "dur":1030, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628845652, "dur":2001, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628847654, "dur":2589, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628850244, "dur":505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628850750, "dur":597, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628851348, "dur":541, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628851891, "dur":909, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Textures\\PowerOfTwoTextureAtlas.cs" }}
,{ "pid":12345, "tid":9, "ts":1752056628854478, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\STP\\ISTPEnabledRenderPipeline.cs" }}
,{ "pid":12345, "tid":9, "ts":1752056628851890, "dur":4245, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628856663, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collections\\Unity.Collections\\NativeStream.cs" }}
,{ "pid":12345, "tid":9, "ts":1752056628856136, "dur":2575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628858712, "dur":257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628858970, "dur":66, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628859037, "dur":59, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628859096, "dur":55, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628859152, "dur":70, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628859222, "dur":624, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628859848, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1752056628860058, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628860129, "dur":1152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1752056628861283, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628861488, "dur":2873, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628864362, "dur":118978, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628983343, "dur":8672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1752056628992018, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628992846, "dur":7551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056629000467, "dur":2452, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":9, "ts":1752056629002927, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056629003034, "dur":29281, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.pdb" }}
,{ "pid":12345, "tid":9, "ts":1752056629032318, "dur":643644, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628809227, "dur":30525, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628839758, "dur":1021, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628840780, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628840889, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628840996, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628841099, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628841217, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628841298, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628841535, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628841669, "dur":140, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628841830, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628841933, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628842039, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628842153, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628842297, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628842392, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628842496, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628842759, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E50243D2AE5AE01D.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628842856, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843030, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.rsp" }}
,{ "pid":12345, "tid":10, "ts":1752056628843116, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843241, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843413, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843562, "dur":193, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1752056628843771, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843889, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628843996, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628844125, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628844218, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.rsp" }}
,{ "pid":12345, "tid":10, "ts":1752056628844301, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628844391, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15201509376401771623.rsp" }}
,{ "pid":12345, "tid":10, "ts":1752056628844486, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/3359792699429921086.rsp" }}
,{ "pid":12345, "tid":10, "ts":1752056628844613, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628844708, "dur":981, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628845690, "dur":2145, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628847836, "dur":2227, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628852103, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachineSequencerCamera.cs" }}
,{ "pid":12345, "tid":10, "ts":1752056628850064, "dur":3461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628854342, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Runtime\\Utils\\HaltonSeq.cs" }}
,{ "pid":12345, "tid":10, "ts":1752056628853526, "dur":3092, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628856619, "dur":2406, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628859026, "dur":51, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628859125, "dur":51, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628859209, "dur":634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628859848, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628860069, "dur":1048, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628861129, "dur":1242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628862372, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628862499, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628862613, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628862675, "dur":1707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628864383, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628864598, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628864720, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628864817, "dur":1471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628866289, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628866431, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628866538, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628866636, "dur":517, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628867154, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628867293, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1752056628867464, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628867968, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628868076, "dur":823, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1752056628868958, "dur":114420, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628983387, "dur":9082, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1752056628992470, "dur":1043, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628993526, "dur":6886, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056629000566, "dur":135, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056629000704, "dur":3703, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1752056629004424, "dur":381, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056629004871, "dur":27860, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":10, "ts":1752056629032735, "dur":643281, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628809271, "dur":30494, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628839769, "dur":1039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628840809, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628840937, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628841040, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628841122, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628841226, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628841308, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628841561, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628841636, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628841715, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628841814, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628841924, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628842029, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628842146, "dur":191, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628842341, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628842547, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628842633, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_57801C939D0EB92F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628842731, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628842830, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B37C4FB38B0D23A8.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628842945, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628843136, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628843190, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628843293, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628843369, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628843545, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628843677, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628843756, "dur":111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628843873, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628843950, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628844062, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628844175, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628844236, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628844371, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628844444, "dur":187, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":11, "ts":1752056628844633, "dur":959, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628845593, "dur":2006, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628847600, "dur":2785, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628850386, "dur":494, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628850880, "dur":603, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628851484, "dur":579, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628852064, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Lighting\\ProbeVolume\\ProbeVolume.cs" }}
,{ "pid":12345, "tid":11, "ts":1752056628852063, "dur":2680, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628854744, "dur":421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628855166, "dur":491, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628855658, "dur":553, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628857479, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\Substring.cs" }}
,{ "pid":12345, "tid":11, "ts":1752056628856212, "dur":2492, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628858705, "dur":677, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628859383, "dur":465, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628859850, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1752056628860037, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628860111, "dur":982, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1752056628861094, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628861231, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628861298, "dur":3109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628864407, "dur":118961, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628983380, "dur":4869, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1752056628988251, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628989103, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628991361, "dur":10505, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":11, "ts":1752056629001874, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056629002029, "dur":29177, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":11, "ts":1752056629031209, "dur":258799, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056629290009, "dur":386014, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628809327, "dur":30452, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628839785, "dur":775, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628840561, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628840667, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628840719, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628840827, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628840966, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628841077, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628841206, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628841315, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628841509, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628841611, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628841701, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628841828, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628841947, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628842016, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628842091, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628842178, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628842343, "dur":358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D79DA43A6C4320A9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628842702, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628842854, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628842979, "dur":362, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628843428, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628843736, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628843847, "dur":155, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":12, "ts":1752056628844008, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628844116, "dur":624, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":12, "ts":1752056628844741, "dur":961, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628845703, "dur":1990, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628847694, "dur":3526, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628851221, "dur":588, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628852098, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\BezierCurve.cs" }}
,{ "pid":12345, "tid":12, "ts":1752056628851812, "dur":2841, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628854654, "dur":417, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628855072, "dur":443, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628855516, "dur":589, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628858081, "dur":912, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collections\\Unity.Collections\\UnsafeHashSet.cs" }}
,{ "pid":12345, "tid":12, "ts":1752056628856106, "dur":3177, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628859283, "dur":574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628859859, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1752056628860089, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628860173, "dur":1025, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1752056628861199, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628861357, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628861436, "dur":2920, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628864357, "dur":119007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628983368, "dur":8387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1752056628991756, "dur":1009, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628992791, "dur":7640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056629000498, "dur":30286, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":12, "ts":1752056629030825, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056629031252, "dur":3213, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb" }}
,{ "pid":12345, "tid":12, "ts":1752056629034468, "dur":641545, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628809378, "dur":30419, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628839803, "dur":872, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628840676, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628840837, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628840945, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628841073, "dur":112, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628841193, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628841296, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628841555, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628841663, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628841752, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628841850, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628842024, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628842158, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628842274, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628842430, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628842578, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628842682, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628842924, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1752056628843021, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843239, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843504, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843623, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843707, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843818, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.rsp" }}
,{ "pid":12345, "tid":13, "ts":1752056628843879, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628843944, "dur":302, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.rsp" }}
,{ "pid":12345, "tid":13, "ts":1752056628844248, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp" }}
,{ "pid":12345, "tid":13, "ts":1752056628844304, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628844384, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/11761956694902994387.rsp" }}
,{ "pid":12345, "tid":13, "ts":1752056628844446, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628844534, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/6508951009339132853.rsp" }}
,{ "pid":12345, "tid":13, "ts":1752056628844595, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628844687, "dur":860, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628845548, "dur":2230, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628847779, "dur":3155, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628850934, "dur":624, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628852025, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\TimelineUndo.cs" }}
,{ "pid":12345, "tid":13, "ts":1752056628851559, "dur":1971, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628853531, "dur":2461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628855993, "dur":694, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628856688, "dur":1722, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628858412, "dur":1332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628859744, "dur":141, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628859886, "dur":4486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628864375, "dur":123933, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628988313, "dur":5101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1752056628993416, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628993569, "dur":6889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056629003635, "dur":53, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056629000518, "dur":3216, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1752056629003774, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056629032288, "dur":51, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056629004028, "dur":28329, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.pdb" }}
,{ "pid":12345, "tid":13, "ts":1752056629032360, "dur":643729, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628809459, "dur":30357, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628839821, "dur":905, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628840728, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628840829, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628840935, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628841198, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628841291, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628841374, "dur":244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628841619, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628841831, "dur":122, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628841956, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628842020, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628842111, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628842217, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628842358, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628842624, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628842715, "dur":213, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1752056628842929, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628843150, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.rsp" }}
,{ "pid":12345, "tid":14, "ts":1752056628843218, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628843386, "dur":79, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.rsp" }}
,{ "pid":12345, "tid":14, "ts":1752056628843468, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628843642, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628843757, "dur":149, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":14, "ts":1752056628843917, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628844009, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628844079, "dur":249, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp2" }}
,{ "pid":12345, "tid":14, "ts":1752056628844342, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628844422, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/6330438945516616120.rsp" }}
,{ "pid":12345, "tid":14, "ts":1752056628844524, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628844658, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628844761, "dur":1482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628846244, "dur":406, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628846651, "dur":2503, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628851962, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\GPUDriven\\GPUResidentBatcher.cs" }}
,{ "pid":12345, "tid":14, "ts":1752056628849155, "dur":3628, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628853902, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerToggle.cs" }}
,{ "pid":12345, "tid":14, "ts":1752056628852784, "dur":3073, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628855858, "dur":770, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628857530, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\Views\\ActionMapsView.cs" }}
,{ "pid":12345, "tid":14, "ts":1752056628856628, "dur":2782, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628859411, "dur":470, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628859881, "dur":4503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628864385, "dur":118963, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628983350, "dur":5504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1752056628988856, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628988940, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628989844, "dur":11055, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":14, "ts":1752056629000911, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056629001197, "dur":29202, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":14, "ts":1752056629030410, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056629030589, "dur":4084, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":14, "ts":1752056629034675, "dur":641363, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628809517, "dur":30314, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628839837, "dur":918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628840756, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628841048, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628841176, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628841269, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628841512, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628841615, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628841707, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628841776, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628841859, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628841922, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628842010, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842078, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842199, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628842311, "dur":153, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842467, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842579, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628842670, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842786, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628842899, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628842982, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1752056628843085, "dur":473, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628843577, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628843734, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628843837, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":15, "ts":1752056628843953, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628844025, "dur":322, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":15, "ts":1752056628844349, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.rsp" }}
,{ "pid":12345, "tid":15, "ts":1752056628844404, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628844494, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/125927855240037756.rsp" }}
,{ "pid":12345, "tid":15, "ts":1752056628844546, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628845248, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"Assets\\RealisticCarControllerV4\\Scripts\\RCC_UI_DecalSetLocation.cs" }}
,{ "pid":12345, "tid":15, "ts":1752056628844679, "dur":1423, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628846102, "dur":482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628846585, "dur":2112, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628848698, "dur":539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628849238, "dur":2687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628852110, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderPipeline\\IVolumetricCloud.cs" }}
,{ "pid":12345, "tid":15, "ts":1752056628851926, "dur":3509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628855436, "dur":601, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628856038, "dur":721, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628856760, "dur":2693, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628859454, "dur":419, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628859874, "dur":4513, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628864388, "dur":119063, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628983455, "dur":9927, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":15, "ts":1752056628993384, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628993452, "dur":6983, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056629000462, "dur":70, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056629000539, "dur":2751, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":15, "ts":1752056629003303, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056629003568, "dur":28390, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":15, "ts":1752056629031961, "dur":643998, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628809565, "dur":30283, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628839854, "dur":882, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628840737, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628840847, "dur":75, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628840924, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628841033, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628841219, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628841331, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628841541, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628841679, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628841767, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628841869, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628841955, "dur":304, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628842263, "dur":382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628842646, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628842894, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628843045, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628843314, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628843558, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628843647, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":16, "ts":1752056628843729, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628843874, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628843950, "dur":320, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1752056628844272, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp" }}
,{ "pid":12345, "tid":16, "ts":1752056628844327, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628844413, "dur":175, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp" }}
,{ "pid":12345, "tid":16, "ts":1752056628844634, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628844730, "dur":961, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628845692, "dur":2142, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628847835, "dur":2349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628850185, "dur":508, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628850694, "dur":571, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628851266, "dur":538, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628852121, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\SplineContainer.cs" }}
,{ "pid":12345, "tid":16, "ts":1752056628853269, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\PropertyAttributes.cs" }}
,{ "pid":12345, "tid":16, "ts":1752056628853833, "dur":1035, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.splines\\Runtime\\MathUtility.cs" }}
,{ "pid":12345, "tid":16, "ts":1752056628851805, "dur":3907, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628855713, "dur":662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628856376, "dur":1935, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628858312, "dur":1305, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628859617, "dur":223, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628859845, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1752056628860072, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628860145, "dur":1233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1752056628861379, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628861563, "dur":2827, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628864393, "dur":119013, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628983411, "dur":10772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1752056628994185, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628994320, "dur":6154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056629000564, "dur":51, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056629000621, "dur":3775, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":16, "ts":1752056629004411, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056629004791, "dur":27004, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":16, "ts":1752056629031798, "dur":644157, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628809642, "dur":30224, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628839873, "dur":709, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628840583, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628840678, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628840788, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628840997, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628841107, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628841524, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628841623, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628841705, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628841796, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628841884, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628842007, "dur":233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628842241, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628842382, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AA29B6A11E640A63.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628842490, "dur":441, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628842969, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1752056628843083, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628843156, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628843261, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628843484, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628843795, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628843897, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844102, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844189, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":17, "ts":1752056628844252, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844320, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844463, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844570, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13763130038827339108.rsp" }}
,{ "pid":12345, "tid":17, "ts":1752056628844632, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628844701, "dur":1041, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628845742, "dur":742, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628846485, "dur":1836, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628848321, "dur":477, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628848799, "dur":1332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628850913, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachineClearShot.cs" }}
,{ "pid":12345, "tid":17, "ts":1752056628851789, "dur":997, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachineBrain.cs" }}
,{ "pid":12345, "tid":17, "ts":1752056628850132, "dur":2928, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628853061, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Common\\RemoveRange.Extensions.cs" }}
,{ "pid":12345, "tid":17, "ts":1752056628855258, "dur":943, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Common\\IAdditionalData.cs" }}
,{ "pid":12345, "tid":17, "ts":1752056628853061, "dur":4239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628857301, "dur":2238, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628859540, "dur":331, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628859872, "dur":4524, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628864396, "dur":118955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628983355, "dur":10415, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":17, "ts":1752056628993772, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628993882, "dur":6607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056629000585, "dur":3628, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":17, "ts":1752056629004247, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056629004801, "dur":28440, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":17, "ts":1752056629033244, "dur":642822, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628809693, "dur":30190, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628839889, "dur":830, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628840722, "dur":371, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628841102, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628841227, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628841554, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628841623, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628841766, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628841894, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628842057, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4AB7AB16DC48CC38.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628842163, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628842265, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628842363, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628842541, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628842659, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628842737, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_61F1B4EFC6980E0D.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628842829, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628842957, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628843050, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628843274, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628843391, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":18, "ts":1752056628843505, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628843618, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628843670, "dur":249, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_B7135FF78EF23DD9.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628843923, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628844040, "dur":70, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2" }}
,{ "pid":12345, "tid":18, "ts":1752056628844134, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628844200, "dur":304, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":18, "ts":1752056628844507, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13525290774574426104.rsp" }}
,{ "pid":12345, "tid":18, "ts":1752056628844623, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628844744, "dur":976, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628845721, "dur":2107, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628848522, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEngine.TestRunner\\UnityTestProtocol\\TestFinishMessage.cs" }}
,{ "pid":12345, "tid":18, "ts":1752056628850323, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs" }}
,{ "pid":12345, "tid":18, "ts":1752056628847829, "dur":3571, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628851401, "dur":643, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628852045, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\PostProcessing\\IPostProcessComponent.cs" }}
,{ "pid":12345, "tid":18, "ts":1752056628852045, "dur":3892, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628855938, "dur":731, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628856670, "dur":2341, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628859012, "dur":301, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628859314, "dur":536, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628859852, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628860103, "dur":657, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1752056628860761, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628861073, "dur":3279, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628864356, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1752056628864556, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628864647, "dur":840, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1752056628865489, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628865640, "dur":117777, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628983419, "dur":6976, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":18, "ts":1752056628990398, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628990566, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628990705, "dur":11522, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":18, "ts":1752056629002247, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056629002531, "dur":28841, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":18, "ts":1752056629031375, "dur":644595, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628809749, "dur":30148, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628839902, "dur":687, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628840590, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628840683, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628840742, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628840851, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628840984, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628841094, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628841226, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628841337, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628841543, "dur":53, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628841599, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628841715, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628841806, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628841912, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628842006, "dur":94, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628842104, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628842209, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628842349, "dur":397, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628842747, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628842809, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628842876, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C511BE0634F804BB.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628842997, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628843232, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628843443, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628843698, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628843806, "dur":1030, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":19, "ts":1752056628847030, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":19, "ts":1752056628844837, "dur":3073, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628847911, "dur":2469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628850381, "dur":571, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628850953, "dur":614, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628851835, "dur":979, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\AnimationPreviewUtilities.cs" }}
,{ "pid":12345, "tid":19, "ts":1752056628853705, "dur":930, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelineAsset.cs" }}
,{ "pid":12345, "tid":19, "ts":1752056628851568, "dur":4877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628856447, "dur":1957, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628858405, "dur":1392, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628859859, "dur":1792, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628861653, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.dll.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1752056628861843, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628861921, "dur":616, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.dll (+2 others)" }}
,{ "pid":12345, "tid":19, "ts":1752056628862538, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628862715, "dur":1662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628864378, "dur":119068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628983450, "dur":9714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":19, "ts":1752056628993167, "dur":843, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628994046, "dur":6419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056629000566, "dur":205, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056629000784, "dur":29509, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":19, "ts":1752056629030351, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056629030543, "dur":2834, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":19, "ts":1752056629033380, "dur":642669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628809820, "dur":30092, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628839917, "dur":738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628840656, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628840822, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628840999, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628841131, "dur":106, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628841242, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628841352, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628841649, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628841745, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628841851, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628841963, "dur":215, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":20, "ts":1752056628842183, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_9EE980C253D6A9A7.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628842286, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628842528, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628842640, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628842888, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628842972, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628843260, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628843547, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628843628, "dur":15905, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056628859535, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628859712, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628859788, "dur":73, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628859861, "dur":2682, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628862545, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628862744, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628862802, "dur":837, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056628863640, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628863789, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628864021, "dur":790, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056628864812, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628864955, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1752056628865187, "dur":588, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056628865775, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628865863, "dur":583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":20, "ts":1752056628866495, "dur":125, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628867031, "dur":97030, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":20, "ts":1752056628983343, "dur":9949, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":20, "ts":1752056628993294, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628993378, "dur":7101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056629000569, "dur":223, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056629029714, "dur":911, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056629000798, "dur":30082, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":20, "ts":1752056629030890, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056629031240, "dur":4030, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":20, "ts":1752056629035273, "dur":640788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628809864, "dur":30061, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628839930, "dur":674, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628840605, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628840712, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628840814, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628840947, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628841054, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628841190, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628841252, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628841352, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628841565, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628841659, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628841738, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628841820, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628841917, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628841986, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628842054, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628842138, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628842244, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628842333, "dur":247, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628842581, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628842785, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F825EB11843D5C3C.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628842894, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628843039, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628843234, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628843438, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628843930, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628844010, "dur":217, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp2" }}
,{ "pid":12345, "tid":21, "ts":1752056628844228, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp" }}
,{ "pid":12345, "tid":21, "ts":1752056628844289, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628844360, "dur":104, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp" }}
,{ "pid":12345, "tid":21, "ts":1752056628844465, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/11295223868419055766.rsp" }}
,{ "pid":12345, "tid":21, "ts":1752056628844561, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628844640, "dur":979, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628845619, "dur":2254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628847874, "dur":2942, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628850817, "dur":595, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628851413, "dur":530, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628852128, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderGraph\\RenderGraph.cs" }}
,{ "pid":12345, "tid":21, "ts":1752056628852790, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderGraph\\RenderGraph.Compiler.cs" }}
,{ "pid":12345, "tid":21, "ts":1752056628851944, "dur":3476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628855421, "dur":571, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628855992, "dur":652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628856773, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\Commands\\ControlSchemeCommands.cs" }}
,{ "pid":12345, "tid":21, "ts":1752056628856645, "dur":2590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628859236, "dur":631, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628859868, "dur":4481, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628864354, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1752056628864556, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628864652, "dur":573, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1752056628865225, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628865387, "dur":118046, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628983436, "dur":10553, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Splines.dll (+pdb)" }}
,{ "pid":12345, "tid":21, "ts":1752056628994047, "dur":6359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056629000555, "dur":173, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056629000736, "dur":4535, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Splines.dll" }}
,{ "pid":12345, "tid":21, "ts":1752056629005280, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056629005503, "dur":28364, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":21, "ts":1752056629033870, "dur":642136, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628809911, "dur":30025, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628839942, "dur":686, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628840629, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628840814, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628840943, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628841021, "dur":121, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628841145, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628841258, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628841606, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628841725, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628841814, "dur":110, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628841927, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628842024, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628842116, "dur":205, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628842325, "dur":517, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628842843, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628842973, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628843064, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628843161, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628843319, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628843521, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":22, "ts":1752056628843637, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628843766, "dur":258, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":22, "ts":1752056628844027, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628844100, "dur":273, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp2" }}
,{ "pid":12345, "tid":22, "ts":1752056628844377, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628844468, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13155616310095448955.rsp" }}
,{ "pid":12345, "tid":22, "ts":1752056628844597, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628844676, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13155616310095448955.rsp" }}
,{ "pid":12345, "tid":22, "ts":1752056628844755, "dur":881, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628845637, "dur":2393, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628848143, "dur":520, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628848664, "dur":486, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628849150, "dur":2722, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628851872, "dur":915, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Volume\\VolumeComponent.EditorOnly.cs" }}
,{ "pid":12345, "tid":22, "ts":1752056628851872, "dur":3790, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628855663, "dur":611, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628856275, "dur":1725, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628858001, "dur":1804, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628859856, "dur":385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1752056628860242, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628860380, "dur":653, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":22, "ts":1752056628861034, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628861206, "dur":3175, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628864381, "dur":119058, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628983446, "dur":6470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":22, "ts":1752056628989918, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628990163, "dur":798, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628991017, "dur":10497, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":22, "ts":1752056629001523, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056629001782, "dur":28676, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":22, "ts":1752056629030467, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056629030687, "dur":3107, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":22, "ts":1752056629033797, "dur":642196, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628809949, "dur":30002, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628839957, "dur":764, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628840722, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628840845, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628840945, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628841075, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628841190, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628841349, "dur":244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628841594, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628841666, "dur":167, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628841843, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628841984, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628842047, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628842190, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628842280, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628842350, "dur":149, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628842502, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628842610, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628842801, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1752056628842933, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843043, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843133, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843252, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843340, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843522, "dur":120, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":23, "ts":1752056628843645, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.rsp" }}
,{ "pid":12345, "tid":23, "ts":1752056628843708, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843805, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843878, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628843949, "dur":213, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2" }}
,{ "pid":12345, "tid":23, "ts":1752056628844181, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628844260, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":23, "ts":1752056628844322, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628844408, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15228710169658269968.rsp" }}
,{ "pid":12345, "tid":23, "ts":1752056628844471, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628844578, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/9432179616866573143.rsp" }}
,{ "pid":12345, "tid":23, "ts":1752056628844657, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628844782, "dur":2698, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628849346, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\RequirePlatformSupportAttribute.cs" }}
,{ "pid":12345, "tid":23, "ts":1752056628850567, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\GUI\\Views\\TestListGUIBase.cs" }}
,{ "pid":12345, "tid":23, "ts":1752056628847480, "dur":3630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628851111, "dur":763, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628851875, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Utilities\\GPUPrefixSum\\GPUPrefixSum.ShaderIDs.cs" }}
,{ "pid":12345, "tid":23, "ts":1752056628854116, "dur":1323, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Utilities\\BatchRendererGroupGlobals.cs" }}
,{ "pid":12345, "tid":23, "ts":1752056628851875, "dur":4210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628856086, "dur":595, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628856681, "dur":2251, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628858999, "dur":368, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628859367, "dur":516, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628859884, "dur":4481, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628864366, "dur":118993, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628983383, "dur":9520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":23, "ts":1752056628992904, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628993036, "dur":7360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056629000463, "dur":200, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056629000675, "dur":41157, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":23, "ts":1752056629041866, "dur":634120, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628810007, "dur":29957, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628839966, "dur":662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628840629, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628840730, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628840831, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628841063, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628841209, "dur":302, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628841519, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628841625, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628841760, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628841876, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628841973, "dur":227, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628842204, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628842320, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628842400, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628842483, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628842614, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628842705, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_DC6D18346848EB0E.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628842811, "dur":359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628843181, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628843399, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628843493, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":24, "ts":1752056628843560, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628843630, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.rsp" }}
,{ "pid":12345, "tid":24, "ts":1752056628843698, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628843758, "dur":127, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.rsp" }}
,{ "pid":12345, "tid":24, "ts":1752056628843888, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628844017, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628844089, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":24, "ts":1752056628844154, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628844220, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.rsp" }}
,{ "pid":12345, "tid":24, "ts":1752056628844300, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628844381, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":24, "ts":1752056628844553, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628844635, "dur":925, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628845561, "dur":1837, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628848133, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestLaunchers\\TestLauncherBase.cs" }}
,{ "pid":12345, "tid":24, "ts":1752056628847399, "dur":2509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628852085, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Components\\CinemachineRotateWithFollowTarget.cs" }}
,{ "pid":12345, "tid":24, "ts":1752056628849909, "dur":3156, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628854492, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":24, "ts":1752056628853066, "dur":2771, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628855837, "dur":695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628856533, "dur":1776, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628858310, "dur":1417, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628859727, "dur":136, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628859863, "dur":2915, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628862780, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1752056628862894, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628862953, "dur":589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":24, "ts":1752056628863543, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628863639, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628863701, "dur":666, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628864372, "dur":119039, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628983414, "dur":9348, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":24, "ts":1752056628992764, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628992878, "dur":7521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056629000537, "dur":268, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056629000809, "dur":30772, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":24, "ts":1752056629031583, "dur":644369, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056629711809, "dur":7677, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11572, "tid": 4822, "ts": 1752056629724747, "dur": 533, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629725368, "dur": 7914, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11572, "tid": 4822, "ts": 1752056629720849, "dur": 12478, "ph": "X", "name": "Write chrome-trace events", "args": {} },
