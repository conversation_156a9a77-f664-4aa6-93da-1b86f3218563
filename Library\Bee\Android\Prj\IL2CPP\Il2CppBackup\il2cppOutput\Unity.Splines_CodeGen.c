﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void BezierCurve__ctor_m1C544723045F9DCE5B08353AB5055378C093AB15 (void);
extern void BezierCurve__ctor_m3CFBCD60FCE57921D800FB28E912A4D563C25E3A (void);
extern void BezierCurve_Equals_mDA4EE12C81DC76D11465A44BFD53E8ADDBFB8282 (void);
extern void BezierCurve_Equals_mE91B7BFB60AF0923F1AC868E19A62B803341BADF (void);
extern void BezierCurve_GetHashCode_mC5545401839CFBD84C98814A8F16A3C54851AE8B (void);
extern void BezierKnot__ctor_mCA9E68FFC4131E58A86B5F18E9A2C0E9E14735DC (void);
extern void BezierKnot__ctor_m8ADF3DADA826DCC898ED2E3C372772CF49E550B6 (void);
extern void BezierKnot_Transform_mCEE833536057769D54966A96E95D94CAFCFE818D (void);
extern void BezierKnot_BakeTangentDirectionToRotation_m8F7764F0651E04A0673EED8ADE98A0642A9DD3A1 (void);
extern void BezierKnot_OnBeforeSerialize_mF1E04FF2DEC07549A50007B8A3C6B584567699B0 (void);
extern void BezierKnot_OnAfterDeserialize_mC3747A23181CFA89C94AD5D49472DA75DEC9B5E3 (void);
extern void BezierKnot_ToString_mA5C1A411AAB07C4FD27828196DCFFBBBAA607A7F (void);
extern void BezierKnot_Equals_m321FC0E108FC61ADAE24D865A9951DF09AF62C0B (void);
extern void BezierKnot_Equals_m494E1B07278E6038D738588C8889EDED427652B3 (void);
extern void BezierKnot_GetHashCode_m4FF60F61DD0FD8B1503DC0A2A07C1173937575F8 (void);
extern void CurveUtility_EvaluatePosition_mA0BE42D108515C581DDE156FE1C0D7E275675FAD (void);
extern void CurveUtility_EvaluateTangent_m6EA4B13C7792E18A79CFF52E589861A6CCB678AC (void);
extern void CurveUtility_EvaluateAcceleration_m31A0A519960972A4DDFCF4284453BF1F6AAB76E0 (void);
extern void CurveUtility_CalculateCurveLengths_m94A91434788712D0C704758B1837C30765FA892E (void);
extern void CurveUtility_CalculateCurveLengths_m4E24DCFC7F33EDB0E9DBE9313FE5DB337CE73887 (void);
extern void CurveUtility_Approximately_m1CF08A4A8026824CDF807E0CD2F7A8C110DABC74 (void);
extern void CurveUtility_EvaluateUpVectors_m9C8183C7E3E60B06AF561AAE7956CA0952A6A96A (void);
extern void CurveUtility_EvaluateUpVector_m753E63BA7C6C982AAE1CFC9D3C239BA485EEBBB6 (void);
extern void CurveUtility_GetNextRotationMinimizingFrame_m2D6DF717BDFE412914159E4F72DA9CA954A2440B (void);
extern void CurveUtility__cctor_m6703E027E640B625F3C18A9AA86590BB4A4960C4 (void);
extern void DistanceToInterpolation__cctor_mE1749BCB48E4163A024B55E3292E27A4BEEEC8A7 (void);
extern void KnotLinkCollection_GetKnotLinksInternal_m3AC381467EB3CA8BC1EB24697EA5E94F8CF33A6D (void);
extern void KnotLinkCollection_TryGetKnotLinks_m3BB720E3EF49A6BF83E61B6058AF558D19929353 (void);
extern void KnotLinkCollection_Unlink_m23B63CA7E429CC871BD113BCE2BEEF307EEB67B3 (void);
extern void KnotLinkCollection_KnotRemoved_m60E3070D00A314EFEA09ED68C98211BE8F027030 (void);
extern void KnotLinkCollection_KnotRemoved_mE46CE98F497A0EB4915678B0B9AFA5CC8F8F39E8 (void);
extern void KnotLinkCollection_KnotInserted_m047AAF13BF91AA20314B43A26B5457572D2E8933 (void);
extern void KnotLinkCollection_KnotInserted_m63428A481F65181BECA383C42E536CF750C15661 (void);
extern void KnotLinkCollection_ShiftKnotIndices_m1F4AD9CF0DCAE1068BD51DE02AEA15B7EA095FCD (void);
extern void KnotLinkCollection__ctor_mBCDE4AB160EED9AE40E279CD1869F1E3FAE54BBF (void);
extern void KnotLink_GetEnumerator_m3FB111C3ACBCFBCB37FBD7C700E896DD92F56D02 (void);
extern void KnotLink_System_Collections_IEnumerable_GetEnumerator_m0C1FD9F48929B8259E1D11641CB014F18D012998 (void);
extern void KnotLink_get_Count_m94E152D680EAEEE40DEE4BCBED4DDF031864038E (void);
extern void KnotLink_get_Item_m504A5E99886B4AA3580B464276B605E4113105E8 (void);
extern void KnotLink__ctor_m9FDE95DF8BBC716FF3A08ACDFF222ED48AD280F4 (void);
extern void MathUtility_All_m509B69F5A620AD0D0EA1C5774AE992979E423E3C (void);
extern void NativeSpline_get_Closed_m8CBE4D0A5A2EA8A70EE42FA88FD5AC4A4CAFA241 (void);
extern void NativeSpline_get_Count_m25E371A5A76FACA52EC4B0D4945C3E06817F3814 (void);
extern void NativeSpline_GetLength_m2D57F97F17FD46AC6BCE6080B823D65D5C4462DE (void);
extern void NativeSpline_get_Item_mB1C95195B66AE2752AE9198669A4FB1DB5D5AC1C (void);
extern void NativeSpline_GetEnumerator_mDE3AE45B6DA5B17805F9B494983CA97445EC1373 (void);
extern void NativeSpline_System_Collections_IEnumerable_GetEnumerator_m9CE978E7E8E64428D4A4C5FFAE74F71703414686 (void);
extern void NativeSpline__ctor_mE5C316FFE4FEECD7E5F6E7EEAE3FC185DBA41645 (void);
extern void NativeSpline__ctor_m2298B6EC4E853BBBBB7CC13D333C9F9A544AF50C (void);
extern void NativeSpline__ctor_m60C4B48216F28D77598B5340D2F1880D80331EA1 (void);
extern void NativeSpline_GetCurve_m721D3FCF52F045376791025E9D69533506226DA2 (void);
extern void NativeSpline_GetCurveLength_m982AB89F9074C260DD4D7A334B1BC2AD8C8299D4 (void);
extern void NativeSpline_GetCurveUpVector_m34AC1408432D36A5A6A3495A9085197476DC9A71 (void);
extern void NativeSpline_Dispose_mFB66BE97D01248DE027168A27B31D1D186B6CB7A (void);
extern void NativeSpline_GetCurveInterpolation_mCC5CF636B99BA4DA99765B75674E6C2CB5CF15C0 (void);
extern void Spline_get_embeddedSplineData_m15F18609F5628E9B6A2A67FB00BA961A0E8D983A (void);
extern void Spline_get_Count_mFE728C6E6C081A89BB9286F36FA22DD728CA1AC5 (void);
extern void Spline_get_IsReadOnly_mAF205C5EF447D2AFE0762B606E524A6A52262742 (void);
extern void Spline_add_Changed_mA95AFC4E706B6D31D7E1A6DA414EC2ECA3765A62 (void);
extern void Spline_remove_Changed_mC33996D7BF8FC6D4FDFABEF70861F8B83F806B07 (void);
extern void Spline_SetDirtyNoNotify_mA6AD6EA47A917363AE2F1B7B9A08928CB0ADC9BC (void);
extern void Spline_SetDirty_m6326C753EB75CC045C3F535F9AFF8E20C2A24CDB (void);
extern void Spline_OnSplineChanged_m325ABDB26E16A6B93F5413CB9603A69C0F9A2BC6 (void);
extern void Spline_EnsureMetaDataValid_m6B34F5A628617649BD90397F23690E627FDE6B34 (void);
extern void Spline_GetTangentMode_mDD3E69D7599CF42518E844AFD7B565BD597238A6 (void);
extern void Spline_SetTangentMode_mA5E280D4663CB87BD2BD26837AC7AFC07DC06B25 (void);
extern void Spline_SetTangentMode_m7D909416CCAC1CF10901A3E360DE0DE8D83B9D79 (void);
extern void Spline_SetTangentModeNoNotify_m238C211DF84C92F3DA110173F66AD5B4E89155A0 (void);
extern void Spline_ApplyTangentModeNoNotify_m6A6A57812D01BC0FE3BAF39F1D269CFE63FBB769 (void);
extern void Spline_get_Closed_m7017D6D64D461D2AFAA4984D4266184FF8D8CD1A (void);
extern void Spline_IndexOf_mFE51228E8653E44AC16F5B667FFD74A4AB57EFBB (void);
extern void Spline_Insert_mCD9F4A9B4C656167F7DC2C2D7C22FD01A671D0F1 (void);
extern void Spline_Insert_m8B3FA5B3E7285F75CA447A9D5D1ED3B655A52BA7 (void);
extern void Spline_Insert_m5409E8430A560BDEE4EDEA74D26BB84C07239C91 (void);
extern void Spline_InsertNoNotify_mD6155534AA7464DD354BC651798BBDAA2AD2CD0C (void);
extern void Spline_RemoveAt_m95A3C8A939849C0B772FC18874D1DC5A0EA8E597 (void);
extern void Spline_get_Item_mA0C0C35EC12F200181EC88C838B86CF79379774F (void);
extern void Spline_set_Item_m851C213A9F832E797ACF84E44DCDEB6BBD9ACDA4 (void);
extern void Spline_SetKnot_m7279FF945E34A20667BE3DE234D206C9A2EFCEAC (void);
extern void Spline_SetKnotNoNotify_m305C6D5F018E98CB5AE1022F0F1549EDE7C56C8F (void);
extern void Spline__ctor_mBEB56A7CCFAB5E8C174EA0A0CB13D9E2C253EFC6 (void);
extern void Spline_GetCurve_mDAD6A01E094E60B0F7ACFEA80874FCE08018BEBC (void);
extern void Spline_GetCurveLength_mF9113BF29AFEBCD666D6736793FF241F336E1E00 (void);
extern void Spline_GetLength_m3346863AB71A554F8103E9868E80D609B219F177 (void);
extern void Spline_GetCurveDistanceLut_mC4E91B8723E122AED787A95063718D25097FDBA4 (void);
extern void Spline_GetCurveInterpolation_m24D3C16CD13CC1DCA5B71F83E4E18C39F8085944 (void);
extern void Spline_WarmUpCurveUps_m8822941D5666E1831F2AB1E7676CC9BF2320B36A (void);
extern void Spline_GetCurveUpVector_m5694904B2052028C7C6C3A2D3FB83E44B02A07C3 (void);
extern void Spline_Warmup_m65098704AA45C515895E9A4CD0D0A1822EBEAD3F (void);
extern void Spline_GetEnumerator_mF44DF4CBD06A927BDEB10C783E627149DA4A53A2 (void);
extern void Spline_System_Collections_IEnumerable_GetEnumerator_m0EE193F76C902BE519A212B852DD5372958BD983 (void);
extern void Spline_Add_m0C608DD5E668A43F21E18443FD5FBF8251D29A8A (void);
extern void Spline_Add_mE9447E2111379DCF6BAF6A8343F13B2B1643AC23 (void);
extern void Spline_Clear_m0D46A50D666F719CDD484CD47AF557E727ADC96F (void);
extern void Spline_Contains_m9DCBF23E55C26AD115FEB377240D7A5E117535A0 (void);
extern void Spline_CopyTo_m6912B870D57338C89D8466D2C6C1CB161FCF2CA5 (void);
extern void Spline_Remove_mDC2E54284F4A328FC3908A83DEF1CDD721083176 (void);
extern void Spline_CacheKnotOperationCurves_m91823774C291552AB7BFB65FF1A64B18A0B2824D (void);
extern void MetaData_get_DistanceToInterpolation_m2C1FF82E185A4E8CC9052A6EF30496D7819ACC45 (void);
extern void MetaData_get_UpVectors_m74D0D4581E2A45300000054954C515E6928CA4EF (void);
extern void MetaData__ctor_mB713B7E092C6DBFBC132B96BBF7EFD1770A19022 (void);
extern void MetaData_InvalidateCache_m9F04B2C14BF165C440E16063E6E32337A2C40882 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15__ctor_m44FE69081C8C5173A5867F0033F992B6633BABF8 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_IDisposable_Dispose_mBCF036DE8EB2C5DB632E769B038108B721B1902B (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_MoveNext_m6E862A5D59CDFC34B761B9E36DF3D14659D0D1CB (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally1_mC66CE3BBB5DFEF79C0CC5B67DB0070F3CD11799E (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally2_mB1DE10E0A831DA5141659C95FD4A939A9470B818 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally3_m5C8959F8CCB9DDCAD387056487D1571F32F76364 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally4_m5CD923E59D5BE47469C67B71CC82EBDDD7B7BB44 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_Collections_Generic_IEnumeratorU3CUnityEngine_Splines_ISplineModificationHandlerU3E_get_Current_m8F043EC146504DE3F52AB198193D2F363B33080A (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerator_Reset_m22D964F6A7BBF7EB6501A6D5970AC46A744FEC5E (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerator_get_Current_m47366C215CED3B036777DDB7463AC08E7746C922 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_Collections_Generic_IEnumerableU3CUnityEngine_Splines_ISplineModificationHandlerU3E_GetEnumerator_m58846E4F3DD604B13948D4B8D81EB1DAB9AE4283 (void);
extern void U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerable_GetEnumerator_m2A09454780B06CFED484C7B8FB2592C049B6A44A (void);
extern void SplineContainer_add_SplineAdded_m2A588B4AE632F4FF44C34304DD43E90B78B18A33 (void);
extern void SplineContainer_remove_SplineAdded_m04AA622DB167729EAFB40684C24F831ED52FF75B (void);
extern void SplineContainer_add_SplineRemoved_mBF6A5F7B163DB7D6F5E68D359707B7AD48DE68E6 (void);
extern void SplineContainer_remove_SplineRemoved_mDB2FEBCCF163B6C0AC891E0CD4F01FAAAEA4988E (void);
extern void SplineContainer_add_SplineReordered_m4E78E2E6C6EEC6A1F35499863E550C9B42B115C9 (void);
extern void SplineContainer_remove_SplineReordered_mDD2AFD712E74B176D412CA153C94BA69804131F0 (void);
extern void SplineContainer_get_Splines_m436AE59D526EC597F3EC8F0B0454F9A64EEF7F77 (void);
extern void SplineContainer_set_Splines_m90498594C12F41154102FF342DA295C5621E7BBD (void);
extern void SplineContainer_IndexOf_m73F04B4EB156B8AA823C04089606BE595F913A3B (void);
extern void SplineContainer_get_KnotLinkCollection_mBC2EB1AD4AD0208B484BF5E9041356A532BF87C6 (void);
extern void SplineContainer_get_Item_m18C3254B90E943F8ED7FEFA4DB78679AFC51FB9F (void);
extern void SplineContainer_OnEnable_m3A02E5C5D54C59A9DE374EBDC7ED9FA8919745A8 (void);
extern void SplineContainer_OnDisable_m63C34CC0B1B9E7D6E693DE4A30794F4EBEB2E224 (void);
extern void SplineContainer_OnDestroy_m4896A1073D7CDB06FBD234AF57E96534E77A96B6 (void);
extern void SplineContainer_Warmup_mD060F0862BEA4205E191A27069B3645782C12AC7 (void);
extern void SplineContainer_ClearCaches_mFCD3954DD1259445E4003F3BE5DD5535B4F8C17D (void);
extern void SplineContainer_DisposeNativeSplinesCache_m77BDB7E25BAFC017525CA0B22EDB0534197F440A (void);
extern void SplineContainer_OnSplineChanged_m297E7C58808DFF8CA496CD45920FAA15A304F3FC (void);
extern void SplineContainer_OnKnotModified_m76C358866E6427DF7C5EE8C9C4C1F55B938FF052 (void);
extern void SplineContainer_get_IsNonUniformlyScaled_m6B3E17C7FCD5891B29941CF24F05B720E1A175F2 (void);
extern void SplineContainer_get_Spline_m7029D34D27F67B91152B2D1FA1ABDDC1FE3CA1AB (void);
extern void SplineContainer_set_Spline_m2F50C26D7C8272CCD9C98BD9537554F818F6E8EE (void);
extern void SplineContainer_Evaluate_mC05A1E24916A25DDEEE68A39F1ACD7D8C60002F6 (void);
extern void SplineContainer_Evaluate_mDFD216A18D0A2BADCD875A6B7BDE355F9E40E608 (void);
extern void SplineContainer_EvaluatePosition_m5D376C35BAF7E999403ACBDA82DC28CA45A7EE52 (void);
extern void SplineContainer_EvaluatePosition_m8E70656D386D0B4050169BAF42E6B8C7FF34CFD4 (void);
extern void SplineContainer_EvaluateTangent_m6B54F4213DCACBE9320964CC27B775AFEAFE9369 (void);
extern void SplineContainer_EvaluateTangent_m25A3456AFD2897ADB6B6EF92922B7E7535740FCF (void);
extern void SplineContainer_EvaluateUpVector_mFACF694C89F2AE91CEBF47F1BEF3716E235ED927 (void);
extern void SplineContainer_EvaluateUpVector_m5A32895E1AEC93EE94DDDE419137276271064F0E (void);
extern void SplineContainer_EvaluateAcceleration_mB9A784E7FB7BD8CB46E303DE5399EF5A72023F73 (void);
extern void SplineContainer_EvaluateAcceleration_m22785C0665D26C86298234BB32A9CEB2C50BB3A3 (void);
extern void SplineContainer_CalculateLength_mEA0C49EBF4B7490732E1A30D1B188B09CCB9337B (void);
extern void SplineContainer_CalculateLength_mDC00BD74D458C0C21B038B1A525187AA3AAA986A (void);
extern void SplineContainer_OnBeforeSerialize_m6BAC7C338B1EF74D802D99EC4A4C7CCA11589852 (void);
extern void SplineContainer_OnAfterDeserialize_mCDFFFA75C56E0630E6D7B28E4F9C4ABA0980E155 (void);
extern void SplineContainer__ctor_m26A95C21DDC590929A1EF35C638EEE7F6A2599FC (void);
extern void U3CU3Ec__DisplayClass21_0__ctor_m0CDB4A96B9613A576BC22CC673A4EA8F008BBEA4 (void);
extern void U3CU3Ec__DisplayClass21_0_U3Cset_SplinesU3Eb__0_m61BF92E4A3E6327422BF68EE9195250739E92485 (void);
extern void SplineKnotIndex__ctor_m5285C9FF855A849F46154817A5603BCA5AEEA89E (void);
extern void SplineKnotIndex_Equals_mA31ECC7A6CAC3E5E028A6349344845A0C9B4B8AB (void);
extern void SplineKnotIndex_Equals_mE695B045700A70C6E31AF9051FE42AFBD1D5095E (void);
extern void SplineKnotIndex_GetHashCode_m300A8FFA4CBA8246F1C17549B6F9662FF33BAF66 (void);
extern void SplineKnotIndex_ToString_m37121F48A4DE10B2B87A7F6D5BA6435F6B36018D (void);
extern void SplineKnotIndex__cctor_m0130A2AF4A67BDD693F56138D6A82BFAF06EA434 (void);
extern void SplineMath_PointLineNearestPoint_mF8D6A16025555A72FECCF80B756AA1370C212AAC (void);
extern void SplineModificationData__ctor_m7EC9C675F49DB3843FA55A98DA8065D0747A9105 (void);
extern void SplineRange_get_Start_m85D0F4480493D66E109CFDC0DC2867B796DA23B3 (void);
extern void SplineRange_get_End_mFDEA15F9DBE6BAB51D2C3EC483EAB3A7DD21ADAB (void);
extern void SplineRange_get_Count_m71E1A150F7E1896F91C2CE285E2CB86473D675A4 (void);
extern void SplineRange_get_Direction_mB267FF0827A7AB70131F2C98301A4DBD6E8D0A1C (void);
extern void SplineRange__ctor_m06EFDF2A13B91C2664C843DDE5DBBD9027D1D7D7 (void);
extern void SplineRange__ctor_mBD384AF9E33176CD8412B30D5B650EA2D3597271 (void);
extern void SplineRange_get_Item_m99AAFB6C1F7595D844E1D8AC912B7AC818F57BEF (void);
extern void SplineRange_GetEnumerator_m18E1A3F37D70D222E02E6403A09923E527FD2718 (void);
extern void SplineRange_System_Collections_IEnumerable_GetEnumerator_mBC4ECC83BCA21FB318F73DA436D3A5F4EF9A054A (void);
extern void SplineRange_ToString_mD54B16AEBFDC7F3B63897117F64C675011682C32 (void);
extern void SplineRangeEnumerator_MoveNext_mD7F160934A6C7FC1A43387F1F02A3B082878A36B (void);
extern void SplineRangeEnumerator_Reset_m239822992C54B013C18EF864BC10CAC75F65D9F6 (void);
extern void SplineRangeEnumerator_get_Current_m878CFC6620D283BD9B083C75F75D0557DC05DF56 (void);
extern void SplineRangeEnumerator_System_Collections_IEnumerator_get_Current_mCCF53031B3BE064CAFD663BD491CEB6FFC412778 (void);
extern void SplineRangeEnumerator__ctor_m301A9C9AB2BD0D0476E759E10D16BFAC666402E5 (void);
extern void SplineRangeEnumerator_Dispose_m64415DB83EC462A2CE734557A84BE4EDFBA08A8D (void);
extern void SplineUtility_GetSubdivisionCount_m6AF53FEA69AAE85344725F75CAF2ACB65C7BC1B5 (void);
extern void SplineUtility_WrapInterpolation_m5B4F6DE0F91482077554C7DFA70A47E4400E4C52 (void);
extern void SplineUtility_PreviousIndex_m03C304E723FFE789AB50A11FC5BD1C4F0BE88C23 (void);
extern void SplineUtility_NextIndex_mE588D0FF2C3D18ADB4B6A5A1078B434448805318 (void);
extern void SplineUtility_GetExplicitLinearTangent_m9E57982B7246B2251D9F417FE0255D4331E438CD (void);
extern void SplineUtility_GetExplicitLinearTangent_m22F7D0CEBC584A92CA887BFD9E4F78B158A71586 (void);
extern void SplineUtility_GetAutoSmoothTangent_mCF7C902C19BED3F656B558802437836AEBC5701E (void);
extern void SplineUtility_GetAutoSmoothKnot_m12A864C65151AC303E027658729DEC3B24663A5D (void);
extern void SplineUtility_GetKnotRotation_m63F705BAE45CDDE11F86D7AFCD47A7BE9448DD1D (void);
extern void Segment__ctor_m4C11441126261ECF07E724888C6CDBF9910BA73E (void);
static Il2CppMethodPointer s_methodPointers[276] = 
{
	NULL,
	NULL,
	BezierCurve__ctor_m1C544723045F9DCE5B08353AB5055378C093AB15,
	BezierCurve__ctor_m3CFBCD60FCE57921D800FB28E912A4D563C25E3A,
	BezierCurve_Equals_mDA4EE12C81DC76D11465A44BFD53E8ADDBFB8282,
	BezierCurve_Equals_mE91B7BFB60AF0923F1AC868E19A62B803341BADF,
	BezierCurve_GetHashCode_mC5545401839CFBD84C98814A8F16A3C54851AE8B,
	BezierKnot__ctor_mCA9E68FFC4131E58A86B5F18E9A2C0E9E14735DC,
	BezierKnot__ctor_m8ADF3DADA826DCC898ED2E3C372772CF49E550B6,
	BezierKnot_Transform_mCEE833536057769D54966A96E95D94CAFCFE818D,
	BezierKnot_BakeTangentDirectionToRotation_m8F7764F0651E04A0673EED8ADE98A0642A9DD3A1,
	BezierKnot_OnBeforeSerialize_mF1E04FF2DEC07549A50007B8A3C6B584567699B0,
	BezierKnot_OnAfterDeserialize_mC3747A23181CFA89C94AD5D49472DA75DEC9B5E3,
	BezierKnot_ToString_mA5C1A411AAB07C4FD27828196DCFFBBBAA607A7F,
	BezierKnot_Equals_m321FC0E108FC61ADAE24D865A9951DF09AF62C0B,
	BezierKnot_Equals_m494E1B07278E6038D738588C8889EDED427652B3,
	BezierKnot_GetHashCode_m4FF60F61DD0FD8B1503DC0A2A07C1173937575F8,
	CurveUtility_EvaluatePosition_mA0BE42D108515C581DDE156FE1C0D7E275675FAD,
	CurveUtility_EvaluateTangent_m6EA4B13C7792E18A79CFF52E589861A6CCB678AC,
	CurveUtility_EvaluateAcceleration_m31A0A519960972A4DDFCF4284453BF1F6AAB76E0,
	CurveUtility_CalculateCurveLengths_m94A91434788712D0C704758B1837C30765FA892E,
	CurveUtility_CalculateCurveLengths_m4E24DCFC7F33EDB0E9DBE9313FE5DB337CE73887,
	CurveUtility_Approximately_m1CF08A4A8026824CDF807E0CD2F7A8C110DABC74,
	CurveUtility_EvaluateUpVectors_m9C8183C7E3E60B06AF561AAE7956CA0952A6A96A,
	CurveUtility_EvaluateUpVector_m753E63BA7C6C982AAE1CFC9D3C239BA485EEBBB6,
	CurveUtility_GetNextRotationMinimizingFrame_m2D6DF717BDFE412914159E4F72DA9CA954A2440B,
	NULL,
	CurveUtility__cctor_m6703E027E640B625F3C18A9AA86590BB4A4960C4,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DistanceToInterpolation__cctor_mE1749BCB48E4163A024B55E3292E27A4BEEEC8A7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	KnotLinkCollection_GetKnotLinksInternal_m3AC381467EB3CA8BC1EB24697EA5E94F8CF33A6D,
	KnotLinkCollection_TryGetKnotLinks_m3BB720E3EF49A6BF83E61B6058AF558D19929353,
	KnotLinkCollection_Unlink_m23B63CA7E429CC871BD113BCE2BEEF307EEB67B3,
	KnotLinkCollection_KnotRemoved_m60E3070D00A314EFEA09ED68C98211BE8F027030,
	KnotLinkCollection_KnotRemoved_mE46CE98F497A0EB4915678B0B9AFA5CC8F8F39E8,
	KnotLinkCollection_KnotInserted_m047AAF13BF91AA20314B43A26B5457572D2E8933,
	KnotLinkCollection_KnotInserted_m63428A481F65181BECA383C42E536CF750C15661,
	KnotLinkCollection_ShiftKnotIndices_m1F4AD9CF0DCAE1068BD51DE02AEA15B7EA095FCD,
	KnotLinkCollection__ctor_mBCDE4AB160EED9AE40E279CD1869F1E3FAE54BBF,
	KnotLink_GetEnumerator_m3FB111C3ACBCFBCB37FBD7C700E896DD92F56D02,
	KnotLink_System_Collections_IEnumerable_GetEnumerator_m0C1FD9F48929B8259E1D11641CB014F18D012998,
	KnotLink_get_Count_m94E152D680EAEEE40DEE4BCBED4DDF031864038E,
	KnotLink_get_Item_m504A5E99886B4AA3580B464276B605E4113105E8,
	KnotLink__ctor_m9FDE95DF8BBC716FF3A08ACDFF222ED48AD280F4,
	MathUtility_All_m509B69F5A620AD0D0EA1C5774AE992979E423E3C,
	NativeSpline_get_Closed_m8CBE4D0A5A2EA8A70EE42FA88FD5AC4A4CAFA241,
	NativeSpline_get_Count_m25E371A5A76FACA52EC4B0D4945C3E06817F3814,
	NativeSpline_GetLength_m2D57F97F17FD46AC6BCE6080B823D65D5C4462DE,
	NativeSpline_get_Item_mB1C95195B66AE2752AE9198669A4FB1DB5D5AC1C,
	NativeSpline_GetEnumerator_mDE3AE45B6DA5B17805F9B494983CA97445EC1373,
	NativeSpline_System_Collections_IEnumerable_GetEnumerator_m9CE978E7E8E64428D4A4C5FFAE74F71703414686,
	NativeSpline__ctor_mE5C316FFE4FEECD7E5F6E7EEAE3FC185DBA41645,
	NativeSpline__ctor_m2298B6EC4E853BBBBB7CC13D333C9F9A544AF50C,
	NativeSpline__ctor_m60C4B48216F28D77598B5340D2F1880D80331EA1,
	NativeSpline_GetCurve_m721D3FCF52F045376791025E9D69533506226DA2,
	NativeSpline_GetCurveLength_m982AB89F9074C260DD4D7A334B1BC2AD8C8299D4,
	NativeSpline_GetCurveUpVector_m34AC1408432D36A5A6A3495A9085197476DC9A71,
	NativeSpline_Dispose_mFB66BE97D01248DE027168A27B31D1D186B6CB7A,
	NativeSpline_GetCurveInterpolation_mCC5CF636B99BA4DA99765B75674E6C2CB5CF15C0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Spline_get_embeddedSplineData_m15F18609F5628E9B6A2A67FB00BA961A0E8D983A,
	Spline_get_Count_mFE728C6E6C081A89BB9286F36FA22DD728CA1AC5,
	Spline_get_IsReadOnly_mAF205C5EF447D2AFE0762B606E524A6A52262742,
	Spline_add_Changed_mA95AFC4E706B6D31D7E1A6DA414EC2ECA3765A62,
	Spline_remove_Changed_mC33996D7BF8FC6D4FDFABEF70861F8B83F806B07,
	Spline_SetDirtyNoNotify_mA6AD6EA47A917363AE2F1B7B9A08928CB0ADC9BC,
	Spline_SetDirty_m6326C753EB75CC045C3F535F9AFF8E20C2A24CDB,
	Spline_OnSplineChanged_m325ABDB26E16A6B93F5413CB9603A69C0F9A2BC6,
	Spline_EnsureMetaDataValid_m6B34F5A628617649BD90397F23690E627FDE6B34,
	Spline_GetTangentMode_mDD3E69D7599CF42518E844AFD7B565BD597238A6,
	Spline_SetTangentMode_mA5E280D4663CB87BD2BD26837AC7AFC07DC06B25,
	Spline_SetTangentMode_m7D909416CCAC1CF10901A3E360DE0DE8D83B9D79,
	Spline_SetTangentModeNoNotify_m238C211DF84C92F3DA110173F66AD5B4E89155A0,
	Spline_ApplyTangentModeNoNotify_m6A6A57812D01BC0FE3BAF39F1D269CFE63FBB769,
	Spline_get_Closed_m7017D6D64D461D2AFAA4984D4266184FF8D8CD1A,
	Spline_IndexOf_mFE51228E8653E44AC16F5B667FFD74A4AB57EFBB,
	Spline_Insert_mCD9F4A9B4C656167F7DC2C2D7C22FD01A671D0F1,
	Spline_Insert_m8B3FA5B3E7285F75CA447A9D5D1ED3B655A52BA7,
	Spline_Insert_m5409E8430A560BDEE4EDEA74D26BB84C07239C91,
	Spline_InsertNoNotify_mD6155534AA7464DD354BC651798BBDAA2AD2CD0C,
	Spline_RemoveAt_m95A3C8A939849C0B772FC18874D1DC5A0EA8E597,
	Spline_get_Item_mA0C0C35EC12F200181EC88C838B86CF79379774F,
	Spline_set_Item_m851C213A9F832E797ACF84E44DCDEB6BBD9ACDA4,
	Spline_SetKnot_m7279FF945E34A20667BE3DE234D206C9A2EFCEAC,
	Spline_SetKnotNoNotify_m305C6D5F018E98CB5AE1022F0F1549EDE7C56C8F,
	Spline__ctor_mBEB56A7CCFAB5E8C174EA0A0CB13D9E2C253EFC6,
	Spline_GetCurve_mDAD6A01E094E60B0F7ACFEA80874FCE08018BEBC,
	Spline_GetCurveLength_mF9113BF29AFEBCD666D6736793FF241F336E1E00,
	Spline_GetLength_m3346863AB71A554F8103E9868E80D609B219F177,
	Spline_GetCurveDistanceLut_mC4E91B8723E122AED787A95063718D25097FDBA4,
	Spline_GetCurveInterpolation_m24D3C16CD13CC1DCA5B71F83E4E18C39F8085944,
	Spline_WarmUpCurveUps_m8822941D5666E1831F2AB1E7676CC9BF2320B36A,
	Spline_GetCurveUpVector_m5694904B2052028C7C6C3A2D3FB83E44B02A07C3,
	Spline_Warmup_m65098704AA45C515895E9A4CD0D0A1822EBEAD3F,
	Spline_GetEnumerator_mF44DF4CBD06A927BDEB10C783E627149DA4A53A2,
	Spline_System_Collections_IEnumerable_GetEnumerator_m0EE193F76C902BE519A212B852DD5372958BD983,
	Spline_Add_m0C608DD5E668A43F21E18443FD5FBF8251D29A8A,
	Spline_Add_mE9447E2111379DCF6BAF6A8343F13B2B1643AC23,
	Spline_Clear_m0D46A50D666F719CDD484CD47AF557E727ADC96F,
	Spline_Contains_m9DCBF23E55C26AD115FEB377240D7A5E117535A0,
	Spline_CopyTo_m6912B870D57338C89D8466D2C6C1CB161FCF2CA5,
	Spline_Remove_mDC2E54284F4A328FC3908A83DEF1CDD721083176,
	Spline_CacheKnotOperationCurves_m91823774C291552AB7BFB65FF1A64B18A0B2824D,
	MetaData_get_DistanceToInterpolation_m2C1FF82E185A4E8CC9052A6EF30496D7819ACC45,
	MetaData_get_UpVectors_m74D0D4581E2A45300000054954C515E6928CA4EF,
	MetaData__ctor_mB713B7E092C6DBFBC132B96BBF7EFD1770A19022,
	MetaData_InvalidateCache_m9F04B2C14BF165C440E16063E6E32337A2C40882,
	U3Cget_embeddedSplineDataU3Ed__15__ctor_m44FE69081C8C5173A5867F0033F992B6633BABF8,
	U3Cget_embeddedSplineDataU3Ed__15_System_IDisposable_Dispose_mBCF036DE8EB2C5DB632E769B038108B721B1902B,
	U3Cget_embeddedSplineDataU3Ed__15_MoveNext_m6E862A5D59CDFC34B761B9E36DF3D14659D0D1CB,
	U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally1_mC66CE3BBB5DFEF79C0CC5B67DB0070F3CD11799E,
	U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally2_mB1DE10E0A831DA5141659C95FD4A939A9470B818,
	U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally3_m5C8959F8CCB9DDCAD387056487D1571F32F76364,
	U3Cget_embeddedSplineDataU3Ed__15_U3CU3Em__Finally4_m5CD923E59D5BE47469C67B71CC82EBDDD7B7BB44,
	U3Cget_embeddedSplineDataU3Ed__15_System_Collections_Generic_IEnumeratorU3CUnityEngine_Splines_ISplineModificationHandlerU3E_get_Current_m8F043EC146504DE3F52AB198193D2F363B33080A,
	U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerator_Reset_m22D964F6A7BBF7EB6501A6D5970AC46A744FEC5E,
	U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerator_get_Current_m47366C215CED3B036777DDB7463AC08E7746C922,
	U3Cget_embeddedSplineDataU3Ed__15_System_Collections_Generic_IEnumerableU3CUnityEngine_Splines_ISplineModificationHandlerU3E_GetEnumerator_m58846E4F3DD604B13948D4B8D81EB1DAB9AE4283,
	U3Cget_embeddedSplineDataU3Ed__15_System_Collections_IEnumerable_GetEnumerator_m2A09454780B06CFED484C7B8FB2592C049B6A44A,
	SplineContainer_add_SplineAdded_m2A588B4AE632F4FF44C34304DD43E90B78B18A33,
	SplineContainer_remove_SplineAdded_m04AA622DB167729EAFB40684C24F831ED52FF75B,
	SplineContainer_add_SplineRemoved_mBF6A5F7B163DB7D6F5E68D359707B7AD48DE68E6,
	SplineContainer_remove_SplineRemoved_mDB2FEBCCF163B6C0AC891E0CD4F01FAAAEA4988E,
	SplineContainer_add_SplineReordered_m4E78E2E6C6EEC6A1F35499863E550C9B42B115C9,
	SplineContainer_remove_SplineReordered_mDD2AFD712E74B176D412CA153C94BA69804131F0,
	SplineContainer_get_Splines_m436AE59D526EC597F3EC8F0B0454F9A64EEF7F77,
	SplineContainer_set_Splines_m90498594C12F41154102FF342DA295C5621E7BBD,
	SplineContainer_IndexOf_m73F04B4EB156B8AA823C04089606BE595F913A3B,
	SplineContainer_get_KnotLinkCollection_mBC2EB1AD4AD0208B484BF5E9041356A532BF87C6,
	SplineContainer_get_Item_m18C3254B90E943F8ED7FEFA4DB78679AFC51FB9F,
	SplineContainer_OnEnable_m3A02E5C5D54C59A9DE374EBDC7ED9FA8919745A8,
	SplineContainer_OnDisable_m63C34CC0B1B9E7D6E693DE4A30794F4EBEB2E224,
	SplineContainer_OnDestroy_m4896A1073D7CDB06FBD234AF57E96534E77A96B6,
	SplineContainer_Warmup_mD060F0862BEA4205E191A27069B3645782C12AC7,
	SplineContainer_ClearCaches_mFCD3954DD1259445E4003F3BE5DD5535B4F8C17D,
	SplineContainer_DisposeNativeSplinesCache_m77BDB7E25BAFC017525CA0B22EDB0534197F440A,
	SplineContainer_OnSplineChanged_m297E7C58808DFF8CA496CD45920FAA15A304F3FC,
	SplineContainer_OnKnotModified_m76C358866E6427DF7C5EE8C9C4C1F55B938FF052,
	SplineContainer_get_IsNonUniformlyScaled_m6B3E17C7FCD5891B29941CF24F05B720E1A175F2,
	SplineContainer_get_Spline_m7029D34D27F67B91152B2D1FA1ABDDC1FE3CA1AB,
	SplineContainer_set_Spline_m2F50C26D7C8272CCD9C98BD9537554F818F6E8EE,
	SplineContainer_Evaluate_mC05A1E24916A25DDEEE68A39F1ACD7D8C60002F6,
	SplineContainer_Evaluate_mDFD216A18D0A2BADCD875A6B7BDE355F9E40E608,
	NULL,
	SplineContainer_EvaluatePosition_m5D376C35BAF7E999403ACBDA82DC28CA45A7EE52,
	SplineContainer_EvaluatePosition_m8E70656D386D0B4050169BAF42E6B8C7FF34CFD4,
	NULL,
	SplineContainer_EvaluateTangent_m6B54F4213DCACBE9320964CC27B775AFEAFE9369,
	SplineContainer_EvaluateTangent_m25A3456AFD2897ADB6B6EF92922B7E7535740FCF,
	NULL,
	SplineContainer_EvaluateUpVector_mFACF694C89F2AE91CEBF47F1BEF3716E235ED927,
	SplineContainer_EvaluateUpVector_m5A32895E1AEC93EE94DDDE419137276271064F0E,
	NULL,
	SplineContainer_EvaluateAcceleration_mB9A784E7FB7BD8CB46E303DE5399EF5A72023F73,
	SplineContainer_EvaluateAcceleration_m22785C0665D26C86298234BB32A9CEB2C50BB3A3,
	NULL,
	SplineContainer_CalculateLength_mEA0C49EBF4B7490732E1A30D1B188B09CCB9337B,
	SplineContainer_CalculateLength_mDC00BD74D458C0C21B038B1A525187AA3AAA986A,
	SplineContainer_OnBeforeSerialize_m6BAC7C338B1EF74D802D99EC4A4C7CCA11589852,
	SplineContainer_OnAfterDeserialize_mCDFFFA75C56E0630E6D7B28E4F9C4ABA0980E155,
	NULL,
	SplineContainer__ctor_m26A95C21DDC590929A1EF35C638EEE7F6A2599FC,
	U3CU3Ec__DisplayClass21_0__ctor_m0CDB4A96B9613A576BC22CC673A4EA8F008BBEA4,
	U3CU3Ec__DisplayClass21_0_U3Cset_SplinesU3Eb__0_m61BF92E4A3E6327422BF68EE9195250739E92485,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SplineKnotIndex__ctor_m5285C9FF855A849F46154817A5603BCA5AEEA89E,
	SplineKnotIndex_Equals_mA31ECC7A6CAC3E5E028A6349344845A0C9B4B8AB,
	SplineKnotIndex_Equals_mE695B045700A70C6E31AF9051FE42AFBD1D5095E,
	SplineKnotIndex_GetHashCode_m300A8FFA4CBA8246F1C17549B6F9662FF33BAF66,
	SplineKnotIndex_ToString_m37121F48A4DE10B2B87A7F6D5BA6435F6B36018D,
	SplineKnotIndex__cctor_m0130A2AF4A67BDD693F56138D6A82BFAF06EA434,
	SplineMath_PointLineNearestPoint_mF8D6A16025555A72FECCF80B756AA1370C212AAC,
	SplineModificationData__ctor_m7EC9C675F49DB3843FA55A98DA8065D0747A9105,
	SplineRange_get_Start_m85D0F4480493D66E109CFDC0DC2867B796DA23B3,
	SplineRange_get_End_mFDEA15F9DBE6BAB51D2C3EC483EAB3A7DD21ADAB,
	SplineRange_get_Count_m71E1A150F7E1896F91C2CE285E2CB86473D675A4,
	SplineRange_get_Direction_mB267FF0827A7AB70131F2C98301A4DBD6E8D0A1C,
	SplineRange__ctor_m06EFDF2A13B91C2664C843DDE5DBBD9027D1D7D7,
	SplineRange__ctor_mBD384AF9E33176CD8412B30D5B650EA2D3597271,
	SplineRange_get_Item_m99AAFB6C1F7595D844E1D8AC912B7AC818F57BEF,
	SplineRange_GetEnumerator_m18E1A3F37D70D222E02E6403A09923E527FD2718,
	SplineRange_System_Collections_IEnumerable_GetEnumerator_mBC4ECC83BCA21FB318F73DA436D3A5F4EF9A054A,
	SplineRange_ToString_mD54B16AEBFDC7F3B63897117F64C675011682C32,
	SplineRangeEnumerator_MoveNext_mD7F160934A6C7FC1A43387F1F02A3B082878A36B,
	SplineRangeEnumerator_Reset_m239822992C54B013C18EF864BC10CAC75F65D9F6,
	SplineRangeEnumerator_get_Current_m878CFC6620D283BD9B083C75F75D0557DC05DF56,
	SplineRangeEnumerator_System_Collections_IEnumerator_get_Current_mCCF53031B3BE064CAFD663BD491CEB6FFC412778,
	SplineRangeEnumerator__ctor_m301A9C9AB2BD0D0476E759E10D16BFAC666402E5,
	SplineRangeEnumerator_Dispose_m64415DB83EC462A2CE734557A84BE4EDFBA08A8D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SplineUtility_GetSubdivisionCount_m6AF53FEA69AAE85344725F75CAF2ACB65C7BC1B5,
	NULL,
	NULL,
	NULL,
	NULL,
	SplineUtility_WrapInterpolation_m5B4F6DE0F91482077554C7DFA70A47E4400E4C52,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SplineUtility_PreviousIndex_m03C304E723FFE789AB50A11FC5BD1C4F0BE88C23,
	SplineUtility_NextIndex_mE588D0FF2C3D18ADB4B6A5A1078B434448805318,
	SplineUtility_GetExplicitLinearTangent_m9E57982B7246B2251D9F417FE0255D4331E438CD,
	SplineUtility_GetExplicitLinearTangent_m22F7D0CEBC584A92CA887BFD9E4F78B158A71586,
	SplineUtility_GetAutoSmoothTangent_mCF7C902C19BED3F656B558802437836AEBC5701E,
	SplineUtility_GetAutoSmoothKnot_m12A864C65151AC303E027658729DEC3B24663A5D,
	SplineUtility_GetKnotRotation_m63F705BAE45CDDE11F86D7AFCD47A7BE9448DD1D,
	NULL,
	NULL,
	Segment__ctor_m4C11441126261ECF07E724888C6CDBF9910BA73E,
};
extern void BezierCurve__ctor_m1C544723045F9DCE5B08353AB5055378C093AB15_AdjustorThunk (void);
extern void BezierCurve__ctor_m3CFBCD60FCE57921D800FB28E912A4D563C25E3A_AdjustorThunk (void);
extern void BezierCurve_Equals_mDA4EE12C81DC76D11465A44BFD53E8ADDBFB8282_AdjustorThunk (void);
extern void BezierCurve_Equals_mE91B7BFB60AF0923F1AC868E19A62B803341BADF_AdjustorThunk (void);
extern void BezierCurve_GetHashCode_mC5545401839CFBD84C98814A8F16A3C54851AE8B_AdjustorThunk (void);
extern void BezierKnot__ctor_mCA9E68FFC4131E58A86B5F18E9A2C0E9E14735DC_AdjustorThunk (void);
extern void BezierKnot__ctor_m8ADF3DADA826DCC898ED2E3C372772CF49E550B6_AdjustorThunk (void);
extern void BezierKnot_Transform_mCEE833536057769D54966A96E95D94CAFCFE818D_AdjustorThunk (void);
extern void BezierKnot_BakeTangentDirectionToRotation_m8F7764F0651E04A0673EED8ADE98A0642A9DD3A1_AdjustorThunk (void);
extern void BezierKnot_OnBeforeSerialize_mF1E04FF2DEC07549A50007B8A3C6B584567699B0_AdjustorThunk (void);
extern void BezierKnot_OnAfterDeserialize_mC3747A23181CFA89C94AD5D49472DA75DEC9B5E3_AdjustorThunk (void);
extern void BezierKnot_ToString_mA5C1A411AAB07C4FD27828196DCFFBBBAA607A7F_AdjustorThunk (void);
extern void BezierKnot_Equals_m321FC0E108FC61ADAE24D865A9951DF09AF62C0B_AdjustorThunk (void);
extern void BezierKnot_Equals_m494E1B07278E6038D738588C8889EDED427652B3_AdjustorThunk (void);
extern void BezierKnot_GetHashCode_m4FF60F61DD0FD8B1503DC0A2A07C1173937575F8_AdjustorThunk (void);
extern void NativeSpline_get_Closed_m8CBE4D0A5A2EA8A70EE42FA88FD5AC4A4CAFA241_AdjustorThunk (void);
extern void NativeSpline_get_Count_m25E371A5A76FACA52EC4B0D4945C3E06817F3814_AdjustorThunk (void);
extern void NativeSpline_GetLength_m2D57F97F17FD46AC6BCE6080B823D65D5C4462DE_AdjustorThunk (void);
extern void NativeSpline_get_Item_mB1C95195B66AE2752AE9198669A4FB1DB5D5AC1C_AdjustorThunk (void);
extern void NativeSpline_GetEnumerator_mDE3AE45B6DA5B17805F9B494983CA97445EC1373_AdjustorThunk (void);
extern void NativeSpline_System_Collections_IEnumerable_GetEnumerator_m9CE978E7E8E64428D4A4C5FFAE74F71703414686_AdjustorThunk (void);
extern void NativeSpline__ctor_mE5C316FFE4FEECD7E5F6E7EEAE3FC185DBA41645_AdjustorThunk (void);
extern void NativeSpline__ctor_m2298B6EC4E853BBBBB7CC13D333C9F9A544AF50C_AdjustorThunk (void);
extern void NativeSpline__ctor_m60C4B48216F28D77598B5340D2F1880D80331EA1_AdjustorThunk (void);
extern void NativeSpline_GetCurve_m721D3FCF52F045376791025E9D69533506226DA2_AdjustorThunk (void);
extern void NativeSpline_GetCurveLength_m982AB89F9074C260DD4D7A334B1BC2AD8C8299D4_AdjustorThunk (void);
extern void NativeSpline_GetCurveUpVector_m34AC1408432D36A5A6A3495A9085197476DC9A71_AdjustorThunk (void);
extern void NativeSpline_Dispose_mFB66BE97D01248DE027168A27B31D1D186B6CB7A_AdjustorThunk (void);
extern void NativeSpline_GetCurveInterpolation_mCC5CF636B99BA4DA99765B75674E6C2CB5CF15C0_AdjustorThunk (void);
extern void SplineKnotIndex__ctor_m5285C9FF855A849F46154817A5603BCA5AEEA89E_AdjustorThunk (void);
extern void SplineKnotIndex_Equals_mA31ECC7A6CAC3E5E028A6349344845A0C9B4B8AB_AdjustorThunk (void);
extern void SplineKnotIndex_Equals_mE695B045700A70C6E31AF9051FE42AFBD1D5095E_AdjustorThunk (void);
extern void SplineKnotIndex_GetHashCode_m300A8FFA4CBA8246F1C17549B6F9662FF33BAF66_AdjustorThunk (void);
extern void SplineKnotIndex_ToString_m37121F48A4DE10B2B87A7F6D5BA6435F6B36018D_AdjustorThunk (void);
extern void SplineModificationData__ctor_m7EC9C675F49DB3843FA55A98DA8065D0747A9105_AdjustorThunk (void);
extern void SplineRange_get_Start_m85D0F4480493D66E109CFDC0DC2867B796DA23B3_AdjustorThunk (void);
extern void SplineRange_get_End_mFDEA15F9DBE6BAB51D2C3EC483EAB3A7DD21ADAB_AdjustorThunk (void);
extern void SplineRange_get_Count_m71E1A150F7E1896F91C2CE285E2CB86473D675A4_AdjustorThunk (void);
extern void SplineRange_get_Direction_mB267FF0827A7AB70131F2C98301A4DBD6E8D0A1C_AdjustorThunk (void);
extern void SplineRange__ctor_m06EFDF2A13B91C2664C843DDE5DBBD9027D1D7D7_AdjustorThunk (void);
extern void SplineRange__ctor_mBD384AF9E33176CD8412B30D5B650EA2D3597271_AdjustorThunk (void);
extern void SplineRange_get_Item_m99AAFB6C1F7595D844E1D8AC912B7AC818F57BEF_AdjustorThunk (void);
extern void SplineRange_GetEnumerator_m18E1A3F37D70D222E02E6403A09923E527FD2718_AdjustorThunk (void);
extern void SplineRange_System_Collections_IEnumerable_GetEnumerator_mBC4ECC83BCA21FB318F73DA436D3A5F4EF9A054A_AdjustorThunk (void);
extern void SplineRange_ToString_mD54B16AEBFDC7F3B63897117F64C675011682C32_AdjustorThunk (void);
extern void SplineRangeEnumerator_MoveNext_mD7F160934A6C7FC1A43387F1F02A3B082878A36B_AdjustorThunk (void);
extern void SplineRangeEnumerator_Reset_m239822992C54B013C18EF864BC10CAC75F65D9F6_AdjustorThunk (void);
extern void SplineRangeEnumerator_get_Current_m878CFC6620D283BD9B083C75F75D0557DC05DF56_AdjustorThunk (void);
extern void SplineRangeEnumerator_System_Collections_IEnumerator_get_Current_mCCF53031B3BE064CAFD663BD491CEB6FFC412778_AdjustorThunk (void);
extern void SplineRangeEnumerator__ctor_m301A9C9AB2BD0D0476E759E10D16BFAC666402E5_AdjustorThunk (void);
extern void SplineRangeEnumerator_Dispose_m64415DB83EC462A2CE734557A84BE4EDFBA08A8D_AdjustorThunk (void);
extern void Segment__ctor_m4C11441126261ECF07E724888C6CDBF9910BA73E_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[52] = 
{
	{ 0x06000003, BezierCurve__ctor_m1C544723045F9DCE5B08353AB5055378C093AB15_AdjustorThunk },
	{ 0x06000004, BezierCurve__ctor_m3CFBCD60FCE57921D800FB28E912A4D563C25E3A_AdjustorThunk },
	{ 0x06000005, BezierCurve_Equals_mDA4EE12C81DC76D11465A44BFD53E8ADDBFB8282_AdjustorThunk },
	{ 0x06000006, BezierCurve_Equals_mE91B7BFB60AF0923F1AC868E19A62B803341BADF_AdjustorThunk },
	{ 0x06000007, BezierCurve_GetHashCode_mC5545401839CFBD84C98814A8F16A3C54851AE8B_AdjustorThunk },
	{ 0x06000008, BezierKnot__ctor_mCA9E68FFC4131E58A86B5F18E9A2C0E9E14735DC_AdjustorThunk },
	{ 0x06000009, BezierKnot__ctor_m8ADF3DADA826DCC898ED2E3C372772CF49E550B6_AdjustorThunk },
	{ 0x0600000A, BezierKnot_Transform_mCEE833536057769D54966A96E95D94CAFCFE818D_AdjustorThunk },
	{ 0x0600000B, BezierKnot_BakeTangentDirectionToRotation_m8F7764F0651E04A0673EED8ADE98A0642A9DD3A1_AdjustorThunk },
	{ 0x0600000C, BezierKnot_OnBeforeSerialize_mF1E04FF2DEC07549A50007B8A3C6B584567699B0_AdjustorThunk },
	{ 0x0600000D, BezierKnot_OnAfterDeserialize_mC3747A23181CFA89C94AD5D49472DA75DEC9B5E3_AdjustorThunk },
	{ 0x0600000E, BezierKnot_ToString_mA5C1A411AAB07C4FD27828196DCFFBBBAA607A7F_AdjustorThunk },
	{ 0x0600000F, BezierKnot_Equals_m321FC0E108FC61ADAE24D865A9951DF09AF62C0B_AdjustorThunk },
	{ 0x06000010, BezierKnot_Equals_m494E1B07278E6038D738588C8889EDED427652B3_AdjustorThunk },
	{ 0x06000011, BezierKnot_GetHashCode_m4FF60F61DD0FD8B1503DC0A2A07C1173937575F8_AdjustorThunk },
	{ 0x06000041, NativeSpline_get_Closed_m8CBE4D0A5A2EA8A70EE42FA88FD5AC4A4CAFA241_AdjustorThunk },
	{ 0x06000042, NativeSpline_get_Count_m25E371A5A76FACA52EC4B0D4945C3E06817F3814_AdjustorThunk },
	{ 0x06000043, NativeSpline_GetLength_m2D57F97F17FD46AC6BCE6080B823D65D5C4462DE_AdjustorThunk },
	{ 0x06000044, NativeSpline_get_Item_mB1C95195B66AE2752AE9198669A4FB1DB5D5AC1C_AdjustorThunk },
	{ 0x06000045, NativeSpline_GetEnumerator_mDE3AE45B6DA5B17805F9B494983CA97445EC1373_AdjustorThunk },
	{ 0x06000046, NativeSpline_System_Collections_IEnumerable_GetEnumerator_m9CE978E7E8E64428D4A4C5FFAE74F71703414686_AdjustorThunk },
	{ 0x06000047, NativeSpline__ctor_mE5C316FFE4FEECD7E5F6E7EEAE3FC185DBA41645_AdjustorThunk },
	{ 0x06000048, NativeSpline__ctor_m2298B6EC4E853BBBBB7CC13D333C9F9A544AF50C_AdjustorThunk },
	{ 0x06000049, NativeSpline__ctor_m60C4B48216F28D77598B5340D2F1880D80331EA1_AdjustorThunk },
	{ 0x0600004A, NativeSpline_GetCurve_m721D3FCF52F045376791025E9D69533506226DA2_AdjustorThunk },
	{ 0x0600004B, NativeSpline_GetCurveLength_m982AB89F9074C260DD4D7A334B1BC2AD8C8299D4_AdjustorThunk },
	{ 0x0600004C, NativeSpline_GetCurveUpVector_m34AC1408432D36A5A6A3495A9085197476DC9A71_AdjustorThunk },
	{ 0x0600004D, NativeSpline_Dispose_mFB66BE97D01248DE027168A27B31D1D186B6CB7A_AdjustorThunk },
	{ 0x0600004E, NativeSpline_GetCurveInterpolation_mCC5CF636B99BA4DA99765B75674E6C2CB5CF15C0_AdjustorThunk },
	{ 0x060000DC, SplineKnotIndex__ctor_m5285C9FF855A849F46154817A5603BCA5AEEA89E_AdjustorThunk },
	{ 0x060000DD, SplineKnotIndex_Equals_mA31ECC7A6CAC3E5E028A6349344845A0C9B4B8AB_AdjustorThunk },
	{ 0x060000DE, SplineKnotIndex_Equals_mE695B045700A70C6E31AF9051FE42AFBD1D5095E_AdjustorThunk },
	{ 0x060000DF, SplineKnotIndex_GetHashCode_m300A8FFA4CBA8246F1C17549B6F9662FF33BAF66_AdjustorThunk },
	{ 0x060000E0, SplineKnotIndex_ToString_m37121F48A4DE10B2B87A7F6D5BA6435F6B36018D_AdjustorThunk },
	{ 0x060000E3, SplineModificationData__ctor_m7EC9C675F49DB3843FA55A98DA8065D0747A9105_AdjustorThunk },
	{ 0x060000E4, SplineRange_get_Start_m85D0F4480493D66E109CFDC0DC2867B796DA23B3_AdjustorThunk },
	{ 0x060000E5, SplineRange_get_End_mFDEA15F9DBE6BAB51D2C3EC483EAB3A7DD21ADAB_AdjustorThunk },
	{ 0x060000E6, SplineRange_get_Count_m71E1A150F7E1896F91C2CE285E2CB86473D675A4_AdjustorThunk },
	{ 0x060000E7, SplineRange_get_Direction_mB267FF0827A7AB70131F2C98301A4DBD6E8D0A1C_AdjustorThunk },
	{ 0x060000E8, SplineRange__ctor_m06EFDF2A13B91C2664C843DDE5DBBD9027D1D7D7_AdjustorThunk },
	{ 0x060000E9, SplineRange__ctor_mBD384AF9E33176CD8412B30D5B650EA2D3597271_AdjustorThunk },
	{ 0x060000EA, SplineRange_get_Item_m99AAFB6C1F7595D844E1D8AC912B7AC818F57BEF_AdjustorThunk },
	{ 0x060000EB, SplineRange_GetEnumerator_m18E1A3F37D70D222E02E6403A09923E527FD2718_AdjustorThunk },
	{ 0x060000EC, SplineRange_System_Collections_IEnumerable_GetEnumerator_mBC4ECC83BCA21FB318F73DA436D3A5F4EF9A054A_AdjustorThunk },
	{ 0x060000ED, SplineRange_ToString_mD54B16AEBFDC7F3B63897117F64C675011682C32_AdjustorThunk },
	{ 0x060000EE, SplineRangeEnumerator_MoveNext_mD7F160934A6C7FC1A43387F1F02A3B082878A36B_AdjustorThunk },
	{ 0x060000EF, SplineRangeEnumerator_Reset_m239822992C54B013C18EF864BC10CAC75F65D9F6_AdjustorThunk },
	{ 0x060000F0, SplineRangeEnumerator_get_Current_m878CFC6620D283BD9B083C75F75D0557DC05DF56_AdjustorThunk },
	{ 0x060000F1, SplineRangeEnumerator_System_Collections_IEnumerator_get_Current_mCCF53031B3BE064CAFD663BD491CEB6FFC412778_AdjustorThunk },
	{ 0x060000F2, SplineRangeEnumerator__ctor_m301A9C9AB2BD0D0476E759E10D16BFAC666402E5_AdjustorThunk },
	{ 0x060000F3, SplineRangeEnumerator_Dispose_m64415DB83EC462A2CE734557A84BE4EDFBA08A8D_AdjustorThunk },
	{ 0x06000114, Segment__ctor_m4C11441126261ECF07E724888C6CDBF9910BA73E_AdjustorThunk },
};
static const int32_t s_InvokerIndices[276] = 
{
	-1,
	-1,
	1603,
	3826,
	6011,
	6166,
	10637,
	8800,
	1604,
	5480,
	2601,
	10870,
	10870,
	10698,
	6012,
	6166,
	10637,
	14874,
	14874,
	14874,
	14690,
	14689,
	14028,
	12720,
	12156,
	13747,
	-1,
	16420,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	16420,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	7494,
	2870,
	8705,
	4217,
	8705,
	4217,
	8705,
	4741,
	10870,
	10698,
	10698,
	10637,
	7643,
	10870,
	14083,
	10537,
	10637,
	10781,
	5478,
	10698,
	10698,
	2228,
	1558,
	337,
	5477,
	7632,
	4823,
	10870,
	3591,
	-1,
	-1,
	-1,
	-1,
	-1,
	10698,
	10637,
	10537,
	15983,
	15983,
	10870,
	4217,
	10870,
	10870,
	7016,
	2097,
	2255,
	2097,
	4217,
	10537,
	6946,
	4153,
	2089,
	1399,
	1399,
	8568,
	5478,
	4153,
	2089,
	2089,
	10870,
	5477,
	7632,
	10781,
	7481,
	3591,
	10870,
	4823,
	10870,
	10698,
	10698,
	8461,
	3827,
	10870,
	6012,
	4628,
	6012,
	8568,
	10698,
	10698,
	10870,
	10870,
	8568,
	10870,
	10537,
	10870,
	10870,
	10870,
	10870,
	10698,
	10870,
	10698,
	10698,
	10698,
	15983,
	15983,
	15983,
	15983,
	15983,
	15983,
	10698,
	8627,
	14201,
	10698,
	7481,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	2160,
	4628,
	10537,
	10698,
	8627,
	796,
	378,
	-1,
	9002,
	4823,
	-1,
	9002,
	4823,
	-1,
	9002,
	4823,
	-1,
	9002,
	4823,
	-1,
	10781,
	7632,
	10870,
	10870,
	-1,
	10870,
	10870,
	6166,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	4217,
	6246,
	6166,
	10637,
	10698,
	16420,
	12842,
	677,
	10637,
	10637,
	10637,
	10637,
	4217,
	2097,
	7016,
	10698,
	10698,
	10698,
	10537,
	10870,
	10637,
	10698,
	8707,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	14214,
	-1,
	-1,
	-1,
	-1,
	14439,
	-1,
	-1,
	-1,
	-1,
	-1,
	13148,
	13148,
	14881,
	14875,
	12843,
	11519,
	14890,
	-1,
	-1,
	4735,
};
static const Il2CppTokenRangePair s_rgctxIndices[40] = 
{
	{ 0x0200000A, { 10, 5 } },
	{ 0x0200000B, { 15, 2 } },
	{ 0x02000014, { 17, 13 } },
	{ 0x0200001D, { 46, 36 } },
	{ 0x0200001E, { 92, 6 } },
	{ 0x02000020, { 98, 6 } },
	{ 0x06000001, { 0, 2 } },
	{ 0x06000002, { 2, 5 } },
	{ 0x0600001B, { 7, 3 } },
	{ 0x060000A7, { 30, 3 } },
	{ 0x060000AA, { 33, 3 } },
	{ 0x060000AD, { 36, 3 } },
	{ 0x060000B0, { 39, 3 } },
	{ 0x060000B3, { 42, 3 } },
	{ 0x060000B8, { 45, 1 } },
	{ 0x060000CA, { 82, 4 } },
	{ 0x060000CB, { 86, 6 } },
	{ 0x060000F4, { 104, 5 } },
	{ 0x060000F5, { 109, 4 } },
	{ 0x060000F6, { 113, 4 } },
	{ 0x060000F7, { 117, 4 } },
	{ 0x060000F8, { 121, 5 } },
	{ 0x060000F9, { 126, 5 } },
	{ 0x060000FA, { 131, 2 } },
	{ 0x060000FB, { 133, 4 } },
	{ 0x060000FC, { 137, 2 } },
	{ 0x060000FD, { 139, 6 } },
	{ 0x060000FE, { 145, 5 } },
	{ 0x060000FF, { 150, 1 } },
	{ 0x06000101, { 151, 2 } },
	{ 0x06000102, { 153, 3 } },
	{ 0x06000103, { 156, 4 } },
	{ 0x06000104, { 160, 3 } },
	{ 0x06000106, { 163, 4 } },
	{ 0x06000107, { 167, 3 } },
	{ 0x06000108, { 170, 3 } },
	{ 0x06000109, { 173, 3 } },
	{ 0x0600010A, { 176, 3 } },
	{ 0x06000112, { 179, 2 } },
	{ 0x06000113, { 181, 4 } },
};
extern const uint32_t g_rgctx_TU5BU5DU26_tD739F840E5DE4E45A8BCCC254E377C1876379F4B;
extern const uint32_t g_rgctx_Array_Resize_TisT_tBA9253470EFDC49380AFE485153F47CC36C77F60_mE38B0D9C4A2ACDA1F4EFC20AECA713C076042EDE;
extern const uint32_t g_rgctx_TU5BU5DU26_tDE6AFE15BCEA37DDA84817D1C0041671C2041AEE;
extern const uint32_t g_rgctx_T_t9CA27768F99D4FAF9C1773DEB1985B84B260C737;
extern const uint32_t g_rgctx_Array_IndexOf_TisT_t9CA27768F99D4FAF9C1773DEB1985B84B260C737_mB5266B0CE6BA32BF007167F72AD22B477B9000EE;
extern const uint32_t g_rgctx_TU5BU5D_tD79CF9755568F262FD478EA84319BE259C385829;
extern const uint32_t g_rgctx_ArrayUtility_RemoveAt_TisT_t9CA27768F99D4FAF9C1773DEB1985B84B260C737_m665DBD4EE0BAF2087EE78271D8DD1F85AF45EF1A;
extern const uint32_t g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89_IReadOnlyCollection_1_get_Count_m0AE3F7BD631525AE8D4D10AD65F84AEB07172250;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89_IReadOnlyList_1_get_Item_m117E67BFA0B2E9D72DCF7322865B58EBF57E9B49;
extern const uint32_t g_rgctx_DataPoint_1_t75305CE5BB88CBA0AE28D3C528B4157925415DAD;
extern const uint32_t g_rgctx_TDataType_t32B1EAD517B9C3FD3E18378CB3CE629FCE3FC915;
extern const uint32_t g_rgctx_DataPoint_1_get_Index_mEE3745BD13B248CB3C62C59B874AC4D79D6EC250;
extern const uint32_t g_rgctx_DataPoint_1_t75305CE5BB88CBA0AE28D3C528B4157925415DAD;
extern const uint32_t g_rgctx_DataPoint_1_get_Value_mBE087FAA377D831CCEE99A8DED3464D3289BE9F3;
extern const uint32_t g_rgctx_T_tCC0F6FDB07FDEE2D5EB887E0029FA70CA190652E;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tCC0F6FDB07FDEE2D5EB887E0029FA70CA190652E_IDataPoint_get_Index_mA7FF417EA6B1B70F2A133A6E184ADDE8E0FA51AA;
extern const uint32_t g_rgctx_NativeArray_1_tA3300F7BDF206DBD6697A9DF41F8D6FFA94EEC72;
extern const uint32_t g_rgctx_NativeSlice_1_tC4CD0BB28E0349025E6FCDBDD3AE5A6294BA5A69;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_m4C88D50EC90CB5ED867547046B4190C7A6F677B0;
extern const uint32_t g_rgctx_Slice_1_tE74A510787BC8F40D4F91F72DE569EAFD2F683AD;
extern const uint32_t g_rgctx_NativeSlice_1_GetEnumerator_mE62938FE36E68A820FFC9BE43808DE25C9D64ACC;
extern const uint32_t g_rgctx_NativeSlice_1_tC4CD0BB28E0349025E6FCDBDD3AE5A6294BA5A69;
extern const uint32_t g_rgctx_Enumerator_tF40397D3F790641577BD653FBDEB387D1514EC56;
extern const uint32_t g_rgctx_IEnumerator_1_tAA438BEA42B20CD09DAE7C621055FF1190DA3EC5;
extern const uint32_t g_rgctx_Slice_1_GetEnumerator_m851AFF044D265A720DCD6A4D1212021CC8713F7D;
extern const uint32_t g_rgctx_Slice_1_tE74A510787BC8F40D4F91F72DE569EAFD2F683AD;
extern const uint32_t g_rgctx_NativeSlice_1_get_Length_m4EB18384991E6EA0D086C0D8D35F349318E78235;
extern const uint32_t g_rgctx_NativeSlice_1_get_Item_mD74A93C09964CC8E0FDFA8018CABE96F580AE973;
extern const uint32_t g_rgctx_T_t2377008B41FF790F2F500AFD57B6AF258DDB93BA;
extern const uint32_t g_rgctx_T_tE826E8AAF9B34220513AA6C36DE90ECB308C2440;
extern const uint32_t g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_tE826E8AAF9B34220513AA6C36DE90ECB308C2440_m9223F93267C2A4BF5E2DEBADA371955B4EA658A9;
extern const uint32_t g_rgctx_SplineUtility_Evaluate_TisT_tE826E8AAF9B34220513AA6C36DE90ECB308C2440_m92C27D53D294D2668C42E6D30601366E6AB59607;
extern const uint32_t g_rgctx_T_t1DD1519442B01F40E84D374523A46CE5B8CA680F;
extern const uint32_t g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t1DD1519442B01F40E84D374523A46CE5B8CA680F_m27FABDD9898065DBE3C69F8DB2D160BF5D9DFF3C;
extern const uint32_t g_rgctx_SplineUtility_EvaluatePosition_TisT_t1DD1519442B01F40E84D374523A46CE5B8CA680F_mD543492D68504BD2B41958198CF5BAE6B892BC41;
extern const uint32_t g_rgctx_T_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B;
extern const uint32_t g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B_mA8E8BD83B460D7E2026B6A4CDA7C5D1D37CE1369;
extern const uint32_t g_rgctx_SplineUtility_EvaluateTangent_TisT_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B_m3D1C619659DCAFA736EBA270C0703DFE2B46D5B8;
extern const uint32_t g_rgctx_T_t4854496694E9D202AC18F66F2C0EDA9144674750;
extern const uint32_t g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t4854496694E9D202AC18F66F2C0EDA9144674750_m2CE81DDA4BA386F3A165DEABA5D764B581C910A5;
extern const uint32_t g_rgctx_SplineUtility_EvaluateUpVector_TisT_t4854496694E9D202AC18F66F2C0EDA9144674750_m4B19DBAB3F27EB4F6C827C5C03381FFE715BC504;
extern const uint32_t g_rgctx_T_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC;
extern const uint32_t g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC_mD16F5F4E30264E65455F22A0574C36A9E75B505C;
extern const uint32_t g_rgctx_SplineUtility_EvaluateAcceleration_TisT_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC_mC60113066134299123C2B9FBDAE2156A8E06CFA0;
extern const uint32_t g_rgctx_T_tC32FB0D0E61F840041C23FE09531CF3C5316CDC4;
extern const uint32_t g_rgctx_SplineData_1_tCFB34AD86B46002E99234C0CA6B3CD3035C14674;
extern const uint32_t g_rgctx_List_1_t3FB2F9F664EEF0AC67676DBD832AE4E14D48BDEE;
extern const uint32_t g_rgctx_List_1_get_Item_m72615A2D0F8DBD4FDB33C03E87075AF288C31641;
extern const uint32_t g_rgctx_DataPoint_1_tCBB882C37ED6947ABBEB1AC719BCD1ABBC4FE543;
extern const uint32_t g_rgctx_SplineData_1_SetDataPoint_mB89E67AF1C71A0E3D0F58A50A9DB2D8A7264FA21;
extern const uint32_t g_rgctx_T_tF6BCB6AF6F163E199D6736531AD7BE655BE8D677;
extern const uint32_t g_rgctx_List_1_get_Count_m1BD33DFC6673766C1836FF91B1306E1DFF59322C;
extern const uint32_t g_rgctx_List_1__ctor_m53F034662EAC19A5E9AAFBB791190CE7FE58CBDB;
extern const uint32_t g_rgctx_SplineData_1_get_Count_m0D9E51183941DCED47A76191A354E2C8E8CFCEB5;
extern const uint32_t g_rgctx_DataPointComparer_1_t148F0D7651AC95B83FEAFCD2FC4CC0A0FCE271E4;
extern const uint32_t g_rgctx_SplineData_1_tCFB34AD86B46002E99234C0CA6B3CD3035C14674;
extern const uint32_t g_rgctx_List_1_BinarySearch_m0DD626B614A668D9E8FDD1BF902ACAD76B348016;
extern const uint32_t g_rgctx_IComparer_1_tBE94EE9D2ACE8F1FFD749C891A361FA60689B457;
extern const uint32_t g_rgctx_List_1_Insert_m15234FD81FDCF6024ABAA8D35E586FF8320DA8B4;
extern const uint32_t g_rgctx_SplineData_1_SetDirty_m7441E5B3E8AA3C4E6C1BA0B538B83CF18E1D2851;
extern const uint32_t g_rgctx_List_1_RemoveAt_mF842A55AF83AF088749C1AB29A4142B75B1F10C4;
extern const uint32_t g_rgctx_List_1_Clear_m114FD8166A82F71D70FD0A0AEFE053FD5A5B809A;
extern const uint32_t g_rgctx_SplineData_1_Wrap_mA7512619003D84F26851D664564F8276ECFDDB49;
extern const uint32_t g_rgctx_SplineData_1_SortIfNecessary_mBC37AFB99ADB4762AFB5B7985FF833C335AC7AB1;
extern const uint32_t g_rgctx_DataPoint_1_get_Index_mE5C77606E679574198DE9996165624B33B4A3ACE;
extern const uint32_t g_rgctx_DataPoint_1_tCBB882C37ED6947ABBEB1AC719BCD1ABBC4FE543;
extern const uint32_t g_rgctx_DataPoint_1__ctor_m3C348D41D837455BF21845BF5BF861F37A1AED4F;
extern const uint32_t g_rgctx_SplineData_1_ResolveBinaryIndex_m136F58960334D3A3CE6BC99D17D83CA53F06C47F;
extern const uint32_t g_rgctx_SplineData_1_GetIndex_m05AF40C4B89323D59CD446EFAE8AF6DE8F52A661;
extern const uint32_t g_rgctx_DataPoint_1_get_Value_m2C7EED6FD2B6B2C42332F64FCEA16D78E767F324;
extern const uint32_t g_rgctx_IInterpolator_1_tEB83E141347105D67D20F4EF49792101E2780582;
extern const uint32_t g_rgctx_SplineData_1_RemoveAt_m00A082B7CFCFA49C1274CEE05B47586E1A86B939;
extern const uint32_t g_rgctx_SplineData_1_Add_m257BB2C8F9EC58B6B46A1DF310A63CFEBD2917C0;
extern const uint32_t g_rgctx_List_1_Sort_m21EF6C01811B6D57F2CDC2F2E7E943ACBB03EB25;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__46_t87AE4F2CFD34429FEF8B8EF4364E1B11C65ACDE5;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__46__ctor_mC12E6AB4D52682EE1A1C5C67D134CF9F3CDE2399;
extern const uint32_t g_rgctx_IEnumerator_1_tDBE8F8DAB3EA3D93F8AA35A954CE0CCB46767AD5;
extern const uint32_t g_rgctx_SplineData_1_GetEnumerator_m67F5B3D5051F10DD4618D623B7ABD5349B38E290;
extern const uint32_t g_rgctx_DataPoint_1_set_Index_m70F7286D03B50D748B9892B8C30578DCB92E51D8;
extern const uint32_t g_rgctx_List_1_set_Item_m0C9CAF05B1B8E180D5AB43296CFD74103A3221DC;
extern const uint32_t g_rgctx_DataPointComparer_1__ctor_mB2DBA95A3B9DE0A86362EA949AD0E81316D8C777;
extern const uint32_t g_rgctx_TSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925;
extern const uint32_t g_rgctx_TInterpolator_tA2ABB84604483B149DE979A83A27C9684ED9452C;
extern const uint32_t g_rgctx_SplineData_1_Evaluate_TisTSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925_TisTInterpolator_tA2ABB84604483B149DE979A83A27C9684ED9452C_m432D880C2A33105184A1C1603CF259688B45D8FE;
extern const uint32_t g_rgctx_SplineUtility_ConvertIndexUnit_TisTSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925_m6FF58AA780DB43E4D4636907C4E945BF6A522D9B;
extern const uint32_t g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const uint32_t g_rgctx_TInterpolator_t5071944AD58118FB8EBA838FF07CD2813729811E;
extern const Il2CppRGCTXConstrainedData g_rgctx_TInterpolator_t5071944AD58118FB8EBA838FF07CD2813729811E_IInterpolator_1_Interpolate_mD30408AE08CC7785CA68F486560BDC78C8187BDC;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__46_t17FCE2519220B2B391C95D2101710431F59D12DB;
extern const uint32_t g_rgctx_SplineData_1_tBE41EA5E7FF312668E91BF7014CDDC089F0D744C;
extern const uint32_t g_rgctx_SplineData_1_get_Count_mEB03CB50D8263F8BF88BC238390C80E728C0DF2B;
extern const uint32_t g_rgctx_List_1_tFB7D510D963D4F2B759CC8050F30FFC0F64313CA;
extern const uint32_t g_rgctx_List_1_get_Item_mF7AB3251AC3B3B768C7662BDD5E63A0692D43256;
extern const uint32_t g_rgctx_DataPoint_1_t53C68C59AB1D0F26CEF6553C5386EEF53526FCAE;
extern const uint32_t g_rgctx_SplineDataDictionary_1_t071212C231516BC55125D67A960F352467ABFC77;
extern const uint32_t g_rgctx_List_1_t041F1FAFFB5BE14074BDBAE33C2804488A7EAC50;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m8305EE2D1986080A4D52967EDBF0090FEDEE2130;
extern const uint32_t g_rgctx_Enumerator_tBE411FAD0C1CD56F0E1994EFF103E7AE1C24F399;
extern const uint32_t g_rgctx_IEnumerator_1_t4E632B68BF8F90C3BBDFC904FAFC4D0EE3739143;
extern const uint32_t g_rgctx_List_1__ctor_m31C9495D1EE2363A6705F917916AA51827025CEE;
extern const uint32_t g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_mED1D582887753212605AD9F32403975B61B5AB6C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_ISpline_GetCurveUpVector_mF3667BFEA2DD21D1174BEFC8DEF009BC708AFF3A;
extern const uint32_t g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_m2DDF94C8FB0486DF5A52FB877FF2D682B1AD8276;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const uint32_t g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_mE20E07B8061EBDBE6494ABBCF010C9BD444360B0;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const uint32_t g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_t4C07EEC31A789FD8356E54267E119483C81527BE_mF485644800E8E1C68C517CA1F3CE78EFA06D7187;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE_ISpline_GetCurveUpVector_mF3667BFEA2DD21D1174BEFC8DEF009BC708AFF3A;
extern const uint32_t g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27;
extern const uint32_t g_rgctx_SplineUtility_NextIndex_TisT_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_mE9C431AA6D94D70DD83A590F74733BFE5ADCF2C3;
extern const uint32_t g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27;
extern const uint32_t g_rgctx_SplineUtility_NextIndex_TisT_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_mF46B489C94F550C55684C4A25ADE5580688066B3;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const uint32_t g_rgctx_T_tD55A1FD547021FA95E0C42203CC0B6E9180F925D;
extern const uint32_t g_rgctx_SplineUtility_EvaluateUpVectorsForCurve_TisT_tD55A1FD547021FA95E0C42203CC0B6E9180F925D_mC824CAE5D64C43B3A00957DC21E4D73300D05F20;
extern const uint32_t g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_tB0D7011DEDCF640A88E2C978EB4853F10E743796_m4F727BADD465CC7FC2BB78BE458035C6A5094EFB;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448;
extern const uint32_t g_rgctx_T_t0D2686DD9E94635D170C85D174D0FA38BE871C85;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_t0D2686DD9E94635D170C85D174D0FA38BE871C85_m4919A93C118C04632FC167F27AF6F8037DE7F3FE;
extern const uint32_t g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetCurveLength_mE6D0FCF62857F5DC91AA6ABA5ACD6FADF295CAFF;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetCurveInterpolation_m30CB829BA7DFC1D0094C5E707D7B32361C3255B0;
extern const uint32_t g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_GetCurveLength_mE6D0FCF62857F5DC91AA6ABA5ACD6FADF295CAFF;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const uint32_t g_rgctx_T_t18D81B111684C2C7048EA84037049818E145275E;
extern const uint32_t g_rgctx_T_t4B9B04773716E49476D418E2D212645C915E2FB3;
extern const uint32_t g_rgctx_SplineUtility_EvaluatePosition_TisT_t4B9B04773716E49476D418E2D212645C915E2FB3_m2F913D7E4388EBD2EDD17BB888614317DA21B45C;
extern const uint32_t g_rgctx_T_t0B91953085D57033099A645B6D8E062822A74453;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0B91953085D57033099A645B6D8E062822A74453_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const uint32_t g_rgctx_SplineUtility_GetNearestPoint_TisT_t0B91953085D57033099A645B6D8E062822A74453_m8BD331CBDC9D5D570D5050F8CF6FDA1F5D689B5D;
extern const uint32_t g_rgctx_T_t20095777D555DA832E286946F891AAEE23F12BBA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t20095777D555DA832E286946F891AAEE23F12BBA_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const uint32_t g_rgctx_SplineUtility_GetNormalizedInterpolation_TisT_t20095777D555DA832E286946F891AAEE23F12BBA_m223212ECD8F6A391B0BAFEC693ABF4A80E1FC303;
extern const uint32_t g_rgctx_SplineUtility_ConvertNormalizedIndexUnit_TisT_t20095777D555DA832E286946F891AAEE23F12BBA_mAD3B0DE644C5D4F504961F97A45CBDAA5774094B;
extern const uint32_t g_rgctx_T_t40C115F36A48CB577AE2D1C82D89BB26E6219758;
extern const uint32_t g_rgctx_SplineUtility_SplineToCurveT_TisT_t40C115F36A48CB577AE2D1C82D89BB26E6219758_m165ABA39420D8E29CE1D17289CDEBB24C9100999;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t40C115F36A48CB577AE2D1C82D89BB26E6219758_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const uint32_t g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F;
extern const uint32_t g_rgctx_SplineUtility_CurveToSplineT_TisT_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_mB6997060DA7E74F0B454200041D6E66451778852;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F;
extern const uint32_t g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const uint32_t g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5;
extern const uint32_t g_rgctx_T_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E;
extern const uint32_t g_rgctx_SplineUtility_PreviousIndex_TisT_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E_mECE606F1C53CA8C121899F0A0657E00AA11B6016;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27;
extern const uint32_t g_rgctx_T_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7;
extern const uint32_t g_rgctx_SplineUtility_NextIndex_TisT_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7_m14CC3E59EC9958382D54FF382FA721CCCBA285B9;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27;
extern const uint32_t g_rgctx_T_t0F71BF1A31B2BB84397CB538215F077556AA486B;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0F71BF1A31B2BB84397CB538215F077556AA486B_ISplineContainer_get_Splines_m3FD717D404AF904C4C8CB1DD234AD9B6BE0992F6;
extern const uint32_t g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_ISplineContainer_get_KnotLinkCollection_m47A97534AE64E9ED80C8CB94D3F3A13F68121246;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_ISplineContainer_get_Splines_m3FD717D404AF904C4C8CB1DD234AD9B6BE0992F6;
extern const uint32_t g_rgctx_SplineUtility_IsIndexValid_TisT_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_mD4678ACEBA9373320E85D96A807CC4808A7FEE23;
static const Il2CppRGCTXDefinition s_rgctxValues[185] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tD739F840E5DE4E45A8BCCC254E377C1876379F4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tBA9253470EFDC49380AFE485153F47CC36C77F60_mE38B0D9C4A2ACDA1F4EFC20AECA713C076042EDE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tDE6AFE15BCEA37DDA84817D1C0041671C2041AEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9CA27768F99D4FAF9C1773DEB1985B84B260C737 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_IndexOf_TisT_t9CA27768F99D4FAF9C1773DEB1985B84B260C737_mB5266B0CE6BA32BF007167F72AD22B477B9000EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD79CF9755568F262FD478EA84319BE259C385829 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayUtility_RemoveAt_TisT_t9CA27768F99D4FAF9C1773DEB1985B84B260C737_m665DBD4EE0BAF2087EE78271D8DD1F85AF45EF1A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89_IReadOnlyCollection_1_get_Count_m0AE3F7BD631525AE8D4D10AD65F84AEB07172250 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tDD4B6436B191231EB6114A4DDF0B19A30D03CB89_IReadOnlyList_1_get_Item_m117E67BFA0B2E9D72DCF7322865B58EBF57E9B49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPoint_1_t75305CE5BB88CBA0AE28D3C528B4157925415DAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDataType_t32B1EAD517B9C3FD3E18378CB3CE629FCE3FC915 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1_get_Index_mEE3745BD13B248CB3C62C59B874AC4D79D6EC250 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPoint_1_t75305CE5BB88CBA0AE28D3C528B4157925415DAD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1_get_Value_mBE087FAA377D831CCEE99A8DED3464D3289BE9F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCC0F6FDB07FDEE2D5EB887E0029FA70CA190652E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tCC0F6FDB07FDEE2D5EB887E0029FA70CA190652E_IDataPoint_get_Index_mA7FF417EA6B1B70F2A133A6E184ADDE8E0FA51AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tA3300F7BDF206DBD6697A9DF41F8D6FFA94EEC72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tC4CD0BB28E0349025E6FCDBDD3AE5A6294BA5A69 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_m4C88D50EC90CB5ED867547046B4190C7A6F677B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Slice_1_tE74A510787BC8F40D4F91F72DE569EAFD2F683AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_GetEnumerator_mE62938FE36E68A820FFC9BE43808DE25C9D64ACC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tC4CD0BB28E0349025E6FCDBDD3AE5A6294BA5A69 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tF40397D3F790641577BD653FBDEB387D1514EC56 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tAA438BEA42B20CD09DAE7C621055FF1190DA3EC5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Slice_1_GetEnumerator_m851AFF044D265A720DCD6A4D1212021CC8713F7D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Slice_1_tE74A510787BC8F40D4F91F72DE569EAFD2F683AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Length_m4EB18384991E6EA0D086C0D8D35F349318E78235 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Item_mD74A93C09964CC8E0FDFA8018CABE96F580AE973 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2377008B41FF790F2F500AFD57B6AF258DDB93BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE826E8AAF9B34220513AA6C36DE90ECB308C2440 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_tE826E8AAF9B34220513AA6C36DE90ECB308C2440_m9223F93267C2A4BF5E2DEBADA371955B4EA658A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_Evaluate_TisT_tE826E8AAF9B34220513AA6C36DE90ECB308C2440_m92C27D53D294D2668C42E6D30601366E6AB59607 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1DD1519442B01F40E84D374523A46CE5B8CA680F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t1DD1519442B01F40E84D374523A46CE5B8CA680F_m27FABDD9898065DBE3C69F8DB2D160BF5D9DFF3C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluatePosition_TisT_t1DD1519442B01F40E84D374523A46CE5B8CA680F_mD543492D68504BD2B41958198CF5BAE6B892BC41 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B_mA8E8BD83B460D7E2026B6A4CDA7C5D1D37CE1369 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluateTangent_TisT_t0457AB0B0393AFF67E1AFF17AF44F05AC0C5277B_m3D1C619659DCAFA736EBA270C0703DFE2B46D5B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4854496694E9D202AC18F66F2C0EDA9144674750 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_t4854496694E9D202AC18F66F2C0EDA9144674750_m2CE81DDA4BA386F3A165DEABA5D764B581C910A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluateUpVector_TisT_t4854496694E9D202AC18F66F2C0EDA9144674750_m4B19DBAB3F27EB4F6C827C5C03381FFE715BC504 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineContainer_GetOrBakeNativeSpline_TisT_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC_mD16F5F4E30264E65455F22A0574C36A9E75B505C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluateAcceleration_TisT_tFB99D2D55CA167643263FA95EE313B05CF4DC9EC_mC60113066134299123C2B9FBDAE2156A8E06CFA0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC32FB0D0E61F840041C23FE09531CF3C5316CDC4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SplineData_1_tCFB34AD86B46002E99234C0CA6B3CD3035C14674 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t3FB2F9F664EEF0AC67676DBD832AE4E14D48BDEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m72615A2D0F8DBD4FDB33C03E87075AF288C31641 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPoint_1_tCBB882C37ED6947ABBEB1AC719BCD1ABBC4FE543 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_SetDataPoint_mB89E67AF1C71A0E3D0F58A50A9DB2D8A7264FA21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF6BCB6AF6F163E199D6736531AD7BE655BE8D677 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m1BD33DFC6673766C1836FF91B1306E1DFF59322C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m53F034662EAC19A5E9AAFBB791190CE7FE58CBDB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_get_Count_m0D9E51183941DCED47A76191A354E2C8E8CFCEB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPointComparer_1_t148F0D7651AC95B83FEAFCD2FC4CC0A0FCE271E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SplineData_1_tCFB34AD86B46002E99234C0CA6B3CD3035C14674 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_BinarySearch_m0DD626B614A668D9E8FDD1BF902ACAD76B348016 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_tBE94EE9D2ACE8F1FFD749C891A361FA60689B457 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Insert_m15234FD81FDCF6024ABAA8D35E586FF8320DA8B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_SetDirty_m7441E5B3E8AA3C4E6C1BA0B538B83CF18E1D2851 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_mF842A55AF83AF088749C1AB29A4142B75B1F10C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m114FD8166A82F71D70FD0A0AEFE053FD5A5B809A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_Wrap_mA7512619003D84F26851D664564F8276ECFDDB49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_SortIfNecessary_mBC37AFB99ADB4762AFB5B7985FF833C335AC7AB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1_get_Index_mE5C77606E679574198DE9996165624B33B4A3ACE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPoint_1_tCBB882C37ED6947ABBEB1AC719BCD1ABBC4FE543 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1__ctor_m3C348D41D837455BF21845BF5BF861F37A1AED4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_ResolveBinaryIndex_m136F58960334D3A3CE6BC99D17D83CA53F06C47F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_GetIndex_m05AF40C4B89323D59CD446EFAE8AF6DE8F52A661 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1_get_Value_m2C7EED6FD2B6B2C42332F64FCEA16D78E767F324 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IInterpolator_1_tEB83E141347105D67D20F4EF49792101E2780582 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_RemoveAt_m00A082B7CFCFA49C1274CEE05B47586E1A86B939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_Add_m257BB2C8F9EC58B6B46A1DF310A63CFEBD2917C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Sort_m21EF6C01811B6D57F2CDC2F2E7E943ACBB03EB25 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__46_t87AE4F2CFD34429FEF8B8EF4364E1B11C65ACDE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__46__ctor_mC12E6AB4D52682EE1A1C5C67D134CF9F3CDE2399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tDBE8F8DAB3EA3D93F8AA35A954CE0CCB46767AD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_GetEnumerator_m67F5B3D5051F10DD4618D623B7ABD5349B38E290 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPoint_1_set_Index_m70F7286D03B50D748B9892B8C30578DCB92E51D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m0C9CAF05B1B8E180D5AB43296CFD74103A3221DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DataPointComparer_1__ctor_mB2DBA95A3B9DE0A86362EA949AD0E81316D8C777 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TInterpolator_tA2ABB84604483B149DE979A83A27C9684ED9452C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_Evaluate_TisTSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925_TisTInterpolator_tA2ABB84604483B149DE979A83A27C9684ED9452C_m432D880C2A33105184A1C1603CF259688B45D8FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_ConvertIndexUnit_TisTSpline_tE3783190FE5F0375B8A1750372FFA9811D5D5925_m6FF58AA780DB43E4D4636907C4E945BF6A522D9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSpline_tE655A643CFD818272E49257FAA4F63CCCDA69341_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TInterpolator_t5071944AD58118FB8EBA838FF07CD2813729811E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TInterpolator_t5071944AD58118FB8EBA838FF07CD2813729811E_IInterpolator_1_Interpolate_mD30408AE08CC7785CA68F486560BDC78C8187BDC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__46_t17FCE2519220B2B391C95D2101710431F59D12DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SplineData_1_tBE41EA5E7FF312668E91BF7014CDDC089F0D744C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineData_1_get_Count_mEB03CB50D8263F8BF88BC238390C80E728C0DF2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tFB7D510D963D4F2B759CC8050F30FFC0F64313CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mF7AB3251AC3B3B768C7662BDD5E63A0692D43256 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DataPoint_1_t53C68C59AB1D0F26CEF6553C5386EEF53526FCAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SplineDataDictionary_1_t071212C231516BC55125D67A960F352467ABFC77 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t041F1FAFFB5BE14074BDBAE33C2804488A7EAC50 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m8305EE2D1986080A4D52967EDBF0090FEDEE2130 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tBE411FAD0C1CD56F0E1994EFF103E7AE1C24F399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t4E632B68BF8F90C3BBDFC904FAFC4D0EE3739143 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m31C9495D1EE2363A6705F917916AA51827025CEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_mED1D582887753212605AD9F32403975B61B5AB6C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2DD4F4D934DF123E580CE15761AFD4195AE078F5_ISpline_GetCurveUpVector_mF3667BFEA2DD21D1174BEFC8DEF009BC708AFF3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_m2DDF94C8FB0486DF5A52FB877FF2D682B1AD8276 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tC89F1EB3A5A9A7B54716C2340EA726F662BC1EA1_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_mE20E07B8061EBDBE6494ABBCF010C9BD444360B0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0FA2A890E1F1C970BE87EBB9E79FE840072192FA_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_t4C07EEC31A789FD8356E54267E119483C81527BE_mF485644800E8E1C68C517CA1F3CE78EFA06D7187 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t4C07EEC31A789FD8356E54267E119483C81527BE_ISpline_GetCurveUpVector_mF3667BFEA2DD21D1174BEFC8DEF009BC708AFF3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_NextIndex_TisT_tA6F99F4EF5CE8A1D6A2614032F433EF3F4587A43_mE9C431AA6D94D70DD83A590F74733BFE5ADCF2C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_NextIndex_TisT_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_mF46B489C94F550C55684C4A25ADE5580688066B3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t5EC9E88003E9A7223A78B80D788338B00BDC00BE_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD55A1FD547021FA95E0C42203CC0B6E9180F925D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluateUpVectorsForCurve_TisT_tD55A1FD547021FA95E0C42203CC0B6E9180F925D_mC824CAE5D64C43B3A00957DC21E4D73300D05F20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_tB0D7011DEDCF640A88E2C978EB4853F10E743796_m4F727BADD465CC7FC2BB78BE458035C6A5094EFB },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB0D7011DEDCF640A88E2C978EB4853F10E743796_ISpline_GetCurve_mAC68B62E5632EADF77C274D4921CDE981D5FC448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0D2686DD9E94635D170C85D174D0FA38BE871C85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_t0D2686DD9E94635D170C85D174D0FA38BE871C85_m4919A93C118C04632FC167F27AF6F8037DE7F3FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetCurveLength_mE6D0FCF62857F5DC91AA6ABA5ACD6FADF295CAFF },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tBE7D5FBDC932F44F90BFEA9D56FF3E57CB71EEC6_ISpline_GetCurveInterpolation_m30CB829BA7DFC1D0094C5E707D7B32361C3255B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_GetCurveLength_mE6D0FCF62857F5DC91AA6ABA5ACD6FADF295CAFF },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t9E052495D86054C10F9795F19BD9476D6D621C7A_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t18D81B111684C2C7048EA84037049818E145275E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4B9B04773716E49476D418E2D212645C915E2FB3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_EvaluatePosition_TisT_t4B9B04773716E49476D418E2D212645C915E2FB3_m2F913D7E4388EBD2EDD17BB888614317DA21B45C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0B91953085D57033099A645B6D8E062822A74453 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0B91953085D57033099A645B6D8E062822A74453_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_GetNearestPoint_TisT_t0B91953085D57033099A645B6D8E062822A74453_m8BD331CBDC9D5D570D5050F8CF6FDA1F5D689B5D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t20095777D555DA832E286946F891AAEE23F12BBA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t20095777D555DA832E286946F891AAEE23F12BBA_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_GetNormalizedInterpolation_TisT_t20095777D555DA832E286946F891AAEE23F12BBA_m223212ECD8F6A391B0BAFEC693ABF4A80E1FC303 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_ConvertNormalizedIndexUnit_TisT_t20095777D555DA832E286946F891AAEE23F12BBA_mAD3B0DE644C5D4F504961F97A45CBDAA5774094B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t40C115F36A48CB577AE2D1C82D89BB26E6219758 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_SplineToCurveT_TisT_t40C115F36A48CB577AE2D1C82D89BB26E6219758_m165ABA39420D8E29CE1D17289CDEBB24C9100999 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t40C115F36A48CB577AE2D1C82D89BB26E6219758_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_CurveToSplineT_TisT_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_mB6997060DA7E74F0B454200041D6E66451778852 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t23DDFA76D4F3809E6F169E82C65A51CFE9596A4F_ISpline_GetLength_m5ED904AD83A23D4B08F964DA13358B487803B03F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tAC97FE9AF32D441AF951AFE953687B801ADEB3E0_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D_IReadOnlyCollection_1_get_Count_mEC142963D4B5D4ED57320CE779138624D36D91E2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t88EF991C400A7BEF5A1232AEC7E09578180B7D4D_ISpline_get_Closed_mF773CF0C1BDF5985E2BD9908E47875B5749975E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_PreviousIndex_TisT_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E_mECE606F1C53CA8C121899F0A0657E00AA11B6016 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t519D62C099DBED6829A1C5C5F39AEEE395E32F7E_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_NextIndex_TisT_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7_m14CC3E59EC9958382D54FF382FA721CCCBA285B9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t5B601860AF798901AACC913FBCBD5E06AEFDDDB7_IReadOnlyList_1_get_Item_m031980F1718F6EC132A5B813167D8FDD2DCCFA27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0F71BF1A31B2BB84397CB538215F077556AA486B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0F71BF1A31B2BB84397CB538215F077556AA486B_ISplineContainer_get_Splines_m3FD717D404AF904C4C8CB1DD234AD9B6BE0992F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_ISplineContainer_get_KnotLinkCollection_m47A97534AE64E9ED80C8CB94D3F3A13F68121246 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_ISplineContainer_get_Splines_m3FD717D404AF904C4C8CB1DD234AD9B6BE0992F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SplineUtility_IsIndexValid_TisT_t212FA2EBDBC1AEF2B9EB7761A7CDCAFB37508040_mD4678ACEBA9373320E85D96A807CC4808A7FEE23 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Splines_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Splines_CodeGenModule = 
{
	"Unity.Splines.dll",
	276,
	s_methodPointers,
	52,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	40,
	s_rgctxIndices,
	185,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
