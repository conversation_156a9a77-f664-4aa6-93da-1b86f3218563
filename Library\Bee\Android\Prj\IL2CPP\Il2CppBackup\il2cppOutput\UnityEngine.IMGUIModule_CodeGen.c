﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void Event_get_rawType_mD7CD874F3C8DFD4DFB6237E79A7C3A484B33CE56 (void);
extern void Event_get_mousePosition_mD6D2DF45C75E6FADD415D27D0E93563DED37D9B9 (void);
extern void Event_set_mousePosition_m221CDC5C9556DE91E82242A693D9E14FAC371F38 (void);
extern void Event_get_delta_m1BBF28E2FC379EDD3907DC987E6BD7E6521D69A0 (void);
extern void Event_set_delta_mA4F7805B9B53B36C7DAA31735CC9097D363B9F9A (void);
extern void Event_get_pointerType_mFFB3FB3E83412151A66FEC136FA00EBDB563B94B (void);
extern void Event_get_button_m57F81B5CCB26866E776D0EBD1250C708A3565C08 (void);
extern void Event_get_modifiers_mD55E7CF06EB720434F0F174EA569B2A29792D39B (void);
extern void Event_set_modifiers_m879319643B5CD23F3223AB8E835C8ABCD3DA72FB (void);
extern void Event_get_pressure_m3E43BF333499DFDCFF2A36258BBC290DDD40D963 (void);
extern void Event_get_twist_m557A5139AD77A15D25598A3F83676E558D1202BF (void);
extern void Event_get_tilt_m5F37D44342F42D691336B23EB075171CFEE7C7A3 (void);
extern void Event_get_penStatus_m41CC65A15A4209D8168AE8BC64C691DE22F83611 (void);
extern void Event_get_clickCount_mEF418EB4A36318F07E5F3463E4E5E8A4C454DE7D (void);
extern void Event_get_character_m8F7A92E90EF65B9379C01432B42D6BF818C32A61 (void);
extern void Event_set_character_mA159F1742FD9EA968F32556C5FE1A2401069BAF5 (void);
extern void Event_get_Internal_keyCode_m7532349D62074F6E6F58F9F9FFEFE48E767CBCC0 (void);
extern void Event_set_Internal_keyCode_m4265541E2DB04D85560BCDCC880862F17BD56E41 (void);
extern void Event_get_keyCode_mADBB236A741F96D86E4A536E15FFECFD4C367B64 (void);
extern void Event_set_keyCode_m698D040F2EE0BE55B1B06A3FD865CC0A5D7B1168 (void);
extern void Event_get_displayIndex_m7DBF1B18DD9B5E5B4EEA979FCA87351E3E5B16C3 (void);
extern void Event_set_displayIndex_m8208F1B0471C0B45C0BB248F9A0178EB40FBE100 (void);
extern void Event_get_type_m8A825D6DA432B967DAA3E22E5C8571620A75F8A8 (void);
extern void Event_set_type_m16D35A8AF665F4A73A447F9EE7CA36963F34FEC2 (void);
extern void Event_get_commandName_m14F2015FA5A9050C3C42AF1BD9D0E85D4FF78C24 (void);
extern void Event_set_commandName_m8DA7262E1CD1005911EAB9777DE9FEC2D97504FA (void);
extern void Event_Internal_Use_m303C630AFC4EAE76036545C09C79729E90D81CB9 (void);
extern void Event_Internal_Create_m38519A1960401042CAB57086F9E038116B8D3EAF (void);
extern void Event_Internal_Destroy_m25BA236C0C66CB87A89B81336D7BFB55917127BB (void);
extern void Event_CopyFromPtr_mC78295EF5861558EC93D3F8691E2A8B50DE84E29 (void);
extern void Event_PopEvent_mC780BAA7CE4F0E75C8B5C7DC5EB430C278B0D0AE (void);
extern void Event_GetEventAtIndex_m6A5EC17990487B54E754E77B5A663E05B394ACB0 (void);
extern void Event_GetEventCount_m5057138698EAA0B529F4FC7EDC96EDCB957F7FBC (void);
extern void Event_Internal_SetNativeEvent_mF0C015181EABFE56E2C90CD5C6DCA410C2C42746 (void);
extern void Event_Internal_MakeMasterEventCurrent_m67675F107F56ADDBCF72ECB4C3BE4DCE831C8214 (void);
extern void Event_GetDoubleClickTime_mF3D10CD927983547C6BF3479083B4155DE693826 (void);
extern void Event__ctor_m14342F32F62A39A8B8032286E2DCC07FEF72BFF4 (void);
extern void Event__ctor_mA5E77C0596952812A96703685523819CF50D71A0 (void);
extern void Event_Finalize_m0882CB2E5E0C20C5C9669518C4DB5D95F840DAB7 (void);
extern void Event_CopyFrom_m2F9B9704FBE156C5D58FF630F7968568C19821F5 (void);
extern void Event_get_shift_mB8409DA839B09DC6137848E131A6DBE70BB9E70A (void);
extern void Event_get_control_m1E363A7ABA4F2E8CF41C661A48D53D85D635D320 (void);
extern void Event_get_alt_m57F7F5C1F5FFCE43EFA6889F83CFA42DCA18A74B (void);
extern void Event_get_command_m202DE2CB0BE0AAB5CDFEC9DA1BBD3B51E8497547 (void);
extern void Event_get_capsLock_m96978250936197432547F10654BDA72912FA3CD8 (void);
extern void Event_get_numeric_m2731D0C083217F923383C2CAFB3DFA5E90886FFC (void);
extern void Event_get_functionKey_mF242E910AD9A24DC00BEF57915D817369E51BEDE (void);
extern void Event_get_current_mBD7135E10C392EAD61AC0A0D2489EF758C8A3FAD (void);
extern void Event_set_current_mDB5FE546AFA00DDF6CC23C106CE076EBEF36BCB3 (void);
extern void Event_get_isKey_mDA8FE1CC5E305BAF181E86A727173C9BE9A1B754 (void);
extern void Event_get_isMouse_mBD11F4FE2996DFAD2648C8A9648E301EDDA51D7A (void);
extern void Event_get_isScrollWheel_mE1E0A512E67F7A0EECD5AC25C4B2F8D31C92B7BE (void);
extern void Event_get_isDirectManipulationDevice_m9A72FB2DF7803E189857D24A65FB568B17533ED0 (void);
extern void Event_KeyboardEvent_m957733139998C86C7ECA28BA50863EB88B71418E (void);
extern void Event_GetHashCode_m9E93319C0E2A92678BC6B3B9A7B1758DBA605E6E (void);
extern void Event_Equals_mBA8BEAB37AE94F9B42F62D946DD61223E0F1258A (void);
extern void Event_ToString_mB30B330C86407E776E932EC18CF177A4066BA56B (void);
extern void Event_Use_mD77A166D8CFEC4997484C58BC55FEB2D288D3453 (void);
extern void Event_get_rawType_Injected_mDCA85E7076A7D6D3B01290CB5A79821C1056C1A2 (void);
extern void Event_get_mousePosition_Injected_mBB69D766EEB30783505E1B74BAE18A63233E15C2 (void);
extern void Event_set_mousePosition_Injected_m31B63D53DE048238D7964C6236B21916172EA60C (void);
extern void Event_get_delta_Injected_m4EEDB796FAFA905815EB8F89C200A8B851314B66 (void);
extern void Event_set_delta_Injected_mD97744D7B81368BBBB63E68934C41B41344E4068 (void);
extern void Event_get_pointerType_Injected_mB28B092716FE76712BF2640B11C4779884AAC9DB (void);
extern void Event_get_button_Injected_mBB57C0D585B25CB1CD2D3253105FD86B3067A561 (void);
extern void Event_get_modifiers_Injected_mEBCDDF68E5FFB407E12D3EE4AFFF1B88CE808004 (void);
extern void Event_set_modifiers_Injected_m81C5EC815905D62AF0B825CA14DD360B33528C33 (void);
extern void Event_get_pressure_Injected_m0BB21470B6093D69CC81B5DF0B0A42B9EF5E5F9A (void);
extern void Event_get_twist_Injected_m602DD5ADC6CEA12B0D0E4D3B70645EE87791F19F (void);
extern void Event_get_tilt_Injected_m0AEEC6BF7F7868238FD5269EC6E8DDD3251DEE67 (void);
extern void Event_get_penStatus_Injected_m9177A19359BD5B539130B65EB8A80A5163EC3BD9 (void);
extern void Event_get_clickCount_Injected_m474D30C83AB5D8442279764154ECC263558F4968 (void);
extern void Event_get_character_Injected_m0327C043FFD70576F14C3EA245B0909CBC162ABC (void);
extern void Event_set_character_Injected_mD4166E8F4946DCC9C8751C7193211621C8F1E9EB (void);
extern void Event_get_Internal_keyCode_Injected_m2DDCD9B211F9C700AD0927734E6C50F7BE843F0C (void);
extern void Event_set_Internal_keyCode_Injected_m94927796AF9F8BBB990A77FDD633B47DBF131F55 (void);
extern void Event_get_displayIndex_Injected_mF1ABC5D6975D6A26206D73EE53DB00C4AD886FC5 (void);
extern void Event_set_displayIndex_Injected_m47F9550915907B117387CEB2773FE4BD906628C9 (void);
extern void Event_get_type_Injected_m7556ED42AF45E8D907B8F1CBCF3976B4817E43DB (void);
extern void Event_set_type_Injected_mFB30C3F9276DBF245E74540E452A50CBC1FBDBA3 (void);
extern void Event_get_commandName_Injected_mC50B8EDA6D4BE29F77ADB83206A74C383BB1080D (void);
extern void Event_set_commandName_Injected_m272833DA9DE22F21DD4BB29E07D01A422654938B (void);
extern void Event_Internal_Use_Injected_m8B1CE93DFF429073D4BB010A26446C512AC05AA2 (void);
extern void Event_CopyFromPtr_Injected_m9F5BB726C0BCD2B59937307886FACE69C90C19B1 (void);
extern void Event_PopEvent_Injected_m812A627713A9BC9C00AB3499F16697F9A7BF869B (void);
extern void Event_GetEventAtIndex_Injected_m3214070478AA0BE8FEAB8B4D48AB2B43DC77195A (void);
extern void BindingsMarshaller_ConvertToNative_mD06CC80A68266C36E72936554BB830F6FB6561E5 (void);
extern void EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA (void);
extern void EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F (void);
extern void EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F (void);
extern void EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313 (void);
extern void EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE (void);
extern void EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6 (void);
extern void EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B (void);
extern void GUI_get_color_m15488B4AD785D10DEB5C66398D0FA9A0C0EA7ABB (void);
extern void GUI_set_color_mA44927D3F2ACB1E228815F10042A6F62B7645648 (void);
extern void GUI_get_backgroundColor_mCAA42085DAB8A1B71E2D0C6C8D86F93CD311FA33 (void);
extern void GUI_set_backgroundColor_m4ED80300A7DE3EAF923E3344E22D6682FA75B264 (void);
extern void GUI_get_contentColor_m32B15C8D6BEEFEBCE667ECD3CF664C83224F103F (void);
extern void GUI_set_contentColor_m3CDC4D626AC8B6D487AD19765D79C593B98AEF26 (void);
extern void GUI_get_changed_m3473B2964DCE8C2ADE081517093168C171BBE448 (void);
extern void GUI_set_changed_mBD91A44AFA77D2BF883B3150AF4AE6AC3ED121DC (void);
extern void GUI_get_enabled_m336E115A84DBD8D18A925D0755B51746B98B516D (void);
extern void GUI_set_enabled_mF2F99A6870ACAFAEFB5E8FF1B69C684951D390C9 (void);
extern void GUI__cctor_m97D837BF457542B0F7308E8999670A46E465A9E3 (void);
extern void GUI_set_nextScrollStepTime_mA35BA69E3FDBC961E42F6C9D02BB4E8776926A09 (void);
extern void GUI_set_skin_mD51BAED314B39004AE3FDE74F9895CA19F3E40E5 (void);
extern void GUI_get_skin_m97EC9EB4628B311C0DB7DF9FB19FAD82D6790A1B (void);
extern void GUI_DoSetSkin_mF4C06A8BE59628B6514F7FBF9422214A48BE03B9 (void);
extern void GUI_get_matrix_m3CA02DED0598EE32BD9E66CA533A78EFB0A246FC (void);
extern void GUI_set_matrix_m7759FEC96FBCB97E02B1BA44D2EC1B3FEEFA257F (void);
extern void GUI_Label_m4A951E57C7DCCF95A0306240144CA2713F546526 (void);
extern void GUI_Label_mFC6559DAC18FE889F1B94729AED3550374D18089 (void);
extern void GUI_Box_mB47BC44807774B77DB8B2BB548D339036544ACC4 (void);
extern void GUI_Box_m4A53BAE78DC7C6724F50E54D9BEB7135BAA0DA0C (void);
extern void GUI_DoLabel_mE43FD8B17DE5AF3B9E12E15B548CD6846F4AF27F (void);
extern void GUI_get_scrollViewStates_m940A384A713B8A7DC67016D1588965A42E561773 (void);
extern void GUI_CallWindowDelegate_m3FC075A6C33D007CBDC6983CDD6515C246E35B3F (void);
extern void GUI_get_color_Injected_m7B9A31188627647FDD914FB8A83C32627769D1CA (void);
extern void GUI_set_color_Injected_mF82410FC38D4C12CEC8ADCC9CCCC00F12035CA12 (void);
extern void GUI_get_backgroundColor_Injected_m81488D0D17EB867EEA60685182EAD8E0BC7CFB1F (void);
extern void GUI_set_backgroundColor_Injected_m16FDF89F7678824BA547AEF70D4EC84615C7D6B8 (void);
extern void GUI_get_contentColor_Injected_mA592670CB3A23833ED6F6FA43D021CA049CB6FAC (void);
extern void GUI_set_contentColor_Injected_mE1EFDCAC30FF6CE60437BF1B8B04488C9A75E2C9 (void);
extern void WindowFunction__ctor_m31D7B6C221D9A078AE5C8BA7C3BC0FA406EA7B71 (void);
extern void WindowFunction_Invoke_m27ADD2F0F97D0149CE0B6F6452B3C23229D2CC85 (void);
extern void GUIClip_get_visibleRect_m93F10FF2376C3BBBF3562A67DD7E207240D2F611 (void);
extern void GUIClip_Internal_Pop_m99B82F9D059E587FD37DEEB41385076E16162E62 (void);
extern void GUIClip_Internal_GetCount_m83C187F97642C73B9241C9A026CDA89A7A9EB8D1 (void);
extern void GUIClip_GetMatrix_mABFDC4C3D2B71C84191EAA109A4373A1D75BD3E1 (void);
extern void GUIClip_SetMatrix_m2C4B22CA0D33E580CBD455CC8E5422C8FF229733 (void);
extern void GUIClip_Internal_PushParentClip_mDA817B810C6724A0F236C876C08CFB0EC64E78A8 (void);
extern void GUIClip_Internal_PushParentClip_mEAE43F73F48A4CD59FD9432B4F1E50124A0F3522 (void);
extern void GUIClip_Internal_PopParentClip_m7B43C8DD6186703019A5B7ADDC1FE48FB67BDEFA (void);
extern void GUIClip_get_visibleRect_Injected_mBF3F116B530BCD6D5B3A5D110245691ADD4AA8BC (void);
extern void GUIClip_GetMatrix_Injected_mFAEC409FC44C49C7681DF684C954DF86AE076B76 (void);
extern void GUIClip_SetMatrix_Injected_m259A180FE5871D9D16330959A560EAC86E0224D0 (void);
extern void GUIClip_Internal_PushParentClip_Injected_m71572BEBE9BFFAA4D306958579D3B0B48411B87A (void);
extern void ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4 (void);
extern void ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D (void);
extern void GUIContent_add_OnTextChanged_m9F326364415BADDD1BDF1E9A5DE384DAD16B283A (void);
extern void GUIContent_remove_OnTextChanged_m8F2239CF4C975A1D00109C5D4F33D55ED3B40CC2 (void);
extern void GUIContent_get_text_mC6D7981351923AD7F802AC659314BA56DF7F3ED6 (void);
extern void GUIContent_set_text_m18A3EB5B4BD316561B3F4AB6BB3CC151684CE14F (void);
extern void GUIContent_get_textWithWhitespace_mDDCAECA04AF38B5C5BAE7C33AF8F2362F1CF113B (void);
extern void GUIContent_set_textWithWhitespace_m1A0A33F0C4EA311A6C4649FB4F78B8F8786936DE (void);
extern void GUIContent_SetTextWithoutNotify_mA593CEED57B890EE6EA83DC4729BDBA2676DF0FE (void);
extern void GUIContent_set_image_mB91F27FCD27EDBA24794D52B7F3DF1CF4E878164 (void);
extern void GUIContent_get_tooltip_mC2D07D7B2884A5F5A56F84A7FE6BF39905AB15BD (void);
extern void GUIContent_set_tooltip_m72C6B6EA0C9FCA1544A7FCF6C78A93E55D8CB415 (void);
extern void GUIContent__ctor_m89AC53A7E9BF9EB9E70297353DEAA6FEC2C800AC (void);
extern void GUIContent__ctor_mD2BDF82C1E1F75DEEF36F2C8EDB60FFB49EE4DBC (void);
extern void GUIContent__ctor_m68E7F8C12A7768D52689E89E1E687F86569A3E78 (void);
extern void GUIContent__ctor_m3FDFF98EA6ACDC116BCCA705EE8F8DEC09A4A0A7 (void);
extern void GUIContent_Temp_m4AE3B839AF38DD23ECC1D585C391E1CA43B8EA73 (void);
extern void GUIContent_ClearStaticCache_m36A399D55991F1B5B1C4A20DCDFF415B8636E934 (void);
extern void GUIContent_ToString_m9F42CA1D8DEFB446686D0010FF57B4F9B140BB9A (void);
extern void GUIContent__cctor_m1605F6A12B7BD089F1592F490DBF324ECEC3FE8C (void);
extern void GUILayout_Width_m3FADF145F37481F9FEFF0E89E8A466CF5532DCE3 (void);
extern void GUILayout_Height_m5E1526C541663A21437ED06E233FDDA08A856B91 (void);
extern void GUILayoutOption__ctor_m4EF826EA43073869166C8D94A1D9EB7898ACC3AA (void);
extern void GUILayoutUtility_Internal_GetWindowRect_m4F0CEA512EAD2BF0BBA0218A10B9C820C24D44CE (void);
extern void GUILayoutUtility_Internal_MoveWindow_mAD1ECDE72F3573D2F71B43C5FB8F90C10919C6CF (void);
extern void GUILayoutUtility_GetLayoutCache_m6086C9523E16ECC3FF4613F5A2441351CF4B2878 (void);
extern void GUILayoutUtility_SelectIDList_m601F4AA990B7FD59A779F5375EC55ADDB86927A9 (void);
extern void GUILayoutUtility_RemoveSelectedIdList_m701496086D15B8BF1C936EB431AFBC6627A787FD (void);
extern void GUILayoutUtility_Begin_m701551F1F833A31A154BFFC9F6F3143A12A33061 (void);
extern void GUILayoutUtility_BeginContainer_m34C50FF74C76B91E32E1A3575ABC0AA0AE0F3DDB (void);
extern void GUILayoutUtility_BeginWindow_m99FBC28B305B9C0589BC73138073BE9420C977F5 (void);
extern void GUILayoutUtility_Layout_mBC6C938DC931B8CABC1FA6C33AA60ECFAC3D9B30 (void);
extern void GUILayoutUtility_LayoutFromEditorWindow_m0D41A3D7897D91D4420C722C47502FCBA0352804 (void);
extern void GUILayoutUtility_LayoutFromContainer_m81EC681FE0A88C36CCA8D4382043279F709EE59E (void);
extern void GUILayoutUtility_LayoutFreeGroup_m81D18A1401F6FF7EB4A3C1CC26D9BE80998BBF5C (void);
extern void GUILayoutUtility_LayoutSingleGroup_m95E3F31426ACA641C57016A1D1A058366A56AFE8 (void);
extern void GUILayoutUtility__cctor_m2CEDA9A8EB23B7D3A5A97825E6B192235954DC48 (void);
extern void GUILayoutUtility_Internal_GetWindowRect_Injected_m03328FF57858A53621C5907B345C56FA2C5AF0EC (void);
extern void GUILayoutUtility_Internal_MoveWindow_Injected_mDFDA2042DAFBDEBD108AC01F6F19E7D0F395B6A7 (void);
extern void LayoutCache_set_id_m532720FF0F65E8039E37D015910E2F1AE1C9F4FB (void);
extern void LayoutCache__ctor_m73B4DC62A0A7669976C8444DDB54EF8D55BF3E0B (void);
extern void LayoutCache_ResetCursor_m728841782E13F82B1AE96E40AF16D6C8EBE6D59A (void);
extern void GUISettings__ctor_m4AA9AFBD94306E007937909CB7F542DF2E491404 (void);
extern void GUISkin__ctor_mAA94A46B37D9C2F70962435F250BBA202CD1EC7A (void);
extern void GUISkin_OnEnable_m5A7FE1F57C549711FCCC2DB0322F8667129AA0BF (void);
extern void GUISkin_CleanupRoots_mAD2E77BE9440832E8BC8CAA9C7F2D85C3D2F8B17 (void);
extern void GUISkin_get_font_m806CF702C59E43DF55BA441030A60F80E9D8CFD5 (void);
extern void GUISkin_set_font_mF98516DE4363C888D7215006D51BD527F3F9DDA9 (void);
extern void GUISkin_get_box_m21BE7FC56D903B95BAFAE8890425D330EA88D893 (void);
extern void GUISkin_set_box_m82E578044569D3831D103FFA1413D81DABF74711 (void);
extern void GUISkin_get_label_m99E1A8D6D8592F88F581437D24DB1EDE05C63E5E (void);
extern void GUISkin_set_label_m7E9E63BBA37F93D886F7E6AF70772ECD7894462B (void);
extern void GUISkin_get_textField_mC554496BAB959445F0CFA30BDC5736DC1F057D48 (void);
extern void GUISkin_set_textField_m4730F5B544F2A87AF3CA75A01FE845E5D40E06BE (void);
extern void GUISkin_get_textArea_m0ECBC9D126D930490F96E100B27F245E555EB7D1 (void);
extern void GUISkin_set_textArea_m916CC2135EE608D81035D3E96787735534DF4E9D (void);
extern void GUISkin_get_button_m51948EBD478CF9223522AD29B7FBD1BABAABE289 (void);
extern void GUISkin_set_button_m45F7F5CBF3E9286F4CD601AA92C0C3207C0BB373 (void);
extern void GUISkin_get_toggle_mD5F318C602494C478F09C2D48741EC7A9CF5B849 (void);
extern void GUISkin_set_toggle_mFE0DA0EC1F1952464B85894CCCFECFA5E0E0C57E (void);
extern void GUISkin_get_window_m760DAF129E72775DFD18CB71720AD306345E91C2 (void);
extern void GUISkin_set_window_mA74900E5D554578F3F45DD858B79C5A8FA4A6220 (void);
extern void GUISkin_get_horizontalSlider_mAA1753FEEDBA6E28A3A56C3E44A8F5B3D6C8336B (void);
extern void GUISkin_set_horizontalSlider_m8357A90F358C1A040308C8D0DEE363D3ABA71575 (void);
extern void GUISkin_get_horizontalSliderThumb_m9EE5EF8204397C2946D7F384AB7D8A17693837BD (void);
extern void GUISkin_set_horizontalSliderThumb_m1042BED23F10E28042D77D7E738F86C1FEDF460E (void);
extern void GUISkin_get_horizontalSliderThumbExtent_m6408F303B8932D6E74B307070689A96EA082D612 (void);
extern void GUISkin_set_horizontalSliderThumbExtent_m8F4C637DB7E25697AB463B9F2F8D50D8493609C1 (void);
extern void GUISkin_get_sliderMixed_mFD8CBA8BE229E299D63822AE3E632DABCC27FF61 (void);
extern void GUISkin_set_sliderMixed_m8A129FB05FAA0970C01A8C3DB14903E13F8E37B3 (void);
extern void GUISkin_get_verticalSlider_mB7EC86D11019F1892365E9C6F2A846A68879BBD5 (void);
extern void GUISkin_set_verticalSlider_m02D94C0BFF867BD8B1ECE05AB50F7F2475DF0E35 (void);
extern void GUISkin_get_verticalSliderThumb_m3D86347FFC94841C8B6CA94F9F946C76E96EBADB (void);
extern void GUISkin_set_verticalSliderThumb_mFBFA636B05068A0E7D24C8C3B06044AB2ACD4C58 (void);
extern void GUISkin_get_verticalSliderThumbExtent_m299DED8D10A1CE0F22B43BAF47D70DA1EB079AFA (void);
extern void GUISkin_set_verticalSliderThumbExtent_m3ECC754FC08BCFA5C3264A6B83C9EE280C1EFCDD (void);
extern void GUISkin_get_horizontalScrollbar_m945A39FBD098D3800A189FC34B9CE9E8AFF3AEEA (void);
extern void GUISkin_set_horizontalScrollbar_mF08764A78F23728E6FE157F08B9A0127157071FA (void);
extern void GUISkin_get_horizontalScrollbarThumb_m5011EED1650028044BCC7F6DE2829AC0243208BB (void);
extern void GUISkin_set_horizontalScrollbarThumb_mDDADEFFD5BF9B88AC4A37AEA13B6FCCC28A3F696 (void);
extern void GUISkin_get_horizontalScrollbarLeftButton_m4A6E58CF80A66F58CF5792B31D08A2D74BF40567 (void);
extern void GUISkin_set_horizontalScrollbarLeftButton_m3FDB02C1FDE47BCE92068EA21C531F1F6D667DBC (void);
extern void GUISkin_get_horizontalScrollbarRightButton_mADFCABC3339BE56E2BAD5443789D8D4FBDD73DAC (void);
extern void GUISkin_set_horizontalScrollbarRightButton_mE5ED9D2BB554FC29F6A69C81B9361A5E6E004CFD (void);
extern void GUISkin_get_verticalScrollbar_m600012E344D3EB4C687E8A4BE78CE33068374D2A (void);
extern void GUISkin_set_verticalScrollbar_m4F55D5B66DB408A5009FC00ABBB9AFFA0C65FFEC (void);
extern void GUISkin_get_verticalScrollbarThumb_m62663C3DDC40AC91FD4666FBF844DCD83DDA7DE6 (void);
extern void GUISkin_set_verticalScrollbarThumb_mECEC0DC79CCD9AABBF6CBA3CE5141C38699B5EC6 (void);
extern void GUISkin_get_verticalScrollbarUpButton_m0B5575CA6AFB1C74899BF931296EFC39B2C1A902 (void);
extern void GUISkin_set_verticalScrollbarUpButton_mF50F99BC770529789363EC9B1C37E610FF8A708C (void);
extern void GUISkin_get_verticalScrollbarDownButton_mFC75161EDB03597ECF09E189C8A57F0E64213E3D (void);
extern void GUISkin_set_verticalScrollbarDownButton_m37DD0E232BB98BD219494A297DDBE7620104D328 (void);
extern void GUISkin_get_scrollView_m5466CD77A4A7E01320DB0E0F57253D41226BB0B8 (void);
extern void GUISkin_set_scrollView_mF2D35906BC020D81F909E65B420494F254E4DC32 (void);
extern void GUISkin_get_customStyles_mAC8A1CFD5756E6C0D367E06B4BDC365E6F6BC39B (void);
extern void GUISkin_set_customStyles_mD22F50472DDB0A9770B18F0A15D3F73EEEC4A8B2 (void);
extern void GUISkin_get_settings_mCBAE5727D7774FAEE47CCC8B4C47AC321DDD85C2 (void);
extern void GUISkin_get_error_mB953A37C8F3296E529190A34E18506C735848C01 (void);
extern void GUISkin_Apply_mA85017BE8C994F6220112EE8D00D3C37C1FF2104 (void);
extern void GUISkin_BuildStyleCache_m8E99CC278C76A6DA63A24BFD2DE42AE313C0F7E1 (void);
extern void GUISkin_GetStyle_mF024BC5177A2AD477ACF44D87BE6A629C91562CA (void);
extern void GUISkin_FindStyle_mF82C37E2481D2B9E96C26EFE0353F8954F844FFE (void);
extern void GUISkin_MakeCurrent_mDB3BB1FBA5BD2FEDDA3F32F11170F47A6444AEED (void);
extern void GUISkin_GetEnumerator_mEC308E2DA9A94E09C622D13F82EB7ECCECF8AFF0 (void);
extern void SkinChangedDelegate__ctor_m20D33B3868351B98B708468F7A8192C1ACF85CD1 (void);
extern void SkinChangedDelegate_Invoke_mD14214487F9A0E4DD7EB7F97927D03EC8F1A3B4C (void);
extern void GUIStyleState_set_textColor_m5868D12858E6402247953BCCDDA7A543BE6084F1 (void);
extern void GUIStyleState_Init_m0D3428E2BA3343F8AC49253DE3AAC54EF07F4873 (void);
extern void GUIStyleState_Cleanup_mF244B2DAEE9DE90A300E6B7D78F9547BBBE59826 (void);
extern void GUIStyleState__ctor_mD47FE21F7FD8D786F7E8E4E8C3DCA224F9237AD7 (void);
extern void GUIStyleState__ctor_m74536B867B0F57F8A7DC74E78018830A948E4555 (void);
extern void GUIStyleState_GetGUIStyleState_m0B273F7909166249E3D98FA410C2D8A72091C7B1 (void);
extern void GUIStyleState_Finalize_m5CC6FBD8C44AF1091CACD6F7032E73B1114765B2 (void);
extern void GUIStyleState_set_textColor_Injected_m42CA271DB3678AFA5B18673C5EB41B04DEA7ECA3 (void);
extern void GUIStyleState_Cleanup_Injected_mC40EE1A133306299830D8FA5C3D90D8887447CC7 (void);
extern void BindingsMarshaller_ConvertToNative_m00C4592A76BD688ADB2901D420CB349EE6DD339B (void);
extern void GUIStyle_get_rawName_m9C87EB1EA6CC5989EFF3567E85A2D0A3DF256782 (void);
extern void GUIStyle_set_rawName_mF8928B91294B5DA15AF365C760BB1437CF507ED6 (void);
extern void GUIStyle_get_font_mBD123E375D357B37F8E1303F288517FD883C1117 (void);
extern void GUIStyle_get_imagePosition_m339AA340B169230E9795B61BEE4BB1EDAD6D95B4 (void);
extern void GUIStyle_get_alignment_mB650B3193AFBD4D1C2EB0A15C978A0FC3E377D2E (void);
extern void GUIStyle_set_alignment_mEDC62A775C9551DBD1FEE4043F115E034EF12937 (void);
extern void GUIStyle_get_wordWrap_mD0046078E78B0F8F1988E055B7EEB261FE8C69AD (void);
extern void GUIStyle_get_clipping_mDF8B2F6EA1DC0E36268C6F58C2062442947AE3AE (void);
extern void GUIStyle_get_contentOffset_m1585F4928435114551C27889DE159EEF55345C70 (void);
extern void GUIStyle_get_fixedWidth_m9CB5B4E096287F75F4E4E3376590C7C085E28DE8 (void);
extern void GUIStyle_get_fixedHeight_m009155CF284509A87E6037D0A392A630FA728F7A (void);
extern void GUIStyle_get_stretchWidth_m528FFD3EB3104D0322F2EADBBE7DBFF3FB34CB37 (void);
extern void GUIStyle_get_stretchHeight_m5ACA8F9CD25746932719C927159A105AADA5061F (void);
extern void GUIStyle_set_stretchHeight_m51C55ED43AA4EDE125E0C423FA0D301E81C09378 (void);
extern void GUIStyle_get_fontSize_mBD6EEA6C9C09825DACE0395B1EC8D773FE94F8CF (void);
extern void GUIStyle_get_fontStyle_m9CFB0CCF194C6D7010325F24300B7BF21CE68FAE (void);
extern void GUIStyle_get_richText_m849DD36265E87291AC9FA304316CB730B866D550 (void);
extern void GUIStyle_Internal_Create_m2C5F872F6FE8C423759017DC72267D6AF637BC75 (void);
extern void GUIStyle_Internal_Destroy_mD93F2F454B69DB5C534AF9F4F6D847F955A39977 (void);
extern void GUIStyle_GetStyleStatePtr_m60D51351B040299578007102C3857E8E8F14FAFB (void);
extern void GUIStyle_GetRectOffsetPtr_mCABE2CEFE5CDB942D464051BF8B0E043BCC59593 (void);
extern void GUIStyle_Internal_Draw_mBEFC164F21949135F404FDA678F368FBA8074D50 (void);
extern void GUIStyle_Internal_Draw2_mD1050A7750AAAEEEEFD4EB6C8C8AFB0591B1221D (void);
extern void GUIStyle_Internal_GetTextRectOffset_m00F59B7312BA8622DA9E3412628886779EDD2FAA (void);
extern void GUIStyle_SetMouseTooltip_mFF3E22C7330AE180E83AB2929049BCD87B13B21E (void);
extern void GUIStyle_IsTooltipActive_mAD93F97B98889CA47BF1305F3D4C87D5EE8DD777 (void);
extern void GUIStyle_SetDefaultFont_mD6B98375749805CA5084CA8C5D6A1295359AE0E3 (void);
extern void GUIStyle_GetDefaultFont_mE8076F2F0F05DB787B49719629DC21BEBF0028E7 (void);
extern void GUIStyle_Internal_DestroyTextGenerator_m887FC7292D8B991D2674805E1FF63667CF56EBDF (void);
extern void GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9 (void);
extern void GUIStyle_Finalize_mFF6A6FBA538B711A6ED369DD83A41F25DE6EEE85 (void);
extern void GUIStyle_get_name_mDF9EF43C46A0B9431DAF4EB0CE1D18EA32E16B75 (void);
extern void GUIStyle_set_name_mE618266DC07236117AAE05FE8D2B14A595FCF020 (void);
extern void GUIStyle_get_normal_mDEA2808FBD692E505784BD9E521738B4321BCA8F (void);
extern void GUIStyle_get_margin_mD0AABA2CB3FB0CFC3C414635E6225D3003315D1B (void);
extern void GUIStyle_get_padding_m04E3210A51B2522158941AFA97ADC19C835987C2 (void);
extern void GUIStyle_get_lineHeight_mC814199D1ABA3CE38358BA70347562B0CDFEB96E (void);
extern void GUIStyle_Draw_m7B978F5F5B576810CF8546142D23FD9990E002D8 (void);
extern void GUIStyle_Draw_mACFC9CE57BD530BB6A9592149DD95108A8014406 (void);
extern void GUIStyle_Draw_m3DBF8DC58719720455DFC818590D77752BA31008 (void);
extern void GUIStyle_get_none_m808A9FE1F78920E4A29ED3484B99588B46D88938 (void);
extern void GUIStyle_GetCursorPixelPosition_m4FFBD3DC05CE503355DF01E57023AC349032CB2F (void);
extern void GUIStyle_GetPreferredSize_mBA7966F20B676F3F1F587D97AC83D87D64397C5D (void);
extern void GUIStyle_ToString_m41A8A58B4D9659047D06EF2A5AE5F170AE198ACF (void);
extern void GUIStyle_GetMeshInfo_m79641B3D54BC4C9CC558A05802241D596284C872 (void);
extern void GUIStyle_GetDimensions_mF0B843919AB6FB621C04E0C1EF800A222B185BEC (void);
extern void GUIStyle_GetLineHeight_m401C4B6C6AA072FEED8214A84E0B1E146591324C (void);
extern void GUIStyle_EmptyManagedCache_mB8A9DA9F8C6BB6A2948C4DD52B392DF643067AC2 (void);
extern void GUIStyle__cctor_m4B955524A4DAEAAF103D78D9316756CEFA16FB62 (void);
extern void GUIStyle_get_rawName_Injected_m31AACF13426ED39D6E7C0EA1218D35576CD0CF29 (void);
extern void GUIStyle_set_rawName_Injected_mA81CAD1E6BC87E14E6A2F62A0D4EFFF61919454D (void);
extern void GUIStyle_get_font_Injected_mC05595AAE4BEBC4D740E156A7089CBFEB56A3E3A (void);
extern void GUIStyle_get_imagePosition_Injected_m2F0ABE9992E935467DCD9BA67447EF32246171B5 (void);
extern void GUIStyle_get_alignment_Injected_m0FC74B05D548FBD4A99DCD0906BCD82C3A892455 (void);
extern void GUIStyle_set_alignment_Injected_m8B4D439423ADEBE54D7C9885AA14E9D0748D4AB6 (void);
extern void GUIStyle_get_wordWrap_Injected_m73520CD7C856238389EF97826FC5014147AC3A5E (void);
extern void GUIStyle_get_clipping_Injected_m737EBBEAFC7EF5C75C1531B80526D2E58C5E563E (void);
extern void GUIStyle_get_contentOffset_Injected_m513BE18A016A98DF77714FE89EE85DA8DED12EC1 (void);
extern void GUIStyle_get_fixedWidth_Injected_m32CE485041E440BF117C81DADB1542C4B67AA805 (void);
extern void GUIStyle_get_fixedHeight_Injected_m958CFA9442BABE3E27FBF2E380DA034088719AD5 (void);
extern void GUIStyle_get_stretchWidth_Injected_mA068F7B72A68C5E4C66FC2CFABF4F7884BD56631 (void);
extern void GUIStyle_get_stretchHeight_Injected_mC737DCD17A7B7460579F62178E5584326635450C (void);
extern void GUIStyle_set_stretchHeight_Injected_m36334CF4EFFD289385BD4CEE02FCF27297633136 (void);
extern void GUIStyle_get_fontSize_Injected_mCF5A4090D50B9F2E8C857E4530D4C4C119F8E0AC (void);
extern void GUIStyle_get_fontStyle_Injected_m423CCEDE4CB29F1F74F99BBEDD864E9D1DF1E5E9 (void);
extern void GUIStyle_get_richText_Injected_mCD010A09029CD34C908F0656AFB59404E371AD63 (void);
extern void GUIStyle_GetStyleStatePtr_Injected_mE5EA42C28345450F52E5B182D65A79FC430BC632 (void);
extern void GUIStyle_GetRectOffsetPtr_Injected_mB750F19C29D4A94361F6BE0ED9A3E2B653A0AC2D (void);
extern void GUIStyle_Internal_Draw_Injected_mF3DFEAF91F9C52DB9C70917B3434F58A48866359 (void);
extern void GUIStyle_Internal_Draw2_Injected_mD7186D675DF56B82C86F5EB7E78CB61D6B8DBDD4 (void);
extern void GUIStyle_Internal_GetTextRectOffset_Injected_m20FA3FA9CD37DE8975F76EEC3982F7AEA383675E (void);
extern void GUIStyle_SetMouseTooltip_Injected_m79A48A1881E44B23C8C782A2B5847B825D76A91E (void);
extern void GUIStyle_IsTooltipActive_Injected_m73B8C97744C579784612C4707433D3418FFB943A (void);
extern void GUIStyle_SetDefaultFont_Injected_mDCF32789075C2882DBFAD695D532C6D93CB8B192 (void);
extern void GUIStyle_GetDefaultFont_Injected_m9C76CB99642C0AA8E348EF467DE4EBBE586CE56F (void);
extern void BindingsMarshaller_ConvertToNative_mACDFB57BFBEE121FFFDF70E3CCAC1F0FB7C935A9 (void);
extern void GUITargetAttribute_GetGUITargetAttrValue_mD0E7A4A7147F6B97077284408283EA380FE040B4 (void);
extern void GUIUtility_get_pixelsPerPoint_m13E69FE793E736FA60A61C6756F2FF57BA6C9F31 (void);
extern void GUIUtility_set_pixelsPerPoint_m73020B1AEBBF5F843FED3FBA80E100224768931A (void);
extern void GUIUtility_get_guiDepth_m011B188F7C41DAE079019E64BC064208E618F315 (void);
extern void GUIUtility_get_textFieldInput_mDB514BD41982E9A309A7E0297270162FA6918EBA (void);
extern void GUIUtility_get_systemCopyBuffer_m01E2DF71533C31A4C552B9177D7CBA0C6CA3FC2A (void);
extern void GUIUtility_set_systemCopyBuffer_mD14AE32BFEA4773BDC679205D470A228B8F225E8 (void);
extern void GUIUtility_Internal_GetControlID_m9836A3FD9B0629A36F356FD8D4606092B2E2AD21 (void);
extern void GUIUtility_GetControlID_m3AACC1B4BDE62E7C3E5D861A470351FA1BAA752E (void);
extern void GUIUtility_BeginContainerFromOwner_mA895E862C2444F93423836CE4B5F35E2F31B8B28 (void);
extern void GUIUtility_BeginContainer_m4A0F355072CE2DBCB50F706885EAAB70DB8C7115 (void);
extern void GUIUtility_Internal_EndContainer_mCE42BC4D58E684B724B58EC3C901E67BA62F1BF7 (void);
extern void GUIUtility_CheckForTabEvent_m6AC98E67A89330ACB330CBBC135E3DFBFCAC2C49 (void);
extern void GUIUtility_SetKeyboardControlToFirstControlId_m02DF215A0F07822021E17AF4153B4C31468287C0 (void);
extern void GUIUtility_SetKeyboardControlToLastControlId_mB7A3C208ADDF009FB9C3C522998459BCD9B107EB (void);
extern void GUIUtility_HasFocusableControls_mE149711C5695D4DB44940D8073487992F1ACB883 (void);
extern void GUIUtility_OwnsId_m46FE01F2CEF3A94173A1DB64A888E4DB1EBC74D2 (void);
extern void GUIUtility_AlignRectToDevice_mE651D8C8024AD7FF9C1773FA000A2626BC263B8C (void);
extern void GUIUtility_get_compositionString_mE06412C5CE41311C00BFC4028716D5F03EDD85E9 (void);
extern void GUIUtility_set_imeCompositionMode_mE5C0A2391D65DAC056B1752D78B5A832DCB314C7 (void);
extern void GUIUtility_set_compositionCursorPos_mECE1139A5660FFE152382DAB2DDBFADB96BB9644 (void);
extern void GUIUtility_Internal_GetHotControl_m8230315B3FECDB164C84AFC40C180C2C7B319892 (void);
extern void GUIUtility_Internal_GetKeyboardControl_mD0783552D4ACDA842F86F126C7A48ADC79340AB8 (void);
extern void GUIUtility_Internal_SetHotControl_m56F3F333B107EFD83C7F3D703DDA48C5A19BFCB8 (void);
extern void GUIUtility_Internal_SetKeyboardControl_mC8401D9C911D310EAA2284161264D2FC9D141418 (void);
extern void GUIUtility_Internal_GetDefaultSkin_m86F21D22A34DC2243194B8929A499FD98D26A234 (void);
extern void GUIUtility_Internal_ExitGUI_m5B145534F61B8CE2A2915A9297D0F25D771D4459 (void);
extern void GUIUtility_MarkGUIChanged_m43158D22AA065483FD91222B898772AEC06809A1 (void);
extern void GUIUtility_GetControlID_m2E0F66C8714A84DD5E9BEF4B9B464DAF1C03A9F7 (void);
extern void GUIUtility_set_guiIsExiting_m0DCDD09CD48330FD781C03D2EA20F973878A2BC5 (void);
extern void GUIUtility_get_hotControl_m6CD6AD33B46A9AFF2261E2C352DC7BAB4C20B026 (void);
extern void GUIUtility_set_hotControl_mFBC648186C83874DE776A508C420183ADB527E9A (void);
extern void GUIUtility_TakeCapture_mD8AB4A480269628E17877B77A94A6481EFC9763C (void);
extern void GUIUtility_RemoveCapture_m295E1BC4B7E1D471AF7C40E3B587B7D525E3D693 (void);
extern void GUIUtility_get_keyboardControl_mB0FAC848390B7F163CD2EE0A911FADD5CAD70B1E (void);
extern void GUIUtility_set_keyboardControl_m10F53FE5B292C2DC3C9A55CB504CC0DF36139465 (void);
extern void GUIUtility_HasKeyFocus_m6AD234443A7B2AB471E14BE141FC5E8ADD261A0F (void);
extern void GUIUtility_ExitGUI_m9B30B2DFC94CC1C04D1F78358D79E9DAC1231B03 (void);
extern void GUIUtility_GetDefaultSkin_m3275F31A9D5C3D90A1BCF5135F5B3968D6CD2C33 (void);
extern void GUIUtility_ProcessEvent_m88640934E0C2BFA9BAC544DD2A91112FE8227FE2 (void);
extern void GUIUtility_EndContainer_m19D0D5BA46EDAD7AF2D408A34D0141C5E481D963 (void);
extern void GUIUtility_BeginGUI_m05702C560EBBC0B0CA3AD4F1FFBB5BD070DA2E04 (void);
extern void GUIUtility_DestroyGUI_m5F94109C61BC0394F8936C899273093A6008702C (void);
extern void GUIUtility_EndGUI_mB34E82D4DD7A0AD22012DBAC207F605A68EA5E2E (void);
extern void GUIUtility_EndGUIFromException_m9C8B34B811C1E32C1BC818A57817FF5E117EC1B0 (void);
extern void GUIUtility_EndContainerGUIFromException_mC60505F763292A2C80F7FBC0644F3B4679414DEB (void);
extern void GUIUtility_ResetGlobalState_mD0A482A31337B6200F644995345CF56849913928 (void);
extern void GUIUtility_IsExitGUIException_mB887DAF961E8C1124916777B812FBF2324F5265F (void);
extern void GUIUtility_ShouldRethrowException_m60E879B4683840AAD5CD514E8C3BDDCC6403B652 (void);
extern void GUIUtility_CheckOnGUI_mD167632D5D038DF66CC97F231CD45736D1F556D6 (void);
extern void GUIUtility_RoundToPixelGrid_m0E594150154A6CCAD942F6B23179FB6886361105 (void);
extern void GUIUtility_AlignRectToDevice_mE788EB722671F5DC10F7ADB8CA1A3427749ECDD1 (void);
extern void GUIUtility_HitTest_m55D2F9EAC7EA99CA0C490546A6B45DA96F5AB3DA (void);
extern void GUIUtility_HitTest_m8C93A1BFB637176154C02F73038A98D1F616A7C2 (void);
extern void GUIUtility__cctor_mAA2103B5A88A5D093DB8AEDBE54C5E4757D84A87 (void);
extern void GUIUtility_get_systemCopyBuffer_Injected_mE69D1D289360DBA9C2260813753B9DDB9D5C3EAD (void);
extern void GUIUtility_set_systemCopyBuffer_Injected_mB90B727093E367031D146DC0D84249C7CAA53124 (void);
extern void GUIUtility_Internal_GetControlID_Injected_m00F0DDAB73176CDD6EB5F19AA64511CF445E1249 (void);
extern void GUIUtility_BeginContainerFromOwner_Injected_m9A05421D661AB304DCB90FB43A56E912E9438A24 (void);
extern void GUIUtility_BeginContainer_Injected_mCA3B5274BBA81F16852B01CF4DFCE96840071420 (void);
extern void GUIUtility_CheckForTabEvent_Injected_m24C24CF2AB79C28490AD2B64407C5903D4DDC76B (void);
extern void GUIUtility_AlignRectToDevice_Injected_mED42E3383D2A790E76602A5AB894DDE4850E43F1 (void);
extern void GUIUtility_get_compositionString_Injected_m0EDBBEEAC7AAFA15E3C7C9C4CCB624F120679FD3 (void);
extern void GUIUtility_set_compositionCursorPos_Injected_mF035733A0EF9A0258AB44982286A8FFFBF2B09A6 (void);
extern void ExitGUIException__ctor_m345D7AD70E401C1AFD46E537CDCEC0F1C8BA342B (void);
extern void IMGUITextHandle_EmptyManagedCache_mAC1ACA3F16AF5B1E61236E71BDAD88A2DA3570FB (void);
extern void IMGUITextHandle_GetTextHandle_m6B91EF10F51F622EF4D4572D737FE6CDCA30C4D3 (void);
extern void IMGUITextHandle_GetTextHandle_m6328A1878F88DC4310AA86B2588578487FA04E83 (void);
extern void IMGUITextHandle_ShouldCleanup_mB7AB830F4456EB705C6A0E4A4E799EE24A8AE6D2 (void);
extern void IMGUITextHandle_ClearUnusedTextHandles_m35270F73AD0FE1AE63D765A80CA83C01A7C0D5B3 (void);
extern void IMGUITextHandle_GetTextHandle_mAA654059E522E2AA9A69E7CBB8AD7A425FB270B9 (void);
extern void IMGUITextHandle_GetLineHeight_m984EAED8831A40A4CD43CBE700513FB3A9852726 (void);
extern void IMGUITextHandle_GetPreferredSize_m17C480F0B46A0A56B50B7649455357963769EB58 (void);
extern void IMGUITextHandle_ConvertGUIStyleToGenerationSettings_m97024F83D0248CA86E198DEF2E5C0A97A422B6DA (void);
extern void IMGUITextHandle_LegacyClippingToNewOverflow_m9BB92CC5B543D6695FEABBAA2133F151BD564623 (void);
extern void IMGUITextHandle__ctor_m0812B91A7679E17A351E3F3C010AA4DB0057AB4E (void);
extern void IMGUITextHandle__cctor_m2D991A3AD827F1B2D4819D6891686BD68C599F8F (void);
extern void TextHandleTuple__ctor_m709BE4E88D85ACC314D665A81ECB76A9614E8786 (void);
extern void GUILayoutEntry_get_style_mEFB6A8443849EC32BD84059C09632B53E44A5876 (void);
extern void GUILayoutEntry_set_style_m0A23F7EFF504A581FC6CA86EF3BE753F060AC48A (void);
extern void GUILayoutEntry_get_marginLeft_m3B362DA8241B4008C2A6CDA693295A609F765221 (void);
extern void GUILayoutEntry_get_marginRight_m032808DC8C04B31150407F3F61E71865C2636D7F (void);
extern void GUILayoutEntry_get_marginTop_m47BAB82D31A45E21F9AAB8229265788C0D19487C (void);
extern void GUILayoutEntry_get_marginBottom_m2BCCF0FC72E0230E155E7A26BA9FFD904AD4C221 (void);
extern void GUILayoutEntry_get_marginHorizontal_m9847FB7747542BB322195F9CF4B75F55339CD7B5 (void);
extern void GUILayoutEntry_get_marginVertical_mCD309A186E80B22E75DD8F15D2598B9B739C7AD3 (void);
extern void GUILayoutEntry__ctor_m011B3DA69713EEA6BD98D4056B5ADE01F237E5B2 (void);
extern void GUILayoutEntry_CalcWidth_m77BB8C0413A27303E4E61CB53586FD4A825C5EF3 (void);
extern void GUILayoutEntry_CalcHeight_m295D607AB2FDD78D7C665BB3FB3A495E2E8CC0A6 (void);
extern void GUILayoutEntry_SetHorizontal_m268577E88A2AE5870C14EFDA9CB88C94CAC2ACE9 (void);
extern void GUILayoutEntry_SetVertical_mA20893626441C55001C1940C53A6A100DD22D61F (void);
extern void GUILayoutEntry_ApplyStyleSettings_m2D3679DAF547D104FE48E7D6D8E27B639F6A666B (void);
extern void GUILayoutEntry_ApplyOptions_mF024E6CEAAD97888AE293810E01F8431D79456A3 (void);
extern void GUILayoutEntry_ToString_mD3785AC5958EB56ECA6E5D325D166C5F5725E615 (void);
extern void GUILayoutEntry__cctor_mF6F64749802F89E5AA0A1458CE99CA5FC0D639C2 (void);
extern void GUILayoutGroup_get_marginLeft_m343D82AA90154850B9B2A97B9E471D5235761EB3 (void);
extern void GUILayoutGroup_get_marginRight_m2710F9CCC1B6D67BC4F9D9487B082B7E143757D0 (void);
extern void GUILayoutGroup_get_marginTop_mA61C984665E93EE9E8670753AF919208528C4F87 (void);
extern void GUILayoutGroup_get_marginBottom_m1EC579493343750FB013A6F01AD84DFEC8D489BD (void);
extern void GUILayoutGroup__ctor_m2AA89FAB5BB5BA76F4059D106A59E346739755D8 (void);
extern void GUILayoutGroup_ApplyOptions_mD4C0BFAC7A90FB32BC6DC99ECA3EEA6C1C9396D2 (void);
extern void GUILayoutGroup_ApplyStyleSettings_m5A88CB0FC11FE81405684C3EFF7EF7DA974D2649 (void);
extern void GUILayoutGroup_ResetCursor_m58C36F1ABC54BE5EFC16D512318BED9EB8918127 (void);
extern void GUILayoutGroup_CalcWidth_mFA744462378028538F1E3AAB39CB6AF0FBB1851B (void);
extern void GUILayoutGroup_SetHorizontal_m37D01CDDE4FAEDB20E0D469805EF96B878DFB5D5 (void);
extern void GUILayoutGroup_CalcHeight_mAA9676BD80BAFC48F515ACA00E83FB7E9EE1FC2A (void);
extern void GUILayoutGroup_SetVertical_m28ADC75A1C5148E22EDD149221535C4B97BC5FE2 (void);
extern void GUILayoutGroup_ToString_m7859D80D5D81B23684C4309DA0565D4CE1D2680C (void);
extern void GUILayoutGroup__cctor_m9214FACB657F5C28173EDCF59DAD85F14E7E2800 (void);
extern void GUIScrollGroup__ctor_m95351A883B27B71698A4B84815CEA687D109F3FB (void);
extern void GUIScrollGroup_CalcWidth_m6B927DBF94A8940301A9FB64190403E5667712CE (void);
extern void GUIScrollGroup_SetHorizontal_m31FCDD252E67D51FC954C8E2C358BA0EB3AD7601 (void);
extern void GUIScrollGroup_CalcHeight_mCB0CEC4871F6540145949E4CE8242172A75B2E5F (void);
extern void GUIScrollGroup_SetVertical_m8609CD909413A7364781818DDE37A314D8795FD6 (void);
extern void ObjectGUIState__ctor_mA9AB2887ABAF5102164545D7F0CE59BCF05618B4 (void);
extern void ObjectGUIState_Dispose_m156DC13F33DEFB261C8B13EB98A1A3782D182DE8 (void);
extern void ObjectGUIState_Finalize_m10310B7E07DB5215C7845BF0F770B587D4F4C1B8 (void);
extern void ObjectGUIState_Destroy_m316F4C75D0C8F18896A69BB9E39D90C0CDBE8726 (void);
extern void ObjectGUIState_Internal_Create_m22F3AED2A44D4D00B478C2626295D432F74383EA (void);
extern void ObjectGUIState_Internal_Destroy_m936A111D9F70932A3030FE851C9E3BD82FD1F425 (void);
extern void BindingsMarshaller_ConvertToNative_m09E6A8BB8FD9F387A27D6B7322C538A9632B5FC8 (void);
extern void RuntimeTextSettings_get_defaultTextSettings_mCDC1440CA20563BCCBC6A6ED93B7D53D589A3CCD (void);
extern void RuntimeTextSettings_GetFontShader_mEBBC9BF9514C93E8F31FE85FFCBC81EC5628B09B (void);
extern void RuntimeTextSettings_GetStaticFallbackOSFontAsset_m4D2E042EBF13C0D078F09851324E7BEC2176CDF3 (void);
extern void RuntimeTextSettings_SetStaticFallbackOSFontAsset_m265BF39CFD9DC3E89375AFA00B453F98003F56AD (void);
extern void RuntimeTextSettings__ctor_m71FB8625BA344A10F14506252F7F6EBF3DB78676 (void);
extern void ScrollViewState__ctor_m9619262C4C72300A8B26011F627C68DF67425E53 (void);
extern void SliderState__ctor_m650A11534C71EF571FD631CC3E910B756A16889E (void);
extern void TextEditingUtilities_get_hasSelection_mB7272F47E994E6B88A2B0229BED793D4C5B23219 (void);
extern void TextEditingUtilities_set_revealCursor_m76B8081758CCD459072EAEF3B8FE3017A57735C2 (void);
extern void TextEditingUtilities_get_stringCursorIndex_m0A921BBC3646F59E7A99BEA8157166603748EA8E (void);
extern void TextEditingUtilities_get_cursorIndex_m0D23C8510F3F2A20BBF5796F00CC36DA8BB32BD9 (void);
extern void TextEditingUtilities_set_cursorIndex_mED289FA84CA33C4A695463C856524B501A78FEF4 (void);
extern void TextEditingUtilities_get_cursorIndexNoValidation_mDBE41EB8F520D35613CBE19F58C8DD772A29B76D (void);
extern void TextEditingUtilities_set_cursorIndexNoValidation_m905F2A16C7C97532B6DB66CC7A406D80820C9AD1 (void);
extern void TextEditingUtilities_set_selectIndexNoValidation_mABFB9F721CD2316AEDF619FA4E9F28D22EB64FD2 (void);
extern void TextEditingUtilities_get_stringSelectIndex_m19395861C05FC86D5765555A627E5C4D96BE4D2F (void);
extern void TextEditingUtilities_get_selectIndex_m1331CF40E64C203B1713CFC2FEC5E0F30FFC737A (void);
extern void TextEditingUtilities_set_selectIndex_mFB98D1E5E2C236DEEE7A45619965502B73E0ADBA (void);
extern void TextEditingUtilities_get_text_mC8854D29B1F95E04E0FFB49F1F2327E77598EF8E (void);
extern void TextEditingUtilities_set_text_mC1FD19476AF4FA014E9DBA5A33C54A57E2FA70EC (void);
extern void TextEditingUtilities_SetTextWithoutNotify_m9EDB056450908DE504396BC41C057016806B4AE1 (void);
extern void TextEditingUtilities__ctor_m6503B88727D1F4008C31E4FB54F2153A44E99B07 (void);
extern void TextEditingUtilities_UpdateImeState_mC13FC46AB62C566576C0D653774896FFA438003B (void);
extern void TextEditingUtilities_ShouldUpdateImeWindowPosition_m58FC98A57B608095F3EB6688A0D95FA64B8444E1 (void);
extern void TextEditingUtilities_SetImeWindowPosition_m1DFAAA8DBA6A946B204470806FB968359BFB3C48 (void);
extern void TextEditingUtilities_GeneratePreviewString_mA97B83DDA33F11F6580B86F6F2C438F19018A037 (void);
extern void TextEditingUtilities_EnableCursorPreviewState_mD28C1FAC4AFFEE396D903D1EED90DF1F5BC6A85B (void);
extern void TextEditingUtilities_RestoreCursorState_m36D71121DFD69D0EB43FCF48E49E1F616F9346C4 (void);
extern void TextEditingUtilities_HandleKeyEvent_m155A0EFF2CC76B511BDA3405C68A48408C1C6256 (void);
extern void TextEditingUtilities_PerformOperation_m8DC34D9795E11260FA9F4C4961601B189F342820 (void);
extern void TextEditingUtilities_MapKey_mCC38540E032D8765E0E280B94072141E74FF43FF (void);
extern void TextEditingUtilities_InitKeyActions_m08B59B3C8199E8DA37622E85D269F9E848B06381 (void);
extern void TextEditingUtilities_DeleteLineBack_m34769E8A0D70CE5BDBA3B11DA506BC7CA859CCFA (void);
extern void TextEditingUtilities_DeleteWordBack_mC235A97BDFEE8CE4C993BCF02DF4E86AB4BB8F1D (void);
extern void TextEditingUtilities_DeleteWordForward_m7A29C8E5BD4E3F01F7A379C16D971BBF565E5CAA (void);
extern void TextEditingUtilities_Delete_mF34D04ACA64C871CDE0F5F5DD6CC36A0667926DD (void);
extern void TextEditingUtilities_Backspace_m08DB317F2AEDA35F6227D52FF92B3EE4F7AA5909 (void);
extern void TextEditingUtilities_DeleteSelection_mF7E0C7A8B7A8984A5DA2C55839BFE60E0A70B847 (void);
extern void TextEditingUtilities_ReplaceSelection_m49F49CDB5D91B695392E2CE1B7BDC5A46817BBCE (void);
extern void TextEditingUtilities_Insert_m74BE32F1C3044BE4A095E43FF6BF8D84150306F6 (void);
extern void TextEditingUtilities_CanPaste_m81E3C512EF04804A3594020C3CD084F5BD85B3E7 (void);
extern void TextEditingUtilities_Cut_m5E08F36BC2F88E0E55483A524E815A3EAA429D2B (void);
extern void TextEditingUtilities_Paste_m876D2AD7A881EAC57D762E15F9ACB1AC26B3C28C (void);
extern void TextEditingUtilities_ReplaceNewlinesWithSpaces_m368B355EF98969A0A9E527D67C43E173B3FCAC74 (void);
extern void TextEditingUtilities_OnBlur_mCD1823DED60BE96C25C0E31B8DEC5F8EB1ACFD13 (void);
extern void TextEditingUtilities_TouchScreenKeyboardShouldBeUsed_mAF399D5C01CD9A7B7F4E1C188792420AFBA99D53 (void);
extern void TextEditor_get_showCursor_m2A6C5BACAAC0FEC2985858F08839D34B5A296AFA (void);
extern void TextEditor_get_text_mB5A19231EF7159855775CF3E9C5BC5346156E168 (void);
extern void TextEditor_set_text_mB71257AAD99A56AD5EA96DB546B17296E60C4455 (void);
extern void TextEditor_get_textWithWhitespace_m7C77FB0BD5B679C23C2E26217E640643759B9277 (void);
extern void TextEditor_set_textWithWhitespace_mF1C88E0F8C969821913285168E6252D227E51498 (void);
extern void TextEditor_get_position_m40763329A82988B1C5D5C1DA9919932061C99E13 (void);
extern void TextEditor__ctor_m4AEAC85E4950B709A35F26D1F0DAB3C9D35E3494 (void);
extern void TextEditor_OnTextChangedHandle_m349D5B2435419A7872C00BC62AF9C03CB0DCFDD3 (void);
extern void TextEditor_OnContentTextChangedHandle_m4FEDA32B87D849D01DC03A06ACA6F07F893FE466 (void);
extern void TextEditor_UpdateTextHandle_m2AC449E0917F90F2D9F0EABDDA1C89DEE98F7073 (void);
extern void TextEditor_UpdateScrollOffset_mD3F056830FF3FFC3461ED965EB0B7E306536FC3B (void);
extern void TextEditor_OnCursorIndexChange_m9B9C472B0F62917E96E5E27F15A76C9E4E493012 (void);
extern void TextEditor_OnSelectIndexChange_m99E1BBDFC6398F47F3170A6A46C5428F292FEE21 (void);
extern void TextSelectingUtilities_get_hasSelection_m86EA37D0A10EC2C4C1886C7E770DAB34DB8A66CD (void);
extern void TextSelectingUtilities_get_revealCursor_mD9502AE79AB9AC44496008153F267CBB4A9B3C16 (void);
extern void TextSelectingUtilities_set_revealCursor_m2A3BFE850A09B0716824E763373A521A24CC5F52 (void);
extern void TextSelectingUtilities_get_m_CharacterCount_m4C9189574E900CF75E41BB29861246D64A83CB0E (void);
extern void TextSelectingUtilities_get_characterCount_mF1A48644B9D24C0AE96B6259FDAFC87CB1BD5FEB (void);
extern void TextSelectingUtilities_get_m_TextElementInfos_mB87B7D1C40D71249B4E20DA1D7501194A2477368 (void);
extern void TextSelectingUtilities_get_cursorIndex_m89386F5B913CD481337E49A0635834F433B4AC5F (void);
extern void TextSelectingUtilities_set_cursorIndex_m0D3D5D519DA459906E983EABAA4AF692C4763269 (void);
extern void TextSelectingUtilities_get_cursorIndexNoValidation_mA6143409C53305FB602A73CDEC6EB201E2763ED9 (void);
extern void TextSelectingUtilities_set_cursorIndexNoValidation_mB590BB148DA02766188234EE77321EB51D4FACA9 (void);
extern void TextSelectingUtilities_SetCursorIndexWithoutNotify_mF240906A6FA8A38F5CB6AD1C5601265D05CCCAEE (void);
extern void TextSelectingUtilities_get_selectIndex_m1535CA4D982CC3A4C46E4491FA36E9514243CCB9 (void);
extern void TextSelectingUtilities_set_selectIndex_m1697EFBA1D0B91E0B20FD23B69A43D32D2110F10 (void);
extern void TextSelectingUtilities_set_selectIndexNoValidation_mD676910A3F674119D5C43EC8B47C6D5528862062 (void);
extern void TextSelectingUtilities_SetSelectIndexWithoutNotify_m33DB6522D2FA6877E3B991CDFD4944836A5D77FF (void);
extern void TextSelectingUtilities_get_selectedText_m4A131331842BA17B453A09FE4663A75B7B356013 (void);
extern void TextSelectingUtilities__ctor_m0D593E63B3CFE982829CFF9C93C5858E27AB84AE (void);
extern void TextSelectingUtilities_HandleKeyEvent_mD6AD5FEF96C31860C66D49324F98BD7AB27AE551 (void);
extern void TextSelectingUtilities_PerformOperation_m3F865D868A3A9264B28FAABBFE92F654BF3706FC (void);
extern void TextSelectingUtilities_MapKey_mDCCB5F98583F4FC584FD97AF27D37F91A7C5048B (void);
extern void TextSelectingUtilities_InitKeyActions_m005DD393E320ADA5BEFC2AA357EE964A8AB6CCED (void);
extern void TextSelectingUtilities_ClearCursorPos_m743C82F3CC7576E5050B6FA23133EC3FA8E9ED9C (void);
extern void TextSelectingUtilities_OnFocus_mCAC979E4683D3A0B91C91FCC19516E5FEE605A9C (void);
extern void TextSelectingUtilities_SelectAll_m89B71F5AF97AC5848616468DEFCF062C26DF23FD (void);
extern void TextSelectingUtilities_SelectNone_m47791B4FBE066CCC974155E1BD9FE8ACCB48D21A (void);
extern void TextSelectingUtilities_SelectLeft_mB51FE46E45D1C077ACB44AC5E2BF63C52AC3727D (void);
extern void TextSelectingUtilities_SelectRight_mFC95A2800C1CBEC7606EA8901F75CC946ED3BCA8 (void);
extern void TextSelectingUtilities_SelectUp_m1D90105D04CF6CBAE84D059CEF0495FD2FF0C22A (void);
extern void TextSelectingUtilities_SelectDown_m8E2EE5EC95CE507A7814D0FAAD865557C2A191C1 (void);
extern void TextSelectingUtilities_SelectTextEnd_mFF22956A56670B41BEE1A4527A9057B63BE89927 (void);
extern void TextSelectingUtilities_SelectTextStart_mD4F085F9AF7C2441D60E54CDE64B96A6934D13A6 (void);
extern void TextSelectingUtilities_SelectToStartOfNextWord_mC53BBECD698C32BAD6B4856F52ACA9E9D59F3E52 (void);
extern void TextSelectingUtilities_SelectToEndOfPreviousWord_mAC43A04CA459FE506650A86B695900300E4B24D8 (void);
extern void TextSelectingUtilities_SelectWordRight_m75669B452A334F48FB1CE5E0AC6792862E706B5E (void);
extern void TextSelectingUtilities_SelectWordLeft_mC488E7BBD9E8187F426A2F1CA82C528D76F4FCCC (void);
extern void TextSelectingUtilities_SelectGraphicalLineStart_m87E74A85CBD46469B2D1263436D2BE2FE5ABDB38 (void);
extern void TextSelectingUtilities_SelectGraphicalLineEnd_m60A6059D86AEB922ED7829EAF9C4E53B800911B4 (void);
extern void TextSelectingUtilities_SelectParagraphForward_m64D82C33CE1B82DC1B994B6CA027D717AB4280EB (void);
extern void TextSelectingUtilities_SelectParagraphBackward_m77EE0A0A167E91E1850937F543DBA615DC6DE0A0 (void);
extern void TextSelectingUtilities_SelectCurrentWord_mCC2AC7DD6D2BA6D2DF3DD728D883FF0D6963A959 (void);
extern void TextSelectingUtilities_SelectCurrentParagraph_mBD0B848A023ED86697EBA135E81B59ACD13B2B7A (void);
extern void TextSelectingUtilities_MoveRight_m8F1910A2773A39EF5CE248349F5A6CD7166AB795 (void);
extern void TextSelectingUtilities_MoveLeft_m094C534A56FC3CDA9C2423E46D179F359693370E (void);
extern void TextSelectingUtilities_MoveUp_mF0F3EE17A2CB3C4C1AEB950E80A5237A55D2711D (void);
extern void TextSelectingUtilities_MoveDown_m7EF798D6A19267DE30ED50C66697F5BC8AB814B8 (void);
extern void TextSelectingUtilities_MoveLineStart_m561A829C19F6C50028473CD5F81C508F3EEFE276 (void);
extern void TextSelectingUtilities_MoveLineEnd_mB4BABB86B094C9B88DEF94E0392BAE4396283B61 (void);
extern void TextSelectingUtilities_MoveGraphicalLineStart_m1ECAAAF8A29D63C5D7E76170D3745E3EB9E2266F (void);
extern void TextSelectingUtilities_MoveGraphicalLineEnd_m8BB408E28EA20EB56531B1FFD417FE54296008BE (void);
extern void TextSelectingUtilities_MoveTextStart_m7A276F1B11A1DAF468AC84324E592005B5D47350 (void);
extern void TextSelectingUtilities_MoveTextEnd_mDA69E553CA7D50781E845169852F1A0059FF0EB9 (void);
extern void TextSelectingUtilities_MoveParagraphForward_m88210A22BC823945D6AA137D50603388233502EA (void);
extern void TextSelectingUtilities_MoveParagraphBackward_m087604CE592162192829ADB142B786B0C436A58C (void);
extern void TextSelectingUtilities_MoveWordRight_mDCDC1B673D599BB94C95D6DC364D8877E87E3383 (void);
extern void TextSelectingUtilities_MoveToStartOfNextWord_mD1CEFD9620822349FDE09237943F943EB512A8C2 (void);
extern void TextSelectingUtilities_MoveToEndOfPreviousWord_m5BE2565747FFC49AA41526504653159852FC50A7 (void);
extern void TextSelectingUtilities_MoveWordLeft_m7D8131CD2DF2DF180D3FC249E2F0E17E250E5D3D (void);
extern void TextSelectingUtilities_MouseDragSelectsWholeWords_mB586078A58B5D56A53138856AB8DE9BD33535CC1 (void);
extern void TextSelectingUtilities_ExpandSelectGraphicalLineStart_m5E109D0A6A12D3D2FDF09F3E1407EB0347F7C4EF (void);
extern void TextSelectingUtilities_ExpandSelectGraphicalLineEnd_mFCA738E71AADB3C1206F279BC2681CA906A36D74 (void);
extern void TextSelectingUtilities_DblClickSnap_m6472F8DA3F0FC46FF75FFB394B283F5E5EC834FA (void);
extern void TextSelectingUtilities_MoveCursorToPosition_Internal_mE4AEE1AA57B8CCBB371C24B4F4B1AA2FF89886FD (void);
extern void TextSelectingUtilities_SelectToPosition_m75C9B53E1227CF9D487D5C8D771F0D8ACFEDC2F8 (void);
extern void TextSelectingUtilities_FindNextSeperator_mCF332FBDFEA6BCB471EDF75D76A139A580DB0E2A (void);
extern void TextSelectingUtilities_FindPrevSeperator_mD0F56A4106D376F28D6A0E8E07D7D4DAB9FF8C7E (void);
extern void TextSelectingUtilities_FindStartOfNextWord_m3D436C6FB45B3574F6F436B7CBAD30F5372C6870 (void);
extern void TextSelectingUtilities_FindEndOfPreviousWord_mA7AF965D2F01728EE872F2CB3F03083ABA63D174 (void);
extern void TextSelectingUtilities_FindEndOfClassification_m134E7AB62A9E90595A3C6DCC1DD517515B85AB1A (void);
extern void TextSelectingUtilities_ClampTextIndex_m04DED78B3B466D3E5AF59442F736A822ABC8D7E0 (void);
extern void TextSelectingUtilities_IndexOfEndOfLine_m9D87F72DC289F7095E615D97D821BBCE8D2B978B (void);
extern void TextSelectingUtilities_PreviousCodePointIndex_mCE1A8A7E9180B182AEA4DB6562EBDE854A860C6D (void);
extern void TextSelectingUtilities_NextCodePointIndex_mA936059BE65DAABE0446AFB5425BB4EA83392607 (void);
extern void TextSelectingUtilities_GetGraphicalLineStart_mEDB6AF99C1BBE0A1180AEE4FE67DDC0A223BD218 (void);
extern void TextSelectingUtilities_GetGraphicalLineEnd_mD956DB2F4EC24F3DE89069232733376B615D3204 (void);
extern void TextSelectingUtilities_Copy_m69701E12FFE465B70E677DCCCCF3148873A5FE0A (void);
extern void TextSelectingUtilities_ClassifyChar_m887E3900015DB05F1EF0E5F4630748CCA5C3CD52 (void);
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_m3791FADF6D0284BCC1AF6156A077038C2AA23055 (void);
static Il2CppMethodPointer s_methodPointers[587] = 
{
	Event_get_rawType_mD7CD874F3C8DFD4DFB6237E79A7C3A484B33CE56,
	Event_get_mousePosition_mD6D2DF45C75E6FADD415D27D0E93563DED37D9B9,
	Event_set_mousePosition_m221CDC5C9556DE91E82242A693D9E14FAC371F38,
	Event_get_delta_m1BBF28E2FC379EDD3907DC987E6BD7E6521D69A0,
	Event_set_delta_mA4F7805B9B53B36C7DAA31735CC9097D363B9F9A,
	Event_get_pointerType_mFFB3FB3E83412151A66FEC136FA00EBDB563B94B,
	Event_get_button_m57F81B5CCB26866E776D0EBD1250C708A3565C08,
	Event_get_modifiers_mD55E7CF06EB720434F0F174EA569B2A29792D39B,
	Event_set_modifiers_m879319643B5CD23F3223AB8E835C8ABCD3DA72FB,
	Event_get_pressure_m3E43BF333499DFDCFF2A36258BBC290DDD40D963,
	Event_get_twist_m557A5139AD77A15D25598A3F83676E558D1202BF,
	Event_get_tilt_m5F37D44342F42D691336B23EB075171CFEE7C7A3,
	Event_get_penStatus_m41CC65A15A4209D8168AE8BC64C691DE22F83611,
	Event_get_clickCount_mEF418EB4A36318F07E5F3463E4E5E8A4C454DE7D,
	Event_get_character_m8F7A92E90EF65B9379C01432B42D6BF818C32A61,
	Event_set_character_mA159F1742FD9EA968F32556C5FE1A2401069BAF5,
	Event_get_Internal_keyCode_m7532349D62074F6E6F58F9F9FFEFE48E767CBCC0,
	Event_set_Internal_keyCode_m4265541E2DB04D85560BCDCC880862F17BD56E41,
	Event_get_keyCode_mADBB236A741F96D86E4A536E15FFECFD4C367B64,
	Event_set_keyCode_m698D040F2EE0BE55B1B06A3FD865CC0A5D7B1168,
	Event_get_displayIndex_m7DBF1B18DD9B5E5B4EEA979FCA87351E3E5B16C3,
	Event_set_displayIndex_m8208F1B0471C0B45C0BB248F9A0178EB40FBE100,
	Event_get_type_m8A825D6DA432B967DAA3E22E5C8571620A75F8A8,
	Event_set_type_m16D35A8AF665F4A73A447F9EE7CA36963F34FEC2,
	Event_get_commandName_m14F2015FA5A9050C3C42AF1BD9D0E85D4FF78C24,
	Event_set_commandName_m8DA7262E1CD1005911EAB9777DE9FEC2D97504FA,
	Event_Internal_Use_m303C630AFC4EAE76036545C09C79729E90D81CB9,
	Event_Internal_Create_m38519A1960401042CAB57086F9E038116B8D3EAF,
	Event_Internal_Destroy_m25BA236C0C66CB87A89B81336D7BFB55917127BB,
	Event_CopyFromPtr_mC78295EF5861558EC93D3F8691E2A8B50DE84E29,
	Event_PopEvent_mC780BAA7CE4F0E75C8B5C7DC5EB430C278B0D0AE,
	Event_GetEventAtIndex_m6A5EC17990487B54E754E77B5A663E05B394ACB0,
	Event_GetEventCount_m5057138698EAA0B529F4FC7EDC96EDCB957F7FBC,
	Event_Internal_SetNativeEvent_mF0C015181EABFE56E2C90CD5C6DCA410C2C42746,
	Event_Internal_MakeMasterEventCurrent_m67675F107F56ADDBCF72ECB4C3BE4DCE831C8214,
	Event_GetDoubleClickTime_mF3D10CD927983547C6BF3479083B4155DE693826,
	Event__ctor_m14342F32F62A39A8B8032286E2DCC07FEF72BFF4,
	Event__ctor_mA5E77C0596952812A96703685523819CF50D71A0,
	Event_Finalize_m0882CB2E5E0C20C5C9669518C4DB5D95F840DAB7,
	Event_CopyFrom_m2F9B9704FBE156C5D58FF630F7968568C19821F5,
	Event_get_shift_mB8409DA839B09DC6137848E131A6DBE70BB9E70A,
	Event_get_control_m1E363A7ABA4F2E8CF41C661A48D53D85D635D320,
	Event_get_alt_m57F7F5C1F5FFCE43EFA6889F83CFA42DCA18A74B,
	Event_get_command_m202DE2CB0BE0AAB5CDFEC9DA1BBD3B51E8497547,
	Event_get_capsLock_m96978250936197432547F10654BDA72912FA3CD8,
	Event_get_numeric_m2731D0C083217F923383C2CAFB3DFA5E90886FFC,
	Event_get_functionKey_mF242E910AD9A24DC00BEF57915D817369E51BEDE,
	Event_get_current_mBD7135E10C392EAD61AC0A0D2489EF758C8A3FAD,
	Event_set_current_mDB5FE546AFA00DDF6CC23C106CE076EBEF36BCB3,
	Event_get_isKey_mDA8FE1CC5E305BAF181E86A727173C9BE9A1B754,
	Event_get_isMouse_mBD11F4FE2996DFAD2648C8A9648E301EDDA51D7A,
	Event_get_isScrollWheel_mE1E0A512E67F7A0EECD5AC25C4B2F8D31C92B7BE,
	Event_get_isDirectManipulationDevice_m9A72FB2DF7803E189857D24A65FB568B17533ED0,
	Event_KeyboardEvent_m957733139998C86C7ECA28BA50863EB88B71418E,
	Event_GetHashCode_m9E93319C0E2A92678BC6B3B9A7B1758DBA605E6E,
	Event_Equals_mBA8BEAB37AE94F9B42F62D946DD61223E0F1258A,
	Event_ToString_mB30B330C86407E776E932EC18CF177A4066BA56B,
	Event_Use_mD77A166D8CFEC4997484C58BC55FEB2D288D3453,
	Event_get_rawType_Injected_mDCA85E7076A7D6D3B01290CB5A79821C1056C1A2,
	Event_get_mousePosition_Injected_mBB69D766EEB30783505E1B74BAE18A63233E15C2,
	Event_set_mousePosition_Injected_m31B63D53DE048238D7964C6236B21916172EA60C,
	Event_get_delta_Injected_m4EEDB796FAFA905815EB8F89C200A8B851314B66,
	Event_set_delta_Injected_mD97744D7B81368BBBB63E68934C41B41344E4068,
	Event_get_pointerType_Injected_mB28B092716FE76712BF2640B11C4779884AAC9DB,
	Event_get_button_Injected_mBB57C0D585B25CB1CD2D3253105FD86B3067A561,
	Event_get_modifiers_Injected_mEBCDDF68E5FFB407E12D3EE4AFFF1B88CE808004,
	Event_set_modifiers_Injected_m81C5EC815905D62AF0B825CA14DD360B33528C33,
	Event_get_pressure_Injected_m0BB21470B6093D69CC81B5DF0B0A42B9EF5E5F9A,
	Event_get_twist_Injected_m602DD5ADC6CEA12B0D0E4D3B70645EE87791F19F,
	Event_get_tilt_Injected_m0AEEC6BF7F7868238FD5269EC6E8DDD3251DEE67,
	Event_get_penStatus_Injected_m9177A19359BD5B539130B65EB8A80A5163EC3BD9,
	Event_get_clickCount_Injected_m474D30C83AB5D8442279764154ECC263558F4968,
	Event_get_character_Injected_m0327C043FFD70576F14C3EA245B0909CBC162ABC,
	Event_set_character_Injected_mD4166E8F4946DCC9C8751C7193211621C8F1E9EB,
	Event_get_Internal_keyCode_Injected_m2DDCD9B211F9C700AD0927734E6C50F7BE843F0C,
	Event_set_Internal_keyCode_Injected_m94927796AF9F8BBB990A77FDD633B47DBF131F55,
	Event_get_displayIndex_Injected_mF1ABC5D6975D6A26206D73EE53DB00C4AD886FC5,
	Event_set_displayIndex_Injected_m47F9550915907B117387CEB2773FE4BD906628C9,
	Event_get_type_Injected_m7556ED42AF45E8D907B8F1CBCF3976B4817E43DB,
	Event_set_type_Injected_mFB30C3F9276DBF245E74540E452A50CBC1FBDBA3,
	Event_get_commandName_Injected_mC50B8EDA6D4BE29F77ADB83206A74C383BB1080D,
	Event_set_commandName_Injected_m272833DA9DE22F21DD4BB29E07D01A422654938B,
	Event_Internal_Use_Injected_m8B1CE93DFF429073D4BB010A26446C512AC05AA2,
	Event_CopyFromPtr_Injected_m9F5BB726C0BCD2B59937307886FACE69C90C19B1,
	Event_PopEvent_Injected_m812A627713A9BC9C00AB3499F16697F9A7BF869B,
	Event_GetEventAtIndex_Injected_m3214070478AA0BE8FEAB8B4D48AB2B43DC77195A,
	BindingsMarshaller_ConvertToNative_mD06CC80A68266C36E72936554BB830F6FB6561E5,
	EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA,
	EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F,
	EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F,
	EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313,
	EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE,
	EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6,
	EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B,
	GUI_get_color_m15488B4AD785D10DEB5C66398D0FA9A0C0EA7ABB,
	GUI_set_color_mA44927D3F2ACB1E228815F10042A6F62B7645648,
	GUI_get_backgroundColor_mCAA42085DAB8A1B71E2D0C6C8D86F93CD311FA33,
	GUI_set_backgroundColor_m4ED80300A7DE3EAF923E3344E22D6682FA75B264,
	GUI_get_contentColor_m32B15C8D6BEEFEBCE667ECD3CF664C83224F103F,
	GUI_set_contentColor_m3CDC4D626AC8B6D487AD19765D79C593B98AEF26,
	GUI_get_changed_m3473B2964DCE8C2ADE081517093168C171BBE448,
	GUI_set_changed_mBD91A44AFA77D2BF883B3150AF4AE6AC3ED121DC,
	GUI_get_enabled_m336E115A84DBD8D18A925D0755B51746B98B516D,
	GUI_set_enabled_mF2F99A6870ACAFAEFB5E8FF1B69C684951D390C9,
	GUI__cctor_m97D837BF457542B0F7308E8999670A46E465A9E3,
	GUI_set_nextScrollStepTime_mA35BA69E3FDBC961E42F6C9D02BB4E8776926A09,
	GUI_set_skin_mD51BAED314B39004AE3FDE74F9895CA19F3E40E5,
	GUI_get_skin_m97EC9EB4628B311C0DB7DF9FB19FAD82D6790A1B,
	GUI_DoSetSkin_mF4C06A8BE59628B6514F7FBF9422214A48BE03B9,
	GUI_get_matrix_m3CA02DED0598EE32BD9E66CA533A78EFB0A246FC,
	GUI_set_matrix_m7759FEC96FBCB97E02B1BA44D2EC1B3FEEFA257F,
	GUI_Label_m4A951E57C7DCCF95A0306240144CA2713F546526,
	GUI_Label_mFC6559DAC18FE889F1B94729AED3550374D18089,
	GUI_Box_mB47BC44807774B77DB8B2BB548D339036544ACC4,
	GUI_Box_m4A53BAE78DC7C6724F50E54D9BEB7135BAA0DA0C,
	GUI_DoLabel_mE43FD8B17DE5AF3B9E12E15B548CD6846F4AF27F,
	GUI_get_scrollViewStates_m940A384A713B8A7DC67016D1588965A42E561773,
	GUI_CallWindowDelegate_m3FC075A6C33D007CBDC6983CDD6515C246E35B3F,
	GUI_get_color_Injected_m7B9A31188627647FDD914FB8A83C32627769D1CA,
	GUI_set_color_Injected_mF82410FC38D4C12CEC8ADCC9CCCC00F12035CA12,
	GUI_get_backgroundColor_Injected_m81488D0D17EB867EEA60685182EAD8E0BC7CFB1F,
	GUI_set_backgroundColor_Injected_m16FDF89F7678824BA547AEF70D4EC84615C7D6B8,
	GUI_get_contentColor_Injected_mA592670CB3A23833ED6F6FA43D021CA049CB6FAC,
	GUI_set_contentColor_Injected_mE1EFDCAC30FF6CE60437BF1B8B04488C9A75E2C9,
	WindowFunction__ctor_m31D7B6C221D9A078AE5C8BA7C3BC0FA406EA7B71,
	WindowFunction_Invoke_m27ADD2F0F97D0149CE0B6F6452B3C23229D2CC85,
	GUIClip_get_visibleRect_m93F10FF2376C3BBBF3562A67DD7E207240D2F611,
	GUIClip_Internal_Pop_m99B82F9D059E587FD37DEEB41385076E16162E62,
	GUIClip_Internal_GetCount_m83C187F97642C73B9241C9A026CDA89A7A9EB8D1,
	GUIClip_GetMatrix_mABFDC4C3D2B71C84191EAA109A4373A1D75BD3E1,
	GUIClip_SetMatrix_m2C4B22CA0D33E580CBD455CC8E5422C8FF229733,
	GUIClip_Internal_PushParentClip_mDA817B810C6724A0F236C876C08CFB0EC64E78A8,
	GUIClip_Internal_PushParentClip_mEAE43F73F48A4CD59FD9432B4F1E50124A0F3522,
	GUIClip_Internal_PopParentClip_m7B43C8DD6186703019A5B7ADDC1FE48FB67BDEFA,
	GUIClip_get_visibleRect_Injected_mBF3F116B530BCD6D5B3A5D110245691ADD4AA8BC,
	GUIClip_GetMatrix_Injected_mFAEC409FC44C49C7681DF684C954DF86AE076B76,
	GUIClip_SetMatrix_Injected_m259A180FE5871D9D16330959A560EAC86E0224D0,
	GUIClip_Internal_PushParentClip_Injected_m71572BEBE9BFFAA4D306958579D3B0B48411B87A,
	ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4,
	ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D,
	GUIContent_add_OnTextChanged_m9F326364415BADDD1BDF1E9A5DE384DAD16B283A,
	GUIContent_remove_OnTextChanged_m8F2239CF4C975A1D00109C5D4F33D55ED3B40CC2,
	GUIContent_get_text_mC6D7981351923AD7F802AC659314BA56DF7F3ED6,
	GUIContent_set_text_m18A3EB5B4BD316561B3F4AB6BB3CC151684CE14F,
	GUIContent_get_textWithWhitespace_mDDCAECA04AF38B5C5BAE7C33AF8F2362F1CF113B,
	GUIContent_set_textWithWhitespace_m1A0A33F0C4EA311A6C4649FB4F78B8F8786936DE,
	GUIContent_SetTextWithoutNotify_mA593CEED57B890EE6EA83DC4729BDBA2676DF0FE,
	GUIContent_set_image_mB91F27FCD27EDBA24794D52B7F3DF1CF4E878164,
	GUIContent_get_tooltip_mC2D07D7B2884A5F5A56F84A7FE6BF39905AB15BD,
	GUIContent_set_tooltip_m72C6B6EA0C9FCA1544A7FCF6C78A93E55D8CB415,
	GUIContent__ctor_m89AC53A7E9BF9EB9E70297353DEAA6FEC2C800AC,
	GUIContent__ctor_mD2BDF82C1E1F75DEEF36F2C8EDB60FFB49EE4DBC,
	GUIContent__ctor_m68E7F8C12A7768D52689E89E1E687F86569A3E78,
	GUIContent__ctor_m3FDFF98EA6ACDC116BCCA705EE8F8DEC09A4A0A7,
	GUIContent_Temp_m4AE3B839AF38DD23ECC1D585C391E1CA43B8EA73,
	GUIContent_ClearStaticCache_m36A399D55991F1B5B1C4A20DCDFF415B8636E934,
	GUIContent_ToString_m9F42CA1D8DEFB446686D0010FF57B4F9B140BB9A,
	GUIContent__cctor_m1605F6A12B7BD089F1592F490DBF324ECEC3FE8C,
	GUILayout_Width_m3FADF145F37481F9FEFF0E89E8A466CF5532DCE3,
	GUILayout_Height_m5E1526C541663A21437ED06E233FDDA08A856B91,
	GUILayoutOption__ctor_m4EF826EA43073869166C8D94A1D9EB7898ACC3AA,
	GUILayoutUtility_Internal_GetWindowRect_m4F0CEA512EAD2BF0BBA0218A10B9C820C24D44CE,
	GUILayoutUtility_Internal_MoveWindow_mAD1ECDE72F3573D2F71B43C5FB8F90C10919C6CF,
	GUILayoutUtility_GetLayoutCache_m6086C9523E16ECC3FF4613F5A2441351CF4B2878,
	GUILayoutUtility_SelectIDList_m601F4AA990B7FD59A779F5375EC55ADDB86927A9,
	GUILayoutUtility_RemoveSelectedIdList_m701496086D15B8BF1C936EB431AFBC6627A787FD,
	GUILayoutUtility_Begin_m701551F1F833A31A154BFFC9F6F3143A12A33061,
	GUILayoutUtility_BeginContainer_m34C50FF74C76B91E32E1A3575ABC0AA0AE0F3DDB,
	GUILayoutUtility_BeginWindow_m99FBC28B305B9C0589BC73138073BE9420C977F5,
	GUILayoutUtility_Layout_mBC6C938DC931B8CABC1FA6C33AA60ECFAC3D9B30,
	GUILayoutUtility_LayoutFromEditorWindow_m0D41A3D7897D91D4420C722C47502FCBA0352804,
	GUILayoutUtility_LayoutFromContainer_m81EC681FE0A88C36CCA8D4382043279F709EE59E,
	GUILayoutUtility_LayoutFreeGroup_m81D18A1401F6FF7EB4A3C1CC26D9BE80998BBF5C,
	GUILayoutUtility_LayoutSingleGroup_m95E3F31426ACA641C57016A1D1A058366A56AFE8,
	GUILayoutUtility__cctor_m2CEDA9A8EB23B7D3A5A97825E6B192235954DC48,
	GUILayoutUtility_Internal_GetWindowRect_Injected_m03328FF57858A53621C5907B345C56FA2C5AF0EC,
	GUILayoutUtility_Internal_MoveWindow_Injected_mDFDA2042DAFBDEBD108AC01F6F19E7D0F395B6A7,
	LayoutCache_set_id_m532720FF0F65E8039E37D015910E2F1AE1C9F4FB,
	LayoutCache__ctor_m73B4DC62A0A7669976C8444DDB54EF8D55BF3E0B,
	LayoutCache_ResetCursor_m728841782E13F82B1AE96E40AF16D6C8EBE6D59A,
	GUISettings__ctor_m4AA9AFBD94306E007937909CB7F542DF2E491404,
	GUISkin__ctor_mAA94A46B37D9C2F70962435F250BBA202CD1EC7A,
	GUISkin_OnEnable_m5A7FE1F57C549711FCCC2DB0322F8667129AA0BF,
	GUISkin_CleanupRoots_mAD2E77BE9440832E8BC8CAA9C7F2D85C3D2F8B17,
	GUISkin_get_font_m806CF702C59E43DF55BA441030A60F80E9D8CFD5,
	GUISkin_set_font_mF98516DE4363C888D7215006D51BD527F3F9DDA9,
	GUISkin_get_box_m21BE7FC56D903B95BAFAE8890425D330EA88D893,
	GUISkin_set_box_m82E578044569D3831D103FFA1413D81DABF74711,
	GUISkin_get_label_m99E1A8D6D8592F88F581437D24DB1EDE05C63E5E,
	GUISkin_set_label_m7E9E63BBA37F93D886F7E6AF70772ECD7894462B,
	GUISkin_get_textField_mC554496BAB959445F0CFA30BDC5736DC1F057D48,
	GUISkin_set_textField_m4730F5B544F2A87AF3CA75A01FE845E5D40E06BE,
	GUISkin_get_textArea_m0ECBC9D126D930490F96E100B27F245E555EB7D1,
	GUISkin_set_textArea_m916CC2135EE608D81035D3E96787735534DF4E9D,
	GUISkin_get_button_m51948EBD478CF9223522AD29B7FBD1BABAABE289,
	GUISkin_set_button_m45F7F5CBF3E9286F4CD601AA92C0C3207C0BB373,
	GUISkin_get_toggle_mD5F318C602494C478F09C2D48741EC7A9CF5B849,
	GUISkin_set_toggle_mFE0DA0EC1F1952464B85894CCCFECFA5E0E0C57E,
	GUISkin_get_window_m760DAF129E72775DFD18CB71720AD306345E91C2,
	GUISkin_set_window_mA74900E5D554578F3F45DD858B79C5A8FA4A6220,
	GUISkin_get_horizontalSlider_mAA1753FEEDBA6E28A3A56C3E44A8F5B3D6C8336B,
	GUISkin_set_horizontalSlider_m8357A90F358C1A040308C8D0DEE363D3ABA71575,
	GUISkin_get_horizontalSliderThumb_m9EE5EF8204397C2946D7F384AB7D8A17693837BD,
	GUISkin_set_horizontalSliderThumb_m1042BED23F10E28042D77D7E738F86C1FEDF460E,
	GUISkin_get_horizontalSliderThumbExtent_m6408F303B8932D6E74B307070689A96EA082D612,
	GUISkin_set_horizontalSliderThumbExtent_m8F4C637DB7E25697AB463B9F2F8D50D8493609C1,
	GUISkin_get_sliderMixed_mFD8CBA8BE229E299D63822AE3E632DABCC27FF61,
	GUISkin_set_sliderMixed_m8A129FB05FAA0970C01A8C3DB14903E13F8E37B3,
	GUISkin_get_verticalSlider_mB7EC86D11019F1892365E9C6F2A846A68879BBD5,
	GUISkin_set_verticalSlider_m02D94C0BFF867BD8B1ECE05AB50F7F2475DF0E35,
	GUISkin_get_verticalSliderThumb_m3D86347FFC94841C8B6CA94F9F946C76E96EBADB,
	GUISkin_set_verticalSliderThumb_mFBFA636B05068A0E7D24C8C3B06044AB2ACD4C58,
	GUISkin_get_verticalSliderThumbExtent_m299DED8D10A1CE0F22B43BAF47D70DA1EB079AFA,
	GUISkin_set_verticalSliderThumbExtent_m3ECC754FC08BCFA5C3264A6B83C9EE280C1EFCDD,
	GUISkin_get_horizontalScrollbar_m945A39FBD098D3800A189FC34B9CE9E8AFF3AEEA,
	GUISkin_set_horizontalScrollbar_mF08764A78F23728E6FE157F08B9A0127157071FA,
	GUISkin_get_horizontalScrollbarThumb_m5011EED1650028044BCC7F6DE2829AC0243208BB,
	GUISkin_set_horizontalScrollbarThumb_mDDADEFFD5BF9B88AC4A37AEA13B6FCCC28A3F696,
	GUISkin_get_horizontalScrollbarLeftButton_m4A6E58CF80A66F58CF5792B31D08A2D74BF40567,
	GUISkin_set_horizontalScrollbarLeftButton_m3FDB02C1FDE47BCE92068EA21C531F1F6D667DBC,
	GUISkin_get_horizontalScrollbarRightButton_mADFCABC3339BE56E2BAD5443789D8D4FBDD73DAC,
	GUISkin_set_horizontalScrollbarRightButton_mE5ED9D2BB554FC29F6A69C81B9361A5E6E004CFD,
	GUISkin_get_verticalScrollbar_m600012E344D3EB4C687E8A4BE78CE33068374D2A,
	GUISkin_set_verticalScrollbar_m4F55D5B66DB408A5009FC00ABBB9AFFA0C65FFEC,
	GUISkin_get_verticalScrollbarThumb_m62663C3DDC40AC91FD4666FBF844DCD83DDA7DE6,
	GUISkin_set_verticalScrollbarThumb_mECEC0DC79CCD9AABBF6CBA3CE5141C38699B5EC6,
	GUISkin_get_verticalScrollbarUpButton_m0B5575CA6AFB1C74899BF931296EFC39B2C1A902,
	GUISkin_set_verticalScrollbarUpButton_mF50F99BC770529789363EC9B1C37E610FF8A708C,
	GUISkin_get_verticalScrollbarDownButton_mFC75161EDB03597ECF09E189C8A57F0E64213E3D,
	GUISkin_set_verticalScrollbarDownButton_m37DD0E232BB98BD219494A297DDBE7620104D328,
	GUISkin_get_scrollView_m5466CD77A4A7E01320DB0E0F57253D41226BB0B8,
	GUISkin_set_scrollView_mF2D35906BC020D81F909E65B420494F254E4DC32,
	GUISkin_get_customStyles_mAC8A1CFD5756E6C0D367E06B4BDC365E6F6BC39B,
	GUISkin_set_customStyles_mD22F50472DDB0A9770B18F0A15D3F73EEEC4A8B2,
	GUISkin_get_settings_mCBAE5727D7774FAEE47CCC8B4C47AC321DDD85C2,
	GUISkin_get_error_mB953A37C8F3296E529190A34E18506C735848C01,
	GUISkin_Apply_mA85017BE8C994F6220112EE8D00D3C37C1FF2104,
	GUISkin_BuildStyleCache_m8E99CC278C76A6DA63A24BFD2DE42AE313C0F7E1,
	GUISkin_GetStyle_mF024BC5177A2AD477ACF44D87BE6A629C91562CA,
	GUISkin_FindStyle_mF82C37E2481D2B9E96C26EFE0353F8954F844FFE,
	GUISkin_MakeCurrent_mDB3BB1FBA5BD2FEDDA3F32F11170F47A6444AEED,
	GUISkin_GetEnumerator_mEC308E2DA9A94E09C622D13F82EB7ECCECF8AFF0,
	SkinChangedDelegate__ctor_m20D33B3868351B98B708468F7A8192C1ACF85CD1,
	SkinChangedDelegate_Invoke_mD14214487F9A0E4DD7EB7F97927D03EC8F1A3B4C,
	GUIStyleState_set_textColor_m5868D12858E6402247953BCCDDA7A543BE6084F1,
	GUIStyleState_Init_m0D3428E2BA3343F8AC49253DE3AAC54EF07F4873,
	GUIStyleState_Cleanup_mF244B2DAEE9DE90A300E6B7D78F9547BBBE59826,
	GUIStyleState__ctor_mD47FE21F7FD8D786F7E8E4E8C3DCA224F9237AD7,
	GUIStyleState__ctor_m74536B867B0F57F8A7DC74E78018830A948E4555,
	GUIStyleState_GetGUIStyleState_m0B273F7909166249E3D98FA410C2D8A72091C7B1,
	GUIStyleState_Finalize_m5CC6FBD8C44AF1091CACD6F7032E73B1114765B2,
	GUIStyleState_set_textColor_Injected_m42CA271DB3678AFA5B18673C5EB41B04DEA7ECA3,
	GUIStyleState_Cleanup_Injected_mC40EE1A133306299830D8FA5C3D90D8887447CC7,
	BindingsMarshaller_ConvertToNative_m00C4592A76BD688ADB2901D420CB349EE6DD339B,
	GUIStyle_get_rawName_m9C87EB1EA6CC5989EFF3567E85A2D0A3DF256782,
	GUIStyle_set_rawName_mF8928B91294B5DA15AF365C760BB1437CF507ED6,
	GUIStyle_get_font_mBD123E375D357B37F8E1303F288517FD883C1117,
	GUIStyle_get_imagePosition_m339AA340B169230E9795B61BEE4BB1EDAD6D95B4,
	GUIStyle_get_alignment_mB650B3193AFBD4D1C2EB0A15C978A0FC3E377D2E,
	GUIStyle_set_alignment_mEDC62A775C9551DBD1FEE4043F115E034EF12937,
	GUIStyle_get_wordWrap_mD0046078E78B0F8F1988E055B7EEB261FE8C69AD,
	GUIStyle_get_clipping_mDF8B2F6EA1DC0E36268C6F58C2062442947AE3AE,
	GUIStyle_get_contentOffset_m1585F4928435114551C27889DE159EEF55345C70,
	GUIStyle_get_fixedWidth_m9CB5B4E096287F75F4E4E3376590C7C085E28DE8,
	GUIStyle_get_fixedHeight_m009155CF284509A87E6037D0A392A630FA728F7A,
	GUIStyle_get_stretchWidth_m528FFD3EB3104D0322F2EADBBE7DBFF3FB34CB37,
	GUIStyle_get_stretchHeight_m5ACA8F9CD25746932719C927159A105AADA5061F,
	GUIStyle_set_stretchHeight_m51C55ED43AA4EDE125E0C423FA0D301E81C09378,
	GUIStyle_get_fontSize_mBD6EEA6C9C09825DACE0395B1EC8D773FE94F8CF,
	GUIStyle_get_fontStyle_m9CFB0CCF194C6D7010325F24300B7BF21CE68FAE,
	GUIStyle_get_richText_m849DD36265E87291AC9FA304316CB730B866D550,
	GUIStyle_Internal_Create_m2C5F872F6FE8C423759017DC72267D6AF637BC75,
	GUIStyle_Internal_Destroy_mD93F2F454B69DB5C534AF9F4F6D847F955A39977,
	GUIStyle_GetStyleStatePtr_m60D51351B040299578007102C3857E8E8F14FAFB,
	GUIStyle_GetRectOffsetPtr_mCABE2CEFE5CDB942D464051BF8B0E043BCC59593,
	GUIStyle_Internal_Draw_mBEFC164F21949135F404FDA678F368FBA8074D50,
	GUIStyle_Internal_Draw2_mD1050A7750AAAEEEEFD4EB6C8C8AFB0591B1221D,
	GUIStyle_Internal_GetTextRectOffset_m00F59B7312BA8622DA9E3412628886779EDD2FAA,
	GUIStyle_SetMouseTooltip_mFF3E22C7330AE180E83AB2929049BCD87B13B21E,
	GUIStyle_IsTooltipActive_mAD93F97B98889CA47BF1305F3D4C87D5EE8DD777,
	GUIStyle_SetDefaultFont_mD6B98375749805CA5084CA8C5D6A1295359AE0E3,
	GUIStyle_GetDefaultFont_mE8076F2F0F05DB787B49719629DC21BEBF0028E7,
	GUIStyle_Internal_DestroyTextGenerator_m887FC7292D8B991D2674805E1FF63667CF56EBDF,
	GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9,
	GUIStyle_Finalize_mFF6A6FBA538B711A6ED369DD83A41F25DE6EEE85,
	GUIStyle_get_name_mDF9EF43C46A0B9431DAF4EB0CE1D18EA32E16B75,
	GUIStyle_set_name_mE618266DC07236117AAE05FE8D2B14A595FCF020,
	GUIStyle_get_normal_mDEA2808FBD692E505784BD9E521738B4321BCA8F,
	GUIStyle_get_margin_mD0AABA2CB3FB0CFC3C414635E6225D3003315D1B,
	GUIStyle_get_padding_m04E3210A51B2522158941AFA97ADC19C835987C2,
	GUIStyle_get_lineHeight_mC814199D1ABA3CE38358BA70347562B0CDFEB96E,
	GUIStyle_Draw_m7B978F5F5B576810CF8546142D23FD9990E002D8,
	GUIStyle_Draw_mACFC9CE57BD530BB6A9592149DD95108A8014406,
	GUIStyle_Draw_m3DBF8DC58719720455DFC818590D77752BA31008,
	GUIStyle_get_none_m808A9FE1F78920E4A29ED3484B99588B46D88938,
	GUIStyle_GetCursorPixelPosition_m4FFBD3DC05CE503355DF01E57023AC349032CB2F,
	GUIStyle_GetPreferredSize_mBA7966F20B676F3F1F587D97AC83D87D64397C5D,
	GUIStyle_ToString_m41A8A58B4D9659047D06EF2A5AE5F170AE198ACF,
	GUIStyle_GetMeshInfo_m79641B3D54BC4C9CC558A05802241D596284C872,
	GUIStyle_GetDimensions_mF0B843919AB6FB621C04E0C1EF800A222B185BEC,
	GUIStyle_GetLineHeight_m401C4B6C6AA072FEED8214A84E0B1E146591324C,
	GUIStyle_EmptyManagedCache_mB8A9DA9F8C6BB6A2948C4DD52B392DF643067AC2,
	GUIStyle__cctor_m4B955524A4DAEAAF103D78D9316756CEFA16FB62,
	GUIStyle_get_rawName_Injected_m31AACF13426ED39D6E7C0EA1218D35576CD0CF29,
	GUIStyle_set_rawName_Injected_mA81CAD1E6BC87E14E6A2F62A0D4EFFF61919454D,
	GUIStyle_get_font_Injected_mC05595AAE4BEBC4D740E156A7089CBFEB56A3E3A,
	GUIStyle_get_imagePosition_Injected_m2F0ABE9992E935467DCD9BA67447EF32246171B5,
	GUIStyle_get_alignment_Injected_m0FC74B05D548FBD4A99DCD0906BCD82C3A892455,
	GUIStyle_set_alignment_Injected_m8B4D439423ADEBE54D7C9885AA14E9D0748D4AB6,
	GUIStyle_get_wordWrap_Injected_m73520CD7C856238389EF97826FC5014147AC3A5E,
	GUIStyle_get_clipping_Injected_m737EBBEAFC7EF5C75C1531B80526D2E58C5E563E,
	GUIStyle_get_contentOffset_Injected_m513BE18A016A98DF77714FE89EE85DA8DED12EC1,
	GUIStyle_get_fixedWidth_Injected_m32CE485041E440BF117C81DADB1542C4B67AA805,
	GUIStyle_get_fixedHeight_Injected_m958CFA9442BABE3E27FBF2E380DA034088719AD5,
	GUIStyle_get_stretchWidth_Injected_mA068F7B72A68C5E4C66FC2CFABF4F7884BD56631,
	GUIStyle_get_stretchHeight_Injected_mC737DCD17A7B7460579F62178E5584326635450C,
	GUIStyle_set_stretchHeight_Injected_m36334CF4EFFD289385BD4CEE02FCF27297633136,
	GUIStyle_get_fontSize_Injected_mCF5A4090D50B9F2E8C857E4530D4C4C119F8E0AC,
	GUIStyle_get_fontStyle_Injected_m423CCEDE4CB29F1F74F99BBEDD864E9D1DF1E5E9,
	GUIStyle_get_richText_Injected_mCD010A09029CD34C908F0656AFB59404E371AD63,
	GUIStyle_GetStyleStatePtr_Injected_mE5EA42C28345450F52E5B182D65A79FC430BC632,
	GUIStyle_GetRectOffsetPtr_Injected_mB750F19C29D4A94361F6BE0ED9A3E2B653A0AC2D,
	GUIStyle_Internal_Draw_Injected_mF3DFEAF91F9C52DB9C70917B3434F58A48866359,
	GUIStyle_Internal_Draw2_Injected_mD7186D675DF56B82C86F5EB7E78CB61D6B8DBDD4,
	GUIStyle_Internal_GetTextRectOffset_Injected_m20FA3FA9CD37DE8975F76EEC3982F7AEA383675E,
	GUIStyle_SetMouseTooltip_Injected_m79A48A1881E44B23C8C782A2B5847B825D76A91E,
	GUIStyle_IsTooltipActive_Injected_m73B8C97744C579784612C4707433D3418FFB943A,
	GUIStyle_SetDefaultFont_Injected_mDCF32789075C2882DBFAD695D532C6D93CB8B192,
	GUIStyle_GetDefaultFont_Injected_m9C76CB99642C0AA8E348EF467DE4EBBE586CE56F,
	BindingsMarshaller_ConvertToNative_mACDFB57BFBEE121FFFDF70E3CCAC1F0FB7C935A9,
	GUITargetAttribute_GetGUITargetAttrValue_mD0E7A4A7147F6B97077284408283EA380FE040B4,
	GUIUtility_get_pixelsPerPoint_m13E69FE793E736FA60A61C6756F2FF57BA6C9F31,
	GUIUtility_set_pixelsPerPoint_m73020B1AEBBF5F843FED3FBA80E100224768931A,
	GUIUtility_get_guiDepth_m011B188F7C41DAE079019E64BC064208E618F315,
	GUIUtility_get_textFieldInput_mDB514BD41982E9A309A7E0297270162FA6918EBA,
	GUIUtility_get_systemCopyBuffer_m01E2DF71533C31A4C552B9177D7CBA0C6CA3FC2A,
	GUIUtility_set_systemCopyBuffer_mD14AE32BFEA4773BDC679205D470A228B8F225E8,
	GUIUtility_Internal_GetControlID_m9836A3FD9B0629A36F356FD8D4606092B2E2AD21,
	GUIUtility_GetControlID_m3AACC1B4BDE62E7C3E5D861A470351FA1BAA752E,
	GUIUtility_BeginContainerFromOwner_mA895E862C2444F93423836CE4B5F35E2F31B8B28,
	GUIUtility_BeginContainer_m4A0F355072CE2DBCB50F706885EAAB70DB8C7115,
	GUIUtility_Internal_EndContainer_mCE42BC4D58E684B724B58EC3C901E67BA62F1BF7,
	GUIUtility_CheckForTabEvent_m6AC98E67A89330ACB330CBBC135E3DFBFCAC2C49,
	GUIUtility_SetKeyboardControlToFirstControlId_m02DF215A0F07822021E17AF4153B4C31468287C0,
	GUIUtility_SetKeyboardControlToLastControlId_mB7A3C208ADDF009FB9C3C522998459BCD9B107EB,
	GUIUtility_HasFocusableControls_mE149711C5695D4DB44940D8073487992F1ACB883,
	GUIUtility_OwnsId_m46FE01F2CEF3A94173A1DB64A888E4DB1EBC74D2,
	GUIUtility_AlignRectToDevice_mE651D8C8024AD7FF9C1773FA000A2626BC263B8C,
	GUIUtility_get_compositionString_mE06412C5CE41311C00BFC4028716D5F03EDD85E9,
	GUIUtility_set_imeCompositionMode_mE5C0A2391D65DAC056B1752D78B5A832DCB314C7,
	GUIUtility_set_compositionCursorPos_mECE1139A5660FFE152382DAB2DDBFADB96BB9644,
	GUIUtility_Internal_GetHotControl_m8230315B3FECDB164C84AFC40C180C2C7B319892,
	GUIUtility_Internal_GetKeyboardControl_mD0783552D4ACDA842F86F126C7A48ADC79340AB8,
	GUIUtility_Internal_SetHotControl_m56F3F333B107EFD83C7F3D703DDA48C5A19BFCB8,
	GUIUtility_Internal_SetKeyboardControl_mC8401D9C911D310EAA2284161264D2FC9D141418,
	GUIUtility_Internal_GetDefaultSkin_m86F21D22A34DC2243194B8929A499FD98D26A234,
	GUIUtility_Internal_ExitGUI_m5B145534F61B8CE2A2915A9297D0F25D771D4459,
	GUIUtility_MarkGUIChanged_m43158D22AA065483FD91222B898772AEC06809A1,
	GUIUtility_GetControlID_m2E0F66C8714A84DD5E9BEF4B9B464DAF1C03A9F7,
	GUIUtility_set_guiIsExiting_m0DCDD09CD48330FD781C03D2EA20F973878A2BC5,
	GUIUtility_get_hotControl_m6CD6AD33B46A9AFF2261E2C352DC7BAB4C20B026,
	GUIUtility_set_hotControl_mFBC648186C83874DE776A508C420183ADB527E9A,
	GUIUtility_TakeCapture_mD8AB4A480269628E17877B77A94A6481EFC9763C,
	GUIUtility_RemoveCapture_m295E1BC4B7E1D471AF7C40E3B587B7D525E3D693,
	GUIUtility_get_keyboardControl_mB0FAC848390B7F163CD2EE0A911FADD5CAD70B1E,
	GUIUtility_set_keyboardControl_m10F53FE5B292C2DC3C9A55CB504CC0DF36139465,
	GUIUtility_HasKeyFocus_m6AD234443A7B2AB471E14BE141FC5E8ADD261A0F,
	GUIUtility_ExitGUI_m9B30B2DFC94CC1C04D1F78358D79E9DAC1231B03,
	GUIUtility_GetDefaultSkin_m3275F31A9D5C3D90A1BCF5135F5B3968D6CD2C33,
	GUIUtility_ProcessEvent_m88640934E0C2BFA9BAC544DD2A91112FE8227FE2,
	GUIUtility_EndContainer_m19D0D5BA46EDAD7AF2D408A34D0141C5E481D963,
	GUIUtility_BeginGUI_m05702C560EBBC0B0CA3AD4F1FFBB5BD070DA2E04,
	GUIUtility_DestroyGUI_m5F94109C61BC0394F8936C899273093A6008702C,
	GUIUtility_EndGUI_mB34E82D4DD7A0AD22012DBAC207F605A68EA5E2E,
	GUIUtility_EndGUIFromException_m9C8B34B811C1E32C1BC818A57817FF5E117EC1B0,
	GUIUtility_EndContainerGUIFromException_mC60505F763292A2C80F7FBC0644F3B4679414DEB,
	GUIUtility_ResetGlobalState_mD0A482A31337B6200F644995345CF56849913928,
	GUIUtility_IsExitGUIException_mB887DAF961E8C1124916777B812FBF2324F5265F,
	GUIUtility_ShouldRethrowException_m60E879B4683840AAD5CD514E8C3BDDCC6403B652,
	GUIUtility_CheckOnGUI_mD167632D5D038DF66CC97F231CD45736D1F556D6,
	GUIUtility_RoundToPixelGrid_m0E594150154A6CCAD942F6B23179FB6886361105,
	GUIUtility_AlignRectToDevice_mE788EB722671F5DC10F7ADB8CA1A3427749ECDD1,
	GUIUtility_HitTest_m55D2F9EAC7EA99CA0C490546A6B45DA96F5AB3DA,
	GUIUtility_HitTest_m8C93A1BFB637176154C02F73038A98D1F616A7C2,
	GUIUtility__cctor_mAA2103B5A88A5D093DB8AEDBE54C5E4757D84A87,
	GUIUtility_get_systemCopyBuffer_Injected_mE69D1D289360DBA9C2260813753B9DDB9D5C3EAD,
	GUIUtility_set_systemCopyBuffer_Injected_mB90B727093E367031D146DC0D84249C7CAA53124,
	GUIUtility_Internal_GetControlID_Injected_m00F0DDAB73176CDD6EB5F19AA64511CF445E1249,
	GUIUtility_BeginContainerFromOwner_Injected_m9A05421D661AB304DCB90FB43A56E912E9438A24,
	GUIUtility_BeginContainer_Injected_mCA3B5274BBA81F16852B01CF4DFCE96840071420,
	GUIUtility_CheckForTabEvent_Injected_m24C24CF2AB79C28490AD2B64407C5903D4DDC76B,
	GUIUtility_AlignRectToDevice_Injected_mED42E3383D2A790E76602A5AB894DDE4850E43F1,
	GUIUtility_get_compositionString_Injected_m0EDBBEEAC7AAFA15E3C7C9C4CCB624F120679FD3,
	GUIUtility_set_compositionCursorPos_Injected_mF035733A0EF9A0258AB44982286A8FFFBF2B09A6,
	ExitGUIException__ctor_m345D7AD70E401C1AFD46E537CDCEC0F1C8BA342B,
	IMGUITextHandle_EmptyManagedCache_mAC1ACA3F16AF5B1E61236E71BDAD88A2DA3570FB,
	IMGUITextHandle_GetTextHandle_m6B91EF10F51F622EF4D4572D737FE6CDCA30C4D3,
	IMGUITextHandle_GetTextHandle_m6328A1878F88DC4310AA86B2588578487FA04E83,
	IMGUITextHandle_ShouldCleanup_mB7AB830F4456EB705C6A0E4A4E799EE24A8AE6D2,
	IMGUITextHandle_ClearUnusedTextHandles_m35270F73AD0FE1AE63D765A80CA83C01A7C0D5B3,
	IMGUITextHandle_GetTextHandle_mAA654059E522E2AA9A69E7CBB8AD7A425FB270B9,
	IMGUITextHandle_GetLineHeight_m984EAED8831A40A4CD43CBE700513FB3A9852726,
	IMGUITextHandle_GetPreferredSize_m17C480F0B46A0A56B50B7649455357963769EB58,
	IMGUITextHandle_ConvertGUIStyleToGenerationSettings_m97024F83D0248CA86E198DEF2E5C0A97A422B6DA,
	IMGUITextHandle_LegacyClippingToNewOverflow_m9BB92CC5B543D6695FEABBAA2133F151BD564623,
	IMGUITextHandle__ctor_m0812B91A7679E17A351E3F3C010AA4DB0057AB4E,
	IMGUITextHandle__cctor_m2D991A3AD827F1B2D4819D6891686BD68C599F8F,
	TextHandleTuple__ctor_m709BE4E88D85ACC314D665A81ECB76A9614E8786,
	GUILayoutEntry_get_style_mEFB6A8443849EC32BD84059C09632B53E44A5876,
	GUILayoutEntry_set_style_m0A23F7EFF504A581FC6CA86EF3BE753F060AC48A,
	GUILayoutEntry_get_marginLeft_m3B362DA8241B4008C2A6CDA693295A609F765221,
	GUILayoutEntry_get_marginRight_m032808DC8C04B31150407F3F61E71865C2636D7F,
	GUILayoutEntry_get_marginTop_m47BAB82D31A45E21F9AAB8229265788C0D19487C,
	GUILayoutEntry_get_marginBottom_m2BCCF0FC72E0230E155E7A26BA9FFD904AD4C221,
	GUILayoutEntry_get_marginHorizontal_m9847FB7747542BB322195F9CF4B75F55339CD7B5,
	GUILayoutEntry_get_marginVertical_mCD309A186E80B22E75DD8F15D2598B9B739C7AD3,
	GUILayoutEntry__ctor_m011B3DA69713EEA6BD98D4056B5ADE01F237E5B2,
	GUILayoutEntry_CalcWidth_m77BB8C0413A27303E4E61CB53586FD4A825C5EF3,
	GUILayoutEntry_CalcHeight_m295D607AB2FDD78D7C665BB3FB3A495E2E8CC0A6,
	GUILayoutEntry_SetHorizontal_m268577E88A2AE5870C14EFDA9CB88C94CAC2ACE9,
	GUILayoutEntry_SetVertical_mA20893626441C55001C1940C53A6A100DD22D61F,
	GUILayoutEntry_ApplyStyleSettings_m2D3679DAF547D104FE48E7D6D8E27B639F6A666B,
	GUILayoutEntry_ApplyOptions_mF024E6CEAAD97888AE293810E01F8431D79456A3,
	GUILayoutEntry_ToString_mD3785AC5958EB56ECA6E5D325D166C5F5725E615,
	GUILayoutEntry__cctor_mF6F64749802F89E5AA0A1458CE99CA5FC0D639C2,
	GUILayoutGroup_get_marginLeft_m343D82AA90154850B9B2A97B9E471D5235761EB3,
	GUILayoutGroup_get_marginRight_m2710F9CCC1B6D67BC4F9D9487B082B7E143757D0,
	GUILayoutGroup_get_marginTop_mA61C984665E93EE9E8670753AF919208528C4F87,
	GUILayoutGroup_get_marginBottom_m1EC579493343750FB013A6F01AD84DFEC8D489BD,
	GUILayoutGroup__ctor_m2AA89FAB5BB5BA76F4059D106A59E346739755D8,
	GUILayoutGroup_ApplyOptions_mD4C0BFAC7A90FB32BC6DC99ECA3EEA6C1C9396D2,
	GUILayoutGroup_ApplyStyleSettings_m5A88CB0FC11FE81405684C3EFF7EF7DA974D2649,
	GUILayoutGroup_ResetCursor_m58C36F1ABC54BE5EFC16D512318BED9EB8918127,
	GUILayoutGroup_CalcWidth_mFA744462378028538F1E3AAB39CB6AF0FBB1851B,
	GUILayoutGroup_SetHorizontal_m37D01CDDE4FAEDB20E0D469805EF96B878DFB5D5,
	GUILayoutGroup_CalcHeight_mAA9676BD80BAFC48F515ACA00E83FB7E9EE1FC2A,
	GUILayoutGroup_SetVertical_m28ADC75A1C5148E22EDD149221535C4B97BC5FE2,
	GUILayoutGroup_ToString_m7859D80D5D81B23684C4309DA0565D4CE1D2680C,
	GUILayoutGroup__cctor_m9214FACB657F5C28173EDCF59DAD85F14E7E2800,
	GUIScrollGroup__ctor_m95351A883B27B71698A4B84815CEA687D109F3FB,
	GUIScrollGroup_CalcWidth_m6B927DBF94A8940301A9FB64190403E5667712CE,
	GUIScrollGroup_SetHorizontal_m31FCDD252E67D51FC954C8E2C358BA0EB3AD7601,
	GUIScrollGroup_CalcHeight_mCB0CEC4871F6540145949E4CE8242172A75B2E5F,
	GUIScrollGroup_SetVertical_m8609CD909413A7364781818DDE37A314D8795FD6,
	ObjectGUIState__ctor_mA9AB2887ABAF5102164545D7F0CE59BCF05618B4,
	ObjectGUIState_Dispose_m156DC13F33DEFB261C8B13EB98A1A3782D182DE8,
	ObjectGUIState_Finalize_m10310B7E07DB5215C7845BF0F770B587D4F4C1B8,
	ObjectGUIState_Destroy_m316F4C75D0C8F18896A69BB9E39D90C0CDBE8726,
	ObjectGUIState_Internal_Create_m22F3AED2A44D4D00B478C2626295D432F74383EA,
	ObjectGUIState_Internal_Destroy_m936A111D9F70932A3030FE851C9E3BD82FD1F425,
	BindingsMarshaller_ConvertToNative_m09E6A8BB8FD9F387A27D6B7322C538A9632B5FC8,
	RuntimeTextSettings_get_defaultTextSettings_mCDC1440CA20563BCCBC6A6ED93B7D53D589A3CCD,
	RuntimeTextSettings_GetFontShader_mEBBC9BF9514C93E8F31FE85FFCBC81EC5628B09B,
	RuntimeTextSettings_GetStaticFallbackOSFontAsset_m4D2E042EBF13C0D078F09851324E7BEC2176CDF3,
	RuntimeTextSettings_SetStaticFallbackOSFontAsset_m265BF39CFD9DC3E89375AFA00B453F98003F56AD,
	RuntimeTextSettings__ctor_m71FB8625BA344A10F14506252F7F6EBF3DB78676,
	ScrollViewState__ctor_m9619262C4C72300A8B26011F627C68DF67425E53,
	SliderState__ctor_m650A11534C71EF571FD631CC3E910B756A16889E,
	TextEditingUtilities_get_hasSelection_mB7272F47E994E6B88A2B0229BED793D4C5B23219,
	TextEditingUtilities_set_revealCursor_m76B8081758CCD459072EAEF3B8FE3017A57735C2,
	TextEditingUtilities_get_stringCursorIndex_m0A921BBC3646F59E7A99BEA8157166603748EA8E,
	TextEditingUtilities_get_cursorIndex_m0D23C8510F3F2A20BBF5796F00CC36DA8BB32BD9,
	TextEditingUtilities_set_cursorIndex_mED289FA84CA33C4A695463C856524B501A78FEF4,
	TextEditingUtilities_get_cursorIndexNoValidation_mDBE41EB8F520D35613CBE19F58C8DD772A29B76D,
	TextEditingUtilities_set_cursorIndexNoValidation_m905F2A16C7C97532B6DB66CC7A406D80820C9AD1,
	TextEditingUtilities_set_selectIndexNoValidation_mABFB9F721CD2316AEDF619FA4E9F28D22EB64FD2,
	TextEditingUtilities_get_stringSelectIndex_m19395861C05FC86D5765555A627E5C4D96BE4D2F,
	TextEditingUtilities_get_selectIndex_m1331CF40E64C203B1713CFC2FEC5E0F30FFC737A,
	TextEditingUtilities_set_selectIndex_mFB98D1E5E2C236DEEE7A45619965502B73E0ADBA,
	TextEditingUtilities_get_text_mC8854D29B1F95E04E0FFB49F1F2327E77598EF8E,
	TextEditingUtilities_set_text_mC1FD19476AF4FA014E9DBA5A33C54A57E2FA70EC,
	TextEditingUtilities_SetTextWithoutNotify_m9EDB056450908DE504396BC41C057016806B4AE1,
	TextEditingUtilities__ctor_m6503B88727D1F4008C31E4FB54F2153A44E99B07,
	TextEditingUtilities_UpdateImeState_mC13FC46AB62C566576C0D653774896FFA438003B,
	TextEditingUtilities_ShouldUpdateImeWindowPosition_m58FC98A57B608095F3EB6688A0D95FA64B8444E1,
	TextEditingUtilities_SetImeWindowPosition_m1DFAAA8DBA6A946B204470806FB968359BFB3C48,
	TextEditingUtilities_GeneratePreviewString_mA97B83DDA33F11F6580B86F6F2C438F19018A037,
	TextEditingUtilities_EnableCursorPreviewState_mD28C1FAC4AFFEE396D903D1EED90DF1F5BC6A85B,
	TextEditingUtilities_RestoreCursorState_m36D71121DFD69D0EB43FCF48E49E1F616F9346C4,
	TextEditingUtilities_HandleKeyEvent_m155A0EFF2CC76B511BDA3405C68A48408C1C6256,
	TextEditingUtilities_PerformOperation_m8DC34D9795E11260FA9F4C4961601B189F342820,
	TextEditingUtilities_MapKey_mCC38540E032D8765E0E280B94072141E74FF43FF,
	TextEditingUtilities_InitKeyActions_m08B59B3C8199E8DA37622E85D269F9E848B06381,
	TextEditingUtilities_DeleteLineBack_m34769E8A0D70CE5BDBA3B11DA506BC7CA859CCFA,
	TextEditingUtilities_DeleteWordBack_mC235A97BDFEE8CE4C993BCF02DF4E86AB4BB8F1D,
	TextEditingUtilities_DeleteWordForward_m7A29C8E5BD4E3F01F7A379C16D971BBF565E5CAA,
	TextEditingUtilities_Delete_mF34D04ACA64C871CDE0F5F5DD6CC36A0667926DD,
	TextEditingUtilities_Backspace_m08DB317F2AEDA35F6227D52FF92B3EE4F7AA5909,
	TextEditingUtilities_DeleteSelection_mF7E0C7A8B7A8984A5DA2C55839BFE60E0A70B847,
	TextEditingUtilities_ReplaceSelection_m49F49CDB5D91B695392E2CE1B7BDC5A46817BBCE,
	TextEditingUtilities_Insert_m74BE32F1C3044BE4A095E43FF6BF8D84150306F6,
	TextEditingUtilities_CanPaste_m81E3C512EF04804A3594020C3CD084F5BD85B3E7,
	TextEditingUtilities_Cut_m5E08F36BC2F88E0E55483A524E815A3EAA429D2B,
	TextEditingUtilities_Paste_m876D2AD7A881EAC57D762E15F9ACB1AC26B3C28C,
	TextEditingUtilities_ReplaceNewlinesWithSpaces_m368B355EF98969A0A9E527D67C43E173B3FCAC74,
	TextEditingUtilities_OnBlur_mCD1823DED60BE96C25C0E31B8DEC5F8EB1ACFD13,
	TextEditingUtilities_TouchScreenKeyboardShouldBeUsed_mAF399D5C01CD9A7B7F4E1C188792420AFBA99D53,
	TextEditor_get_showCursor_m2A6C5BACAAC0FEC2985858F08839D34B5A296AFA,
	TextEditor_get_text_mB5A19231EF7159855775CF3E9C5BC5346156E168,
	TextEditor_set_text_mB71257AAD99A56AD5EA96DB546B17296E60C4455,
	TextEditor_get_textWithWhitespace_m7C77FB0BD5B679C23C2E26217E640643759B9277,
	TextEditor_set_textWithWhitespace_mF1C88E0F8C969821913285168E6252D227E51498,
	TextEditor_get_position_m40763329A82988B1C5D5C1DA9919932061C99E13,
	TextEditor__ctor_m4AEAC85E4950B709A35F26D1F0DAB3C9D35E3494,
	TextEditor_OnTextChangedHandle_m349D5B2435419A7872C00BC62AF9C03CB0DCFDD3,
	TextEditor_OnContentTextChangedHandle_m4FEDA32B87D849D01DC03A06ACA6F07F893FE466,
	TextEditor_UpdateTextHandle_m2AC449E0917F90F2D9F0EABDDA1C89DEE98F7073,
	TextEditor_UpdateScrollOffset_mD3F056830FF3FFC3461ED965EB0B7E306536FC3B,
	TextEditor_OnCursorIndexChange_m9B9C472B0F62917E96E5E27F15A76C9E4E493012,
	TextEditor_OnSelectIndexChange_m99E1BBDFC6398F47F3170A6A46C5428F292FEE21,
	TextSelectingUtilities_get_hasSelection_m86EA37D0A10EC2C4C1886C7E770DAB34DB8A66CD,
	TextSelectingUtilities_get_revealCursor_mD9502AE79AB9AC44496008153F267CBB4A9B3C16,
	TextSelectingUtilities_set_revealCursor_m2A3BFE850A09B0716824E763373A521A24CC5F52,
	TextSelectingUtilities_get_m_CharacterCount_m4C9189574E900CF75E41BB29861246D64A83CB0E,
	TextSelectingUtilities_get_characterCount_mF1A48644B9D24C0AE96B6259FDAFC87CB1BD5FEB,
	TextSelectingUtilities_get_m_TextElementInfos_mB87B7D1C40D71249B4E20DA1D7501194A2477368,
	TextSelectingUtilities_get_cursorIndex_m89386F5B913CD481337E49A0635834F433B4AC5F,
	TextSelectingUtilities_set_cursorIndex_m0D3D5D519DA459906E983EABAA4AF692C4763269,
	TextSelectingUtilities_get_cursorIndexNoValidation_mA6143409C53305FB602A73CDEC6EB201E2763ED9,
	TextSelectingUtilities_set_cursorIndexNoValidation_mB590BB148DA02766188234EE77321EB51D4FACA9,
	TextSelectingUtilities_SetCursorIndexWithoutNotify_mF240906A6FA8A38F5CB6AD1C5601265D05CCCAEE,
	TextSelectingUtilities_get_selectIndex_m1535CA4D982CC3A4C46E4491FA36E9514243CCB9,
	TextSelectingUtilities_set_selectIndex_m1697EFBA1D0B91E0B20FD23B69A43D32D2110F10,
	TextSelectingUtilities_set_selectIndexNoValidation_mD676910A3F674119D5C43EC8B47C6D5528862062,
	TextSelectingUtilities_SetSelectIndexWithoutNotify_m33DB6522D2FA6877E3B991CDFD4944836A5D77FF,
	TextSelectingUtilities_get_selectedText_m4A131331842BA17B453A09FE4663A75B7B356013,
	TextSelectingUtilities__ctor_m0D593E63B3CFE982829CFF9C93C5858E27AB84AE,
	TextSelectingUtilities_HandleKeyEvent_mD6AD5FEF96C31860C66D49324F98BD7AB27AE551,
	TextSelectingUtilities_PerformOperation_m3F865D868A3A9264B28FAABBFE92F654BF3706FC,
	TextSelectingUtilities_MapKey_mDCCB5F98583F4FC584FD97AF27D37F91A7C5048B,
	TextSelectingUtilities_InitKeyActions_m005DD393E320ADA5BEFC2AA357EE964A8AB6CCED,
	TextSelectingUtilities_ClearCursorPos_m743C82F3CC7576E5050B6FA23133EC3FA8E9ED9C,
	TextSelectingUtilities_OnFocus_mCAC979E4683D3A0B91C91FCC19516E5FEE605A9C,
	TextSelectingUtilities_SelectAll_m89B71F5AF97AC5848616468DEFCF062C26DF23FD,
	TextSelectingUtilities_SelectNone_m47791B4FBE066CCC974155E1BD9FE8ACCB48D21A,
	TextSelectingUtilities_SelectLeft_mB51FE46E45D1C077ACB44AC5E2BF63C52AC3727D,
	TextSelectingUtilities_SelectRight_mFC95A2800C1CBEC7606EA8901F75CC946ED3BCA8,
	TextSelectingUtilities_SelectUp_m1D90105D04CF6CBAE84D059CEF0495FD2FF0C22A,
	TextSelectingUtilities_SelectDown_m8E2EE5EC95CE507A7814D0FAAD865557C2A191C1,
	TextSelectingUtilities_SelectTextEnd_mFF22956A56670B41BEE1A4527A9057B63BE89927,
	TextSelectingUtilities_SelectTextStart_mD4F085F9AF7C2441D60E54CDE64B96A6934D13A6,
	TextSelectingUtilities_SelectToStartOfNextWord_mC53BBECD698C32BAD6B4856F52ACA9E9D59F3E52,
	TextSelectingUtilities_SelectToEndOfPreviousWord_mAC43A04CA459FE506650A86B695900300E4B24D8,
	TextSelectingUtilities_SelectWordRight_m75669B452A334F48FB1CE5E0AC6792862E706B5E,
	TextSelectingUtilities_SelectWordLeft_mC488E7BBD9E8187F426A2F1CA82C528D76F4FCCC,
	TextSelectingUtilities_SelectGraphicalLineStart_m87E74A85CBD46469B2D1263436D2BE2FE5ABDB38,
	TextSelectingUtilities_SelectGraphicalLineEnd_m60A6059D86AEB922ED7829EAF9C4E53B800911B4,
	TextSelectingUtilities_SelectParagraphForward_m64D82C33CE1B82DC1B994B6CA027D717AB4280EB,
	TextSelectingUtilities_SelectParagraphBackward_m77EE0A0A167E91E1850937F543DBA615DC6DE0A0,
	TextSelectingUtilities_SelectCurrentWord_mCC2AC7DD6D2BA6D2DF3DD728D883FF0D6963A959,
	TextSelectingUtilities_SelectCurrentParagraph_mBD0B848A023ED86697EBA135E81B59ACD13B2B7A,
	TextSelectingUtilities_MoveRight_m8F1910A2773A39EF5CE248349F5A6CD7166AB795,
	TextSelectingUtilities_MoveLeft_m094C534A56FC3CDA9C2423E46D179F359693370E,
	TextSelectingUtilities_MoveUp_mF0F3EE17A2CB3C4C1AEB950E80A5237A55D2711D,
	TextSelectingUtilities_MoveDown_m7EF798D6A19267DE30ED50C66697F5BC8AB814B8,
	TextSelectingUtilities_MoveLineStart_m561A829C19F6C50028473CD5F81C508F3EEFE276,
	TextSelectingUtilities_MoveLineEnd_mB4BABB86B094C9B88DEF94E0392BAE4396283B61,
	TextSelectingUtilities_MoveGraphicalLineStart_m1ECAAAF8A29D63C5D7E76170D3745E3EB9E2266F,
	TextSelectingUtilities_MoveGraphicalLineEnd_m8BB408E28EA20EB56531B1FFD417FE54296008BE,
	TextSelectingUtilities_MoveTextStart_m7A276F1B11A1DAF468AC84324E592005B5D47350,
	TextSelectingUtilities_MoveTextEnd_mDA69E553CA7D50781E845169852F1A0059FF0EB9,
	TextSelectingUtilities_MoveParagraphForward_m88210A22BC823945D6AA137D50603388233502EA,
	TextSelectingUtilities_MoveParagraphBackward_m087604CE592162192829ADB142B786B0C436A58C,
	TextSelectingUtilities_MoveWordRight_mDCDC1B673D599BB94C95D6DC364D8877E87E3383,
	TextSelectingUtilities_MoveToStartOfNextWord_mD1CEFD9620822349FDE09237943F943EB512A8C2,
	TextSelectingUtilities_MoveToEndOfPreviousWord_m5BE2565747FFC49AA41526504653159852FC50A7,
	TextSelectingUtilities_MoveWordLeft_m7D8131CD2DF2DF180D3FC249E2F0E17E250E5D3D,
	TextSelectingUtilities_MouseDragSelectsWholeWords_mB586078A58B5D56A53138856AB8DE9BD33535CC1,
	TextSelectingUtilities_ExpandSelectGraphicalLineStart_m5E109D0A6A12D3D2FDF09F3E1407EB0347F7C4EF,
	TextSelectingUtilities_ExpandSelectGraphicalLineEnd_mFCA738E71AADB3C1206F279BC2681CA906A36D74,
	TextSelectingUtilities_DblClickSnap_m6472F8DA3F0FC46FF75FFB394B283F5E5EC834FA,
	TextSelectingUtilities_MoveCursorToPosition_Internal_mE4AEE1AA57B8CCBB371C24B4F4B1AA2FF89886FD,
	TextSelectingUtilities_SelectToPosition_m75C9B53E1227CF9D487D5C8D771F0D8ACFEDC2F8,
	TextSelectingUtilities_FindNextSeperator_mCF332FBDFEA6BCB471EDF75D76A139A580DB0E2A,
	TextSelectingUtilities_FindPrevSeperator_mD0F56A4106D376F28D6A0E8E07D7D4DAB9FF8C7E,
	TextSelectingUtilities_FindStartOfNextWord_m3D436C6FB45B3574F6F436B7CBAD30F5372C6870,
	TextSelectingUtilities_FindEndOfPreviousWord_mA7AF965D2F01728EE872F2CB3F03083ABA63D174,
	TextSelectingUtilities_FindEndOfClassification_m134E7AB62A9E90595A3C6DCC1DD517515B85AB1A,
	TextSelectingUtilities_ClampTextIndex_m04DED78B3B466D3E5AF59442F736A822ABC8D7E0,
	TextSelectingUtilities_IndexOfEndOfLine_m9D87F72DC289F7095E615D97D821BBCE8D2B978B,
	TextSelectingUtilities_PreviousCodePointIndex_mCE1A8A7E9180B182AEA4DB6562EBDE854A860C6D,
	TextSelectingUtilities_NextCodePointIndex_mA936059BE65DAABE0446AFB5425BB4EA83392607,
	TextSelectingUtilities_GetGraphicalLineStart_mEDB6AF99C1BBE0A1180AEE4FE67DDC0A223BD218,
	TextSelectingUtilities_GetGraphicalLineEnd_mD956DB2F4EC24F3DE89069232733376B615D3204,
	TextSelectingUtilities_Copy_m69701E12FFE465B70E677DCCCCF3148873A5FE0A,
	TextSelectingUtilities_ClassifyChar_m887E3900015DB05F1EF0E5F4630748CCA5C3CD52,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_m3791FADF6D0284BCC1AF6156A077038C2AA23055,
};
extern void EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA_AdjustorThunk (void);
extern void EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F_AdjustorThunk (void);
extern void EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F_AdjustorThunk (void);
extern void EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313_AdjustorThunk (void);
extern void EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE_AdjustorThunk (void);
extern void EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6_AdjustorThunk (void);
extern void EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B_AdjustorThunk (void);
extern void ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4_AdjustorThunk (void);
extern void ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[9] = 
{
	{ 0x06000058, EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA_AdjustorThunk },
	{ 0x06000059, EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F_AdjustorThunk },
	{ 0x0600005A, EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F_AdjustorThunk },
	{ 0x0600005B, EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313_AdjustorThunk },
	{ 0x0600005C, EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE_AdjustorThunk },
	{ 0x0600005D, EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6_AdjustorThunk },
	{ 0x0600005E, EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B_AdjustorThunk },
	{ 0x0600008B, ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4_AdjustorThunk },
	{ 0x0600008C, ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D_AdjustorThunk },
};
static const int32_t s_InvokerIndices[587] = 
{
	10637,
	10859,
	8781,
	10859,
	8781,
	10637,
	10637,
	10637,
	8568,
	10781,
	10781,
	10859,
	10637,
	10637,
	10854,
	8775,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10698,
	8627,
	10870,
	15623,
	15981,
	8570,
	15451,
	14703,
	16321,
	15981,
	15979,
	16321,
	10870,
	8568,
	10870,
	8627,
	10537,
	10537,
	10537,
	10537,
	10537,
	10537,
	10537,
	16341,
	15983,
	10537,
	10537,
	10537,
	10537,
	15710,
	10637,
	6166,
	10698,
	10870,
	15581,
	14708,
	14708,
	14708,
	14708,
	15581,
	15581,
	15581,
	14712,
	15831,
	15831,
	14708,
	15581,
	15581,
	15888,
	14716,
	15581,
	14712,
	15581,
	14712,
	15581,
	14712,
	14708,
	14708,
	15981,
	14713,
	15449,
	14702,
	15626,
	10537,
	8468,
	10537,
	8468,
	10537,
	6112,
	6112,
	16294,
	15973,
	16294,
	15973,
	16294,
	15973,
	16291,
	15972,
	16291,
	15972,
	16420,
	15974,
	15983,
	16341,
	15983,
	16337,
	15982,
	14834,
	13714,
	14834,
	13714,
	13714,
	16341,
	11246,
	15971,
	15971,
	15971,
	15971,
	15971,
	15971,
	4630,
	8568,
	16365,
	16420,
	16321,
	16337,
	15982,
	14720,
	13646,
	16420,
	15971,
	15971,
	15971,
	13498,
	4579,
	10870,
	8627,
	8627,
	10698,
	8627,
	10698,
	8627,
	8627,
	8627,
	10698,
	8627,
	10870,
	8627,
	4638,
	2193,
	15710,
	16420,
	10698,
	16420,
	15720,
	15720,
	4265,
	15787,
	14704,
	14294,
	14294,
	14700,
	15979,
	15983,
	13608,
	16420,
	16420,
	14840,
	15983,
	15983,
	16420,
	14699,
	14699,
	8568,
	8568,
	10870,
	10870,
	10870,
	10870,
	16420,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	16341,
	10870,
	10870,
	7489,
	7489,
	10870,
	10698,
	4630,
	10870,
	8473,
	16323,
	10870,
	10870,
	4630,
	14314,
	10870,
	14708,
	15981,
	15626,
	10698,
	8627,
	10698,
	10637,
	10637,
	8568,
	10537,
	10637,
	10859,
	10781,
	10781,
	10537,
	10537,
	8468,
	10637,
	10637,
	10537,
	15626,
	15981,
	7362,
	7362,
	357,
	1562,
	1972,
	14814,
	15451,
	15983,
	16341,
	15979,
	10870,
	10870,
	10698,
	8627,
	10698,
	10698,
	10698,
	10781,
	357,
	716,
	219,
	16341,
	1971,
	3645,
	10698,
	11313,
	12049,
	14793,
	16420,
	16420,
	14708,
	14708,
	15625,
	15581,
	15581,
	14712,
	15449,
	15581,
	14708,
	15831,
	15831,
	15449,
	15449,
	14709,
	15581,
	15581,
	15449,
	14245,
	14245,
	11303,
	12025,
	12024,
	14672,
	15439,
	15981,
	16323,
	15626,
	14201,
	16379,
	15991,
	16321,
	16291,
	16341,
	15983,
	13150,
	13150,
	15983,
	15983,
	16420,
	15583,
	16420,
	16420,
	16291,
	15447,
	13353,
	16341,
	15979,
	15994,
	16321,
	16321,
	15979,
	15979,
	15705,
	16420,
	16420,
	14186,
	15972,
	16321,
	15979,
	16420,
	16420,
	16321,
	15979,
	15447,
	16420,
	16341,
	13605,
	16420,
	13603,
	15979,
	15979,
	15451,
	15451,
	16420,
	15451,
	15451,
	16420,
	15835,
	15789,
	13075,
	13074,
	16420,
	15971,
	15971,
	13147,
	15981,
	15981,
	15581,
	12682,
	15971,
	15971,
	10870,
	16420,
	12598,
	11829,
	13076,
	16420,
	13279,
	15832,
	10859,
	12127,
	15579,
	10870,
	16420,
	4733,
	10698,
	8627,
	10637,
	10637,
	10637,
	10637,
	10637,
	10637,
	722,
	10870,
	10870,
	4735,
	4735,
	8627,
	8627,
	10698,
	16420,
	10637,
	10637,
	10637,
	10637,
	10870,
	8627,
	8627,
	10870,
	10870,
	4735,
	10870,
	4735,
	10698,
	16420,
	10870,
	10870,
	4735,
	10870,
	4735,
	10870,
	10870,
	10870,
	10870,
	16323,
	15981,
	15626,
	16341,
	10698,
	10698,
	8627,
	10870,
	10870,
	10870,
	10537,
	8468,
	10637,
	10637,
	8568,
	10637,
	8568,
	8568,
	10637,
	10637,
	8568,
	10698,
	8627,
	8627,
	2193,
	10537,
	10537,
	8781,
	7472,
	10870,
	10870,
	6166,
	8568,
	14804,
	10870,
	10537,
	10537,
	10537,
	10537,
	10537,
	10537,
	8627,
	6311,
	10537,
	10537,
	10537,
	15710,
	10870,
	10537,
	10537,
	10698,
	8627,
	10698,
	8627,
	10738,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10537,
	10537,
	8468,
	10637,
	10637,
	10698,
	10637,
	8568,
	10637,
	8568,
	8568,
	10637,
	8568,
	8568,
	8568,
	10698,
	8627,
	6166,
	6112,
	14804,
	10870,
	10870,
	8468,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	8468,
	10870,
	10870,
	8468,
	4780,
	8781,
	7016,
	7016,
	7016,
	7016,
	3195,
	7016,
	7016,
	7016,
	7016,
	7016,
	7016,
	10870,
	7016,
	15905,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule = 
{
	"UnityEngine.IMGUIModule.dll",
	587,
	s_methodPointers,
	9,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
