# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 135ms
  generate-prefab-packages
    [gap of 11ms]
    exec-prefab 810ms
    [gap of 31ms]
  generate-prefab-packages completed in 852ms
  execute-generate-process
    exec-configure 953ms
    [gap of 1214ms]
  execute-generate-process completed in 2168ms
  [gap of 33ms]
  remove-unexpected-so-files 296ms
  [gap of 62ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 3589ms

