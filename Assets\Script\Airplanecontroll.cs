using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

[RequireComponent(typeof(Rigidbody))]
public class AirplaneControl : MonoBehaviour
{
    [Header("Movement Settings")]
    public float pitchSpeed = 10f;
    public float yawSpeed = 10f;
    public float rollSpeed = 10f;
    public float maxMoveSpeed = 100f;

    [Header("Rotation Limits")]
    public float minPitchAngle = -45f;
    public float maxPitchAngle = 45f;
    public float minYawAngle = -360f;
    public float maxYawAngle = 360f;
    public float minRollAngle = -45f;
    public float maxRollAngle = 45f;

    [Header("Input Keys")]
    public KeyCode pitchUpKey = KeyCode.W;
    public KeyCode pitchDownKey = KeyCode.S;
    public KeyCode yawLeftKey = KeyCode.A;
    public KeyCode yawRightKey = KeyCode.D;
    public KeyCode rollLeftKey = KeyCode.Q;
    public KeyCode rollRightKey = KeyCode.E;

    [Header("Mobile UI Controls")]
    public Button pitchUpButton;
    public Button pitchDownButton;
    public Button yawLeftButton;
    public Button yawRightButton;
    public Button rollLeftButton;
    public Button rollRightButton;
    public Slider moveSpeedSlider;
    public Joystick controlJoystick;
    public Button startEngineButton;

    [Header("Audio Settings")]
    public AudioSource engineIdleSound;
    public AudioSource engineLowRpmSound;
    public AudioSource engineHighRpmSound;
    public AudioSource engineStartSound;

    private Rigidbody rb;
    private float pitchInput = 0f;
    private float yawInput = 0f;
    private float rollInput = 0f;
    private float moveSpeed = 0f;
    public bool isEngineStarted = false;

    private bool isPitchUpPressed = false;
    private bool isPitchDownPressed = false;
    private bool isYawLeftPressed = false;
    private bool isYawRightPressed = false;
    private bool isRollLeftPressed = false;
    private bool isRollRightPressed = false;

    [Header("Exhaust Effects")]
    public ParticleSystem exhaustSilencerEffect;

    [Header("Refference")]
    public static AirplaneControl instance;

    private void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.mass = 1000f;
        rb.linearDamping = 0.5f;
        rb.angularDamping = 1f;
        rb.isKinematic = true; // Start as kinematic until engine starts

        if (moveSpeedSlider != null)
        {
            moveSpeedSlider.minValue = 0f;
            moveSpeedSlider.maxValue = maxMoveSpeed;
            moveSpeedSlider.onValueChanged.AddListener(value => moveSpeed = value);
        }

        AssignButtonEvents();
        AssignStartEngineButtonEvent();
    }

    private void Start()
    {
        instance = this;
        exhaustSilencerEffect.Stop();
        if (isEngineStarted)
        {
            PlayEngineIdle();
        }
    }

    private void FixedUpdate()
    {
        if (isEngineStarted)
        {
            rb.useGravity = moveSpeed <= 0f;
            SmoothInput();
            ApplyTorque();
            ApplyThrust();
            ApplyLift();
            UpdateEngineSounds();
        }
    }

    private void SmoothInput()
    {
        float joystickVertical = controlJoystick != null ? controlJoystick.Vertical : 0f;
        float joystickHorizontal = controlJoystick != null ? controlJoystick.Horizontal : 0f;
        float joystickRoll = controlJoystick != null ? controlJoystick.Horizontal : 0f;

        pitchInput = Mathf.Lerp(pitchInput, (isPitchUpPressed || Input.GetKey(pitchUpKey)) ? 1f : (isPitchDownPressed || Input.GetKey(pitchDownKey)) ? -1f : joystickVertical, Time.deltaTime * 2f);
        yawInput = Mathf.Lerp(yawInput, (isYawLeftPressed || Input.GetKey(yawLeftKey)) ? -1f : (isYawRightPressed || Input.GetKey(yawRightKey)) ? 1f : joystickHorizontal, Time.deltaTime * 2f);
        rollInput = Mathf.Lerp(rollInput, (isRollLeftPressed || Input.GetKey(rollLeftKey)) ? -1f : (isRollRightPressed || Input.GetKey(rollRightKey)) ? 1f : joystickRoll, Time.deltaTime * 2f);
    }

    private void ApplyThrust()
    {
        Vector3 forwardForce = transform.forward * moveSpeed * rb.mass;
        rb.AddForce(forwardForce);
    }

    private void ApplyTorque()
    {
        if (moveSpeed > 0f)
        {
            Vector3 currentRotation = transform.localEulerAngles;

            float pitch = Mathf.Clamp(NormalizeAngle(currentRotation.x + pitchInput * pitchSpeed * Time.fixedDeltaTime), minPitchAngle, maxPitchAngle);
            float yaw = Mathf.Clamp(NormalizeAngle(currentRotation.y + yawInput * yawSpeed * Time.fixedDeltaTime), minYawAngle, maxYawAngle);
            float roll = Mathf.Clamp(NormalizeAngle(currentRotation.z - rollInput * rollSpeed * Time.fixedDeltaTime), minRollAngle, maxRollAngle);

            rb.MoveRotation(Quaternion.Euler(pitch, yaw, roll));
        }
    }

    private void ApplyLift()
    {
        if (moveSpeed > 0f)
        {
            Vector3 lift = transform.up * rb.linearVelocity.magnitude * 0.5f;
            rb.AddForce(lift);
        }
    }

    private void UpdateEngineSounds()
    {
        if (moveSpeed <= 0f)
        {
            PlayEngineIdle();
        }
        else if (moveSpeed < 30f)
        {
            PlayEngineLowRpm();
        }
        else
        {
            PlayEngineHighRpm();
        }
    }

    private void PlayEngineIdle()
    {
        if (engineIdleSound != null && !engineIdleSound.isPlaying)
        {
            engineIdleSound.Play();
            StopOtherEngineSounds(engineIdleSound);
        }
    }

    private void PlayEngineLowRpm()
    {
        if (engineLowRpmSound != null && !engineLowRpmSound.isPlaying)
        {
            engineLowRpmSound.Play();
            StopOtherEngineSounds(engineLowRpmSound);
        }
    }

    private void PlayEngineHighRpm()
    {
        if (engineHighRpmSound != null && !engineHighRpmSound.isPlaying)
        {
            engineHighRpmSound.Play();
            StopOtherEngineSounds(engineHighRpmSound);
        }
    }

    private void StopOtherEngineSounds(AudioSource playingSound)
    {
        if (engineIdleSound != playingSound) engineIdleSound.Stop();
        if (engineLowRpmSound != playingSound) engineLowRpmSound.Stop();
        if (engineHighRpmSound != playingSound) engineHighRpmSound.Stop();
    }

   
    private void StartEngine()
    {
        if (!isEngineStarted)
        {
            isEngineStarted = true;

            // Set rigidbody to non-kinematic when engine starts
            rb.isKinematic = false;

            if (engineStartSound != null)
            {
                engineStartSound.Play();
            }
            if (exhaustSilencerEffect != null)
            {
                exhaustSilencerEffect.Play();
            }
            if (engineStartSound.isPlaying)
            {
                engineStartSound.loop = false;
            }
            Invoke("StartIdleSound", engineStartSound.clip.length);
        }
    }

    private void StartIdleSound()
    {
        PlayEngineIdle();
    }

    public void StopEngine()
    {
        if (isEngineStarted)
        {
            isEngineStarted = false;

            // Set rigidbody to kinematic when engine stops
            rb.isKinematic = true;
            rb.useGravity = false;

            // Stop all engine sounds
            if (engineIdleSound != null) engineIdleSound.Stop();
            if (engineLowRpmSound != null) engineLowRpmSound.Stop();
            if (engineHighRpmSound != null) engineHighRpmSound.Stop();

            // Stop exhaust effects
            if (exhaustSilencerEffect != null)
            {
                exhaustSilencerEffect.Stop();
            }

            // Reset move speed
            moveSpeed = 0f;
            if (moveSpeedSlider != null)
            {
                moveSpeedSlider.value = 0f;
            }
        }
    }

    public bool CanCrash()
    {
        return isEngineStarted && moveSpeed > 0f;
    }

    private void AssignButtonEvents()
    {
        AddButtonEvent(pitchUpButton, () => isPitchUpPressed = true, () => isPitchUpPressed = false);
        AddButtonEvent(pitchDownButton, () => isPitchDownPressed = true, () => isPitchDownPressed = false);
        AddButtonEvent(yawLeftButton, () => isYawLeftPressed = true, () => isYawLeftPressed = false);
        AddButtonEvent(yawRightButton, () => isYawRightPressed = true, () => isYawRightPressed = false);
        AddButtonEvent(rollLeftButton, () => isRollLeftPressed = true, () => isRollLeftPressed = false);
        AddButtonEvent(rollRightButton, () => isRollRightPressed = true, () => isRollRightPressed = false);
    }

   
    private void AssignStartEngineButtonEvent()
    {
        if (startEngineButton != null)
        {
            startEngineButton.onClick.AddListener(StartEngine);
        }
    }

    private void AddButtonEvent(Button button, System.Action onPointerDown, System.Action onPointerUp)
    {
        if (button == null) return;

        EventTrigger trigger = button.gameObject.AddComponent<EventTrigger>();

        EventTrigger.Entry pointerDownEntry = new EventTrigger.Entry { eventID = EventTriggerType.PointerDown };
        pointerDownEntry.callback.AddListener((data) => onPointerDown());
        trigger.triggers.Add(pointerDownEntry);

        EventTrigger.Entry pointerUpEntry = new EventTrigger.Entry { eventID = EventTriggerType.PointerUp };
        pointerUpEntry.callback.AddListener((data) => onPointerUp());
        trigger.triggers.Add(pointerUpEntry);
    }

    private float NormalizeAngle(float angle)
    {
        angle %= 360f;
        if (angle > 180f) angle -= 360f;
        return angle;
    }
}
