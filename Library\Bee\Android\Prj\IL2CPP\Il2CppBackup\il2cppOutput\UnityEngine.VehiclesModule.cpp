﻿#include "pch-cpp.hpp"






struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481;

IL2CPP_EXTERN_C RuntimeField* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t3E8E0BA57A4D4D0EF43301B668B802ED48E39035 
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 
{
	float ___spring;
	float ___damper;
	float ___targetPosition;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 
{
	float ___m_ExtremumSlip;
	float ___m_ExtremumValue;
	float ___m_AsymptoteSlip;
	float ___m_AsymptoteValue;
	float ___m_Stiffness;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR intptr_t MarshalledUnityObject_MarshalNotNull_TisRuntimeObject_mEB1AA6B672D00242BB9DCE007056EC0E9C8DB075_gshared_inline (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
inline intptr_t MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* ___0_obj, const RuntimeMethod* method)
{
	return ((  intptr_t (*) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, const RuntimeMethod*))MarshalledUnityObject_MarshalNotNull_TisRuntimeObject_mEB1AA6B672D00242BB9DCE007056EC0E9C8DB075_gshared_inline)(___0_obj, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0 (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20 (intptr_t ___0__unity_self, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6 (intptr_t ___0__unity_self, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7 (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714 (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4 (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F (intptr_t ___0__unity_self, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE (intptr_t ___0__unity_self, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___2_quat, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB (intptr_t ___0__unity_self, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* ___1_hit, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* V_0 = NULL;
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_0 = __this->___m_Collider;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* _returnValue;
	_returnValue = WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Point;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Normal;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_Force;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_ForwardSlip;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___m_SidewaysSlip;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 V_0;
	memset((&V_0), 0, sizeof(V_0));
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20(G_B2_0, (&V_0), NULL);
		JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6(G_B2_0, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A(G_B2_0, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0(G_B2_0, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73(G_B2_0, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1(G_B2_0, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2 = ___0_value;
		WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20(G_B2_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		bool L_2;
		L_2 = WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		float L_2;
		L_2 = WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F(G_B2_0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___1_quat, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_2 = ___0_pos;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* L_3 = ___1_quat;
		WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE(G_B2_0, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* ___0_hit, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	intptr_t G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	{
		intptr_t L_0;
		L_0 = MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_inline(__this, MarshalledUnityObject_MarshalNotNull_TisWheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481_mE74E2D6553CA63254E4BB822246B5B2BEDAB6331_RuntimeMethod_var);
		intptr_t L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000f;
		}
		G_B1_0 = L_1;
	}
	{
		ThrowHelper_ThrowNullReferenceException_mA9C7629D32240EE0218631933DAC647668CA63CF(__this, NULL);
		G_B2_0 = G_B1_0;
	}

IL_000f:
	{
		WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* L_2 = ___0_hit;
		bool L_3;
		L_3 = WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB(G_B2_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0 (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0_ftn) (intptr_t);
	static WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_radius_Injected_m5CD5D0CB11C12B733B28C52AA4E40E88DA2A5FE0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_radius_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989_ftn) (intptr_t, float);
	static WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_radius_Injected_mAE6D905B1F3F42CDC1806917730A14657EE28989_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_radius_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F_ftn) (intptr_t);
	static WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionDistance_Injected_m850774CCC703B484661C119D1808EA0484D86E7F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionDistance_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C_ftn) (intptr_t, float);
	static WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionDistance_Injected_m02A767E5525EDB8024DA6687D99B1115E746556C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionDistance_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20 (intptr_t ___0__unity_self, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20_ftn) (intptr_t, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionSpring_Injected_m15DDA907765221AF05134BBEEC29943CF0BD0F20_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionSpring_Injected(System.IntPtr,UnityEngine.JointSpring&)");
	_il2cpp_icall_func(___0__unity_self, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6 (intptr_t ___0__unity_self, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6_ftn) (intptr_t, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionSpring_Injected_mE1BB7A53187F8D1F26E5152B6E1B88691C8BFBC6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionSpring_Injected(System.IntPtr,UnityEngine.JointSpring&)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC_ftn) (intptr_t, float);
	static WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forceAppPointDistance_Injected_m6A94C0250FEB50905ED5A244D795B127253EEAAC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forceAppPointDistance_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF_ftn) (intptr_t, float);
	static WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_mass_Injected_m5EBC3BFAF5EEEAECE9ECCD95F200DEC0E4E369AF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_mass_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC_ftn) (intptr_t, float);
	static WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_wheelDampingRate_Injected_m2429D94ED7D3B1290B547B2B5EC6DB9E1450DCEC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_wheelDampingRate_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A_ftn) (intptr_t, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_forwardFriction_Injected_mB00ACC3CAA3B6994E07E6FD32AF88786835E815A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_forwardFriction_Injected(System.IntPtr,UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(___0__unity_self, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0_ftn) (intptr_t, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forwardFriction_Injected_mFB7251606FB9AEBA6B2691407DA0E8CEC17D83B0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forwardFriction_Injected(System.IntPtr,UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73_ftn) (intptr_t, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_sidewaysFriction_Injected_m115463FA7EB8BB2ED03141EC55FDEE029BBB2C73_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_sidewaysFriction_Injected(System.IntPtr,UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(___0__unity_self, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1 (intptr_t ___0__unity_self, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1_ftn) (intptr_t, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_sidewaysFriction_Injected_m42CC1E33775FF0A3EA7D33C44DC1F4F6E4C24DA1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_sidewaysFriction_Injected(System.IntPtr,UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F_ftn) (intptr_t);
	static WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_motorTorque_Injected_m36C99CBC1783A537160723D1530C150E2D3C075F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_motorTorque_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2_ftn) (intptr_t, float);
	static WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_motorTorque_Injected_mDC3AFD111B0711D6D7450F3FDCC18386352888C2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_motorTorque_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7 (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7_ftn) (intptr_t);
	static WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_brakeTorque_Injected_mB40FEA870D8466A725972FBDB3D2D099A9DC15F7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_brakeTorque_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2_ftn) (intptr_t, float);
	static WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_brakeTorque_Injected_mF745FF43BCCE204923ABCF9FD3F0725BFB6927E2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_brakeTorque_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714 (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714_ftn) (intptr_t);
	static WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_steerAngle_Injected_m7D5107032A83BC17496DC445774BA15076612714_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_steerAngle_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20 (intptr_t ___0__unity_self, float ___1_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20_ftn) (intptr_t, float);
	static WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_steerAngle_Injected_m1D19C28872BFFC9C692FA4F50454A3400D5ACC20_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_steerAngle_Injected(System.IntPtr,System.Single)");
	_il2cpp_icall_func(___0__unity_self, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4 (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4_ftn) (intptr_t);
	static WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_isGrounded_Injected_mB07EA10E1CCF2043B0A558ABB8544A222D82ADB4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_isGrounded_Injected(System.IntPtr)");
	bool icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F (intptr_t ___0__unity_self, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F_ftn) (intptr_t);
	static WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_rpm_Injected_m107B8A2A6939BDAF4DE942197039D58ADEAE9E7F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_rpm_Injected(System.IntPtr)");
	float icallRetVal = _il2cpp_icall_func(___0__unity_self);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE (intptr_t ___0__unity_self, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___2_quat, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE_ftn) (intptr_t, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*);
	static WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetWorldPose_Injected_m5CADCBBA43204BA3C8EF66B85A568D7A685B28AE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetWorldPose_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Quaternion&)");
	_il2cpp_icall_func(___0__unity_self, ___1_pos, ___2_quat);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB (intptr_t ___0__unity_self, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* ___1_hit, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB_ftn) (intptr_t, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*);
	static WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetGroundHit_Injected_m8297D4243D94F040DA26F6B09714046754B73AAB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetGroundHit_Injected(System.IntPtr,UnityEngine.WheelHit&)");
	bool icallRetVal = _il2cpp_icall_func(___0__unity_self, ___1_hit);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR intptr_t MarshalledUnityObject_MarshalNotNull_TisRuntimeObject_mEB1AA6B672D00242BB9DCE007056EC0E9C8DB075_gshared_inline (RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	intptr_t V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		RuntimeObject* L_0 = ___0_obj;
		NullCheck(L_0);
		intptr_t L_1 = ((Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)L_0)->___m_CachedPtr;
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		intptr_t L_2 = V_0;
		return L_2;
	}
}
