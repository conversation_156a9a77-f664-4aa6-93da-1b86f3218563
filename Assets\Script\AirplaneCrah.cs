using UnityEngine;
using System.Collections.Generic;
using System.Collections;
public class AirplaneCrash : MonoBehaviour
{
    public GameObject crashParticles;
    public AudioSource crash;
    private Rigidbody rb;
    private bool hasCrashed = false;
    public GameObject Fail,gameui;
    public static AirplaneCrash instance;
    void Start()
    {
        instance = this;
        rb = GetComponent<Rigidbody>();
        rb.isKinematic = true; // Start as kinematic to prevent physics until engine starts

    }

    void Update()
    {
        if (!hasCrashed)
        {
            
        }
    }

    
    void OnCollisionEnter(Collision collision)
    {

        if (!hasCrashed)
        {
            // Only allow crashes if the engine is started and airplane is moving
            if (AirplaneControl.instance != null && AirplaneControl.instance.CanCrash())
            {
                foreach (ContactPoint contact in collision.contacts)
                {
                    if (contact.thisCollider.CompareTag("CrashPart"))
                    {
                        Crash();
                        break;
                    }
                }
            }
        }
    }

    void Crash()
    {
        hasCrashed = true;

       
        this.enabled = false;

       
        if (crashParticles != null)
        {
            crashParticles.gameObject.SetActive(true);
            crash.Play();
            AirplaneControl.instance.moveSpeedSlider.value = 0;
            // gameui.SetActive(false);
            // StartCoroutine(E());
        }

       
        if (rb != null)
        {
            rb.isKinematic = false;
            rb.AddForce(-transform.forward * 500f, ForceMode.Impulse); 
        }
    }
    IEnumerator E()
    {
        yield return new WaitForSeconds(5);
        AudioListener.volume = 0f;
        Fail.SetActive(true);
       
    }
}
