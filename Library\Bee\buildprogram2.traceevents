{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1752047160821591, "dur": 943484, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047160823551, "dur": 86685, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047160940952, "dur": 755422, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047160942213, "dur": 651682, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047160943526, "dur": 93880, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161038193, "dur": 1162, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161042796, "dur": 137153, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161181169, "dur": 399719, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161182409, "dur": 73551, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161318682, "dur": 247752, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047161333225, "dur": 165679, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047161335935, "dur": 87584, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047161423564, "dur": 4842, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047161581791, "dur": 12103, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161709512, "dur": 4756, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161714272, "dur": 50798, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161715650, "dur": 40248, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161776242, "dur": 3049, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047161775567, "dur": 4202, "ph": "X", "name": "Write chrome-trace events", "args": {} },
