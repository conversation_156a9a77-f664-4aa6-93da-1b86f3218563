﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void DataPlayableOutput__ctor_m09D4185F0C250450BE1FF451995AC6DFDF8176DD (void);
extern void DataPlayableOutput_GetHandle_m80B8C82F9002EC765218149A007490FEBAEE07B7 (void);
extern void DataPlayableOutput_Internal_CallOnPlayerChanged_mE9D28970561BDA75DE3C3605481E417D94E1F084 (void);
extern void PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C (void);
extern void PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD (void);
extern void PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C (void);
extern void PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F (void);
extern void PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B (void);
extern void PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1 (void);
extern void PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357 (void);
extern void PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6 (void);
extern void PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842 (void);
extern void PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5 (void);
extern void PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988 (void);
extern void PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E (void);
extern void PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104 (void);
extern void PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8 (void);
extern void PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020 (void);
extern void PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE (void);
extern void PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A (void);
extern void PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896 (void);
extern void PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24 (void);
extern void PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0 (void);
extern void PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1 (void);
extern void PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7 (void);
extern void PlayableDirector_set_time_Injected_m7EDEBA105958A9CFD41C4AFFEB06937ACAA13CF8 (void);
extern void PlayableDirector_get_time_Injected_m5722262666B2F15C64B5808B075D35BC21B5A17F (void);
extern void PlayableDirector_get_duration_Injected_mC5583B608A16B5805391BEBC35A8908C958F40DC (void);
extern void PlayableDirector_Evaluate_Injected_m25F97DF7CF2A0519E7E193BD09A3D933378BDAE6 (void);
extern void PlayableDirector_PlayOnFrame_Injected_mAA9B323F9E7F6F40F72753F281DFB334331D900C (void);
extern void PlayableDirector_Play_Injected_m32655FF0EA2EB49170EB24A157537F9281F255BB (void);
extern void PlayableDirector_Stop_Injected_m78F1D94BF181780F3FAE61B3E40A2D0121F9ECBF (void);
extern void PlayableDirector_Pause_Injected_m54D6643FC6939E2E691242CAA79C828C6D5F44A7 (void);
extern void PlayableDirector_GetReferenceValue_Injected_mEB2069AE5364ACFD7D8ACEC39A0A41AFE9487B5B (void);
extern void PlayableDirector_GetGenericBinding_Injected_m9DEA68F070EBDF1DD99701F71B31E274A66B0959 (void);
extern void PlayableDirector_GetPlayState_Injected_mB4EC1173A623C1F93BBF2F07B35571FCE4BABB9D (void);
extern void PlayableDirector_GetWrapMode_Injected_m829B6C82F8AA732137040887679EAB3A5A8BBDF6 (void);
extern void PlayableDirector_GetGraphHandle_Injected_mC00F0AADEE9F9D60651B56941E2365A010F0A795 (void);
extern void PlayableDirector_Internal_GetPlayableAsset_Injected_m4B7AD1ADA45F69AE77C90254710CA4D8B4721186 (void);
extern void PlayableSystems_CombineTypeAndIndex_mEBEB1D607AD575E4529FB65639A36EC27B6AFD4B (void);
extern void PlayableSystems_Internal_CallSystemDelegate_m136EBFEF08BD32F54BD31618B0ADC065128F8172 (void);
extern void PlayableSystems__cctor_m2BFE9B62FC9C8645D7BFCECA1F01375AA78CF96D (void);
extern void PlayableSystemDelegate__ctor_m10A39FB62385BCDF62B66BD42D6D530E5E6A1327 (void);
extern void PlayableSystemDelegate_Invoke_mD76C614ECD430C10A80EEC63C48F4FF1AB028CDA (void);
extern void DataPlayableOutputList__ctor_m09A66E3E13839499FC39827D4D9D0DDFA2DD67DE (void);
extern void DataPlayableOutputList_get_Item_mA80CA406E46F4F080ABAB99E051D5E2D0AE45E50 (void);
extern void DataPlayableOutputList_get_Count_m0EC8BD2A1019FCD8FC91958C77984A0E82510563 (void);
extern void DataPlayableOutputList_GetEnumerator_m9DF35C937E6EA512473E9325907F67F7193F44E3 (void);
extern void DataPlayableOutputList_System_Collections_IEnumerable_GetEnumerator_mAC992F70F1C916ED4E6C25D1E11E131E74E300CA (void);
extern void DataPlayableOutputEnumerator__ctor_mF4F8AA9EA2C8DFCA012153075C455776E4DCACA9 (void);
extern void DataPlayableOutputEnumerator_get_Current_m5D0C5B0FB965DE063CF30355005A469AF5CA662E (void);
extern void DataPlayableOutputEnumerator_System_Collections_IEnumerator_get_Current_mB75452FA96BDDBB1F1C97FA5BC794110636F4837 (void);
extern void DataPlayableOutputEnumerator_Dispose_m5BD2B7D3897D0EBF12FAE16E334B3951BAD4C8A0 (void);
extern void DataPlayableOutputEnumerator_MoveNext_m79AF7F2D9D94412B14EE28BA574B598B61B382D9 (void);
extern void DataPlayableOutputEnumerator_Reset_mD8CE079A62EAB2C1D0A3D4CC780C7748D47247FF (void);
static Il2CppMethodPointer s_methodPointers[57] = 
{
	DataPlayableOutput__ctor_m09D4185F0C250450BE1FF451995AC6DFDF8176DD,
	DataPlayableOutput_GetHandle_m80B8C82F9002EC765218149A007490FEBAEE07B7,
	DataPlayableOutput_Internal_CallOnPlayerChanged_mE9D28970561BDA75DE3C3605481E417D94E1F084,
	NULL,
	NULL,
	PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C,
	PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD,
	PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C,
	PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F,
	PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B,
	PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1,
	PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357,
	PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6,
	PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842,
	PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5,
	PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988,
	PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E,
	PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104,
	PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8,
	PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020,
	PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE,
	PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A,
	PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896,
	PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24,
	PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0,
	PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1,
	PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7,
	PlayableDirector_set_time_Injected_m7EDEBA105958A9CFD41C4AFFEB06937ACAA13CF8,
	PlayableDirector_get_time_Injected_m5722262666B2F15C64B5808B075D35BC21B5A17F,
	PlayableDirector_get_duration_Injected_mC5583B608A16B5805391BEBC35A8908C958F40DC,
	PlayableDirector_Evaluate_Injected_m25F97DF7CF2A0519E7E193BD09A3D933378BDAE6,
	PlayableDirector_PlayOnFrame_Injected_mAA9B323F9E7F6F40F72753F281DFB334331D900C,
	PlayableDirector_Play_Injected_m32655FF0EA2EB49170EB24A157537F9281F255BB,
	PlayableDirector_Stop_Injected_m78F1D94BF181780F3FAE61B3E40A2D0121F9ECBF,
	PlayableDirector_Pause_Injected_m54D6643FC6939E2E691242CAA79C828C6D5F44A7,
	PlayableDirector_GetReferenceValue_Injected_mEB2069AE5364ACFD7D8ACEC39A0A41AFE9487B5B,
	PlayableDirector_GetGenericBinding_Injected_m9DEA68F070EBDF1DD99701F71B31E274A66B0959,
	PlayableDirector_GetPlayState_Injected_mB4EC1173A623C1F93BBF2F07B35571FCE4BABB9D,
	PlayableDirector_GetWrapMode_Injected_m829B6C82F8AA732137040887679EAB3A5A8BBDF6,
	PlayableDirector_GetGraphHandle_Injected_mC00F0AADEE9F9D60651B56941E2365A010F0A795,
	PlayableDirector_Internal_GetPlayableAsset_Injected_m4B7AD1ADA45F69AE77C90254710CA4D8B4721186,
	PlayableSystems_CombineTypeAndIndex_mEBEB1D607AD575E4529FB65639A36EC27B6AFD4B,
	PlayableSystems_Internal_CallSystemDelegate_m136EBFEF08BD32F54BD31618B0ADC065128F8172,
	PlayableSystems__cctor_m2BFE9B62FC9C8645D7BFCECA1F01375AA78CF96D,
	PlayableSystemDelegate__ctor_m10A39FB62385BCDF62B66BD42D6D530E5E6A1327,
	PlayableSystemDelegate_Invoke_mD76C614ECD430C10A80EEC63C48F4FF1AB028CDA,
	DataPlayableOutputList__ctor_m09A66E3E13839499FC39827D4D9D0DDFA2DD67DE,
	DataPlayableOutputList_get_Item_mA80CA406E46F4F080ABAB99E051D5E2D0AE45E50,
	DataPlayableOutputList_get_Count_m0EC8BD2A1019FCD8FC91958C77984A0E82510563,
	DataPlayableOutputList_GetEnumerator_m9DF35C937E6EA512473E9325907F67F7193F44E3,
	DataPlayableOutputList_System_Collections_IEnumerable_GetEnumerator_mAC992F70F1C916ED4E6C25D1E11E131E74E300CA,
	DataPlayableOutputEnumerator__ctor_mF4F8AA9EA2C8DFCA012153075C455776E4DCACA9,
	DataPlayableOutputEnumerator_get_Current_m5D0C5B0FB965DE063CF30355005A469AF5CA662E,
	DataPlayableOutputEnumerator_System_Collections_IEnumerator_get_Current_mB75452FA96BDDBB1F1C97FA5BC794110636F4837,
	DataPlayableOutputEnumerator_Dispose_m5BD2B7D3897D0EBF12FAE16E334B3951BAD4C8A0,
	DataPlayableOutputEnumerator_MoveNext_m79AF7F2D9D94412B14EE28BA574B598B61B382D9,
	DataPlayableOutputEnumerator_Reset_mD8CE079A62EAB2C1D0A3D4CC780C7748D47247FF,
};
extern void DataPlayableOutput__ctor_m09D4185F0C250450BE1FF451995AC6DFDF8176DD_AdjustorThunk (void);
extern void DataPlayableOutput_GetHandle_m80B8C82F9002EC765218149A007490FEBAEE07B7_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000001, DataPlayableOutput__ctor_m09D4185F0C250450BE1FF451995AC6DFDF8176DD_AdjustorThunk },
	{ 0x06000002, DataPlayableOutput_GetHandle_m80B8C82F9002EC765218149A007490FEBAEE07B7_AdjustorThunk },
};
static const int32_t s_InvokerIndices[57] = 
{
	8643,
	10716,
	13712,
	-1,
	-1,
	10637,
	10637,
	10698,
	10713,
	8521,
	8504,
	10571,
	10571,
	10870,
	8521,
	10870,
	10870,
	10870,
	3539,
	7489,
	10637,
	10637,
	10713,
	10698,
	10870,
	10870,
	10870,
	14710,
	15512,
	15512,
	15981,
	14708,
	15981,
	15981,
	15981,
	13220,
	14246,
	15581,
	15581,
	14708,
	15625,
	14189,
	12202,
	16420,
	4630,
	8627,
	3783,
	6544,
	10637,
	10698,
	10698,
	8627,
	10562,
	10698,
	10870,
	10537,
	10870,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule = 
{
	"UnityEngine.DirectorModule.dll",
	57,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
