{"DataTable": {"attribute_marked_count_always_link_assembly": 1, "attribute_swept_count_always_link_assembly": 0, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 116, "attribute_total_count_preserve": 80, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 233, "attribute_swept_count_required_member": 44, "attribute_total_count_required_member": 277, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 635, "attribute_total_count_require_attribute_usages": 2, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 108, "assembly_counts_link": 53, "assembly_counts_copy": 9, "assembly_counts_delete": 46, "assembly_counts_total_out": 62, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 369, "unrecognized_reflection_access_core_count": 174, "unrecognized_reflection_access_unity_count": 185, "unrecognized_reflection_access_user_count": 10, "recognized_reflection_access_total_count": 47, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 18, "recognized_reflection_access_user_count": 0, "link_xml_total_count": 11, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 10, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 0, "engine_module_deleted": 0, "engine_module_total_out": 0, "option_rule_set": "Conservative", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": false, "option_unity_root_strategy": "UltraConservative", "option_enable_ildump": false}}