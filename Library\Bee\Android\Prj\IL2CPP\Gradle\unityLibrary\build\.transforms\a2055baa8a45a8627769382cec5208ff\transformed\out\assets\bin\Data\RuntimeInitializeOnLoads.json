{"root": [{"assemblyName": "Unity.Cinemachine", "nameSpace": "", "className": "DeltaTimeScaleProcessor", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "CameraUpdateManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "Damper/AverageFrameRateTracker", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Cinemachine", "nameSpace": "Unity.Cinemachine", "className": "CinemachinePostProcessing", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1652832624114795843", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem.ForUI", "nameSpace": "UnityEngine.InputSystem.Plugins.InputForUI", "className": "InputSystemProvider", "methodName": "Bootstrap", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Rendering.LightTransport.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__16164947281921951637", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__15867191014387474753", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}