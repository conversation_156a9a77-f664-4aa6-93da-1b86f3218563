apply plugin: 'com.android.application'
apply from: 'setupSymbols.gradle'
apply from: '../shared/keepUnitySymbols.gradle'

dependencies {
    implementation project(':unityLibrary')
}

android {
    namespace "com.atbfig.firecopter"
    ndkPath "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK"
    ndkVersion "23.1.7779620"
    compileSdk 36
    buildToolsVersion = "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId "com.atbfig.firecopter"
        versionName "1.5"
        minSdk 23
        targetSdk 36
        versionCode 3

        ndk {
            abiFilters "arm64-v8a"
            debugSymbolLevel "none"
        }
    }

    lint {
        abortOnError false
    }

    androidResources {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
    }

    packaging {
        jniLibs {
            useLegacyPackaging true
        }
    }

    buildTypes {
        debug {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            jniDebuggable = true
            signingConfig signingConfigs.debug
        }

        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
        }
    }

    bundle {
        language {
            enableSplit = false
        }

        density {
            enableSplit = false
        }

        abi {
            enableSplit = true
        }

        texture {
            enableSplit = true
        }
    }
}