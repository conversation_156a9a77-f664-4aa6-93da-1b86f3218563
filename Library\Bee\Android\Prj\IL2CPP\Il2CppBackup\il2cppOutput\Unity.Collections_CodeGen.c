﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404 (void);
extern void IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C (void);
extern void EarlyInitHelpers__cctor_m19122BBB2CF7BC74606113B23BF74FA959A5D467 (void);
extern void EarlyInitHelpers_FlushEarlyInits_m2B9C35967B87AF2BD2018C649BF964DFD5C40033 (void);
extern void EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC (void);
extern void EarlyInitFunction__ctor_m74E4D36634E32C8FE21AA86C7D9597F7FD77E0B6 (void);
extern void EarlyInitFunction_Invoke_mA2104BB505B5D3F16622D30BDA3AF83F3FE2FB2D (void);
extern void DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5 (void);
extern void AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025 (void);
extern void AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61 (void);
extern void AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798 (void);
extern void AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5 (void);
extern void AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519 (void);
extern void AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF (void);
extern void AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79 (void);
extern void AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9 (void);
extern void AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1 (void);
extern void TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53 (void);
extern void TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2 (void);
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63 (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1 (void);
extern void AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9 (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2 (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3 (void);
extern void AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A (void);
extern void AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939 (void);
extern void AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243 (void);
extern void AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4 (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7 (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08 (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633 (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225 (void);
extern void TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1 (void);
extern void Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA (void);
extern void CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465 (void);
extern void CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1 (void);
extern void DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085 (void);
extern void GenerateTestsForBurstCompatibilityAttribute_set_GenericTypeArguments_mF1072BD236646D4159082D3BB685B365EA45C01A (void);
extern void GenerateTestsForBurstCompatibilityAttribute__ctor_m86CEFB7F89EBCA8FECA15EC6F21CC9DFCDCDA235 (void);
extern void ExcludeFromBurstCompatTestingAttribute_set_Reason_m54DAB86449D0D2B47E1521F71AE433D1EC2598E5 (void);
extern void ExcludeFromBurstCompatTestingAttribute__ctor_mE85EC7FAEC0AF711D75010FE42AD803D9442806D (void);
extern void Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5 (void);
extern void Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1 (void);
extern void Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126 (void);
extern void Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82 (void);
extern void Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380 (void);
extern void NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA (void);
extern void NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780 (void);
extern void NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0 (void);
extern void NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792 (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223 (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9 (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81 (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5 (void);
extern void NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE (void);
extern void NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4 (void);
extern void NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64 (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92 (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9 (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1 (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922 (void);
extern void NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692 (void);
extern void NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77 (void);
extern void NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52 (void);
extern void NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972 (void);
extern void Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D (void);
extern void Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD (void);
extern void Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A (void);
extern void Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827 (void);
extern void Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E (void);
extern void Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD (void);
extern void Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7 (void);
extern void Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729 (void);
extern void UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816 (void);
extern void UnsafeQueueData_DeallocateQueue_mF4E2184511650FED8D9B85ECDB14B6E43AB06373 (void);
extern void UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502 (void);
extern void UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E (void);
extern void UnsafeBitArray_Free_m593DC0AC7ADB85F86BF8E27CDCA5EF1395650D0D (void);
extern void UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1 (void);
extern void UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2 (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC (void);
extern void UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3 (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1 (void);
extern void UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464 (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065 (void);
extern void UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50 (void);
extern void UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439 (void);
extern void UnsafeTextExtensions_AsUnsafeListOfBytesRO_mB4E57457A3442858265E8A664CCBBC8E0A4C6AF4 (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1 (void);
extern void UnsafeText_Free_m22C162680EFB31663020E8FE94BE67596994A98B (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40 (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2 (void);
extern void __JobReflectionRegistrationOutput__1652832624114795843_CreateJobReflectionData_m6F06A9348EFD949AABE2DB3703E98ECE169E5C2A (void);
extern void __JobReflectionRegistrationOutput__1652832624114795843_EarlyInit_m464D1467C681AD66A33B32BF5ECE7CF7EFD21C95 (void);
extern void U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82 (void);
static Il2CppMethodPointer s_methodPointers[175] = 
{
	EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404,
	IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C,
	EarlyInitHelpers__cctor_m19122BBB2CF7BC74606113B23BF74FA959A5D467,
	EarlyInitHelpers_FlushEarlyInits_m2B9C35967B87AF2BD2018C649BF964DFD5C40033,
	EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC,
	EarlyInitFunction__ctor_m74E4D36634E32C8FE21AA86C7D9597F7FD77E0B6,
	EarlyInitFunction_Invoke_mA2104BB505B5D3F16622D30BDA3AF83F3FE2FB2D,
	DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025,
	NULL,
	AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61,
	AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798,
	AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5,
	AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519,
	AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF,
	AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79,
	AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9,
	AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1,
	TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53,
	TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2,
	AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63,
	AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1,
	AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D,
	AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F,
	AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9,
	AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2,
	AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF,
	AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3,
	AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A,
	AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939,
	AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243,
	AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4,
	Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A,
	Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7,
	Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08,
	Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633,
	Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F,
	Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED,
	Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225,
	NULL,
	NULL,
	NULL,
	NULL,
	TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1,
	Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA,
	CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465,
	CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1,
	DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085,
	NULL,
	NULL,
	GenerateTestsForBurstCompatibilityAttribute_set_GenericTypeArguments_mF1072BD236646D4159082D3BB685B365EA45C01A,
	GenerateTestsForBurstCompatibilityAttribute__ctor_m86CEFB7F89EBCA8FECA15EC6F21CC9DFCDCDA235,
	ExcludeFromBurstCompatTestingAttribute_set_Reason_m54DAB86449D0D2B47E1521F71AE433D1EC2598E5,
	ExcludeFromBurstCompatTestingAttribute__ctor_mE85EC7FAEC0AF711D75010FE42AD803D9442806D,
	Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5,
	Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1,
	NULL,
	Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126,
	Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82,
	Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380,
	NULL,
	NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA,
	NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780,
	NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0,
	NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223,
	NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E,
	NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D,
	NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9,
	NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81,
	NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5,
	NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE,
	NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4,
	NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64,
	NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92,
	NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9,
	ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1,
	ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922,
	NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692,
	NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77,
	NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52,
	NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972,
	Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D,
	Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD,
	Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A,
	Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827,
	Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E,
	Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD,
	Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7,
	Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729,
	UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816,
	UnsafeQueueData_DeallocateQueue_mF4E2184511650FED8D9B85ECDB14B6E43AB06373,
	NULL,
	NULL,
	NULL,
	UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502,
	UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E,
	NULL,
	UnsafeBitArray_Free_m593DC0AC7ADB85F86BF8E27CDCA5EF1395650D0D,
	UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1,
	UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F,
	UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3,
	UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC,
	UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1,
	NULL,
	NULL,
	NULL,
	UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464,
	UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065,
	UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F,
	UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C,
	UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C,
	DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E,
	ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB,
	ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50,
	UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439,
	UnsafeTextExtensions_AsUnsafeListOfBytesRO_mB4E57457A3442858265E8A664CCBBC8E0A4C6AF4,
	UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1,
	UnsafeText_Free_m22C162680EFB31663020E8FE94BE67596994A98B,
	UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC,
	UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C,
	UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40,
	UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2,
	__JobReflectionRegistrationOutput__1652832624114795843_CreateJobReflectionData_m6F06A9348EFD949AABE2DB3703E98ECE169E5C2A,
	__JobReflectionRegistrationOutput__1652832624114795843_EarlyInit_m464D1467C681AD66A33B32BF5ECE7CF7EFD21C95,
	U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82,
};
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk (void);
extern void AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A_AdjustorThunk (void);
extern void AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939_AdjustorThunk (void);
extern void AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243_AdjustorThunk (void);
extern void AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4_AdjustorThunk (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk (void);
extern void DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085_AdjustorThunk (void);
extern void NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA_AdjustorThunk (void);
extern void NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780_AdjustorThunk (void);
extern void NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0_AdjustorThunk (void);
extern void NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792_AdjustorThunk (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk (void);
extern void NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE_AdjustorThunk (void);
extern void NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4_AdjustorThunk (void);
extern void NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64_AdjustorThunk (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk (void);
extern void NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692_AdjustorThunk (void);
extern void NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77_AdjustorThunk (void);
extern void NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52_AdjustorThunk (void);
extern void NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972_AdjustorThunk (void);
extern void Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7_AdjustorThunk (void);
extern void Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729_AdjustorThunk (void);
extern void UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816_AdjustorThunk (void);
extern void UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502_AdjustorThunk (void);
extern void UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E_AdjustorThunk (void);
extern void UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1_AdjustorThunk (void);
extern void UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2_AdjustorThunk (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk (void);
extern void UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464_AdjustorThunk (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk (void);
extern void UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F_AdjustorThunk (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[64] = 
{
	{ 0x0600001B, AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk },
	{ 0x0600001C, AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk },
	{ 0x0600001E, AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk },
	{ 0x0600001F, AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk },
	{ 0x06000020, AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk },
	{ 0x06000021, AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk },
	{ 0x06000022, AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk },
	{ 0x06000023, AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A_AdjustorThunk },
	{ 0x06000024, AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939_AdjustorThunk },
	{ 0x06000025, AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243_AdjustorThunk },
	{ 0x06000026, AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4_AdjustorThunk },
	{ 0x06000027, Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk },
	{ 0x06000028, Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk },
	{ 0x06000029, Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk },
	{ 0x0600002A, Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk },
	{ 0x0600002B, Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk },
	{ 0x0600002C, Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk },
	{ 0x0600002D, Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk },
	{ 0x06000036, DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085_AdjustorThunk },
	{ 0x06000044, NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA_AdjustorThunk },
	{ 0x06000045, NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780_AdjustorThunk },
	{ 0x06000046, NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0_AdjustorThunk },
	{ 0x06000047, NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792_AdjustorThunk },
	{ 0x06000058, NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk },
	{ 0x06000059, NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk },
	{ 0x0600005A, NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk },
	{ 0x0600005B, NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk },
	{ 0x0600005C, NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk },
	{ 0x0600005D, NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk },
	{ 0x0600005E, NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE_AdjustorThunk },
	{ 0x0600005F, NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4_AdjustorThunk },
	{ 0x06000060, NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64_AdjustorThunk },
	{ 0x06000061, NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk },
	{ 0x06000062, NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk },
	{ 0x06000063, ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk },
	{ 0x06000064, ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk },
	{ 0x06000065, NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692_AdjustorThunk },
	{ 0x06000066, NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77_AdjustorThunk },
	{ 0x06000067, NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52_AdjustorThunk },
	{ 0x06000068, NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972_AdjustorThunk },
	{ 0x0600006F, Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7_AdjustorThunk },
	{ 0x06000070, Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729_AdjustorThunk },
	{ 0x06000071, UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816_AdjustorThunk },
	{ 0x06000076, UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502_AdjustorThunk },
	{ 0x06000077, UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E_AdjustorThunk },
	{ 0x0600007A, UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1_AdjustorThunk },
	{ 0x0600007B, UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2_AdjustorThunk },
	{ 0x06000083, UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk },
	{ 0x06000097, UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk },
	{ 0x06000098, UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk },
	{ 0x06000099, UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk },
	{ 0x0600009D, UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464_AdjustorThunk },
	{ 0x0600009E, UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk },
	{ 0x0600009F, UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F_AdjustorThunk },
	{ 0x060000A0, UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk },
	{ 0x060000A1, UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk },
	{ 0x060000A2, DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk },
	{ 0x060000A3, ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk },
	{ 0x060000A4, ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk },
	{ 0x060000A7, UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk },
	{ 0x060000A9, UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk },
	{ 0x060000AA, UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk },
	{ 0x060000AB, UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk },
	{ 0x060000AC, UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[175] = 
{
	10870,
	10870,
	16420,
	16420,
	15983,
	4630,
	10870,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	14860,
	-1,
	15971,
	16291,
	15568,
	14672,
	15596,
	15568,
	15568,
	16420,
	4630,
	3202,
	10515,
	10870,
	16025,
	10637,
	6934,
	10884,
	10637,
	10870,
	6166,
	6350,
	10637,
	7202,
	10870,
	10638,
	10638,
	10637,
	8568,
	10870,
	10637,
	-1,
	-1,
	-1,
	-1,
	16420,
	16420,
	15470,
	15579,
	10870,
	-1,
	-1,
	8627,
	10870,
	8627,
	8627,
	12983,
	14686,
	-1,
	15470,
	11335,
	11335,
	-1,
	10870,
	10870,
	10870,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	10537,
	10870,
	8568,
	10870,
	10870,
	10870,
	10870,
	10870,
	10870,
	15447,
	15441,
	16445,
	12257,
	12261,
	11581,
	6166,
	10637,
	8445,
	13505,
	-1,
	-1,
	-1,
	10870,
	10870,
	-1,
	14686,
	10537,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	14686,
	10870,
	10870,
	10870,
	-1,
	-1,
	-1,
	8445,
	8568,
	10537,
	10870,
	10870,
	10870,
	10870,
	10870,
	15408,
	15355,
	10537,
	15971,
	10870,
	10515,
	10637,
	10698,
	16420,
	16420,
	16420,
};
static const Il2CppTokenRangePair s_rgctxIndices[22] = 
{
	{ 0x02000011, { 23, 6 } },
	{ 0x02000025, { 38, 22 } },
	{ 0x0200003C, { 62, 6 } },
	{ 0x02000042, { 72, 6 } },
	{ 0x02000043, { 78, 8 } },
	{ 0x02000047, { 86, 17 } },
	{ 0x0200004D, { 112, 8 } },
	{ 0x06000009, { 0, 4 } },
	{ 0x0600000A, { 4, 2 } },
	{ 0x0600000B, { 6, 5 } },
	{ 0x0600000C, { 11, 3 } },
	{ 0x0600000D, { 14, 2 } },
	{ 0x0600000E, { 16, 5 } },
	{ 0x06000010, { 21, 2 } },
	{ 0x06000037, { 29, 4 } },
	{ 0x0600003F, { 33, 2 } },
	{ 0x06000043, { 35, 3 } },
	{ 0x0600004A, { 60, 2 } },
	{ 0x06000078, { 68, 4 } },
	{ 0x0600008A, { 103, 4 } },
	{ 0x06000090, { 107, 3 } },
	{ 0x06000091, { 110, 2 } },
};
extern const uint32_t g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F;
extern const uint32_t g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204;
extern const uint32_t g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2;
extern const uint32_t g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29;
extern const uint32_t g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B;
extern const uint32_t g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9;
extern const uint32_t g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C;
extern const uint32_t g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617;
extern const uint32_t g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB;
extern const uint32_t g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF;
extern const uint32_t g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792;
extern const uint32_t g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554;
extern const uint32_t g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7;
extern const uint32_t g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92;
extern const uint32_t g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17;
extern const uint32_t g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC;
extern const uint32_t g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046;
extern const uint32_t g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const uint32_t g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A;
extern const uint32_t g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m05DBC7A44FF9DAD310466511144CC0D0092FF1A2;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076;
extern const uint32_t g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A;
extern const uint32_t g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA;
extern const uint32_t g_rgctx_UnsafeList_1_set_Item_m55F6D1F1AE6B627E9916AC7867161D4DFA6EB267;
extern const uint32_t g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976;
extern const uint32_t g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7;
extern const uint32_t g_rgctx_UnsafeList_1_Add_m31E9C9CB6476E5AF889F68A699B2DE541626AE2D;
extern const uint32_t g_rgctx_NativeList_1_get_IsCreated_m28034692FB3A35195BDD71F815562D61130D7939;
extern const uint32_t g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475;
extern const uint32_t g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0;
extern const uint32_t g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E;
extern const uint32_t g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296;
extern const uint32_t g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384;
extern const uint32_t g_rgctx_UU26_t6E44BF865BC62898D5ECAB2D870625781521B69F;
extern const uint32_t g_rgctx_UnsafeList_1_Create_TisU_tAB741574063FA4E9A22A701208EE0217BD0FB7B3_m96E02C6605049092E9322B3F5F499BBF5CA2B0B3;
extern const uint32_t g_rgctx_UnsafeQueue_1U2A_t346ECD7AA60CC0B712CC0A6D62F3FA7B12044DD8;
extern const uint32_t g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731;
extern const uint32_t g_rgctx_UnsafeQueue_1_Dispose_m6813829F54FEC31082580562A198BD12F944DC98;
extern const uint32_t g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731;
extern const uint32_t g_rgctx_Unmanaged_Free_TisUnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731_mD105604A2B26407DDF1C5192C10EC9E2AFD454EB;
extern const uint32_t g_rgctx_UnsafeQueue_1_get_IsCreated_mA1F8A614AB0FAE94D15FB9EFCB2167F4A49A8FFD;
extern const uint32_t g_rgctx_NativeList_1_t3CFDFD36F88F4E43D16922BA56BBB626C58B3903;
extern const uint32_t g_rgctx_UnsafeList_1U2A_t022EF6FDEDC54758BCB59D2D4493D0CB979F6674;
extern const uint32_t g_rgctx_UnsafeList_1_t1C599235926FCF8AA5834FD6698E6F945ABDE64E;
extern const uint32_t g_rgctx_TU2A_tA3BDF0C5372274913606981B68BFFB39E7ACE3FC;
extern const uint32_t g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E;
extern const uint32_t g_rgctx_TKeyU2A_tBC54584B7218EA60CC14F8E09B72CCBC0B29AAFE;
extern const uint32_t g_rgctx_HashMapHelper_1U2A_t5CEBA338D2DAFE89C0ACC9FC00EC7B9A41350094;
extern const uint32_t g_rgctx_HashMapHelper_1_Dispose_m8809E5F1A5B7E17F903B5210658F6DF0D2E1F803;
extern const uint32_t g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E;
extern const uint32_t g_rgctx_Unmanaged_Free_TisHashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E_mC24A99CF170BD9350FA0109D2AF3F098B4F0492A;
extern const uint32_t g_rgctx_UnsafeHashMap_2_get_IsCreated_m5151D7A4A961C290E40795AB4FFDE861B4FCC80D;
extern const uint32_t g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68;
extern const uint32_t g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68;
extern const uint32_t g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96;
extern const uint32_t g_rgctx_HashMapHelper_1_Dispose_m24274E013E518996265A47C2D3DA864B36B89DD6;
extern const uint32_t g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96;
extern const uint32_t g_rgctx_HashMapHelper_1_get_IsCreated_m530BA624A88470DC9FD059C81F9F68E8964C486E;
extern const uint32_t g_rgctx_IEnumerator_1_tD3B69D84966D4D8CBF6548C3A689A1080AEA5614;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E;
extern const uint32_t g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1;
extern const uint32_t g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0;
extern const uint32_t g_rgctx_UnsafeList_1__ctor_mB4DC9C7FDDD8459A5985E14F365473C199A3B3E6;
extern const uint32_t g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7;
extern const uint32_t g_rgctx_UnsafeList_1_get_IsCreated_mC1CC3BDA221944B65278B4F18CE0CB07BF618466;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301;
extern const uint32_t g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939;
extern const uint32_t g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D;
extern const uint32_t g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_UU26_t5E4DD5532E4E2D16A863020640E69C9DAAE252E6;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_m4C9D55555D8D78FB343F93C6FC881B5522D5A662;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3F329AE97A7BCCAF3600F13135080670DEBCABC9;
extern const uint32_t g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73;
extern const uint32_t g_rgctx_UnsafeList_1_ResizeExact_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m4BE8398AA411662D1BF1D2E76EC3558FEB20ECC0;
extern const uint32_t g_rgctx_UnsafeRingQueue_1U2A_t3F23854F9AB67CB5D4FD97923F4BDBC13D8C68A6;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_Dispose_m99EAC3F731930FB75E82E61247A3A6C275889183;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144;
extern const uint32_t g_rgctx_Unmanaged_Free_TisUnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144_m282CDB764993A89C3A1BB6F5EC72DC6AA5CD29C7;
extern const uint32_t g_rgctx_TU2A_t3AA0F4F01B5C2973240CAD0082C9C9F29DCF1A2E;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_get_IsCreated_m6B65E1CD42C387030AF533C0903162F4D2B6BA4A;
extern const uint32_t g_rgctx_Unmanaged_Free_TisT_t6BFCBF80FC8103569C292690B1ABCFD575907821_m17C79CF9056428C2824756091922F227A9495990;
static const Il2CppRGCTXDefinition s_rgctxValues[120] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m05DBC7A44FF9DAD310466511144CC0D0092FF1A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_set_Item_m55F6D1F1AE6B627E9916AC7867161D4DFA6EB267 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Add_m31E9C9CB6476E5AF889F68A699B2DE541626AE2D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_get_IsCreated_m28034692FB3A35195BDD71F815562D61130D7939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t6E44BF865BC62898D5ECAB2D870625781521B69F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Create_TisU_tAB741574063FA4E9A22A701208EE0217BD0FB7B3_m96E02C6605049092E9322B3F5F499BBF5CA2B0B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1U2A_t346ECD7AA60CC0B712CC0A6D62F3FA7B12044DD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeQueue_1_Dispose_m6813829F54FEC31082580562A198BD12F944DC98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisUnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731_mD105604A2B26407DDF1C5192C10EC9E2AFD454EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeQueue_1_get_IsCreated_mA1F8A614AB0FAE94D15FB9EFCB2167F4A49A8FFD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_t3CFDFD36F88F4E43D16922BA56BBB626C58B3903 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_t022EF6FDEDC54758BCB59D2D4493D0CB979F6674 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t1C599235926FCF8AA5834FD6698E6F945ABDE64E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA3BDF0C5372274913606981B68BFFB39E7ACE3FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKeyU2A_tBC54584B7218EA60CC14F8E09B72CCBC0B29AAFE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1U2A_t5CEBA338D2DAFE89C0ACC9FC00EC7B9A41350094 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_Dispose_m8809E5F1A5B7E17F903B5210658F6DF0D2E1F803 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisHashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E_mC24A99CF170BD9350FA0109D2AF3F098B4F0492A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeHashMap_2_get_IsCreated_m5151D7A4A961C290E40795AB4FFDE861B4FCC80D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_Dispose_m24274E013E518996265A47C2D3DA864B36B89DD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_get_IsCreated_m530BA624A88470DC9FD059C81F9F68E8964C486E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tD3B69D84966D4D8CBF6548C3A689A1080AEA5614 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1__ctor_mB4DC9C7FDDD8459A5985E14F365473C199A3B3E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_IsCreated_mC1CC3BDA221944B65278B4F18CE0CB07BF618466 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t5E4DD5532E4E2D16A863020640E69C9DAAE252E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_m4C9D55555D8D78FB343F93C6FC881B5522D5A662 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3F329AE97A7BCCAF3600F13135080670DEBCABC9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ResizeExact_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m4BE8398AA411662D1BF1D2E76EC3558FEB20ECC0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1U2A_t3F23854F9AB67CB5D4FD97923F4BDBC13D8C68A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeRingQueue_1_Dispose_m99EAC3F731930FB75E82E61247A3A6C275889183 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisUnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144_m282CDB764993A89C3A1BB6F5EC72DC6AA5CD29C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t3AA0F4F01B5C2973240CAD0082C9C9F29DCF1A2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeRingQueue_1_get_IsCreated_m6B65E1CD42C387030AF533C0903162F4D2B6BA4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisT_t6BFCBF80FC8103569C292690B1ABCFD575907821_m17C79CF9056428C2824756091922F227A9495990 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule = 
{
	"Unity.Collections.dll",
	175,
	s_methodPointers,
	64,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	22,
	s_rgctxIndices,
	120,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
