<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs"><file name="arm64-v8a/libil2cpp.so" path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libil2cpp.so"/><file name="arm64-v8a/libmain.so" path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libmain.so"/><file name="arm64-v8a/libunity.so" path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libunity.so"/><file name="arm64-v8a/lib_burst_generated.so" path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\lib_burst_generated.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\release\jniLibs"/></dataSet></merger>