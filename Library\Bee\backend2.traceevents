{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752056061252377, "dur":63, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061252502, "dur":139528, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061392037, "dur":2864, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061395146, "dur":259, "ph":"X", "name": "<PERSON><PERSON>",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1752056061395405, "dur":1300, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061396827, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":0, "ts":1752056061396939, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":0, "ts":1752056061397239, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"UnityLinker D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":0, "ts":1752056061397494, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061397814, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061397934, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061398001, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_pcel.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061398093, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061398329, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061398501, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061398760, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061398891, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061398988, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061399046, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061399153, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061399217, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061399362, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061399686, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061399923, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AccessibilityModule-FeaturesChecked.txt_ttts.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061400011, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061400158, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061400925, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061401001, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061401148, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061401457, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_vjsc.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061401567, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ParticleSystemModule-FeaturesChecked.txt_kapj.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061401786, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061401876, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PropertiesModule-FeaturesChecked.txt_m9z5.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061402172, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061402676, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061403192, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061403400, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061403536, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061403777, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752056061403949, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info" }}
,{ "pid":12345, "tid":0, "ts":1752056061404956, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zals4mmypley.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061405155, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061405220, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061405388, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061405660, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061405984, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061406041, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061406828, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061406897, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u401qaj1n1mo.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061407084, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nbxnh3djstad.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061407308, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g2blxqh88x7t.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061407509, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/oiquu7a5omm5.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061407709, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nvfz6xmq45j5.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061408134, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061408353, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061408554, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061408806, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061408881, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061409113, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7mxwfh15kra1.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061409267, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061409383, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061409904, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061410010, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061410178, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061410658, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eqopvfazu5ej.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061410901, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061411065, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ww4ib49dv6tl.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061411409, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgalojc4k9cf.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061411711, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7yuwsct9xqc.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061411789, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061412328, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gomkx7xyft0v.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061412478, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061412638, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061412901, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413129, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413331, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413400, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/btc00jpnpfwv.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413626, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413746, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413833, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/608olej0nq3l.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061413986, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061414118, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrt9ubex4zyy.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061414405, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pf89h968la8d.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061414511, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b3veiy14s00c.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061414722, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061414820, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ac7tbdjsuouj.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415010, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415112, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415212, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415449, "dur":120, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f69jcmtdltpb.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415586, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gy0yzb544nxj.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415660, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v2ltsjojhjw6.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415759, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/giph8s2gk77p.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415834, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hq9xs8ifo9o5.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061415894, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25cm8hxcrx0o.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416008, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nlgaorbaulc8.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416091, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot0q86r8xntn.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416161, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/983eog650b56.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416231, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uo723pk33zgq.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416381, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mlw3ex27neg0.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416446, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/70zru6g5q2rj.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416537, "dur":101, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tonwuvb1xski.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416653, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uw7cfbsz1u1x.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416725, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fcb1uk6w4idh.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416817, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0repauku76o0.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416906, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tg0fx1n1w1ut.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061416975, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2n3g8s5g95on.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061417121, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061417338, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061417569, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m7tsc9fssqjw.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061417750, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxnazyrk9929.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061417945, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418032, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sqf1sldz1ha.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418090, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418176, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pgj75bd160pu.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418454, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418550, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/91501rpe8hhy.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418613, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418701, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418921, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061418980, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061419148, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zwfl6jeaz8l8.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061419614, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061419954, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yo7r87bsn5oh.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061420074, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061420136, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxs8b7zbmatr.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061420430, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061422067, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061422162, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6386mtmlure2.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061423019, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":0, "ts":1752056061426228, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427103, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__103.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427163, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__104.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427234, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__105.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427367, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__108.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427644, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__13.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061427872, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__17.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061428139, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061428381, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__27.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061428648, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__31.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061428853, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__35.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061429285, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061429906, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__57.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061430723, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__73.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061430972, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__78.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061431183, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__81.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061431354, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__85.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061431631, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__90.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061431848, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__94.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061432308, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061432477, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061432564, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061432849, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061433651, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__13.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061433789, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__16.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061434091, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061434341, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061435645, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061435994, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061436111, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061438059, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061438365, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061438626, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061438954, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061439052, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061439468, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061439742, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061440003, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061440117, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061440204, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061440477, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061440712, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061440921, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061441166, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061441422, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061441832, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061441902, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061442467, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VehiclesModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752056061442711, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752056061443103, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061443374, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Mono.Security.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061443633, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061443888, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061443961, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061444234, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061444528, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061444768, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061445016, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061445293, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AssetBundleModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061445536, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061447318, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752056061447706, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061447793, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061448193, "dur":140, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VehiclesModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061448429, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VideoModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752056061448717, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json" }}
,{ "pid":12345, "tid":0, "ts":1752056061449951, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":0, "ts":1752056061396744, "dur":53473, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061450230, "dur":216535, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061666768, "dur":989, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061667825, "dur":97, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061667971, "dur":124, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061668751, "dur":23638, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752056061397116, "dur":53247, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061450370, "dur":2580, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061452951, "dur":1738, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061454690, "dur":35280, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061489984, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061490038, "dur":397, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":1, "ts":1752056061490437, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":1, "ts":1752056061490557, "dur":109, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":1, "ts":1752056061490718, "dur":4032, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061494760, "dur":353, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056061495207, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056061495422, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056061495628, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061495838, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1752056061496477, "dur":521, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":1, "ts":1752056061497131, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":1, "ts":1752056061497236, "dur":9175, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061506469, "dur":6748, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VehiclesModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061513247, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061506412, "dur":7038, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x76fzfo8l23p.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061513643, "dur":6227, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061519891, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061513601, "dur":6503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/91501rpe8hhy.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061520105, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061520325, "dur":936, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061520278, "dur":1050, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061521702, "dur":4640, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__57.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061526352, "dur":153, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061521659, "dur":4847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061526580, "dur":12504, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__7.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061539107, "dur":429, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061526548, "dur":12988, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061539636, "dur":4571, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061544223, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061539596, "dur":4832, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rjj1gat48zbs.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061544514, "dur":1756, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061546289, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061544483, "dur":1866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061546590, "dur":6520, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061553129, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061546544, "dur":6810, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061553455, "dur":5480, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__87.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061558953, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061553404, "dur":5708, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ij1d4hlxunhx.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061559172, "dur":2645, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061561836, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061559153, "dur":2909, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061562138, "dur":5149, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__102.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061567313, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061562108, "dur":5480, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2vtz1tzlp5yt.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061567679, "dur":3702, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__95.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061571398, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061567649, "dur":3953, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3xdscggiq2nt.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061571785, "dur":712, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AccessibilityModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061571747, "dur":812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9oigfpdat6n.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061572676, "dur":6198, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061578886, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061572626, "dur":6487, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qxqd40q4j6kr.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061579381, "dur":6130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061585530, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061579332, "dur":6428, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/snuim7hzfdjp.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061586067, "dur":3701, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061589788, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061586031, "dur":3987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061590133, "dur":2683, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061592837, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061590088, "dur":2969, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3rk5hkcdfkff.o" }}
,{ "pid":12345, "tid":1, "ts":1752056061593354, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061593621, "dur":134, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061593900, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Timeline_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061594292, "dur":275, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061594583, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056061594783, "dur":1058, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__46.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752056061595878, "dur":284, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061596313, "dur":303, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AccessibilityModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752056061596617, "dur":70200, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061396679, "dur":53603, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061450297, "dur":2420, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061452717, "dur":1955, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061454673, "dur":35121, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061489825, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061489887, "dur":211, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":2, "ts":1752056061490137, "dur":204, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ParticleSystemModule-FeaturesChecked.txt_kapj.info" }}
,{ "pid":12345, "tid":2, "ts":1752056061490345, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":2, "ts":1752056061490592, "dur":78, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":2, "ts":1752056061490673, "dur":208, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":2, "ts":1752056061490882, "dur":3878, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061494761, "dur":320, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056061495167, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056061495720, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056061495954, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056061496135, "dur":445, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752056061496850, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1752056061497098, "dur":162, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1752056061497261, "dur":9207, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061506569, "dur":196, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":2, "ts":1752056061506788, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061506478, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jov4p39u0xgj.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061506949, "dur":367, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":2, "ts":1752056061507336, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061506915, "dur":481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061507398, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061507563, "dur":5761, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061513339, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061507469, "dur":6121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/06e9mmocdc22.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061513687, "dur":5773, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__3.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061519482, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061513652, "dur":6030, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hq9xs8ifo9o5.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061519816, "dur":3666, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VFXModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061523503, "dur":270, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061519774, "dur":4000, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zadaggpr090.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061523863, "dur":6057, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__5.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061529934, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061523832, "dur":6327, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b3veiy14s00c.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061530283, "dur":5038, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061535336, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061530242, "dur":5338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2jrgs6r4v1zy.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061535896, "dur":2165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__71.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061538077, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061535851, "dur":2441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061538412, "dur":1168, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1752056061539599, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061538369, "dur":1289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ijiwlknrkr7.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061539796, "dur":5799, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__5.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061545606, "dur":137, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061539731, "dur":6013, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nlgaorbaulc8.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061546231, "dur":3200, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__77.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061549449, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061546197, "dur":3486, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bh1uvuvgsii2.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061549974, "dur":4814, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__4.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061554807, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061549939, "dur":5108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qnsq2on7pcpt.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061555138, "dur":1333, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1752056061556496, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061555106, "dur":1449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4g4fb246gu9o.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061556981, "dur":5040, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061562053, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061556935, "dur":5322, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061562342, "dur":5442, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__70.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061567805, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061562313, "dur":5730, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061568144, "dur":9378, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__47.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061577545, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061568103, "dur":9686, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061577790, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061577907, "dur":4321, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061582247, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061577872, "dur":4596, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061582627, "dur":5440, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061588084, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061582571, "dur":5763, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061588456, "dur":1308, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1752056061589783, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061588411, "dur":1425, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cc6dokwk1bcr.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061589937, "dur":3656, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__21.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061593613, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061589894, "dur":3966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061593897, "dur":173, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":2, "ts":1752056061594367, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056061594642, "dur":345, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__79.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061595016, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061595296, "dur":277, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061595639, "dur":262, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061595932, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061596412, "dur":275, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752056061596689, "dur":70103, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061398580, "dur":52169, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061450750, "dur":2632, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061453383, "dur":36316, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061489762, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061489871, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061490174, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061490285, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":3, "ts":1752056061490468, "dur":72, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":3, "ts":1752056061490544, "dur":308, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":3, "ts":1752056061490855, "dur":3893, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061494749, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752056061495181, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752056061495440, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752056061495796, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061496164, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061496460, "dur":603, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1752056061497241, "dur":9180, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061506502, "dur":5958, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__106.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061512484, "dur":150, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061506423, "dur":6212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a74817od3lkr.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061512851, "dur":1724, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1752056061514597, "dur":64, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061512819, "dur":1843, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9up4h199he7v.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061515063, "dur":4494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.DirectorModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061519572, "dur":269, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061515013, "dur":4829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m7tsc9fssqjw.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061519941, "dur":1967, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":3, "ts":1752056061521926, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061519902, "dur":2078, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061522062, "dur":1641, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1752056061522033, "dur":1732, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9nmkg3j9skao.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061523977, "dur":6788, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__48.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061530783, "dur":182, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061523933, "dur":7033, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061531073, "dur":4234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__99.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061535329, "dur":300, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061531034, "dur":4596, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b9o1cu593i9q.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061535728, "dur":3718, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061539458, "dur":175, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061535693, "dur":3941, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lj6ht2s4c6zd.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061539770, "dur":4494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061544290, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061539727, "dur":4768, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mri45r890jp3.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061544594, "dur":3672, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061548283, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061544552, "dur":3958, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/34e4vqh3e4xi.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061548669, "dur":4001, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__6.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061552690, "dur":174, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061548634, "dur":4231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tal1f6v1rh3z.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061552958, "dur":3986, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061556956, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061552919, "dur":4257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4z4l2zsbjmwu.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061557306, "dur":4751, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__9.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061562075, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061557268, "dur":5011, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iza11ye77ffg.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061562486, "dur":5944, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061568449, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061562446, "dur":6239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v2ltsjojhjw6.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061568781, "dur":5615, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Mathematics.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061574421, "dur":182, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061568747, "dur":5859, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/83b95fuxpl22.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061574862, "dur":5381, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061580255, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061574816, "dur":5699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061580601, "dur":4125, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061584748, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061580559, "dur":4399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/coeqiwfgefg2.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061585238, "dur":3960, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061589209, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061585189, "dur":4220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061589547, "dur":3551, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061593116, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061589499, "dur":3842, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4czkg6x1i6ey.o" }}
,{ "pid":12345, "tid":3, "ts":1752056061593593, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__9.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061594086, "dur":418, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__26.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061594572, "dur":121, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__37.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061594730, "dur":272, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__85.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061595179, "dur":127, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061595349, "dur":475, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061595882, "dur":537, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752056061596459, "dur":69985, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061666454, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056061666530, "dur":333, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061396649, "dur":53593, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061457710, "dur":2460, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":4, "ts":1752056061460172, "dur":275, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/xamarin-android" }}
,{ "pid":12345, "tid":4, "ts":1752056061460447, "dur":270, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061460717, "dur":284, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061461002, "dur":237, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061461239, "dur":242, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061461481, "dur":265, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061461746, "dur":235, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061461982, "dur":284, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":4, "ts":1752056061462267, "dur":17110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061479378, "dur":227, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061479605, "dur":199, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061479805, "dur":200, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061480005, "dur":197, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061480202, "dur":209, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061480411, "dur":237, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":4, "ts":1752056061480649, "dur":238, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":4, "ts":1752056061480888, "dur":326, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":4, "ts":1752056061481214, "dur":227, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":4, "ts":1752056061481441, "dur":197, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":4, "ts":1752056061481638, "dur":128, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":4, "ts":1752056061481767, "dur":125, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":4, "ts":1752056061481892, "dur":121, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":4, "ts":1752056061482013, "dur":119, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":4, "ts":1752056061482132, "dur":128, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":4, "ts":1752056061482261, "dur":271, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":4, "ts":1752056061482533, "dur":277, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":4, "ts":1752056061482810, "dur":241, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":4, "ts":1752056061483052, "dur":244, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":4, "ts":1752056061483296, "dur":337, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":4, "ts":1752056061483633, "dur":119, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":4, "ts":1752056061483752, "dur":109, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":4, "ts":1752056061483861, "dur":111, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":4, "ts":1752056061483972, "dur":114, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":4, "ts":1752056061484096, "dur":114, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":4, "ts":1752056061484210, "dur":128, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061484338, "dur":112, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061484451, "dur":111, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061484562, "dur":110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061484672, "dur":115, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":4, "ts":1752056061484788, "dur":178, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":4, "ts":1752056061484966, "dur":1037, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":4, "ts":1752056061486003, "dur":1005, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":4, "ts":1752056061487008, "dur":1022, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":4, "ts":1752056061488031, "dur":117, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-anydpi-v26" }}
,{ "pid":12345, "tid":4, "ts":1752056061488148, "dur":130, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-mdpi" }}
,{ "pid":12345, "tid":4, "ts":1752056061488278, "dur":201, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":4, "ts":1752056061488479, "dur":123, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/Libs/arm64-v8a" }}
,{ "pid":12345, "tid":4, "ts":1752056061488602, "dur":407, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1752056061489009, "dur":387, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1752056061489396, "dur":148, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Android/Data" }}
,{ "pid":12345, "tid":4, "ts":1752056061450247, "dur":39448, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061489720, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":4, "ts":1752056061489947, "dur":1846, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":4, "ts":1752056061491794, "dur":1285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061494732, "dur":1723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/il2cpp_conv_dqml.traceevents" }}
,{ "pid":12345, "tid":4, "ts":1752056061496456, "dur":4861, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061506435, "dur":8841, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061515309, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061506401, "dur":9148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lh8sv41vny8b.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061515634, "dur":5669, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__6.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061521319, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061515604, "dur":5934, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bwj8l05dy1ay.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061521665, "dur":4376, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__12.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061526060, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061521626, "dur":4629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gso6qzdxurfn.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061526337, "dur":4565, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061530920, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061526298, "dur":4822, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vrairmxpxc62.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061531483, "dur":6427, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__9.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061537941, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061531428, "dur":6724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0dcuv44u1v7h.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061538491, "dur":5494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061544001, "dur":173, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061538437, "dur":5739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061544500, "dur":3423, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__88.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061547946, "dur":276, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061544463, "dur":3759, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3y41jrzuxbtg.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061548329, "dur":5624, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061553967, "dur":151, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061548281, "dur":5838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061554253, "dur":1645, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752056061555916, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061554209, "dur":1758, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061556060, "dur":4677, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__28.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061560754, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061556022, "dur":4940, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/106avjl0gpni.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061561793, "dur":3603, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061565412, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061561750, "dur":3858, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p6sl71dxdw31.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061565830, "dur":3855, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061569704, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061565790, "dur":4130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061570050, "dur":6045, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061576111, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061570006, "dur":6291, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061576588, "dur":915, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752056061577523, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061576532, "dur":1057, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061577698, "dur":872, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752056061577658, "dur":973, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061578958, "dur":7679, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061586649, "dur":127, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061578917, "dur":7859, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p8v3lbbirbiz.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061586856, "dur":4940, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061591816, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061586819, "dur":5243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":4, "ts":1752056061593177, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__106.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061593596, "dur":114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__6.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061594017, "dur":94, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__31.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061594297, "dur":153, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061594460, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056061594796, "dur":98, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__68.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061595008, "dur":682, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__96.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061595761, "dur":265, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752056061596058, "dur":524, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst.Unsafe_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752056061596583, "dur":70219, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061396761, "dur":53549, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061450314, "dur":3102, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061453416, "dur":36519, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061489972, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061490036, "dur":383, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info" }}
,{ "pid":12345, "tid":5, "ts":1752056061490421, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":5, "ts":1752056061490560, "dur":59, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":5, "ts":1752056061490649, "dur":4087, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061494751, "dur":280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752056061495109, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752056061495457, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752056061495667, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061496476, "dur":228, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752056061496807, "dur":233, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VideoModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1752056061497114, "dur":135, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1752056061497251, "dur":9185, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061506569, "dur":2245, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__109.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061508838, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061506470, "dur":2554, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/haw1h6x6c0o9.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061509132, "dur":8044, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061517193, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061509100, "dur":8313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061517508, "dur":6319, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061523845, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061517467, "dur":6594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061524325, "dur":4417, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Collections.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061528761, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061524271, "dur":4705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3cc7s5m7xd51.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061529080, "dur":5668, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061534765, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061529030, "dur":5897, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061535000, "dur":2143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752056061534974, "dur":2227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061537289, "dur":4854, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061542161, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061537252, "dur":5140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061542610, "dur":7069, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__53.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061549711, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061542579, "dur":7333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061550004, "dur":6109, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061556131, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061549969, "dur":6396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a85wbpqwoi20.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061556366, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061556469, "dur":4771, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__69.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061561257, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061556441, "dur":5039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061561559, "dur":4493, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061566073, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061561529, "dur":4783, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ldqhcp8dvt5c.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061567369, "dur":6584, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__29.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061573974, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061567320, "dur":6865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061574289, "dur":5622, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061579930, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061574250, "dur":5883, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zg0e63qyl8i.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061580215, "dur":1905, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752056061582150, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061580185, "dur":2031, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/upsfj2k4spt9.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061582531, "dur":8578, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061591133, "dur":157, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061582462, "dur":8829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061591501, "dur":1724, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752056061593244, "dur":69, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061591437, "dur":1876, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":5, "ts":1752056061593522, "dur":211, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__7.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061594021, "dur":166, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061594297, "dur":170, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VideoModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061594475, "dur":221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056061594697, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__27.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061594804, "dur":137, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__71.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061594971, "dur":446, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__42.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061595456, "dur":739, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Mathematics.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752056061596241, "dur":301, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752056061596543, "dur":70214, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061397027, "dur":53301, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061450337, "dur":2752, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061453090, "dur":36613, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061489721, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":6, "ts":1752056061489944, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061490056, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061490176, "dur":131, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info" }}
,{ "pid":12345, "tid":6, "ts":1752056061490351, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":6, "ts":1752056061490485, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":6, "ts":1752056061490632, "dur":4123, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061494757, "dur":1682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1752056061496440, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061496604, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061496695, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1752056061496813, "dur":528, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1752056061497342, "dur":9086, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061506509, "dur":4794, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061511335, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061506432, "dur":5102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061511724, "dur":3697, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061515439, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061511674, "dur":4006, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061515765, "dur":6526, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__104.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061522311, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061515735, "dur":6739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ojh41iuhwvrs.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061522540, "dur":738, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752056061522511, "dur":836, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061523434, "dur":2161, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Collections_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752056061525615, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061523394, "dur":2284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bcwljkgbs91u.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061525763, "dur":7244, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061533025, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061525734, "dur":7493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tqqyoib0g7mb.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061533529, "dur":4267, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__55.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061537814, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061533478, "dur":4568, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061538178, "dur":3985, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061542181, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061538144, "dur":4226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061542594, "dur":4841, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061547649, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061542548, "dur":5335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061547961, "dur":3878, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__28.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061551859, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061547930, "dur":4153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8eeewr1tvkgc.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061552252, "dur":5438, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__6.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061557701, "dur":150, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061552212, "dur":5639, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot0q86r8xntn.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061557945, "dur":3595, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061561550, "dur":173, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061557906, "dur":3818, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061561804, "dur":2731, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__90.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061564557, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061561773, "dur":3033, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7yuwsct9xqc.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061564885, "dur":4297, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__74.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061569201, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061564856, "dur":4561, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061569515, "dur":961, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752056061569472, "dur":1057, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061570631, "dur":5673, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061576315, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061570587, "dur":5968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061576697, "dur":1399, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752056061578126, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061576646, "dur":1546, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061578294, "dur":5136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061583447, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061578250, "dur":5401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z5rdbpowxtq2.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061584114, "dur":3549, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061587680, "dur":134, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061584069, "dur":3746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m20qes3fkulp.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061587868, "dur":3522, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__83.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061591408, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061587850, "dur":3781, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a8p433v5g7vz.o" }}
,{ "pid":12345, "tid":6, "ts":1752056061593187, "dur":69, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__104.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061594151, "dur":118, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061594416, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__53.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061594531, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061594729, "dur":198, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__59.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061594957, "dur":409, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__29.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061595530, "dur":278, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061595852, "dur":276, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752056061596134, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056061596232, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752056061596514, "dur":70240, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061397084, "dur":53259, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061450349, "dur":2375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061452725, "dur":1523, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061454249, "dur":35606, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061489862, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061490141, "dur":487, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info" }}
,{ "pid":12345, "tid":7, "ts":1752056061490629, "dur":4109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061494739, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752056061495115, "dur":797, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752056061495995, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752056061496459, "dur":140, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Splines.pdb" }}
,{ "pid":12345, "tid":7, "ts":1752056061496654, "dur":791, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1752056061497447, "dur":8971, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061506510, "dur":8239, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__10.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061514788, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061506421, "dur":8620, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061515170, "dur":4622, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061519812, "dur":291, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061515125, "dur":4978, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061520104, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061520334, "dur":5135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__89.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061525485, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061520289, "dur":5435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/46oi46ft1daw.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061526144, "dur":1869, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752056061528037, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061526098, "dur":1993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061528485, "dur":6181, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061534686, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061528442, "dur":6451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kcj9lts71wxr.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061535071, "dur":7494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061542583, "dur":142, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061534957, "dur":7768, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061542892, "dur":3750, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__41.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061546653, "dur":1149, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061542864, "dur":4939, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061547804, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061547950, "dur":6330, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061554300, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061547899, "dur":6592, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/608olej0nq3l.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061554787, "dur":6148, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061560948, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061554743, "dur":6448, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061561268, "dur":5262, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061566548, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061561240, "dur":5506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061566863, "dur":59, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752056061566942, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061566819, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vge3azf3u12w.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061567337, "dur":2070, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752056061567288, "dur":2184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061569547, "dur":4699, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061574265, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061569523, "dur":4965, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zals4mmypley.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061574765, "dur":2666, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061577443, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061574724, "dur":2968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/stosql5gpuhq.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061578066, "dur":4882, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061582966, "dur":131, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061577792, "dur":5305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061583302, "dur":5927, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061589240, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061583258, "dur":6208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxs8b7zbmatr.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061589623, "dur":3616, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__39.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061593260, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061589583, "dur":3921, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":7, "ts":1752056061593967, "dur":184, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061594294, "dur":111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061594416, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056061594693, "dur":134, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__98.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061594879, "dur":237, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__33.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061595245, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061595384, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752056061595598, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GuidGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":7, "ts":1752056061595766, "dur":273, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061596076, "dur":252, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":7, "ts":1752056061596359, "dur":270, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__8.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752056061596630, "dur":70175, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061397216, "dur":53161, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061450382, "dur":2824, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061453207, "dur":36634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061489974, "dur":164, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VehiclesModule-FeaturesChecked.txt_a1wc.info" }}
,{ "pid":12345, "tid":8, "ts":1752056061490209, "dur":488, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":8, "ts":1752056061490699, "dur":4086, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061494786, "dur":354, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752056061495236, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752056061495477, "dur":240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752056061495803, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752056061496035, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752056061496174, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061496479, "dur":242, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1752056061496890, "dur":586, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1752056061497477, "dur":8979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061506537, "dur":9525, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061516091, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061506458, "dur":9838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061516424, "dur":4539, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__34.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061520983, "dur":164, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061516378, "dur":4770, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061521418, "dur":1683, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1752056061523119, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061521381, "dur":1794, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061523255, "dur":2122, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1752056061525399, "dur":71, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061523221, "dur":2250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061525473, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061525590, "dur":4420, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061530026, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061525549, "dur":4610, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061530237, "dur":4282, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__51.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061534533, "dur":149, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061530200, "dur":4483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061534818, "dur":7546, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTweenPro.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061542384, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061534783, "dur":7825, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/075gzxgtt4dn.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061542698, "dur":5919, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__93.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061548635, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061542670, "dur":6204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x26fhxnnkq0q.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061548977, "dur":5703, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061554692, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061548937, "dur":5943, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfkoufio19j2.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061555010, "dur":6039, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__85.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061561076, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061554971, "dur":6333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvd93sq1nxoy.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061561407, "dur":6059, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061567485, "dur":272, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061561376, "dur":6382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/01zek1xq1lza.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061567969, "dur":4685, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061572685, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061567914, "dur":4968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bugq7q8skmw0.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061572954, "dur":7428, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061580401, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061572924, "dur":7696, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vclibqzzr333.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061580697, "dur":4221, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__3.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061584947, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061580673, "dur":4491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ssf61wkon6f.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061585239, "dur":4787, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061590043, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061585207, "dur":5045, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vomfw8rdeo41.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061590472, "dur":3841, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061594332, "dur":283, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056061590431, "dur":4185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1awb9h9nex66.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061594641, "dur":1087, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1awb9h9nex66.o" }}
,{ "pid":12345, "tid":8, "ts":1752056061596177, "dur":279, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752056061596492, "dur":70269, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061397433, "dur":52956, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061450394, "dur":3125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061453521, "dur":36360, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061489948, "dur":307, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info" }}
,{ "pid":12345, "tid":9, "ts":1752056061490292, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/IntermediateLauncherManifestDiag.txt_4oyd.info" }}
,{ "pid":12345, "tid":9, "ts":1752056061490362, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1752056061490514, "dur":74, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1752056061490655, "dur":4111, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061494768, "dur":389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752056061495252, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752056061495503, "dur":379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752056061495883, "dur":384, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061496435, "dur":83, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":9, "ts":1752056061496615, "dur":247, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1752056061497083, "dur":334, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1752056061497420, "dur":9004, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061506480, "dur":6764, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061513275, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061506437, "dur":7076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061513515, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061513901, "dur":8774, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__4.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061522698, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061513863, "dur":9079, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061523076, "dur":6615, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__61.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061529703, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061523028, "dur":6852, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061529978, "dur":2029, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752056061532026, "dur":64, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061529933, "dur":2158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061532244, "dur":1150, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp-firstpass_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752056061533412, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061532201, "dur":1276, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lzrbp09zxf4t.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061533574, "dur":1648, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752056061535238, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061533539, "dur":1750, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061535353, "dur":7343, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061542707, "dur":175, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061535330, "dur":7552, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pgj75bd160pu.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061542959, "dur":4587, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061547661, "dur":271, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061542924, "dur":5009, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061548031, "dur":5018, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061553071, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061547985, "dur":5323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061553721, "dur":7122, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__64.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061560859, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061553666, "dur":7396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hjtq22u5el9s.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061561212, "dur":3374, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061564615, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061561169, "dur":3672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/70zru6g5q2rj.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061564935, "dur":1025, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752056061564883, "dur":1125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061566385, "dur":5623, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061572027, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061566350, "dur":5900, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061573127, "dur":6002, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__13.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061579153, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061573088, "dur":6294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061579511, "dur":4146, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__31.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061583677, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061579478, "dur":4408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gvsnt8uqb9un.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061583983, "dur":4513, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__84.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061588506, "dur":142, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061583943, "dur":4705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgalojc4k9cf.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061588870, "dur":5553, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061594633, "dur":293, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056061588817, "dur":6110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061594955, "dur":390, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" }}
,{ "pid":12345, "tid":9, "ts":1752056061595374, "dur":294, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061595711, "dur":241, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752056061596135, "dur":523, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752056061596659, "dur":70166, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061397489, "dur":52913, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061450407, "dur":3109, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061453517, "dur":36292, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061489818, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061489882, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061489996, "dur":246, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/DOTween-FeaturesChecked.txt_esvd.info" }}
,{ "pid":12345, "tid":10, "ts":1752056061490291, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":10, "ts":1752056061490444, "dur":64, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":10, "ts":1752056061490520, "dur":60, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":10, "ts":1752056061490583, "dur":193, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":10, "ts":1752056061490777, "dur":3985, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061494765, "dur":2001, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752056061496769, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061496843, "dur":484, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752056061497382, "dur":9023, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061506495, "dur":8981, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061515506, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061506408, "dur":9329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2wexl85vs87e.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061515809, "dur":5883, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061521716, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061515781, "dur":6194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061522047, "dur":3080, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061525146, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061522015, "dur":3396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/77qu6x31cp75.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061525412, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061525639, "dur":4665, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__1.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061530320, "dur":161, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061525599, "dur":4883, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061530873, "dur":1867, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Mathematics_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1752056061530827, "dur":1979, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sk3psbif85dy.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061533105, "dur":6537, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__46.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061539653, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061533071, "dur":6790, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061539952, "dur":1777, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061541740, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061539922, "dur":2005, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061542016, "dur":6863, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__73.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061548890, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061541973, "dur":7110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061549172, "dur":4458, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__108.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061553649, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061549136, "dur":4723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g2blxqh88x7t.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061553925, "dur":1185, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1752056061555129, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061553897, "dur":1291, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e7m8oxn5tfdg.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061555293, "dur":4917, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__2.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061560222, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061555258, "dur":5153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lz9rfs69d1h2.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061560501, "dur":3787, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__79.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061564321, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061560466, "dur":4060, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hc86qdobyu54.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061564850, "dur":1770, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1752056061566639, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061564803, "dur":1893, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r1w5t531a50x.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061566792, "dur":4784, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__107.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061571597, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061566750, "dur":5081, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vm8whh3a69va.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061571920, "dur":1064, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1752056061572999, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061571884, "dur":1171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s8wzl21r6wdv.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061573168, "dur":4805, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061577993, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061573124, "dur":5126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qpxrszza8ri.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061578324, "dur":6288, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061584631, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061578295, "dur":6582, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mcvqv3cujwyv.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061584963, "dur":8504, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__10.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061593478, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061584930, "dur":8739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ac7tbdjsuouj.o" }}
,{ "pid":12345, "tid":10, "ts":1752056061593847, "dur":93, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Cinemachine__3.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061594086, "dur":115, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__1.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061594217, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056061594431, "dur":123, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__67.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061594636, "dur":414, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__14.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061595534, "dur":280, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Postprocessing.Runtime__1.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752056061595873, "dur":528, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":10, "ts":1752056061596472, "dur":70138, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056061397561, "dur":52852, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056061450416, "dur":2409, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056061452826, "dur":824, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056061453651, "dur":36056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056061489728, "dur":176019, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":11, "ts":1752056061665822, "dur":560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":11, "ts":1752056061666508, "dur":268, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061397619, "dur":52807, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061450431, "dur":2545, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061452977, "dur":2028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061455005, "dur":34750, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061489859, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061489917, "dur":395, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info" }}
,{ "pid":12345, "tid":12, "ts":1752056061490316, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":12, "ts":1752056061490424, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":12, "ts":1752056061490516, "dur":50, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":12, "ts":1752056061490569, "dur":160, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":12, "ts":1752056061490731, "dur":4000, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061494732, "dur":345, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752056061495161, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752056061495301, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061495493, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752056061495785, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752056061496039, "dur":342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":12, "ts":1752056061496382, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061496451, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":12, "ts":1752056061496939, "dur":141, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/lib_burst_generated.so" }}
,{ "pid":12345, "tid":12, "ts":1752056061497228, "dur":9185, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061506454, "dur":204, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752056061506674, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061506415, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061506809, "dur":4180, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061511027, "dur":136, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061506781, "dur":4382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cczik646utbr.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061511528, "dur":5769, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__6.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061517316, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061511494, "dur":6041, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wiz2x7f2vocw.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061517708, "dur":6417, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061524144, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061517677, "dur":6673, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jbm9bjl27jgz.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061524886, "dur":6276, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061531187, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061524852, "dur":6570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061531529, "dur":4847, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061536402, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061531485, "dur":5148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061536980, "dur":4640, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__59.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061541631, "dur":116, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061536941, "dur":4806, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061541830, "dur":6441, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061548289, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061541809, "dur":6678, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061548694, "dur":3153, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752056061551859, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061548653, "dur":3267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hzzs0hwl5jko.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061552026, "dur":7299, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061559342, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061551980, "dur":7574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061559927, "dur":4404, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__3.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061564350, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061559663, "dur":4930, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/88j8emx3u48y.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061564668, "dur":7478, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__11.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061572162, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061564637, "dur":7726, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/weayk7q21k6f.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061572444, "dur":1723, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752056061574192, "dur":64, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061572418, "dur":1838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a951q8orlkl2.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061574368, "dur":5880, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__1.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061580277, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061574322, "dur":6139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061580462, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061580910, "dur":2270, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061583196, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061580863, "dur":2546, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zwfl6jeaz8l8.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061583596, "dur":2398, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752056061586010, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061583564, "dur":2501, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aggn294o7ldd.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061586379, "dur":6091, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__49.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061592489, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056061586343, "dur":6342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" }}
,{ "pid":12345, "tid":12, "ts":1752056061593533, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__8.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061594299, "dur":186, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061594574, "dur":403, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__88.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061595009, "dur":265, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061595322, "dur":134, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061595519, "dur":245, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061595980, "dur":505, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752056061596486, "dur":70277, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061397684, "dur":52764, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061452281, "dur":866, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":13, "ts":1752056061450458, "dur":3421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061453880, "dur":35944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061489873, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info" }}
,{ "pid":12345, "tid":13, "ts":1752056061490019, "dur":797, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info" }}
,{ "pid":12345, "tid":13, "ts":1752056061490817, "dur":3983, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061494801, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752056061495096, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752056061495368, "dur":232, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752056061495602, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061496142, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061496476, "dur":205, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":13, "ts":1752056061496720, "dur":577, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1752056061497298, "dur":9104, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061506436, "dur":7608, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061514074, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061506403, "dur":7878, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6z6dmin38mqq.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061514395, "dur":6234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__58.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061520647, "dur":175, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061514354, "dur":6469, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061520893, "dur":4905, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__96.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061525814, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061520869, "dur":5150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/45x8wvwgu448.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061526194, "dur":3474, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__19.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061529683, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061526149, "dur":3732, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061530178, "dur":4751, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__22.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061534951, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061530152, "dur":5012, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061535166, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061535321, "dur":729, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1752056061535278, "dur":835, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061536354, "dur":7258, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Splines.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061543627, "dur":155, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061536318, "dur":7464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/k4kibxippfaw.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061544145, "dur":6527, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__15.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061550690, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061544107, "dur":6787, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nvfz6xmq45j5.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061551040, "dur":5649, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061556708, "dur":168, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061551003, "dur":5873, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hu59tggcxxy3.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061557209, "dur":6355, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061563579, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061557171, "dur":6600, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gbjz3jrhufsx.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061563845, "dur":7609, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__31.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061571475, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061563814, "dur":7901, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9glks0exwd2t.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061572026, "dur":7686, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061579723, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061571986, "dur":7897, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i6hutl07x9cu.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061580028, "dur":4797, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__42.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061584847, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061579992, "dur":5092, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061585085, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061585238, "dur":7495, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__16.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061592756, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061585206, "dur":7798, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" }}
,{ "pid":12345, "tid":13, "ts":1752056061594434, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056061595176, "dur":506, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__36.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061595712, "dur":260, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":13, "ts":1752056061596118, "dur":237, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061596410, "dur":259, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752056061596670, "dur":70158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061397765, "dur":52704, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061452330, "dur":1018, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":14, "ts":1752056061450475, "dur":3365, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061453840, "dur":35874, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061489721, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_in83.info" }}
,{ "pid":12345, "tid":14, "ts":1752056061489889, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info" }}
,{ "pid":12345, "tid":14, "ts":1752056061490042, "dur":285, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info" }}
,{ "pid":12345, "tid":14, "ts":1752056061490328, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":14, "ts":1752056061490499, "dur":76, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":14, "ts":1752056061490578, "dur":182, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":14, "ts":1752056061490761, "dur":3979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061494741, "dur":601, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1752056061495435, "dur":879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1752056061496469, "dur":177, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1752056061496727, "dur":247, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1752056061497007, "dur":530, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":14, "ts":1752056061497539, "dur":8905, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061506545, "dur":6562, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061513131, "dur":175, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061506461, "dur":6845, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ehcxqa0cwkap.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061513463, "dur":5247, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__50.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061518736, "dur":263, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061513350, "dur":5650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25hyt1fxyzo0.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061519091, "dur":6178, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__75.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061525288, "dur":255, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061519053, "dur":6490, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ptdqv50klsp3.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061525637, "dur":1907, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752056061527555, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061525601, "dur":2012, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061527722, "dur":124, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":14, "ts":1752056061527678, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n6qmfnbbo4ko.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061527995, "dur":762, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752056061528774, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061527960, "dur":870, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/56ben38xdc5p.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061529236, "dur":4920, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__65.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061534173, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061529208, "dur":5164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061534746, "dur":4425, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__35.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061539194, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061534709, "dur":4705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061539469, "dur":6289, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__14.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061545779, "dur":155, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061539451, "dur":6484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2fcbro8s1h8l.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061546073, "dur":3839, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__94.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061549939, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061546028, "dur":4147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iia4w61kneca.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061550922, "dur":5989, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061556932, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061550883, "dur":6274, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hl1iyi40oidl.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061557245, "dur":5213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__101.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061562485, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061557209, "dur":5478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hgqbmklomhna.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061562767, "dur":4578, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__25.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061567365, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061562733, "dur":4876, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uxfiflc76aj0.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061567815, "dur":3653, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061571489, "dur":254, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061567741, "dur":4003, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061572186, "dur":5220, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061577426, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061572149, "dur":5511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061577791, "dur":6854, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061584668, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061577732, "dur":7098, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061585122, "dur":7152, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061592288, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056061585086, "dur":7390, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pn5tr6jvsup6.o" }}
,{ "pid":12345, "tid":14, "ts":1752056061593986, "dur":85, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__33.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061594363, "dur":178, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061594788, "dur":121, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061594940, "dur":259, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__90.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061595238, "dur":323, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061595785, "dur":312, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061596126, "dur":257, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Postprocessing.Runtime.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752056061596488, "dur":70279, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061397835, "dur":52650, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061450490, "dur":2204, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061452695, "dur":1570, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061454266, "dur":35456, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061489935, "dur":256, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-firstpass-FeaturesChecked.txt_ww6f.info" }}
,{ "pid":12345, "tid":15, "ts":1752056061490206, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061490263, "dur":116, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info" }}
,{ "pid":12345, "tid":15, "ts":1752056061490381, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":15, "ts":1752056061490567, "dur":340, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":15, "ts":1752056061490909, "dur":3859, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061494769, "dur":1622, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1752056061496393, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061496450, "dur":181, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1752056061496728, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":15, "ts":1752056061496870, "dur":503, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":15, "ts":1752056061497376, "dur":9112, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061506530, "dur":4150, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061510699, "dur":142, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061506489, "dur":4352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061510926, "dur":9341, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__20.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061520288, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061510894, "dur":9579, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061520581, "dur":5024, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061525622, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061520538, "dur":5317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061525935, "dur":6266, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061532220, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061525908, "dur":6536, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061532509, "dur":7414, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__8.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061539937, "dur":193, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061532484, "dur":7647, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uo723pk33zgq.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061540235, "dur":4299, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AccessibilityModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061544554, "dur":129, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061540190, "dur":4494, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a5qnuk1zwuff.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061544941, "dur":2581, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061547664, "dur":290, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061544896, "dur":3058, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061547955, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061548214, "dur":5446, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__8.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061553683, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061548166, "dur":5728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061554287, "dur":5351, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__40.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061559653, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061554241, "dur":5614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061559950, "dur":4391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061564359, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061559915, "dur":4677, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ohmn0a84o3bk.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061564681, "dur":4182, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__33.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061568891, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061564652, "dur":4467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mqzzornlhliq.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061569219, "dur":5994, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061575232, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061569176, "dur":6267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lk3p68855ye7.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061575533, "dur":5860, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061581411, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061575499, "dur":6126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mckl3g7t98d1.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061581800, "dur":6812, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__82.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061588631, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061581751, "dur":7013, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psmgouh8djkg.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061588825, "dur":5269, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061594110, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061588806, "dur":5504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":15, "ts":1752056061594566, "dur":92, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__12.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061594876, "dur":225, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__86.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061595159, "dur":451, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__70.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752056061595644, "dur":504, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752056061596157, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056061596309, "dur":296, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752056061596607, "dur":70180, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061397949, "dur":52550, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061450505, "dur":2747, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061453253, "dur":36670, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061489975, "dur":190, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info" }}
,{ "pid":12345, "tid":16, "ts":1752056061490256, "dur":258, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":16, "ts":1752056061490516, "dur":57, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":16, "ts":1752056061490574, "dur":172, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":16, "ts":1752056061490747, "dur":3999, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061494749, "dur":368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":16, "ts":1752056061495118, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061495234, "dur":1281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":16, "ts":1752056061496886, "dur":240, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Postprocessing.Runtime.pdb" }}
,{ "pid":12345, "tid":16, "ts":1752056061497231, "dur":9184, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061506473, "dur":7475, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__7.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061513977, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061506417, "dur":7748, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wbnw19jb1pmh.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061514356, "dur":4594, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061518970, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061514311, "dur":4891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ibt2f73hq3tq.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061519279, "dur":5564, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__78.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061524860, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061519250, "dur":5825, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ww4ib49dv6tl.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061525155, "dur":4964, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061530140, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061525122, "dur":5269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xh4u7f3orcpz.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061530881, "dur":1473, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":16, "ts":1752056061532372, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061530829, "dur":1593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061532613, "dur":2866, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__111.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061535499, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061532577, "dur":3111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/oiquu7a5omm5.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061535788, "dur":6185, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061541991, "dur":172, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061535760, "dur":6403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061542260, "dur":4094, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__54.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061546371, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061542217, "dur":4367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061547641, "dur":5324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime__2.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061552983, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061546703, "dur":6498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f69jcmtdltpb.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061553409, "dur":3545, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061556974, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061553365, "dur":3859, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w9jen0mqna8j.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061557225, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061557506, "dur":6548, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__5.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061564071, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061557470, "dur":6845, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061564424, "dur":5126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__17.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061569571, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061564385, "dur":5403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9i336mhmbwc4.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061569959, "dur":5692, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061575679, "dur":173, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061569897, "dur":5956, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yo7r87bsn5oh.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061575923, "dur":4970, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061580912, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061575894, "dur":5220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061581217, "dur":4256, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061585485, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061581174, "dur":4535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o285j2jc1yvx.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061585792, "dur":1383, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__8.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061587194, "dur":183, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061585754, "dur":1624, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1zy5u90sb95f.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061587471, "dur":5500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__91.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061592993, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061587435, "dur":5789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061593225, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061593362, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" }}
,{ "pid":12345, "tid":16, "ts":1752056061593763, "dur":100, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061593921, "dur":178, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061594483, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061594683, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056061595006, "dur":417, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__31.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061595463, "dur":791, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Cinemachine.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061596282, "dur":287, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":16, "ts":1752056061596571, "dur":70237, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061398018, "dur":52499, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061450522, "dur":2415, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061452938, "dur":1685, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061454623, "dur":35284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061489939, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061490058, "dur":436, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":17, "ts":1752056061490501, "dur":89, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":17, "ts":1752056061490612, "dur":4121, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061494735, "dur":953, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752056061495755, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752056061495972, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752056061496506, "dur":293, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1752056061496840, "dur":545, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1752056061497386, "dur":9044, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061506485, "dur":7941, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061514454, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061506431, "dur":8253, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/giph8s2gk77p.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061514756, "dur":5299, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__81.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061520076, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061514727, "dur":5545, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4cjs8k3abv8w.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061520386, "dur":1829, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061522234, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061520335, "dur":1959, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061522379, "dur":1154, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061523553, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061522343, "dur":1270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061523721, "dur":5098, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061528841, "dur":164, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061523670, "dur":5336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061529519, "dur":1570, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AssetBundleModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061531109, "dur":73, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061529467, "dur":1716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6386mtmlure2.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061531304, "dur":5395, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061536714, "dur":121, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061531242, "dur":5594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrt9ubex4zyy.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061536917, "dur":4852, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__4.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061541782, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061536884, "dur":5093, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25cm8hxcrx0o.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061542076, "dur":4021, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__100.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061546115, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061542041, "dur":4284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g85u144pif6u.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061546704, "dur":5417, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__10.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061552138, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061546664, "dur":5671, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u401qaj1n1mo.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061552655, "dur":3823, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp-firstpass.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061556514, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061552580, "dur":4145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9kll8spbuk4q.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061556821, "dur":6519, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__4.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061563362, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061556787, "dur":6718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7mxwfh15kra1.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061563585, "dur":6428, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061570038, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061563553, "dur":6713, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xqgs9zqegqf7.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061570367, "dur":5639, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__17.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061576026, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061570330, "dur":5906, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061576458, "dur":5855, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__52.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061582332, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061576422, "dur":6125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot52tqat5phz.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061582996, "dur":2607, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061585620, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061582950, "dur":2729, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061585807, "dur":3144, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061588970, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061585764, "dur":3419, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mlw3ex27neg0.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061589252, "dur":340, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061589609, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061589224, "dur":452, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061589770, "dur":951, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061590739, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061589726, "dur":1072, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b82i0fmwhe88.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061590880, "dur":3317, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061594216, "dur":316, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056061590849, "dur":3683, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061594570, "dur":285, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" }}
,{ "pid":12345, "tid":17, "ts":1752056061594883, "dur":400, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__26.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061595336, "dur":209, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061595645, "dur":274, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061595993, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752056061596302, "dur":291, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752056061596594, "dur":70205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061398151, "dur":52380, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061450535, "dur":2252, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061452788, "dur":2351, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061455140, "dur":34813, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061490000, "dur":802, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_v0bu.info" }}
,{ "pid":12345, "tid":18, "ts":1752056061490803, "dur":3980, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061494784, "dur":373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752056061495158, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061495492, "dur":602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752056061496215, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061496685, "dur":229, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1752056061497021, "dur":411, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":18, "ts":1752056061497433, "dur":9039, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061506543, "dur":6303, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061512869, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061506478, "dur":6607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wpbgkm2rrn3.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061513249, "dur":94, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061513361, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061513207, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061513576, "dur":6193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime__1.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061519794, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061513528, "dur":6518, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uq6vwcfgi5ds.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061520172, "dur":1254, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061521445, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061520103, "dur":1401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061521578, "dur":4362, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__26.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061525971, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061521550, "dur":4663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061526368, "dur":626, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061526338, "dur":723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061527675, "dur":5832, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061533526, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061527623, "dur":6119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ulhaqys58uf2.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061533814, "dur":1206, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTweenPro_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061533784, "dur":1295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/009ns7gybvdd.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061535142, "dur":1512, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.Unsafe_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061536669, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061535116, "dur":1611, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s2bwaz6dxjth.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061536778, "dur":919, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061537715, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061536758, "dur":1018, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061537891, "dur":5023, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__103.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061542931, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061537854, "dur":5264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nbxnh3djstad.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061543209, "dur":4381, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__4.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061547678, "dur":278, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061543173, "dur":4784, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tonwuvb1xski.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061548117, "dur":5451, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__60.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061553591, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061548026, "dur":5813, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061553936, "dur":4398, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061558359, "dur":172, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061553893, "dur":4639, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/l5t0o7id251b.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061558617, "dur":4174, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061562813, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061558579, "dur":4422, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061563374, "dur":5115, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061568502, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061563327, "dur":5388, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8z7p351pgz6z.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061569090, "dur":4524, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__14.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061573632, "dur":169, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061569053, "dur":4748, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3h1q05zvbzay.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061573877, "dur":3853, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__86.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061577748, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061573838, "dur":4149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbhxj24yql0h.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061578073, "dur":796, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752056061578040, "dur":905, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n96lhotmyyf2.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061579181, "dur":8173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__5.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061587368, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061579138, "dur":8478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qggk6qrgvdze.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061587703, "dur":1555, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__7.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061589277, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061587668, "dur":1859, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eqopvfazu5ej.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061589618, "dur":4771, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061594409, "dur":342, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056061589582, "dur":5170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":18, "ts":1752056061594873, "dur":351, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__15.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061595264, "dur":1078, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061596431, "dur":282, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752056061596714, "dur":70080, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061398230, "dur":52315, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061450550, "dur":2502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061453052, "dur":713, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061453765, "dur":36103, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061489877, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061490168, "dur":307, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info" }}
,{ "pid":12345, "tid":19, "ts":1752056061490478, "dur":61, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":19, "ts":1752056061490541, "dur":125, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":19, "ts":1752056061490668, "dur":4106, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061494783, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1752056061495355, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061495495, "dur":574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1752056061496071, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061496239, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061496464, "dur":467, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1752056061497010, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":19, "ts":1752056061497255, "dur":9153, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061506467, "dur":9047, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061515544, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061506415, "dur":9338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061515945, "dur":4330, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween__1.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061520298, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061515892, "dur":4643, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ayg1b2oww8v.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061520610, "dur":2324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline__1.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061522958, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061520580, "dur":2604, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2n3g8s5g95on.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061523562, "dur":3624, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__27.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061527199, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061523524, "dur":3888, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nzaqginwsw4s.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061527413, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061527994, "dur":5224, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__68.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061533241, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061527957, "dur":5445, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061533611, "dur":3467, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__56.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061537098, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061533575, "dur":3766, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061537342, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061537661, "dur":3788, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__3.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061541469, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061537614, "dur":4056, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bdu1ja51muwa.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061541891, "dur":6537, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__38.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061548450, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061541853, "dur":6823, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ovnm8cdik8r9.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061549376, "dur":7505, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__66.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061556900, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061549337, "dur":7803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36gv2rp1lb2r.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061557260, "dur":4768, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween__2.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061562047, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061557210, "dur":5053, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tq14glgmzkkf.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061562452, "dur":5958, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061568431, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061562408, "dur":6266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxnazyrk9929.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061568766, "dur":766, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VehiclesModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752056061569553, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061568729, "dur":879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hcfnlxn37dpc.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061570209, "dur":4941, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__105.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061575167, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061570167, "dur":5190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9ykshs8ezj3b.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061575504, "dur":969, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VFXModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752056061576491, "dur":68, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061575462, "dur":1097, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sszjk6y5r3mc.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061576636, "dur":3479, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__27.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061580137, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061576609, "dur":3744, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061580450, "dur":4083, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__36.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061584552, "dur":172, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061580410, "dur":4315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061584726, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061585015, "dur":3664, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.DirectorModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752056061584973, "dur":3753, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6b8lrklf1hrp.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061588787, "dur":4432, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__76.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061593238, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056061588766, "dur":4695, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a9gebffiog1m.o" }}
,{ "pid":12345, "tid":19, "ts":1752056061593550, "dur":230, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__9.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061593830, "dur":66, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061593950, "dur":129, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__6.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061594155, "dur":124, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061594363, "dur":254, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061594656, "dur":573, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__21.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061595268, "dur":407, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061595771, "dur":786, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752056061596558, "dur":70231, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061398282, "dur":52286, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061450576, "dur":2108, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061452685, "dur":1469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061454155, "dur":35555, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061489768, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061489882, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061489937, "dur":662, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/DOTweenPro-FeaturesChecked.txt_dxgf.info" }}
,{ "pid":12345, "tid":20, "ts":1752056061490604, "dur":64, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":20, "ts":1752056061490670, "dur":197, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":20, "ts":1752056061490868, "dur":3890, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061494759, "dur":969, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1752056061495731, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061495872, "dur":763, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1752056061496637, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061496691, "dur":814, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1752056061497582, "dur":355, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1752056061497573, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Generate Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056061498190, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061498332, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ModifyAndroidProjectCallback D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":20, "ts":1752056061498574, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061498799, "dur":7700, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061506587, "dur":6699, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__2.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061513307, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061506501, "dur":7048, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jdprqsinlzuw.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061513638, "dur":6325, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061519982, "dur":181, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061513596, "dur":6568, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061520281, "dur":3652, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061523951, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061520244, "dur":3894, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uw7cfbsz1u1x.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061524215, "dur":6826, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061531065, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061524185, "dur":7120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4pl7klguf1rd.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061531442, "dur":11334, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061542796, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061531361, "dur":11627, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061543376, "dur":5286, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__80.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061548679, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061543341, "dur":5542, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/34ybo5mc7srh.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061548952, "dur":7211, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__97.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061556174, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061548926, "dur":7436, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pl1wc5aexsgs.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061556362, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061556577, "dur":4372, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__92.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061560965, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061556533, "dur":4650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wumzwh7ogojb.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061561628, "dur":607, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752056061562253, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061561585, "dur":734, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061562404, "dur":6139, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.Unsafe.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061568569, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061562373, "dur":6461, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8dae7wztktyl.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061568938, "dur":6536, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__43.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061575498, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061568903, "dur":6829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061575834, "dur":6610, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061582464, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061575791, "dur":6879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/55wal3sioh2n.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061582854, "dur":4912, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061587812, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061582814, "dur":5246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061588156, "dur":6075, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__62.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061594249, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061588124, "dur":6358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061594512, "dur":198, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" }}
,{ "pid":12345, "tid":20, "ts":1752056061594820, "dur":140, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__25.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061595064, "dur":445, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__75.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061595635, "dur":484, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752056061596210, "dur":259, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VehiclesModule.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752056061596471, "dur":70002, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061666485, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056061666654, "dur":180, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061398376, "dur":52208, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061450589, "dur":2901, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061453491, "dur":36282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061489810, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061489910, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061489972, "dur":238, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info" }}
,{ "pid":12345, "tid":21, "ts":1752056061490272, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info" }}
,{ "pid":12345, "tid":21, "ts":1752056061490370, "dur":1003, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":21, "ts":1752056061491434, "dur":3309, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061494749, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752056061495197, "dur":303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752056061495501, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061495634, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752056061495865, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752056061496234, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061496444, "dur":374, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1752056061497208, "dur":312, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json" }}
,{ "pid":12345, "tid":21, "ts":1752056061497521, "dur":8920, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061506513, "dur":8751, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__3.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061515282, "dur":168, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061506443, "dur":9008, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061515596, "dur":7288, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__5.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061522901, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061515549, "dur":7567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s20kvqf3nfmk.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061523227, "dur":5996, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__6.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061529236, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061523180, "dur":6197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fcb1uk6w4idh.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061529501, "dur":6693, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__3.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061536213, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061529456, "dur":6941, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pf89h968la8d.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061536492, "dur":4252, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061540763, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061536456, "dur":4519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061541217, "dur":5099, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__6.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061546332, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061541183, "dur":5373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061546626, "dur":823, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752056061547655, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061546604, "dur":1108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061547795, "dur":5600, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061553414, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061547761, "dur":5876, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061554079, "dur":6526, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AssetBundleModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061560617, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061554038, "dur":6764, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ig5tdiuec55o.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061560932, "dur":6787, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061567742, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061560862, "dur":7111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061568114, "dur":1332, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752056061569467, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061568073, "dur":1445, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uuardhox5g5u.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061569593, "dur":4602, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061574226, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061569559, "dur":4903, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x88dxcrw5he9.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061574565, "dur":2854, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752056061574524, "dur":2950, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061577646, "dur":6873, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__110.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061584543, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061577605, "dur":7171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/32m6eyw7lyam.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061585284, "dur":5331, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__34.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061590630, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061585193, "dur":5654, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyjg963jlnz5.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061590926, "dur":1220, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752056061592165, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061590891, "dur":1327, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tyrz3lym5pkq.o" }}
,{ "pid":12345, "tid":21, "ts":1752056061593399, "dur":79, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061593519, "dur":137, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__4.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061593980, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__34.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061594132, "dur":122, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061594296, "dur":126, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061594463, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056061594808, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__15.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061595057, "dur":197, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__72.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061595292, "dur":363, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061595719, "dur":266, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752056061596050, "dur":236, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061596379, "dur":268, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752056061596648, "dur":70173, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061398433, "dur":52161, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061450598, "dur":2612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061453211, "dur":36683, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061489951, "dur":162, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info" }}
,{ "pid":12345, "tid":22, "ts":1752056061490303, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+44 others)" }}
,{ "pid":12345, "tid":22, "ts":1752056061490513, "dur":51, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+44 others)" }}
,{ "pid":12345, "tid":22, "ts":1752056061490565, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061491177, "dur":3575, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061494753, "dur":266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752056061495020, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061495124, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752056061495376, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752056061495661, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752056061495834, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061496856, "dur":417, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":22, "ts":1752056061497274, "dur":9152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061506483, "dur":9049, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061515548, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061506427, "dur":9341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061515848, "dur":6255, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.ParticleSystemModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061522121, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061515813, "dur":6517, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sqf1sldz1ha.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061522405, "dur":2362, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752056061522375, "dur":2454, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7ezf8z3szds.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061524910, "dur":6773, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__12.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061531704, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061524881, "dur":7023, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xmue5m705mnv.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061532066, "dur":5140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__18.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061537218, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061531998, "dur":5429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/btc00jpnpfwv.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061537555, "dur":7295, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061544861, "dur":179, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061537505, "dur":7536, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061545497, "dur":6208, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061551724, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061545460, "dur":6472, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061552018, "dur":7226, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061559260, "dur":166, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061551988, "dur":7438, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i54a6c2ohxdh.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061559597, "dur":5131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061564739, "dur":183, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061559559, "dur":5364, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kdkmybhldeiu.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061565200, "dur":8977, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__26.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061574197, "dur":258, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061565157, "dur":9299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v5zyz0s5ad0f.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061574908, "dur":5573, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061580498, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061574869, "dur":5853, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061580825, "dur":5041, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__30.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061585879, "dur":167, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061580792, "dur":5261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fn868ses76d8.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061586386, "dur":1491, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Splines_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752056061586338, "dur":1582, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o44r908tjxtl.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061588325, "dur":6085, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061594429, "dur":341, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056061588286, "dur":6485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061594806, "dur":487, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":22, "ts":1752056061595465, "dur":283, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752056061595778, "dur":305, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752056061596237, "dur":289, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":22, "ts":1752056061596527, "dur":70254, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061398490, "dur":52140, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061450634, "dur":2188, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061452823, "dur":913, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061453736, "dur":35996, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061489774, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061489940, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061490047, "dur":408, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wybo.info" }}
,{ "pid":12345, "tid":23, "ts":1752056061490459, "dur":78, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":23, "ts":1752056061490538, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061490589, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":23, "ts":1752056061490789, "dur":3981, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061494771, "dur":345, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752056061495201, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752056061495512, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752056061495815, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752056061495956, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061496548, "dur":288, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752056061497006, "dur":158, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":23, "ts":1752056061497167, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb" }}
,{ "pid":12345, "tid":23, "ts":1752056061497290, "dur":9191, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061506576, "dur":8100, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061514705, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061506483, "dur":8421, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061515582, "dur":452, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1752056061516055, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061515519, "dur":595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061516209, "dur":2002, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1752056061516175, "dur":2100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c4dabwex14fm.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061518410, "dur":3298, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061521726, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061518358, "dur":3578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061522479, "dur":6959, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061529460, "dur":179, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061522445, "dur":7195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gy0yzb544nxj.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061529927, "dur":2107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":23, "ts":1752056061532063, "dur":63, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061529888, "dur":2239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061532211, "dur":8612, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__33.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061540850, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061532183, "dur":8871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061541146, "dur":6267, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061547655, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061541095, "dur":6798, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c7i60zrowo1v.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061548018, "dur":6618, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__32.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061554652, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061547953, "dur":6900, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061554922, "dur":4485, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061559426, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061554893, "dur":4739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yzxvdadx1wuw.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061559702, "dur":5398, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__67.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061565132, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061559675, "dur":5687, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061565484, "dur":8752, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061574255, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061565416, "dur":9069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kzwq165kpxqi.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061574776, "dur":3275, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061578077, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061574730, "dur":3579, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/983eog650b56.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061578390, "dur":5893, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__98.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061584303, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061578357, "dur":6144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a14ksepb0gsb.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061584712, "dur":6497, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues2.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061591228, "dur":172, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061584616, "dur":6785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gomkx7xyft0v.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061591490, "dur":2757, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__37.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061594266, "dur":277, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056061591446, "dur":3097, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061594578, "dur":183, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":23, "ts":1752056061594807, "dur":257, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__92.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061595241, "dur":253, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061595522, "dur":567, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752056061596135, "dur":59, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":23, "ts":1752056061596231, "dur":268, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1752056061596500, "dur":70252, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061398566, "dur":52173, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061452826, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityaot-linux\\Facades\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":24, "ts":1752056061450744, "dur":2961, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061453706, "dur":36037, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061489806, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info" }}
,{ "pid":12345, "tid":24, "ts":1752056061490062, "dur":250, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_15vy.info" }}
,{ "pid":12345, "tid":24, "ts":1752056061490317, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":24, "ts":1752056061490467, "dur":53, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml" }}
,{ "pid":12345, "tid":24, "ts":1752056061490532, "dur":68, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":24, "ts":1752056061490622, "dur":4119, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061494742, "dur":351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1752056061495094, "dur":347, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061495503, "dur":789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1752056061496376, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061496472, "dur":195, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":24, "ts":1752056061496889, "dur":257, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":24, "ts":1752056061497200, "dur":264, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":24, "ts":1752056061497466, "dur":9011, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061506518, "dur":5397, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__63.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061511938, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061506478, "dur":5676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061512492, "dur":7234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__45.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061519749, "dur":273, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061512459, "dur":7564, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061520025, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061520214, "dur":5070, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__7.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061525306, "dur":268, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061520131, "dur":5444, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/unvu3uwlnpzy.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061526206, "dur":4905, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061531129, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061526158, "dur":5213, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061531465, "dur":1447, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752056061532929, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061531429, "dur":1555, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061533063, "dur":3682, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061536758, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061533030, "dur":3905, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061537003, "dur":6975, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061543996, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061536981, "dur":7253, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nqp3qp5ad6sw.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061544445, "dur":3413, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061547876, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061544411, "dur":3709, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061548252, "dur":4521, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__8.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061552797, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061548203, "dur":4843, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xj7cmp094r3w.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061553156, "dur":7773, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061560953, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061553115, "dur":8069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tg0fx1n1w1ut.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061561409, "dur":7279, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__3.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061568710, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061561370, "dur":7594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6edgv1k7gptl.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061569279, "dur":1411, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752056061570712, "dur":70, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061569244, "dur":1538, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061570983, "dur":5418, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__9.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061576423, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061570944, "dur":5714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061576729, "dur":809, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.ParticleSystemModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752056061577568, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061576700, "dur":926, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jf3wjb0lbtlr.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061577630, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061577758, "dur":6849, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__72.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061584619, "dur":146, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061577720, "dur":7046, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iqmxf7rrbell.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061585347, "dur":1232, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752056061584982, "dur":1640, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sli71m7v0fnl.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061586699, "dur":4428, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__7.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061591156, "dur":152, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061586663, "dur":4645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0repauku76o0.o" }}
,{ "pid":12345, "tid":24, "ts":1752056061593518, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__6.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061593958, "dur":277, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__5.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061594284, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061594500, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061594564, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056061594732, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061594882, "dur":499, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__76.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061595415, "dur":292, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752056061595888, "dur":815, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752056061596704, "dur":70068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056061700327, "dur":5521, "ph":"X", "name": "ProfilerWriteOutput" }
,