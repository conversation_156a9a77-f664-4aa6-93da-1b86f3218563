﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void DisplayNameAttribute__ctor_mC6DDF0F7FED605C29AF53560DCFABEA3A552AF4D (void);
extern void MinAttribute__ctor_mF7CC485FE4513A04FCB6896A87A8E4FF2F69EE18 (void);
extern void MinMaxAttribute__ctor_m2B86DBABF344BD1EC6BC481E04B41AE5BB29AC1D (void);
extern void PostProcessAttribute__ctor_mA3FDCA42F863DEDFC594BB5F5818DE320AD154B3 (void);
extern void TrackballAttribute__ctor_m593E6847AC9390ADE5E2A358E69B29967527C2FD (void);
extern void AmbientOcclusionModeParameter__ctor_mFCE7E1380B0CA42D39F6A66C937D656620E79923 (void);
extern void AmbientOcclusionQualityParameter__ctor_m14FB3DF7CE2DBF5F11EB1879DCC6887086870AAD (void);
extern void AmbientOcclusion_IsEnabledAndSupported_m720CFBDB15B6957D9BDD86026B4CEA80ACB7DA3B (void);
extern void AmbientOcclusion__ctor_m56351F92F528AE1C1572715DFC5317DF012CD11E (void);
extern void AmbientOcclusionRenderer_Init_mA3B0F8AE73F6AA694F983B4A5DC87CB7FD6B5CBA (void);
extern void AmbientOcclusionRenderer_IsAmbientOnly_m5A77F8622151A4EA9FE590655B6E7E085A1D7B06 (void);
extern void AmbientOcclusionRenderer_Get_m67BA05B631A6740564AF7B0CAAE58F2CD2EC305E (void);
extern void AmbientOcclusionRenderer_GetCameraFlags_m29C1369C24DAD23DCA98CE634F2156DF19FF3592 (void);
extern void AmbientOcclusionRenderer_Release_mCC9A8A8DAE2CE44CFD3DD09CCA98A38F3B80083A (void);
extern void AmbientOcclusionRenderer_GetMultiScaleVO_m77469D960AE101C9A8A74055CF75096CD62BCF6D (void);
extern void AmbientOcclusionRenderer_Render_m780A5831389C81820FCA3ACF7D80CB60878FF57E (void);
extern void AmbientOcclusionRenderer__ctor_m3350E39318843D75A15F84BE2EF5523D0AEAD28F (void);
extern void EyeAdaptationParameter__ctor_m7C78083B39B535D78273E813C702706DE51FFF31 (void);
extern void AutoExposure_IsEnabledAndSupported_m5AE7A64565EFC721D5D85707F0F585BD18F98C01 (void);
extern void AutoExposure__ctor_m518431E3FB963370308682EA68492306689158FD (void);
extern void AutoExposureRenderer__ctor_m15269270A88D00BE8CCB970F9BF81E65AED4D43D (void);
extern void AutoExposureRenderer_CheckTexture_m93C720C54FF70AFD18F3B8160E2324548A593558 (void);
extern void AutoExposureRenderer_Render_m16316D56F1316E8690CB7B485E4C0D2BC3351270 (void);
extern void AutoExposureRenderer_Release_m199435220AB5F13A9A37E47F92A6484E4FA2483E (void);
extern void Bloom_IsEnabledAndSupported_m9FB08EC848606657761325B79B76E38FC86D2A65 (void);
extern void Bloom__ctor_m392EA2E063E2471A4B4AC223EB07244D048E8E62 (void);
extern void BloomRenderer_Init_mE2A298B0A535B1EA48D0412D28B29585B120662B (void);
extern void BloomRenderer_Render_m1BF0EADC0232A3AB4A69633601499C3C73C962A4 (void);
extern void BloomRenderer__ctor_m1E750605CCFDEB524EB48B46EAA3FCC67A3E7B5F (void);
extern void ChromaticAberration_IsEnabledAndSupported_mD13C38E0BAF647433F02E58083B835311C5C10F1 (void);
extern void ChromaticAberration__ctor_mC334EB12BDE44B8B59E4322021FF201B6E94B10A (void);
extern void ChromaticAberrationRenderer_Render_m53D46F69F3A469D00104C7D8FC26BC8E9F315EEA (void);
extern void ChromaticAberrationRenderer_Release_m073107CECA2CEAE14A260ECAE2D179E738272578 (void);
extern void ChromaticAberrationRenderer__ctor_m58AE6FAB34E606E0B63222E076606FAC3858C031 (void);
extern void GradingModeParameter__ctor_mF96EA897EA9CEF4FBC07C714CDB95CFE02F93B9F (void);
extern void TonemapperParameter__ctor_m00E44E11E8C010507848A1767300E1053679A5F0 (void);
extern void ColorGrading_IsEnabledAndSupported_mA0B5513241A97D08D46AE8840751FED4BA533101 (void);
extern void ColorGrading__ctor_m33D6A092A5F620B80BB9822003AD59DB98CDC31F (void);
extern void ColorGradingRenderer_Render_m3A5815D59F04D8BF0FBFFCE7E86DCDD666E19592 (void);
extern void ColorGradingRenderer_RenderExternalPipeline3D_m4BA3989C71C16FBA2FD6B820D2363FC9620D1BF7 (void);
extern void ColorGradingRenderer_RenderHDRPipeline3D_mB3D126713A64C33C75EF4DAB05CB53FEA2EE5D43 (void);
extern void ColorGradingRenderer_RenderHDRPipeline2D_m1A21E68693FBB0759745896776D49EA280870D75 (void);
extern void ColorGradingRenderer_RenderLDRPipeline2D_m616DBEC56443AF51A5FF9DF1626C915DA6BC6597 (void);
extern void ColorGradingRenderer_CheckInternalLogLut_m4E3605B5F95F0FDDD1030F4C8A5897149BAA3475 (void);
extern void ColorGradingRenderer_CheckInternalStripLut_mBDFEBFA559753E181002AE5AD1C660CE3CE69B59 (void);
extern void ColorGradingRenderer_GetCurveTexture_m23FDC74489540547C8708E78C5B291EA368FB008 (void);
extern void ColorGradingRenderer_IsRenderTextureFormatSupportedForLinearFiltering_mB1C20AE0DF61C5B0D586965F786E0306CB1FB269 (void);
extern void ColorGradingRenderer_GetLutFormat_m79D6D53EF1CEA4D5A2D8EE3A6C70D5C1201D90B8 (void);
extern void ColorGradingRenderer_GetCurveFormat_m7D31D3D15BFD24ADC8AD53769D4D78A3ED575190 (void);
extern void ColorGradingRenderer_Release_m46B7BEFF864C3FCB6616E05EC71E5A8DCB6EB1D8 (void);
extern void ColorGradingRenderer__ctor_m609EFB8891282C1971EF851DC04C12B20CB2A6BE (void);
extern void KernelSizeParameter__ctor_mC9FA15B0A3D6D56495B3DB38EC1E685C9CDAEB00 (void);
extern void DepthOfField_IsEnabledAndSupported_m5B41DD100C9314561E15314BF33D7B5937AD53F6 (void);
extern void DepthOfField__ctor_m5E5C8EDEDCD6643E99AEF4D37073AD7128D765A5 (void);
extern void DepthOfFieldRenderer__ctor_mDD6E938F8A2D32A3F586D5DF97749638EF57EE4F (void);
extern void DepthOfFieldRenderer_GetCameraFlags_mA24EAFABABBC714F206293BF191323730A15ED1C (void);
extern void DepthOfFieldRenderer_SelectFormat_mA727185D2B1E89DC0962E26672D7509147BD462B (void);
extern void DepthOfFieldRenderer_CalculateMaxCoCRadius_m3813326711DC8EDD78C1FB179682F3AD40319593 (void);
extern void DepthOfFieldRenderer_CheckHistory_m911AC1AF4CF4A874FD38C941F5C58C47C7FE561B (void);
extern void DepthOfFieldRenderer_Render_m32989A445B3004E689397E072AC31DA1F456216A (void);
extern void DepthOfFieldRenderer_Release_m71B33FB0AEB8ED07A52ED8AA9FA28C0198B4AC27 (void);
extern void Dithering_Render_mE6124E38F680E37934C3BBD1E8F73D926D28B661 (void);
extern void Dithering__ctor_m143E4229BCAC785FA75CC5EBDAF7BDB6EFD388EB (void);
extern void FastApproximateAntialiasing__ctor_m4C443FEB81618E3B5EA4D2EEA75BB67F4910EEA1 (void);
extern void Fog_GetCameraFlags_m1FE74A3121D526C50A78863CC9ACBC38F0694CC4 (void);
extern void Fog_IsEnabledAndSupported_m032F2A56D8DE3B1FDACAE8174B20C7CEE8325F0E (void);
extern void Fog_Render_m435B9A6B92833EBD9E237F46F4F066F4F03A732E (void);
extern void Fog__ctor_mA98F3BA37BFA53225EE74C53ACF4DEF218211F28 (void);
extern void Grain_IsEnabledAndSupported_mBC536DDC0E38B9D7B18EB436196900F2B6403592 (void);
extern void Grain__ctor_m10C6547CD0B0CFD9BC6D639C1E920434816235DF (void);
extern void GrainRenderer_Render_m0646753B2E6222A1C00F3AEE8A7E567878919B57 (void);
extern void GrainRenderer_GetLookupFormat_mD362BF97912F5B3D7CF6ED8C21906346A7989697 (void);
extern void GrainRenderer_Release_mAC267606A532714D11C5BE1A2B0D284893A5BDD9 (void);
extern void GrainRenderer__ctor_m2265B02AC4735EB4875F8D32A6EBE73BC80403A1 (void);
extern void LensDistortion_IsEnabledAndSupported_mA7204A7B6840B7B2F9A650A03AAC28CE5DC98587 (void);
extern void LensDistortion__ctor_m257562372044829F89320AA2D2CFF46FFB8E4255 (void);
extern void LensDistortionRenderer_Render_m4D3343279E5BF87021BAF56EC0388DC1395DF52A (void);
extern void LensDistortionRenderer__ctor_mA5092D594C99AF56A5DBBE5C2D4377B73006D05E (void);
extern void MotionBlur_IsEnabledAndSupported_m5028AFA3FA6894818C3DE261DD71B3785B6D4FA3 (void);
extern void MotionBlur__ctor_mC86A4CC11E1CF95CC533BC29A46BA28914B2224B (void);
extern void MotionBlurRenderer_GetCameraFlags_m4DB815C78AD85619B40F4797D896149E52D450CE (void);
extern void MotionBlurRenderer_CreateTemporaryRT_mD976DBC2C27CEEA14915FF82A210B39CBC5E086D (void);
extern void MotionBlurRenderer_Render_m2BFC40DD564E324ACDEDB6B4E13E92057F96DA25 (void);
extern void MotionBlurRenderer__ctor_m3AF94ED2942706ADA6EC007A084462BE9047D774 (void);
extern void MultiScaleVO__ctor_m9BFC7ADD2493601C3DB9371EB189D4635612F1DF (void);
extern void MultiScaleVO_GetCameraFlags_m8E4A64719125B3893304CA2A2EC1F3C85F7687EF (void);
extern void MultiScaleVO_SetResources_m84240174825540B7F4C74D968DC59AD2ECC591C1 (void);
extern void MultiScaleVO_Alloc_m6CBEE89199B35D51B3647752CBBBE5FF548BF5B4 (void);
extern void MultiScaleVO_AllocArray_m36BD4376B54B4A1E87ACA920D78C2E8E33ECCD72 (void);
extern void MultiScaleVO_Release_m0E5ED7DC133AB8FD150B5FFE16BAA0E2DF2D4230 (void);
extern void MultiScaleVO_CalculateZBufferParams_m22B0A0F3F81F8B763B4FC56576A24CEF93836E8F (void);
extern void MultiScaleVO_CalculateTanHalfFovHeight_mC39EF131E77E87D11C46F6F0407FFF242D775BE8 (void);
extern void MultiScaleVO_GetSize_mE37BB8127DB121C2D77ACD8C28FB07142364C992 (void);
extern void MultiScaleVO_GetSizeArray_m69FBD800D97C26290A466A097E7E50A2CE9CDFFA (void);
extern void MultiScaleVO_GenerateAOMap_m5F19CF58D1022B3A75F65C0DBC8BF6F77CBA4BD1 (void);
extern void MultiScaleVO_PushAllocCommands_mBEB5A921C0C0E84B9E42F6FAD1B53492DAAEA31E (void);
extern void MultiScaleVO_PushDownsampleCommands_m2CDAC714FB28F0F9C7ABB7CC7A18E40C1402CEDB (void);
extern void MultiScaleVO_PushRenderCommands_m08B92180E92671B64E66E4EE3000DBC68C3598AF (void);
extern void MultiScaleVO_PushUpsampleCommands_mE4D317E8951EC0CF04E6AECBA833A24332AD2BB0 (void);
extern void MultiScaleVO_PushReleaseCommands_mBE63709DDB6352D2F7025AE89BDE97BE0909C1AC (void);
extern void MultiScaleVO_PreparePropertySheet_m475E87B885128A12DA6B62324E654E9CF9B30C76 (void);
extern void MultiScaleVO_CheckAOTexture_mE6F5AA4D7F06D3BAA1FABE1D8991C36104CD7B6C (void);
extern void MultiScaleVO_PushDebug_mCF96B0B7C0E4CF071BD1658991DCC6570BCDB137 (void);
extern void MultiScaleVO_RenderAfterOpaque_m5DBCFF788852A66C0887BEF1C47531C556C58A96 (void);
extern void MultiScaleVO_RenderAmbientOnly_m4BED5F04E1CAE684E5DA32CB487A8BF192FCB106 (void);
extern void MultiScaleVO_CompositeAmbientOnly_mEAC61D7DC1731ED82CF03EA69F18E95E375FA64A (void);
extern void MultiScaleVO_Release_m9311D4CEA6B8EB25B19A66D012720DD084696D2C (void);
extern void ScalableAO__ctor_m2E13FF1404D88C933436F64E64446A3E8C58EC25 (void);
extern void ScalableAO_GetCameraFlags_mF3DC2F17B0C42898A785241EAB4762496A270FA3 (void);
extern void ScalableAO_DoLazyInitialization_m9CDAE5877C189D77F1F5E7FDAF0D9A6A4EDB1825 (void);
extern void ScalableAO_Render_m28BC49E3F61595E532B03D0588B47E377C5C64DA (void);
extern void ScalableAO_RenderAfterOpaque_mA72DCBC2E4E26729754816757F95BB5A445F2EB8 (void);
extern void ScalableAO_RenderAmbientOnly_m1F800BE3EBE499449E0D10F9332D2225CBA17459 (void);
extern void ScalableAO_CompositeAmbientOnly_m6BDA0561AF5B1D66CC0D7EB7730F0FD8BDE332E1 (void);
extern void ScalableAO_Release_m9A84BB9F350E9F1573DF794DD79CF6C89ACA522D (void);
extern void ScreenSpaceReflectionPresetParameter__ctor_m788759481354D194C96F8569A5FA0CF600DD9959 (void);
extern void ScreenSpaceReflectionResolutionParameter__ctor_m13C6DA3B4C340969D44063CA3750D4563EF4ABC2 (void);
extern void ScreenSpaceReflections_IsEnabledAndSupported_mCDB8B7AE8CC67387ACF1615CB34140C958117C11 (void);
extern void ScreenSpaceReflections__ctor_m00CB810B6E82A82F1D61C5573C138DD088851D7A (void);
extern void ScreenSpaceReflectionsRenderer_GetCameraFlags_m23B86CC0CEA0E26EB5A2051FEC23130634FB0B9B (void);
extern void ScreenSpaceReflectionsRenderer_CheckRT_mC9AB522FC1B490E25DAF07666863F6572954DA94 (void);
extern void ScreenSpaceReflectionsRenderer_Render_m852E13492673A7D34B735265B7B5D7BE4B533D1B (void);
extern void ScreenSpaceReflectionsRenderer_Release_m2B7890B1A7F4B90CFE4D086B85755435840CE5FA (void);
extern void ScreenSpaceReflectionsRenderer__ctor_mE1144CEA7E4E5AE7FCE33024EC6C0754FCA49397 (void);
extern void QualityPreset__ctor_m7AC033D833C201BBECA54DF1886D30824A2955E8 (void);
extern void SubpixelMorphologicalAntialiasing_IsSupported_m1705607A7079C11A6FAAEB07746977F804A8ECFF (void);
extern void SubpixelMorphologicalAntialiasing_Render_m5C772116EF78929A662C6C1EE168A998AEE3F028 (void);
extern void SubpixelMorphologicalAntialiasing__ctor_mFD4BB185541D16D22FD47ABE4427F81D531A50A5 (void);
extern void TemporalAntialiasing_get_jitter_m95640074A28A81D1CFD87CFBE39B9EA8F514C3DC (void);
extern void TemporalAntialiasing_set_jitter_m813C3D64082BE1924BB5B7A1D9A8B1FF9877AABB (void);
extern void TemporalAntialiasing_get_sampleIndex_m6F27F29D737C6529F14ED99D25E1A60BB1A77348 (void);
extern void TemporalAntialiasing_set_sampleIndex_mF0B4CE95B0AAB362A3A6DDE6F77E274A9BBB8ADE (void);
extern void TemporalAntialiasing_IsSupported_mAE5BCF24068A7E94E3BB643E549AA055192C045E (void);
extern void TemporalAntialiasing_GetCameraFlags_mFDB0F48DA9DD43DC96F0001D8AEC3EC0BDF8B9AA (void);
extern void TemporalAntialiasing_ResetHistory_m1E1611D2244E90EB7C3CC493B98C0AA0A1A52010 (void);
extern void TemporalAntialiasing_GenerateRandomOffset_m8C20703188A9078739CEF48A902F636142E06712 (void);
extern void TemporalAntialiasing_GetJitteredProjectionMatrix_m05C5BC645B3761AFDDEBF885743BBB71419F12F1 (void);
extern void TemporalAntialiasing_ConfigureJitteredProjectionMatrix_mD1658F905FA0B40616F94A72596803C4DFD11476 (void);
extern void TemporalAntialiasing_ConfigureStereoJitteredProjectionMatrices_m6778161075072CB286B1C1082F1535B39D13E546 (void);
extern void TemporalAntialiasing_GenerateHistoryName_m39F69FC9226519CDFE2BAECF99334772C7EBD22D (void);
extern void TemporalAntialiasing_CheckHistory_mA35B44ECD13BB4F9CF19E766131FFC11CC5B41AE (void);
extern void TemporalAntialiasing_Render_m1F5CDB905DA7AB9A5D2C8335E7F40A0B984C767F (void);
extern void TemporalAntialiasing_Release_m7F07A845A790B3E98E9618400D360CAC8BCF44B7 (void);
extern void TemporalAntialiasing__ctor_m1F3DCAB166187D2B412DFE69787E46242313D9FD (void);
extern void VignetteModeParameter__ctor_m4D63838C20AE0CB9DDD27D1D31235D918456F304 (void);
extern void Vignette_IsEnabledAndSupported_m2D67AF6B97A94BBD4B2A7FDEAA4028847C5FAFB4 (void);
extern void Vignette__ctor_mB0325E1C20E1C0EACC4E22F90C88C2376A0DC537 (void);
extern void VignetteRenderer_Render_mB738733CA260ECFBA104310C8283AB153FA32110 (void);
extern void VignetteRenderer__ctor_m2CA1FD8CC001CF2FF6CF2C6D4030A42B8B41E89D (void);
extern void HistogramMonitor_OnDisable_m230FF2666443F85287241087449BA4CE5BB789DC (void);
extern void HistogramMonitor_NeedsHalfRes_m73CFC4DC4E26DED01E7819E191E727BA7126073F (void);
extern void HistogramMonitor_ShaderResourcesAvailable_m9A5B187C0479D7E3DF5248EA991F0454FE9B5949 (void);
extern void HistogramMonitor_Render_m500233055734F82F0F28E848401821AB906B55FE (void);
extern void HistogramMonitor__ctor_m076097076CBE7E0589A84CA904C357FBE4822551 (void);
extern void LightMeterMonitor_ShaderResourcesAvailable_m0C0CB920F8EAA6888F402D529CE55422F9569406 (void);
extern void LightMeterMonitor_Render_m55BC89DB32BE88F0059E446D90612DFBA6F00DC3 (void);
extern void LightMeterMonitor__ctor_mBB777FF1522832DE5450C9A5DD64E8F8D30C7C11 (void);
extern void Monitor_get_output_m47DF8F109DA67DEF62A5993E579B62750DA196C0 (void);
extern void Monitor_set_output_mE14C51217A4CCAB88EC656889034C4D55764AECA (void);
extern void Monitor_IsRequestedAndSupported_m54722701F2E06E92EEC20B760616F534C58C07EF (void);
extern void Monitor_NeedsHalfRes_m6288FC8B3CFE546BFEE1590D91DE4CD385312E0B (void);
extern void Monitor_CheckOutput_m413208864EA3CACC6D40DFAEF1DB04DC0A30150C (void);
extern void Monitor_OnEnable_mD8E42BFFD458B4A7D2B3975C01DD33BFA4D1A54C (void);
extern void Monitor_OnDisable_mA3610794EB2C764C2F7892CA17BB71D772A2C943 (void);
extern void Monitor__ctor_m6EF591E063563E2D6DC7B8884243426EAC35561C (void);
extern void VectorscopeMonitor_OnDisable_m001686957BD5361704A15E2E4B386DDD3D02252B (void);
extern void VectorscopeMonitor_NeedsHalfRes_m723A2717D7FE50FF8A2D9027277ADC22A150BC64 (void);
extern void VectorscopeMonitor_ShaderResourcesAvailable_m528D2CE0BBA9B10610EE653F824AA9B1D1DC2661 (void);
extern void VectorscopeMonitor_Render_m84CA6B45C4331890F0455119D86C1A07EB966B5B (void);
extern void VectorscopeMonitor__ctor_mC27FEEE84A6557F3B4BACA78805AA2D7DADF39A7 (void);
extern void WaveformMonitor_OnDisable_mBDEB4FC072BE8751732B2E9EE6A16E587BA75B8B (void);
extern void WaveformMonitor_NeedsHalfRes_mA40030095FA31CFD4D978649A9B82D4ADCE7697D (void);
extern void WaveformMonitor_ShaderResourcesAvailable_mFA14E2CE435250C9C197E09F212F0C14E823082D (void);
extern void WaveformMonitor_Render_m52105139C3FF21B5598DA81E5F456294F9759E91 (void);
extern void WaveformMonitor__ctor_mB3BA30A53C9FE94CF150E6768D3229A08BFDDD77 (void);
extern void ParameterOverride_OnEnable_m0D67F5FA71AEEAB671BF019E14CD91A5EF213A66 (void);
extern void ParameterOverride_OnDisable_mB1BB0C4E6E712345795DAC5066843364DBEF65FC (void);
extern void ParameterOverride__ctor_mF2BC731CBB924D3621F1B84DFFCA4F5CDB42C0E2 (void);
extern void FloatParameter_Interp_m1434238DF5487DF3540A5A71E6B59C533A05122F (void);
extern void FloatParameter__ctor_m9DB1EF263F257D4CF8C8B6BE717EA1D129CEF984 (void);
extern void IntParameter_Interp_m51A6FD54C0AF0F087F75835AD873DBD6417D09E9 (void);
extern void IntParameter__ctor_mD0B577F7B6F29156C6F34A31852CE0015B544312 (void);
extern void BoolParameter__ctor_m3D75EA93FDC7D88D346025CB612A459B92915FE7 (void);
extern void ColorParameter_Interp_m4B0D83B2F72792D5BF6C537BDD0992AC9586442D (void);
extern void ColorParameter__ctor_m30D0D993F9B30802429AA3176816DFC889D6DAA3 (void);
extern void Vector2Parameter_Interp_mB14285E63AE62C206098BDCA80211C3F90041B34 (void);
extern void Vector2Parameter__ctor_mBF44FC58FB4108A066A973BE3891A8B85479214A (void);
extern void Vector4Parameter_Interp_m0899065B59819646F5192F92ACAF28D1F15DF37F (void);
extern void Vector4Parameter__ctor_m8C8DED1875F2A781AC4F18369E08DDA46C13D243 (void);
extern void SplineParameter_OnEnable_mD33338349D3C87DA7758922C0CF7E03FF626E5B1 (void);
extern void SplineParameter_SetValue_m536D3CDC09CA93E5F1E06F5C327D183F5611FBA2 (void);
extern void SplineParameter_Interp_m7FDEAE34BED3B0731915281DA8B0D34A360AF1E8 (void);
extern void SplineParameter__ctor_m11C763DDA15AAC2E1E8A1AAD11E0D8C2E7B11520 (void);
extern void TextureParameter_Interp_m6BDA698BA090B8731A7DECDD1A7D654207CE6EB4 (void);
extern void TextureParameter__ctor_mFB8A59BFF1213A53DD6C87017E3025607C25AD0A (void);
extern void PostProcessBundle_get_attribute_m23310FCCFEE897EBF93C36296D6C5D565B4C19A0 (void);
extern void PostProcessBundle_set_attribute_m2675212E0F5625E6B7436C50F63DB75E26045434 (void);
extern void PostProcessBundle_get_settings_m5BA726BCC8ADCCEA4AD25AD51798950474EFC046 (void);
extern void PostProcessBundle_set_settings_m954BB49AE94AC89911931978FABA469BBBBCC8BF (void);
extern void PostProcessBundle_get_renderer_m9B9D011FC8ED8B384FE16AEC5A5955A87243A61C (void);
extern void PostProcessBundle__ctor_m980F8608CC22C6F40C0382EA3208A4D6889F33FD (void);
extern void PostProcessBundle_Release_m31FD40E9428001418B7A2BB323C7B41202AE7EB6 (void);
extern void PostProcessBundle_ResetHistory_m5EB7270369216D54DE183E1A91C530F562BBCC12 (void);
extern void PostProcessDebugLayer_get_debugOverlayTarget_mEE851EF1356AE899D0EF902AD0E0966B16AFD940 (void);
extern void PostProcessDebugLayer_set_debugOverlayTarget_m7566FD9B25470C7ACCF14606AAF975AE00715332 (void);
extern void PostProcessDebugLayer_get_debugOverlayActive_m7990B750167EF1A31F8A3F96B539B322D3F7FC84 (void);
extern void PostProcessDebugLayer_set_debugOverlayActive_mC83941C3612A2745BD1B6C771FCF0053B6D3BC77 (void);
extern void PostProcessDebugLayer_get_debugOverlay_m50CBB64D3FEE070BD29DEC24C8CD57DD37EFE1C3 (void);
extern void PostProcessDebugLayer_set_debugOverlay_m8A972C54696447B69FBC597FA9E67C3D9B7C322A (void);
extern void PostProcessDebugLayer_OnEnable_m387008B7ED2B3F492C6D5F57EEC62057BF293C3E (void);
extern void PostProcessDebugLayer_OnDisable_mC4762A0AEBE23067EAE67F82A7BCCFE8BD192017 (void);
extern void PostProcessDebugLayer_DestroyDebugOverlayTarget_mA98B3230B16094649A5149FA8FD978634B755846 (void);
extern void PostProcessDebugLayer_SetFrameSize_m38E163EE3B51AA81D8C2AF7B2C5D58498F64287A (void);
extern void PostProcessDebugLayer_PushDebugOverlay_m43912718B6ABE4DB9485F8A51A5B12B879186697 (void);
extern void PostProcessDebugLayer_GetCameraFlags_mAD93FA644A0C5AC136F382ED927B6E3237336EFD (void);
extern void PostProcessDebugLayer_RenderMonitors_mD366E3E3E7D8F0F1A686CB33D352DDBE4B1B8DE8 (void);
extern void PostProcessDebugLayer_RenderSpecialOverlays_mD23CFE4FC14A61B52A41E88A03B85174BEE1D211 (void);
extern void PostProcessDebugLayer_EndFrame_mDBDCD82C1BE519E65293D44A840500FE4B6D58A8 (void);
extern void PostProcessDebugLayer__ctor_m5A99D89DCB5BFFEE99D91396CED829F86B24EE61 (void);
extern void OverlaySettings__ctor_m88E7EAF9BE31DF010DA0CC32DBF93ACF0C6CE22D (void);
extern void PostProcessEffectRenderer_Init_m174682ACFF9ABF0E0FC7E7684A84C9442A30D19C (void);
extern void PostProcessEffectRenderer_GetCameraFlags_mC65D31F164C708C88C624B63E25FCA32C3F0DDFD (void);
extern void PostProcessEffectRenderer_ResetHistory_mB97A23456B52D5B555005D46FB5C38C7887DF918 (void);
extern void PostProcessEffectRenderer_Release_m9464A96E9F63258C17DA6480A46A13F06FC8EAEB (void);
extern void PostProcessEffectRenderer__ctor_m0E9A4D08F3DDABC9B7C4D3E81DF54F43D9729538 (void);
extern void PostProcessEffectSettings_OnEnable_m53B71C90196D55E0557B09526B62F1964F90DE60 (void);
extern void PostProcessEffectSettings_OnDisable_m9FAE66EBBEACD6D7BE2ECCE9FBAAEF0A0C54FDE0 (void);
extern void PostProcessEffectSettings_SetAllOverridesTo_m0EFF9FC7563FE058E728801E91F01808431BF140 (void);
extern void PostProcessEffectSettings_IsEnabledAndSupported_m3A0E447900FC7C1DFA163D851CACD45C7836760D (void);
extern void PostProcessEffectSettings_GetHash_m4E159616856B60028F64AB21661EBABA8072D9E8 (void);
extern void PostProcessEffectSettings__ctor_mD40D0A07297AB8295240B8AFDA7A961384C4F82D (void);
extern void PostProcessEffectSettings_U3COnEnableU3Eb__3_2_mE5072CBCD4F5E941C6DE52C0A9EC5BEAD6A7AE9A (void);
extern void U3CU3Ec__cctor_mE68E1D1B36956CC366071123266B1ED4686FE489 (void);
extern void U3CU3Ec__ctor_mF340714BAF696077FEEB50C8F0921CBADB691665 (void);
extern void U3CU3Ec_U3COnEnableU3Eb__3_0_m85647CDDCD9A9892F0F99E85586B15BF0AD725F3 (void);
extern void U3CU3Ec_U3COnEnableU3Eb__3_1_m50822ACB3F5C9C9F17FB2CD5DD51E85C307D1ED0 (void);
extern void PostProcessEventComparer_Equals_m2166068BCF36DB77A9E738FC50C0A77AC8C4C96D (void);
extern void PostProcessEventComparer_GetHashCode_mDF3708FBF53C71BC1FA3024694AB19BD0553D45A (void);
extern void PostProcessLayer_get_sortedBundles_m1C832D9F4C17EBC0E9E8704923DE1876634588B9 (void);
extern void PostProcessLayer_set_sortedBundles_mF014B030591011A9935170FCC8747EDD3F6075FF (void);
extern void PostProcessLayer_get_cameraDepthFlags_m7B14DC4DFC770826A06AF45843738D2EC68EDD01 (void);
extern void PostProcessLayer_set_cameraDepthFlags_mBC9A8CE97A08E406A3DF3F3F6AAA157330316578 (void);
extern void PostProcessLayer_get_haveBundlesBeenInited_m8FC1BB2B461C9427AE234EAB8B3C83339B0C544B (void);
extern void PostProcessLayer_set_haveBundlesBeenInited_mC73845F7B1DF581C092B4A735244B17E75D979FA (void);
extern void PostProcessLayer_OnEnable_m9CF9B052529EEF6E2016CE00FFBA81C130342EE2 (void);
extern void PostProcessLayer_InitLegacy_m354AC408E683A1B536D8C30BA93720037025BB5E (void);
extern void PostProcessLayer_DynamicResolutionAllowsFinalBlitToCameraTarget_m9AACAA266C7632AF1B271CA5A4B3ADB8713BF199 (void);
extern void PostProcessLayer_OnRenderImage_m88AE7F294CBCA2042C76BC0B45544109752B4FA4 (void);
extern void PostProcessLayer_Init_m0FDB593A6B13381DAFAA2078CCA2F913AAC983F9 (void);
extern void PostProcessLayer_InitBundles_mF98EC0C218D2F1E0D21B643EEBE109CA4F8C71B3 (void);
extern void PostProcessLayer_UpdateBundleSortList_m5FC7EB336E878E425F971CB0BDB37FC55CF40A62 (void);
extern void PostProcessLayer_OnDisable_m8F51B4254A45A8E2A323478871B69427A98F7094 (void);
extern void PostProcessLayer_Reset_m96933BBE49EB42C0B5B754EEF3B22B33C0B20B1C (void);
extern void PostProcessLayer_OnPreCull_m0E5B78C5458852CDF36759FA5E785109E23FA426 (void);
extern void PostProcessLayer_OnPreRender_mB714954409087F5BA3EF538B47338B77F5E20B2E (void);
extern void PostProcessLayer_RequiresInitialBlit_m66CC1F4AC2DC78365D1F134E091EE989EF3224DF (void);
extern void PostProcessLayer_UpdateSrcDstForOpaqueOnly_m06DEFDC679EDCE932F13D9258CD4B159C7172DF3 (void);
extern void PostProcessLayer_BuildCommandBuffers_m8C6C6754F27A786AD80D12898F26711769FCE03D (void);
extern void PostProcessLayer_OnPostRender_m3EF2E660A385A6FE533503FCFBAE2481AB2767F8 (void);
extern void PostProcessLayer_GetBundle_mFE6B4B8D6F1300AB3A011A60E25FB3A9C7EF4A67 (void);
extern void PostProcessLayer_BakeMSVOMap_m2103A1A15DE80E0036FE655A19740615E9196F7D (void);
extern void PostProcessLayer_OverrideSettings_m49989C317280F87560366678EBCFF62739921F7C (void);
extern void PostProcessLayer_SetLegacyCameraFlags_m72D14BAB60AC3CEE06CEFF07D112D46486A2F958 (void);
extern void PostProcessLayer_ResetHistory_mB886C39683321E5A65C86CE6D13860B7DEA79100 (void);
extern void PostProcessLayer_HasOpaqueOnlyEffects_mFD8E5AB8219E71D4464704477E0EFF8A42D94F6B (void);
extern void PostProcessLayer_HasActiveEffects_mE7155905AC4D8288AF4CF87F542511377F110AB7 (void);
extern void PostProcessLayer_SetupContext_mB51E288FAF90059C947F7B56982611A62D92B190 (void);
extern void PostProcessLayer_UpdateVolumeSystem_m67096C3D2687CA32B0CAA5EEC3EFDBDCD1156DD2 (void);
extern void PostProcessLayer_RenderOpaqueOnly_m3A58A1E06E6DBA8D582F18BBCB1BCF58B702B508 (void);
extern void PostProcessLayer_Render_m9AD8F461328CF3AAD58078BE9959D39A5DB20191 (void);
extern void PostProcessLayer_RenderInjectionPoint_m42D83392ABD881E8434BABB7F8BC46E548DE8EBB (void);
extern void PostProcessLayer_RenderList_m478A2ECAB77361DB4A85AE3B2A5FD55A65D6D851 (void);
extern void PostProcessLayer_ApplyFlip_m64131F71DAA216E393712E1B43656056B16F6DFC (void);
extern void PostProcessLayer_ApplyDefaultFlip_mA6FF046E1DC3BABDAE6ABEB463F139329516B2DD (void);
extern void PostProcessLayer_RenderBuiltins_mB9B7DA7EE55CAC51BF153F488BFCAF5800D3FFB0 (void);
extern void PostProcessLayer_RenderFinalPass_mA79AF4EF4D050FED050FF29356384FE8F681044F (void);
extern void PostProcessLayer_ShouldGenerateLogHistogram_m69235046FAF79ECE1E98A97DB703550D1AF56BDD (void);
extern void PostProcessLayer__ctor_mD8712392B04023FC43F79821EC8A498B33043683 (void);
extern void SerializedBundleRef__ctor_mF79561705E11260D6DE0F7022B78D6CBFCD75BC9 (void);
extern void U3CU3Ec__cctor_m7DC9E4B7F3BA5872200F1537B2F1F5FEB649C436 (void);
extern void U3CU3Ec__ctor_m14CE645D40F0ED598B358C218449CC214E67F6D5 (void);
extern void U3CU3Ec_U3CUpdateBundleSortListU3Eb__54_1_m6F744BBAF634606AB77DCEFB1F8645805257300D (void);
extern void U3CU3Ec__DisplayClass54_0__ctor_mE4663058DF29F83DC658899887FEA21A00782E5C (void);
extern void U3CU3Ec__DisplayClass54_0_U3CUpdateBundleSortListU3Eb__0_m68FE9719E12A93BBC74A3446509B93A404D9E081 (void);
extern void U3CU3Ec__DisplayClass54_0_U3CUpdateBundleSortListU3Eb__2_mD7BD333124C1F57B7842028BF5E1B53EB8BBF368 (void);
extern void U3CU3Ec__DisplayClass54_1__ctor_mAC82F6C1E46CBDE83CFF53574D9910FADB232983 (void);
extern void U3CU3Ec__DisplayClass54_1_U3CUpdateBundleSortListU3Eb__3_m240E53745432F10C5F771D79E4B629B46E665D41 (void);
extern void U3CU3Ec__DisplayClass54_2__ctor_m36472C800FCB96E7021A9ED363DBCDFE8102F00F (void);
extern void U3CU3Ec__DisplayClass54_2_U3CUpdateBundleSortListU3Eb__4_mDAA5434E8160298ACAEE90154CA37D7DE54DEC84 (void);
extern void U3CU3Ec__DisplayClass54_3__ctor_mDF80A7734D48DF6C65686CEFA8DEFEE73C64EC51 (void);
extern void U3CU3Ec__DisplayClass54_3_U3CUpdateBundleSortListU3Eb__5_mEF229FE5216D4F13351BCF429CAB0C4B83462935 (void);
extern void PostProcessManager_get_instance_m8C56D207C6B453F8E34E8849FA2BDF8BDF19CA9C (void);
extern void PostProcessManager__ctor_mE18C53FBF36094C52BF5E888AA8E399423BE6688 (void);
extern void PostProcessManager_CleanBaseTypes_m3CE8A80B457017D42537722008D62AD735AD7E02 (void);
extern void PostProcessManager_ReloadBaseTypes_m1945136D79E4679F586C858EE2A72631A3FF9360 (void);
extern void PostProcessManager_SetLayerDirty_m85F4D44F755A2E86AF17993DDA7A2CD47ECF568B (void);
extern void PostProcessManager_UpdateVolumeLayer_mE7C602B2A996873EFEAE5619F4E410AC55CDF9F9 (void);
extern void PostProcessManager_Register_m1C449C90BAD2503D192D361F09DE5B8FD4E8D1D0 (void);
extern void PostProcessManager_Register_m8A5D93C913BA9E62722006B1ED8C5C7BD2A5C36A (void);
extern void PostProcessManager_Unregister_m96F25B0BB879AA90EFAFA0CC051EFC96B3B8982F (void);
extern void PostProcessManager_Unregister_m49EE0FFAD8D79AB25CCF12C82C17C831ADE9C79F (void);
extern void PostProcessManager_ReplaceData_mFCAC48F8D1C2581BDB1A72670EEB91DB2DCF5426 (void);
extern void PostProcessManager_UpdateSettings_m4B9B9670DEDA150A20023F98DEB45DA98F120EEA (void);
extern void PostProcessManager_GrabVolumes_m1AF11581681DB4092AE61C86D06F2463A36F7814 (void);
extern void PostProcessManager_SortByPriority_mC460E161FD3D1293B46CFC546A8E60E0C53B7B15 (void);
extern void U3CU3Ec__cctor_m72950DE1518E19ECC6261EA8098C97085DA5BD25 (void);
extern void U3CU3Ec__ctor_mBDFDD56BBE85E5B393B14B106BAEB772C3CA95D4 (void);
extern void U3CU3Ec_U3CReloadBaseTypesU3Eb__12_0_m865537CCEE58BF53B4AC1A1EF580E114A31BAE85 (void);
extern void PostProcessProfile_OnEnable_m23364CDE321796BC226CBEFAF9DC6F1323938CDC (void);
extern void PostProcessProfile_AddSettings_m610279C7E82C2F2E9156FF80FE7F6C3A2FC1F1C3 (void);
extern void PostProcessProfile_AddSettings_m7446918C8FB730367D486136D3DF233B57A5FCD8 (void);
extern void PostProcessProfile_RemoveSettings_mA0FB19DEC43521661C67F12EF6A13DB3F8D3B9EC (void);
extern void PostProcessProfile_HasSettings_mAAAD536DF4C2CBD9EB73C2683D007F65297BD8BC (void);
extern void PostProcessProfile__ctor_m665590DA298A7A9BB57B1C54FE52F252C1B399DE (void);
extern void U3CU3Ec__cctor_m78C889B816DFCBD83DB0DB0C43FC57BC10969904 (void);
extern void U3CU3Ec__ctor_m28471842FE201AEF91CB04F5F8FBABF89DA37BB7 (void);
extern void U3CU3Ec_U3COnEnableU3Eb__2_0_mEBC7A0AA00EBF9BFA5042D81206E56B406A1FCD7 (void);
extern void PostProcessRenderContext_get_camera_m03C2533C3B4CC5300541CEC486849C58B1F8BBAE (void);
extern void PostProcessRenderContext_set_camera_m616159FD16EF298057D8C92C9D4E04DC583A35CF (void);
extern void PostProcessRenderContext_get_command_m028BE33B6194640A1DE901A6F935658034A3E2CD (void);
extern void PostProcessRenderContext_set_command_m383214635AE4777FFE746270157B265E6CB3A799 (void);
extern void PostProcessRenderContext_get_source_mD66060FCBE2897F0510AE91E86370440226FA430 (void);
extern void PostProcessRenderContext_set_source_m8747B57F6213BC84C7C2990508D029FB4D31C3DE (void);
extern void PostProcessRenderContext_get_destination_m9A1924F891A773105919295D97A474741CAB9F28 (void);
extern void PostProcessRenderContext_set_destination_mED49AE5FCE8CEB9D8B64AA82E1693D4913D48759 (void);
extern void PostProcessRenderContext_get_sourceFormat_m5EB6DD7D586BC0E5B3E3C385CF96AC63575ED61B (void);
extern void PostProcessRenderContext_set_sourceFormat_mFBDC1C325F8E32DA2A40C784659D2DD8BA057258 (void);
extern void PostProcessRenderContext_get_flip_mFE0933034DE2E1C6174C09EED54F03C472D29C32 (void);
extern void PostProcessRenderContext_set_flip_m91DFA83ED3118BB02CF2849D2177C65D186F3E11 (void);
extern void PostProcessRenderContext_get_resources_m89879DF69E4B910F9EE3008AB8DC60B732ABF02A (void);
extern void PostProcessRenderContext_set_resources_mD928596BD2CF34330CA3F8FA83845C9BB39E5651 (void);
extern void PostProcessRenderContext_get_propertySheets_m60E7825143611FEC183803150D8F7C2785514D79 (void);
extern void PostProcessRenderContext_set_propertySheets_m06436C85901FFAF383447F35F3A19909E1C60344 (void);
extern void PostProcessRenderContext_get_userData_m9C88F1615E7C029E65F8A1BA3C0D22986FE27B98 (void);
extern void PostProcessRenderContext_set_userData_m1D9CEBBB566FC3841231E033269B030FDBE98431 (void);
extern void PostProcessRenderContext_get_debugLayer_mD44CD6FBE77E24B243E0EDE0A332A6BB32AA6C25 (void);
extern void PostProcessRenderContext_set_debugLayer_mF9A346FF61AD7CD0AC5605BD3B2A2007084CFA5A (void);
extern void PostProcessRenderContext_get_width_m551FAECA1D38B547E9337BEC7316D6B113B00F03 (void);
extern void PostProcessRenderContext_set_width_m1613BDCAF13B96F72BE7A91E82E9EB6D1C4C39EF (void);
extern void PostProcessRenderContext_get_height_mECB24899496181711525B9E8204F8A85AAA84181 (void);
extern void PostProcessRenderContext_set_height_m4CAF4DB1F50557C276D2683C799FC9CFC19B357F (void);
extern void PostProcessRenderContext_get_stereoActive_m54D0889CF302150E3850FD7BA9AF72BB9EE94C7B (void);
extern void PostProcessRenderContext_set_stereoActive_m133C99DFFB79D8C1D33176258C5A96D8587CDEB0 (void);
extern void PostProcessRenderContext_get_xrActiveEye_m1ED0BDA4DC07BAD4096F3B66DF35520815C7F208 (void);
extern void PostProcessRenderContext_set_xrActiveEye_mA344A4FBC049CA99096413FBE81F0F8F9A12D9A8 (void);
extern void PostProcessRenderContext_get_numberOfEyes_m172652A2003AFD49FB6ED64C3F46249B92D21AE4 (void);
extern void PostProcessRenderContext_set_numberOfEyes_m047ECAB2F0CE41026EECD96F2FDAC3B49AB178B9 (void);
extern void PostProcessRenderContext_get_stereoRenderingMode_m674A9251277DC05913F1EA02B6E5ADA1AE4E00F5 (void);
extern void PostProcessRenderContext_set_stereoRenderingMode_mCCE43DEFBEFEEAE8D4512991FA5117C781710287 (void);
extern void PostProcessRenderContext_get_screenWidth_mF6EF29A7E49F6132C7376A841571FB65A1573147 (void);
extern void PostProcessRenderContext_set_screenWidth_m2472914DC0C907A2A78B7BB990F3DBE9DE785C5C (void);
extern void PostProcessRenderContext_get_screenHeight_mFF2D3EF9A84D53D9125EF96DD746CF8B257687FD (void);
extern void PostProcessRenderContext_set_screenHeight_m3D69896EFE6A480285D300EA3E99164661144FC4 (void);
extern void PostProcessRenderContext_get_isSceneView_mADB2A9705C42D62BCB2DD13C1A64202A7BF32801 (void);
extern void PostProcessRenderContext_set_isSceneView_m25BC585A98717333E55435BF060FBC8352979120 (void);
extern void PostProcessRenderContext_get_antialiasing_m8FA450C8086C96DB987CF8F9B707DA5CFB7AA108 (void);
extern void PostProcessRenderContext_set_antialiasing_m487F83A7E53C46AE4C2015794DA6568950E3C181 (void);
extern void PostProcessRenderContext_get_temporalAntialiasing_m18BFEF6B423A28BD505D611A15643580B8E6CCD7 (void);
extern void PostProcessRenderContext_set_temporalAntialiasing_mF2B2A0D8F39F49690FD4F76B3347C4D168B22597 (void);
extern void PostProcessRenderContext_Reset_m69F96AA7B7CCE9927AB6061B887CF81F8AF06BCB (void);
extern void PostProcessRenderContext_IsTemporalAntialiasingActive_m835118A884E10E19B5625C77EC029B088ECE11E3 (void);
extern void PostProcessRenderContext_IsDebugOverlayEnabled_m10FA8ED9E7FE01A6B9FB48BFF9B19A5F70908927 (void);
extern void PostProcessRenderContext_PushDebugOverlay_m543E6F22232FAC40200E8D93FE089CFE05FF2860 (void);
extern void PostProcessRenderContext_GetDescriptor_m60FCFCD9D6751F4A6F6A3CC93D3630B8052BA662 (void);
extern void PostProcessRenderContext_GetScreenSpaceTemporaryRT_m57C7BBE2412BE154A54A57A7794A157BEB033E83 (void);
extern void PostProcessRenderContext_GetScreenSpaceTemporaryRT_m74A46F835124FF373853218F774E7A178B80145C (void);
extern void PostProcessRenderContext_UpdateSinglePassStereoState_m8BF9AC8139E61F9799B33B8E5B9D989E7A493D98 (void);
extern void PostProcessRenderContext__ctor_m3475CD5D0C8B8A1BCEAC80C4FA8DBBA15A4EEF45 (void);
extern void PostProcessResources__ctor_mD8FED6086600D1B1502981BB6653372136779B5F (void);
extern void Shaders__ctor_mBA5C3BD6B1ACC5F0CA9824495FC283988F5EAEB3 (void);
extern void ComputeShaders__ctor_m311C126CA5133910F014F4FEA6E3ABE8BE1300F1 (void);
extern void SMAALuts__ctor_m687B0322BBE8261955C375339262DDC04550A029 (void);
extern void PostProcessVolume_get_profile_m0055B48AC6C698FDD30573AA75D09D4BE1B2727D (void);
extern void PostProcessVolume_set_profile_m45BDE65CB13CB5AE6C769F117C75563A78BD7BF8 (void);
extern void PostProcessVolume_get_profileRef_m94E97BE839967F36E42FA05C626111242638CFEE (void);
extern void PostProcessVolume_HasInstantiatedProfile_mABB6FB57F696D6B8232710F2E97D2B0E6FB48663 (void);
extern void PostProcessVolume_get_previousLayer_mDD1CFB67F2C14C50E3DE2430D1631145BE7079B2 (void);
extern void PostProcessVolume_OnEnable_m538EE2AF9AB4EAC16D218CFB752A5DDC47D06FFB (void);
extern void PostProcessVolume_OnDisable_m6EC44652B2F1F35FBE42213FBF5F432B594F5138 (void);
extern void PostProcessVolume_Update_m36216CCB5CE0AB129BE95B8FC92E5A13F8A0FC36 (void);
extern void PostProcessVolume_OnDrawGizmos_mB93DE796603855DF433CF9C12D18946CB29FE4BB (void);
extern void PostProcessVolume__ctor_m8F2103B14292EE670A3E07AAC16A7CBF8141EA1E (void);
extern void ColorUtilities_StandardIlluminantY_m1342E9911154F3AD4BFCFD222AAFA54155B6A482 (void);
extern void ColorUtilities_CIExyToLMS_m2FDAC2981ACFA5CB6A3C6ED5F84000B79EEFBBCD (void);
extern void ColorUtilities_ComputeColorBalance_m971A8F4FA36252030994043BECC65CB8BB2C3589 (void);
extern void ColorUtilities_ColorToLift_m006E4E4603E7479E185EA8C7C7BBC9E692D27C7F (void);
extern void ColorUtilities_ColorToInverseGamma_m41A4445E4A50744AD96BD2C8A3AFDDE89B9538C5 (void);
extern void ColorUtilities_ColorToGain_m9A2EE627C2DE6679C170C8CFB25F3DAC11508003 (void);
extern void HableCurve_set_whitePoint_m98FA0F3792036AA3F539010B465DF48676052109 (void);
extern void HableCurve_get_inverseWhitePoint_mDC81A80F665704FE457ACC2CC735D7DBB883F7F4 (void);
extern void HableCurve_set_inverseWhitePoint_m647D3BF345A9A0AB6DC32E044593286CD08F78F6 (void);
extern void HableCurve_get_x0_m6B20430F19A2692748015AAD52E22CF6F19021FF (void);
extern void HableCurve_set_x0_mFF0E70FF9B980FE4A13468E5A4C7B755CA290CB3 (void);
extern void HableCurve_get_x1_mCC8296F534ABB9B938D803FD24218DDD3A1D8FE0 (void);
extern void HableCurve_set_x1_m6EFC16C8CC1F0B430D4D3436FD736B20841AEE32 (void);
extern void HableCurve__ctor_m8E74EA1950C5E352E36A4AC15BE688FC1A17F2A3 (void);
extern void HableCurve_Init_m0331C88204E2EEB65A8727E580614B3733033EF1 (void);
extern void HableCurve_InitSegments_m3E6803A12AF50C93D0D052CCECED893CD85E3C37 (void);
extern void HableCurve_SolveAB_m6CDF86F0CF4191F0BBE8BA819DCA716EAA2439AA (void);
extern void HableCurve_AsSlopeIntercept_mCAF8DEAEB3E843025E1F817421EBE951E54A0B5B (void);
extern void HableCurve_EvalDerivativeLinearGamma_m2B9B56FAB2A3C3C2C5A39BCDC4C1A4FDE0AC7BCA (void);
extern void Segment_Eval_mCDF8072217E3B3B5FD7EAE9716D4FC5B7B5EA83B (void);
extern void Segment__ctor_mB049A24BFA0A15439F0D5949A7AE896695D0D120 (void);
extern void Uniforms__ctor_mA66464181B9A9090B823A9803BD11699B9B7BA74 (void);
extern void Uniforms_get_curve_mD9034647B7B7C3A8B47B414FB7C5B821200AA32E (void);
extern void Uniforms_get_toeSegmentA_m1316C3F6B4E6B82839C9CDACD398FA33D88964BA (void);
extern void Uniforms_get_toeSegmentB_mEC3F93FF1CF8399FDE4AD8FF6355F4D5B9C5D42F (void);
extern void Uniforms_get_midSegmentA_m5A302D93655083BD41167F50535C0B6AA0C9DA6D (void);
extern void Uniforms_get_midSegmentB_m1478AA904368A2DEDAAF4C1BCF343B0566494E21 (void);
extern void Uniforms_get_shoSegmentA_m06733BA44C5FB0E3A7E692DC40252468694D5965 (void);
extern void Uniforms_get_shoSegmentB_m77AB80D8ABA78E633A8BB96CF76149679685184B (void);
extern void HaltonSeq_Get_m6523C48F1CB5F7C3488B47DCB3CDD53B668BC819 (void);
extern void LogHistogram_get_data_m3F3310AAF8C2368E2D3D724BF95E1632F52E8684 (void);
extern void LogHistogram_set_data_mB1A03174A98EFFE606B50D8AB90A73DC141B2435 (void);
extern void LogHistogram_Generate_m5C64740C383CDB7BFD1E459A8066F16BB1FCC6D4 (void);
extern void LogHistogram_GetHistogramScaleOffsetRes_m6FA5B33FBE2EABD16713EFC2C7A9F61E201AA2E8 (void);
extern void LogHistogram_Release_m0CF4E13BFC28F7515A88B4B96B377D1496F3D4B6 (void);
extern void LogHistogram__ctor_mF476B2492B4A33D5D402DC14EB9671CE61364CC7 (void);
extern void PostProcessEffectRendererExtensions_RenderOrLog_mE731AD6E7B4C206271AB39985729F8F4EA49BF15 (void);
extern void PropertySheet_get_properties_m3F54B6A690186CF8AE8CCD585068A4DB80AA50F5 (void);
extern void PropertySheet_set_properties_mAAFE39A44E47611F99AAB7CEF4F189CAD248D2EB (void);
extern void PropertySheet_get_material_m04C2AF7A0AFC078C1477EAE39535742E746118AF (void);
extern void PropertySheet_set_material_m73FB05230DB52CF4F4086E7165FDE8EEE334F709 (void);
extern void PropertySheet__ctor_m856578925217FFB32911485BC651613C936B0E09 (void);
extern void PropertySheet_ClearKeywords_m3A886177632A2DF75DC115107A2BE995375C79E4 (void);
extern void PropertySheet_EnableKeyword_mE87386C289094FE73D84061BE6F716BAE6F43062 (void);
extern void PropertySheet_Release_mE8C333C8D788A25012EB4EC66C855232F0A93653 (void);
extern void PropertySheetFactory__ctor_mA96B46A130766B88D431ECDC3A6B5838DCF916D7 (void);
extern void PropertySheetFactory_Get_mCFDB007DD001F66FCC0EAD7549B63C74857569FC (void);
extern void PropertySheetFactory_Release_mDDF78ACFFC94C6E7015461F0B248E3F93E906CA1 (void);
extern void RuntimeUtilities_get_whiteTexture_mC7E015E50510EE1841B8BC3BD529CCAF22086DB4 (void);
extern void RuntimeUtilities_get_blackTexture_m08881D9E876F9549CFDA7C6B06B3DEA85EF5D11F (void);
extern void RuntimeUtilities_GetLutStrip_m0817F688637D9DEBACCB1388BC6B95E82E2063D3 (void);
extern void RuntimeUtilities_get_fullscreenTriangle_m8C0D0D6F328EC0F374254A4B7E65DF5782465943 (void);
extern void RuntimeUtilities_get_copyStdMaterial_mD30BFA982998C8AD7E2C489DF3416AF8564124D0 (void);
extern void RuntimeUtilities_get_copyStdFromDoubleWideMaterial_m21BBAF82C52F21FF38FF6646CB75C64FAA1E117B (void);
extern void RuntimeUtilities_get_copyMaterial_m727A9F794F0375D736CBA99C9D49F1F6DE002580 (void);
extern void RuntimeUtilities_get_copyFromTexArrayMaterial_m3A4C36563883452C4A44761A8B70C1C08500C0D1 (void);
extern void RuntimeUtilities_get_copySheet_mFDA1B83FCF385E40BFC7659DF705A15423662B80 (void);
extern void RuntimeUtilities_get_copyFromTexArraySheet_m7E8074DD22B6AECC91E832D4A2811364353C03B2 (void);
extern void RuntimeUtilities_isValidResources_m62370A08CF85A9B49B0BF70F4BCFC3940179B9FA (void);
extern void RuntimeUtilities_UpdateResources_mBE0F3DBAC0FFC83774E6B3FE4B792D79F2D2190C (void);
extern void RuntimeUtilities_SetRenderTargetWithLoadStoreAction_mEE3E2CE42BCCD7E75243AED7EAD8D1089E1C69C1 (void);
extern void RuntimeUtilities_SetRenderTargetWithLoadStoreAction_m24F0073A8D00CDF6C78C065C4CC15604079236CE (void);
extern void RuntimeUtilities_BlitFullscreenTriangle_mEC9B2A7C03407D8D98E54B9596664D7AD72E2797 (void);
extern void RuntimeUtilities_BlitFullscreenTriangle_m06AA564FF5237EC15E323082548A497C6DAE4DA9 (void);
extern void RuntimeUtilities_BlitFullscreenTriangle_m5E84F777CA552E3540C83CDAEB6C2075F8406E16 (void);
extern void RuntimeUtilities_BlitFullscreenTriangleFromDoubleWide_mF70FFB7FA914A8F5B41B6B613ED0C69EED52D054 (void);
extern void RuntimeUtilities_BlitFullscreenTriangleToDoubleWide_m519121ABE1997237F33909768F4BEFBA94435E8B (void);
extern void RuntimeUtilities_BlitFullscreenTriangleFromTexArray_m0F023533F28CBAA19FBCC967148FA3E9664DEAA4 (void);
extern void RuntimeUtilities_BlitFullscreenTriangleToTexArray_mBD1CA564E22FA104F9374C123EA833DDD8836B0F (void);
extern void RuntimeUtilities_BlitFullscreenTriangle_mD14ACB5B5332363E7B5D4EE478EB26203FFA6649 (void);
extern void RuntimeUtilities_BuiltinBlit_m551153B7E44E418912ACFCCA2CE92FF20093C5D6 (void);
extern void RuntimeUtilities_CopyTexture_m53717D9C6C018C88760A2A3BD1B6CED4F91C4E2D (void);
extern void RuntimeUtilities_get_scriptableRenderPipelineActive_mB267E1E07B5ADF4398A57180A712752A61A74107 (void);
extern void RuntimeUtilities_get_isSinglePassStereoEnabled_mE604A4AFD0B1368350F752A5769B2E471E923523 (void);
extern void RuntimeUtilities_get_isAndroidOpenGL_m0263F0811E8C77CD6230B4FF6DAE96C3C879C98B (void);
extern void RuntimeUtilities_get_isWebNonWebGPU_mE952C5526AE2FA4BAE0CBCDAA8D2FBF20F17E208 (void);
extern void RuntimeUtilities_get_defaultHDRRenderTextureFormat_m2C5EFD5C7311352FE16AA983D92A89A9C7BB34C8 (void);
extern void RuntimeUtilities_isFloatingPointFormat_mBE4CC69E3E39C3F9993F5E53433E2C0900E4F40C (void);
extern void RuntimeUtilities_hasAlpha_m043D0727DF630E8F2637DB729433B7E55996134E (void);
extern void RuntimeUtilities_Destroy_m88860DDA45529FA1193643863F052D709087B493 (void);
extern void RuntimeUtilities_get_isLinearColorSpace_m101B96DF722CF95AD86CE3E456CACC3D5609E64C (void);
extern void RuntimeUtilities_IsResolvedDepthAvailable_m608BC073F12B7301785E9F6C7761701870E1D6A4 (void);
extern void RuntimeUtilities_IsDynamicResolutionEnabled_m32816C2627738CF63AC0E6732A8EF0FF05147E1F (void);
extern void RuntimeUtilities_Exp2_mDBD94F18960A4067EDA2A0F6C8FBD6C03276220C (void);
extern void RuntimeUtilities_GetJitteredPerspectiveProjectionMatrix_m3FAA99FBEB72A8ADACAB48B86EB551E3E5EF7D18 (void);
extern void RuntimeUtilities_GetJitteredOrthographicProjectionMatrix_m74150797C451CCE5EAC7EACF5E5252E58E1139EC (void);
extern void RuntimeUtilities_GenerateJitteredProjectionMatrixFromOriginal_mD665A981362F8F364ABE3250B901BEF0F693776F (void);
extern void RuntimeUtilities_GetAllAssemblyTypes_mD7E70EABC0276D20B87C9F4094E5504D8FBCEA52 (void);
extern void RuntimeUtilities__cctor_mF5DA1EDCB3AA1A6B0F034A78B049884011515FE4 (void);
extern void U3CU3Ec__cctor_mCCFF835D35938095FEC132A55E85CA7B66F63E27 (void);
extern void U3CU3Ec__ctor_m90691D1FA1D38AC16E174EC5EA02291F7F1FDD7B (void);
extern void U3CU3Ec_U3CGetAllAssemblyTypesU3Eb__93_0_m4CAF0A9205E2356BF3F0C1C58B9F60FDA601B8CB (void);
extern void ShaderIDs__cctor_m97DF40DA87FD5234D3B8047A8995049090267DE7 (void);
extern void Spline__ctor_mF4CB5FE207BD36B6A8B1425A3CEC164DD61B74B3 (void);
extern void Spline_Cache_mF98EF50CC2A26EC6716EB5452FD4B88641A7F833 (void);
extern void Spline_Evaluate_m908B44D36171764274B429D923E47F36BF972516 (void);
extern void Spline_GetHashCode_mF43C4207D64C2A5725F5341460012D79371FBFD7 (void);
extern void TargetPool__ctor_m00A7A8FD2FB5F0FD7BF02F424D32A9FB6B365199 (void);
extern void TargetPool_Get_m0547F5C89835B0586BE4B46593AD5A388FF434B0 (void);
extern void TargetPool_Get_m3E7EF2A9C513F78863718A8237F3A0277A33DC1A (void);
extern void TargetPool_Reset_m2D79A232CECE4951254E2EDA0529F0C877B97BB3 (void);
extern void TextureFormatUtilities__cctor_m38CEE5DD353AAD05D0AC95A00B923120A2706EC8 (void);
extern void TextureFormatUtilities_IsObsolete_m6196F51252D89FDE496B58689F62279DD86D23BC (void);
extern void TextureFormatUtilities_GetUncompressedRenderTextureFormat_mCA81A8A50F7AF487D8D4E2AC8363A5FA6D1ECA1A (void);
extern void TextureFormatUtilities_IsSupported_mB19AE8F3F583E6286877F54F90E0A90676EBA173 (void);
extern void TextureFormatUtilities_IsSupported_m36A432AF0FF57C130ACB43EC4FDE96452003CE5B (void);
extern void TextureLerper_get_instance_m9FBD47F5E67E2E692847BCD402084526D6AF8F37 (void);
extern void TextureLerper__ctor_m326A48946F64C5733E75607AE62A9D247FC7E67D (void);
extern void TextureLerper_BeginFrame_mF2957CC10D96B1A568D284E0A7DA7F4E0CFE0558 (void);
extern void TextureLerper_EndFrame_m827E01313363D563B9D08D245272A162D1872745 (void);
extern void TextureLerper_Get_mD700555241B9F131B78A0302883C35AD0936F120 (void);
extern void TextureLerper_Lerp_m675AC3EF5BB02D904E0988F874D1F6F9AF9FB257 (void);
extern void TextureLerper_Lerp_mF45DC5D430B5C7B0C0067B55F390910DE27232DA (void);
extern void TextureLerper_Clear_mF79AC272E135A60DFE6AB2012B81905934090214 (void);
static Il2CppMethodPointer s_methodPointers[536] = 
{
	DisplayNameAttribute__ctor_mC6DDF0F7FED605C29AF53560DCFABEA3A552AF4D,
	MinAttribute__ctor_mF7CC485FE4513A04FCB6896A87A8E4FF2F69EE18,
	MinMaxAttribute__ctor_m2B86DBABF344BD1EC6BC481E04B41AE5BB29AC1D,
	PostProcessAttribute__ctor_mA3FDCA42F863DEDFC594BB5F5818DE320AD154B3,
	TrackballAttribute__ctor_m593E6847AC9390ADE5E2A358E69B29967527C2FD,
	AmbientOcclusionModeParameter__ctor_mFCE7E1380B0CA42D39F6A66C937D656620E79923,
	AmbientOcclusionQualityParameter__ctor_m14FB3DF7CE2DBF5F11EB1879DCC6887086870AAD,
	AmbientOcclusion_IsEnabledAndSupported_m720CFBDB15B6957D9BDD86026B4CEA80ACB7DA3B,
	AmbientOcclusion__ctor_m56351F92F528AE1C1572715DFC5317DF012CD11E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AmbientOcclusionRenderer_Init_mA3B0F8AE73F6AA694F983B4A5DC87CB7FD6B5CBA,
	AmbientOcclusionRenderer_IsAmbientOnly_m5A77F8622151A4EA9FE590655B6E7E085A1D7B06,
	AmbientOcclusionRenderer_Get_m67BA05B631A6740564AF7B0CAAE58F2CD2EC305E,
	AmbientOcclusionRenderer_GetCameraFlags_m29C1369C24DAD23DCA98CE634F2156DF19FF3592,
	AmbientOcclusionRenderer_Release_mCC9A8A8DAE2CE44CFD3DD09CCA98A38F3B80083A,
	AmbientOcclusionRenderer_GetMultiScaleVO_m77469D960AE101C9A8A74055CF75096CD62BCF6D,
	AmbientOcclusionRenderer_Render_m780A5831389C81820FCA3ACF7D80CB60878FF57E,
	AmbientOcclusionRenderer__ctor_m3350E39318843D75A15F84BE2EF5523D0AEAD28F,
	EyeAdaptationParameter__ctor_m7C78083B39B535D78273E813C702706DE51FFF31,
	AutoExposure_IsEnabledAndSupported_m5AE7A64565EFC721D5D85707F0F585BD18F98C01,
	AutoExposure__ctor_m518431E3FB963370308682EA68492306689158FD,
	AutoExposureRenderer__ctor_m15269270A88D00BE8CCB970F9BF81E65AED4D43D,
	AutoExposureRenderer_CheckTexture_m93C720C54FF70AFD18F3B8160E2324548A593558,
	AutoExposureRenderer_Render_m16316D56F1316E8690CB7B485E4C0D2BC3351270,
	AutoExposureRenderer_Release_m199435220AB5F13A9A37E47F92A6484E4FA2483E,
	Bloom_IsEnabledAndSupported_m9FB08EC848606657761325B79B76E38FC86D2A65,
	Bloom__ctor_m392EA2E063E2471A4B4AC223EB07244D048E8E62,
	BloomRenderer_Init_mE2A298B0A535B1EA48D0412D28B29585B120662B,
	BloomRenderer_Render_m1BF0EADC0232A3AB4A69633601499C3C73C962A4,
	BloomRenderer__ctor_m1E750605CCFDEB524EB48B46EAA3FCC67A3E7B5F,
	ChromaticAberration_IsEnabledAndSupported_mD13C38E0BAF647433F02E58083B835311C5C10F1,
	ChromaticAberration__ctor_mC334EB12BDE44B8B59E4322021FF201B6E94B10A,
	ChromaticAberrationRenderer_Render_m53D46F69F3A469D00104C7D8FC26BC8E9F315EEA,
	ChromaticAberrationRenderer_Release_m073107CECA2CEAE14A260ECAE2D179E738272578,
	ChromaticAberrationRenderer__ctor_m58AE6FAB34E606E0B63222E076606FAC3858C031,
	GradingModeParameter__ctor_mF96EA897EA9CEF4FBC07C714CDB95CFE02F93B9F,
	TonemapperParameter__ctor_m00E44E11E8C010507848A1767300E1053679A5F0,
	ColorGrading_IsEnabledAndSupported_mA0B5513241A97D08D46AE8840751FED4BA533101,
	ColorGrading__ctor_m33D6A092A5F620B80BB9822003AD59DB98CDC31F,
	ColorGradingRenderer_Render_m3A5815D59F04D8BF0FBFFCE7E86DCDD666E19592,
	ColorGradingRenderer_RenderExternalPipeline3D_m4BA3989C71C16FBA2FD6B820D2363FC9620D1BF7,
	ColorGradingRenderer_RenderHDRPipeline3D_mB3D126713A64C33C75EF4DAB05CB53FEA2EE5D43,
	ColorGradingRenderer_RenderHDRPipeline2D_m1A21E68693FBB0759745896776D49EA280870D75,
	ColorGradingRenderer_RenderLDRPipeline2D_m616DBEC56443AF51A5FF9DF1626C915DA6BC6597,
	ColorGradingRenderer_CheckInternalLogLut_m4E3605B5F95F0FDDD1030F4C8A5897149BAA3475,
	ColorGradingRenderer_CheckInternalStripLut_mBDFEBFA559753E181002AE5AD1C660CE3CE69B59,
	ColorGradingRenderer_GetCurveTexture_m23FDC74489540547C8708E78C5B291EA368FB008,
	ColorGradingRenderer_IsRenderTextureFormatSupportedForLinearFiltering_mB1C20AE0DF61C5B0D586965F786E0306CB1FB269,
	ColorGradingRenderer_GetLutFormat_m79D6D53EF1CEA4D5A2D8EE3A6C70D5C1201D90B8,
	ColorGradingRenderer_GetCurveFormat_m7D31D3D15BFD24ADC8AD53769D4D78A3ED575190,
	ColorGradingRenderer_Release_m46B7BEFF864C3FCB6616E05EC71E5A8DCB6EB1D8,
	ColorGradingRenderer__ctor_m609EFB8891282C1971EF851DC04C12B20CB2A6BE,
	KernelSizeParameter__ctor_mC9FA15B0A3D6D56495B3DB38EC1E685C9CDAEB00,
	DepthOfField_IsEnabledAndSupported_m5B41DD100C9314561E15314BF33D7B5937AD53F6,
	DepthOfField__ctor_m5E5C8EDEDCD6643E99AEF4D37073AD7128D765A5,
	DepthOfFieldRenderer__ctor_mDD6E938F8A2D32A3F586D5DF97749638EF57EE4F,
	DepthOfFieldRenderer_GetCameraFlags_mA24EAFABABBC714F206293BF191323730A15ED1C,
	DepthOfFieldRenderer_SelectFormat_mA727185D2B1E89DC0962E26672D7509147BD462B,
	DepthOfFieldRenderer_CalculateMaxCoCRadius_m3813326711DC8EDD78C1FB179682F3AD40319593,
	DepthOfFieldRenderer_CheckHistory_m911AC1AF4CF4A874FD38C941F5C58C47C7FE561B,
	DepthOfFieldRenderer_Render_m32989A445B3004E689397E072AC31DA1F456216A,
	DepthOfFieldRenderer_Release_m71B33FB0AEB8ED07A52ED8AA9FA28C0198B4AC27,
	Dithering_Render_mE6124E38F680E37934C3BBD1E8F73D926D28B661,
	Dithering__ctor_m143E4229BCAC785FA75CC5EBDAF7BDB6EFD388EB,
	FastApproximateAntialiasing__ctor_m4C443FEB81618E3B5EA4D2EEA75BB67F4910EEA1,
	Fog_GetCameraFlags_m1FE74A3121D526C50A78863CC9ACBC38F0694CC4,
	Fog_IsEnabledAndSupported_m032F2A56D8DE3B1FDACAE8174B20C7CEE8325F0E,
	Fog_Render_m435B9A6B92833EBD9E237F46F4F066F4F03A732E,
	Fog__ctor_mA98F3BA37BFA53225EE74C53ACF4DEF218211F28,
	Grain_IsEnabledAndSupported_mBC536DDC0E38B9D7B18EB436196900F2B6403592,
	Grain__ctor_m10C6547CD0B0CFD9BC6D639C1E920434816235DF,
	GrainRenderer_Render_m0646753B2E6222A1C00F3AEE8A7E567878919B57,
	GrainRenderer_GetLookupFormat_mD362BF97912F5B3D7CF6ED8C21906346A7989697,
	GrainRenderer_Release_mAC267606A532714D11C5BE1A2B0D284893A5BDD9,
	GrainRenderer__ctor_m2265B02AC4735EB4875F8D32A6EBE73BC80403A1,
	LensDistortion_IsEnabledAndSupported_mA7204A7B6840B7B2F9A650A03AAC28CE5DC98587,
	LensDistortion__ctor_m257562372044829F89320AA2D2CFF46FFB8E4255,
	LensDistortionRenderer_Render_m4D3343279E5BF87021BAF56EC0388DC1395DF52A,
	LensDistortionRenderer__ctor_mA5092D594C99AF56A5DBBE5C2D4377B73006D05E,
	MotionBlur_IsEnabledAndSupported_m5028AFA3FA6894818C3DE261DD71B3785B6D4FA3,
	MotionBlur__ctor_mC86A4CC11E1CF95CC533BC29A46BA28914B2224B,
	MotionBlurRenderer_GetCameraFlags_m4DB815C78AD85619B40F4797D896149E52D450CE,
	MotionBlurRenderer_CreateTemporaryRT_mD976DBC2C27CEEA14915FF82A210B39CBC5E086D,
	MotionBlurRenderer_Render_m2BFC40DD564E324ACDEDB6B4E13E92057F96DA25,
	MotionBlurRenderer__ctor_m3AF94ED2942706ADA6EC007A084462BE9047D774,
	MultiScaleVO__ctor_m9BFC7ADD2493601C3DB9371EB189D4635612F1DF,
	MultiScaleVO_GetCameraFlags_m8E4A64719125B3893304CA2A2EC1F3C85F7687EF,
	MultiScaleVO_SetResources_m84240174825540B7F4C74D968DC59AD2ECC591C1,
	MultiScaleVO_Alloc_m6CBEE89199B35D51B3647752CBBBE5FF548BF5B4,
	MultiScaleVO_AllocArray_m36BD4376B54B4A1E87ACA920D78C2E8E33ECCD72,
	MultiScaleVO_Release_m0E5ED7DC133AB8FD150B5FFE16BAA0E2DF2D4230,
	MultiScaleVO_CalculateZBufferParams_m22B0A0F3F81F8B763B4FC56576A24CEF93836E8F,
	MultiScaleVO_CalculateTanHalfFovHeight_mC39EF131E77E87D11C46F6F0407FFF242D775BE8,
	MultiScaleVO_GetSize_mE37BB8127DB121C2D77ACD8C28FB07142364C992,
	MultiScaleVO_GetSizeArray_m69FBD800D97C26290A466A097E7E50A2CE9CDFFA,
	MultiScaleVO_GenerateAOMap_m5F19CF58D1022B3A75F65C0DBC8BF6F77CBA4BD1,
	MultiScaleVO_PushAllocCommands_mBEB5A921C0C0E84B9E42F6FAD1B53492DAAEA31E,
	MultiScaleVO_PushDownsampleCommands_m2CDAC714FB28F0F9C7ABB7CC7A18E40C1402CEDB,
	MultiScaleVO_PushRenderCommands_m08B92180E92671B64E66E4EE3000DBC68C3598AF,
	MultiScaleVO_PushUpsampleCommands_mE4D317E8951EC0CF04E6AECBA833A24332AD2BB0,
	MultiScaleVO_PushReleaseCommands_mBE63709DDB6352D2F7025AE89BDE97BE0909C1AC,
	MultiScaleVO_PreparePropertySheet_m475E87B885128A12DA6B62324E654E9CF9B30C76,
	MultiScaleVO_CheckAOTexture_mE6F5AA4D7F06D3BAA1FABE1D8991C36104CD7B6C,
	MultiScaleVO_PushDebug_mCF96B0B7C0E4CF071BD1658991DCC6570BCDB137,
	MultiScaleVO_RenderAfterOpaque_m5DBCFF788852A66C0887BEF1C47531C556C58A96,
	MultiScaleVO_RenderAmbientOnly_m4BED5F04E1CAE684E5DA32CB487A8BF192FCB106,
	MultiScaleVO_CompositeAmbientOnly_mEAC61D7DC1731ED82CF03EA69F18E95E375FA64A,
	MultiScaleVO_Release_m9311D4CEA6B8EB25B19A66D012720DD084696D2C,
	ScalableAO__ctor_m2E13FF1404D88C933436F64E64446A3E8C58EC25,
	ScalableAO_GetCameraFlags_mF3DC2F17B0C42898A785241EAB4762496A270FA3,
	ScalableAO_DoLazyInitialization_m9CDAE5877C189D77F1F5E7FDAF0D9A6A4EDB1825,
	ScalableAO_Render_m28BC49E3F61595E532B03D0588B47E377C5C64DA,
	ScalableAO_RenderAfterOpaque_mA72DCBC2E4E26729754816757F95BB5A445F2EB8,
	ScalableAO_RenderAmbientOnly_m1F800BE3EBE499449E0D10F9332D2225CBA17459,
	ScalableAO_CompositeAmbientOnly_m6BDA0561AF5B1D66CC0D7EB7730F0FD8BDE332E1,
	ScalableAO_Release_m9A84BB9F350E9F1573DF794DD79CF6C89ACA522D,
	ScreenSpaceReflectionPresetParameter__ctor_m788759481354D194C96F8569A5FA0CF600DD9959,
	ScreenSpaceReflectionResolutionParameter__ctor_m13C6DA3B4C340969D44063CA3750D4563EF4ABC2,
	ScreenSpaceReflections_IsEnabledAndSupported_mCDB8B7AE8CC67387ACF1615CB34140C958117C11,
	ScreenSpaceReflections__ctor_m00CB810B6E82A82F1D61C5573C138DD088851D7A,
	ScreenSpaceReflectionsRenderer_GetCameraFlags_m23B86CC0CEA0E26EB5A2051FEC23130634FB0B9B,
	ScreenSpaceReflectionsRenderer_CheckRT_mC9AB522FC1B490E25DAF07666863F6572954DA94,
	ScreenSpaceReflectionsRenderer_Render_m852E13492673A7D34B735265B7B5D7BE4B533D1B,
	ScreenSpaceReflectionsRenderer_Release_m2B7890B1A7F4B90CFE4D086B85755435840CE5FA,
	ScreenSpaceReflectionsRenderer__ctor_mE1144CEA7E4E5AE7FCE33024EC6C0754FCA49397,
	QualityPreset__ctor_m7AC033D833C201BBECA54DF1886D30824A2955E8,
	SubpixelMorphologicalAntialiasing_IsSupported_m1705607A7079C11A6FAAEB07746977F804A8ECFF,
	SubpixelMorphologicalAntialiasing_Render_m5C772116EF78929A662C6C1EE168A998AEE3F028,
	SubpixelMorphologicalAntialiasing__ctor_mFD4BB185541D16D22FD47ABE4427F81D531A50A5,
	TemporalAntialiasing_get_jitter_m95640074A28A81D1CFD87CFBE39B9EA8F514C3DC,
	TemporalAntialiasing_set_jitter_m813C3D64082BE1924BB5B7A1D9A8B1FF9877AABB,
	TemporalAntialiasing_get_sampleIndex_m6F27F29D737C6529F14ED99D25E1A60BB1A77348,
	TemporalAntialiasing_set_sampleIndex_mF0B4CE95B0AAB362A3A6DDE6F77E274A9BBB8ADE,
	TemporalAntialiasing_IsSupported_mAE5BCF24068A7E94E3BB643E549AA055192C045E,
	TemporalAntialiasing_GetCameraFlags_mFDB0F48DA9DD43DC96F0001D8AEC3EC0BDF8B9AA,
	TemporalAntialiasing_ResetHistory_m1E1611D2244E90EB7C3CC493B98C0AA0A1A52010,
	TemporalAntialiasing_GenerateRandomOffset_m8C20703188A9078739CEF48A902F636142E06712,
	TemporalAntialiasing_GetJitteredProjectionMatrix_m05C5BC645B3761AFDDEBF885743BBB71419F12F1,
	TemporalAntialiasing_ConfigureJitteredProjectionMatrix_mD1658F905FA0B40616F94A72596803C4DFD11476,
	TemporalAntialiasing_ConfigureStereoJitteredProjectionMatrices_m6778161075072CB286B1C1082F1535B39D13E546,
	TemporalAntialiasing_GenerateHistoryName_m39F69FC9226519CDFE2BAECF99334772C7EBD22D,
	TemporalAntialiasing_CheckHistory_mA35B44ECD13BB4F9CF19E766131FFC11CC5B41AE,
	TemporalAntialiasing_Render_m1F5CDB905DA7AB9A5D2C8335E7F40A0B984C767F,
	TemporalAntialiasing_Release_m7F07A845A790B3E98E9618400D360CAC8BCF44B7,
	TemporalAntialiasing__ctor_m1F3DCAB166187D2B412DFE69787E46242313D9FD,
	VignetteModeParameter__ctor_m4D63838C20AE0CB9DDD27D1D31235D918456F304,
	Vignette_IsEnabledAndSupported_m2D67AF6B97A94BBD4B2A7FDEAA4028847C5FAFB4,
	Vignette__ctor_mB0325E1C20E1C0EACC4E22F90C88C2376A0DC537,
	VignetteRenderer_Render_mB738733CA260ECFBA104310C8283AB153FA32110,
	VignetteRenderer__ctor_m2CA1FD8CC001CF2FF6CF2C6D4030A42B8B41E89D,
	HistogramMonitor_OnDisable_m230FF2666443F85287241087449BA4CE5BB789DC,
	HistogramMonitor_NeedsHalfRes_m73CFC4DC4E26DED01E7819E191E727BA7126073F,
	HistogramMonitor_ShaderResourcesAvailable_m9A5B187C0479D7E3DF5248EA991F0454FE9B5949,
	HistogramMonitor_Render_m500233055734F82F0F28E848401821AB906B55FE,
	HistogramMonitor__ctor_m076097076CBE7E0589A84CA904C357FBE4822551,
	LightMeterMonitor_ShaderResourcesAvailable_m0C0CB920F8EAA6888F402D529CE55422F9569406,
	LightMeterMonitor_Render_m55BC89DB32BE88F0059E446D90612DFBA6F00DC3,
	LightMeterMonitor__ctor_mBB777FF1522832DE5450C9A5DD64E8F8D30C7C11,
	Monitor_get_output_m47DF8F109DA67DEF62A5993E579B62750DA196C0,
	Monitor_set_output_mE14C51217A4CCAB88EC656889034C4D55764AECA,
	Monitor_IsRequestedAndSupported_m54722701F2E06E92EEC20B760616F534C58C07EF,
	NULL,
	Monitor_NeedsHalfRes_m6288FC8B3CFE546BFEE1590D91DE4CD385312E0B,
	Monitor_CheckOutput_m413208864EA3CACC6D40DFAEF1DB04DC0A30150C,
	Monitor_OnEnable_mD8E42BFFD458B4A7D2B3975C01DD33BFA4D1A54C,
	Monitor_OnDisable_mA3610794EB2C764C2F7892CA17BB71D772A2C943,
	NULL,
	Monitor__ctor_m6EF591E063563E2D6DC7B8884243426EAC35561C,
	VectorscopeMonitor_OnDisable_m001686957BD5361704A15E2E4B386DDD3D02252B,
	VectorscopeMonitor_NeedsHalfRes_m723A2717D7FE50FF8A2D9027277ADC22A150BC64,
	VectorscopeMonitor_ShaderResourcesAvailable_m528D2CE0BBA9B10610EE653F824AA9B1D1DC2661,
	VectorscopeMonitor_Render_m84CA6B45C4331890F0455119D86C1A07EB966B5B,
	VectorscopeMonitor__ctor_mC27FEEE84A6557F3B4BACA78805AA2D7DADF39A7,
	WaveformMonitor_OnDisable_mBDEB4FC072BE8751732B2E9EE6A16E587BA75B8B,
	WaveformMonitor_NeedsHalfRes_mA40030095FA31CFD4D978649A9B82D4ADCE7697D,
	WaveformMonitor_ShaderResourcesAvailable_mFA14E2CE435250C9C197E09F212F0C14E823082D,
	WaveformMonitor_Render_m52105139C3FF21B5598DA81E5F456294F9759E91,
	WaveformMonitor__ctor_mB3BA30A53C9FE94CF150E6768D3229A08BFDDD77,
	NULL,
	NULL,
	NULL,
	ParameterOverride_OnEnable_m0D67F5FA71AEEAB671BF019E14CD91A5EF213A66,
	ParameterOverride_OnDisable_mB1BB0C4E6E712345795DAC5066843364DBEF65FC,
	NULL,
	ParameterOverride__ctor_mF2BC731CBB924D3621F1B84DFFCA4F5CDB42C0E2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FloatParameter_Interp_m1434238DF5487DF3540A5A71E6B59C533A05122F,
	FloatParameter__ctor_m9DB1EF263F257D4CF8C8B6BE717EA1D129CEF984,
	IntParameter_Interp_m51A6FD54C0AF0F087F75835AD873DBD6417D09E9,
	IntParameter__ctor_mD0B577F7B6F29156C6F34A31852CE0015B544312,
	BoolParameter__ctor_m3D75EA93FDC7D88D346025CB612A459B92915FE7,
	ColorParameter_Interp_m4B0D83B2F72792D5BF6C537BDD0992AC9586442D,
	ColorParameter__ctor_m30D0D993F9B30802429AA3176816DFC889D6DAA3,
	Vector2Parameter_Interp_mB14285E63AE62C206098BDCA80211C3F90041B34,
	Vector2Parameter__ctor_mBF44FC58FB4108A066A973BE3891A8B85479214A,
	Vector4Parameter_Interp_m0899065B59819646F5192F92ACAF28D1F15DF37F,
	Vector4Parameter__ctor_m8C8DED1875F2A781AC4F18369E08DDA46C13D243,
	SplineParameter_OnEnable_mD33338349D3C87DA7758922C0CF7E03FF626E5B1,
	SplineParameter_SetValue_m536D3CDC09CA93E5F1E06F5C327D183F5611FBA2,
	SplineParameter_Interp_m7FDEAE34BED3B0731915281DA8B0D34A360AF1E8,
	SplineParameter__ctor_m11C763DDA15AAC2E1E8A1AAD11E0D8C2E7B11520,
	TextureParameter_Interp_m6BDA698BA090B8731A7DECDD1A7D654207CE6EB4,
	TextureParameter__ctor_mFB8A59BFF1213A53DD6C87017E3025607C25AD0A,
	PostProcessBundle_get_attribute_m23310FCCFEE897EBF93C36296D6C5D565B4C19A0,
	PostProcessBundle_set_attribute_m2675212E0F5625E6B7436C50F63DB75E26045434,
	PostProcessBundle_get_settings_m5BA726BCC8ADCCEA4AD25AD51798950474EFC046,
	PostProcessBundle_set_settings_m954BB49AE94AC89911931978FABA469BBBBCC8BF,
	PostProcessBundle_get_renderer_m9B9D011FC8ED8B384FE16AEC5A5955A87243A61C,
	PostProcessBundle__ctor_m980F8608CC22C6F40C0382EA3208A4D6889F33FD,
	PostProcessBundle_Release_m31FD40E9428001418B7A2BB323C7B41202AE7EB6,
	PostProcessBundle_ResetHistory_m5EB7270369216D54DE183E1A91C530F562BBCC12,
	NULL,
	NULL,
	PostProcessDebugLayer_get_debugOverlayTarget_mEE851EF1356AE899D0EF902AD0E0966B16AFD940,
	PostProcessDebugLayer_set_debugOverlayTarget_m7566FD9B25470C7ACCF14606AAF975AE00715332,
	PostProcessDebugLayer_get_debugOverlayActive_m7990B750167EF1A31F8A3F96B539B322D3F7FC84,
	PostProcessDebugLayer_set_debugOverlayActive_mC83941C3612A2745BD1B6C771FCF0053B6D3BC77,
	PostProcessDebugLayer_get_debugOverlay_m50CBB64D3FEE070BD29DEC24C8CD57DD37EFE1C3,
	PostProcessDebugLayer_set_debugOverlay_m8A972C54696447B69FBC597FA9E67C3D9B7C322A,
	PostProcessDebugLayer_OnEnable_m387008B7ED2B3F492C6D5F57EEC62057BF293C3E,
	PostProcessDebugLayer_OnDisable_mC4762A0AEBE23067EAE67F82A7BCCFE8BD192017,
	PostProcessDebugLayer_DestroyDebugOverlayTarget_mA98B3230B16094649A5149FA8FD978634B755846,
	PostProcessDebugLayer_SetFrameSize_m38E163EE3B51AA81D8C2AF7B2C5D58498F64287A,
	PostProcessDebugLayer_PushDebugOverlay_m43912718B6ABE4DB9485F8A51A5B12B879186697,
	PostProcessDebugLayer_GetCameraFlags_mAD93FA644A0C5AC136F382ED927B6E3237336EFD,
	PostProcessDebugLayer_RenderMonitors_mD366E3E3E7D8F0F1A686CB33D352DDBE4B1B8DE8,
	PostProcessDebugLayer_RenderSpecialOverlays_mD23CFE4FC14A61B52A41E88A03B85174BEE1D211,
	PostProcessDebugLayer_EndFrame_mDBDCD82C1BE519E65293D44A840500FE4B6D58A8,
	PostProcessDebugLayer__ctor_m5A99D89DCB5BFFEE99D91396CED829F86B24EE61,
	OverlaySettings__ctor_m88E7EAF9BE31DF010DA0CC32DBF93ACF0C6CE22D,
	PostProcessEffectRenderer_Init_m174682ACFF9ABF0E0FC7E7684A84C9442A30D19C,
	PostProcessEffectRenderer_GetCameraFlags_mC65D31F164C708C88C624B63E25FCA32C3F0DDFD,
	PostProcessEffectRenderer_ResetHistory_mB97A23456B52D5B555005D46FB5C38C7887DF918,
	PostProcessEffectRenderer_Release_m9464A96E9F63258C17DA6480A46A13F06FC8EAEB,
	NULL,
	NULL,
	PostProcessEffectRenderer__ctor_m0E9A4D08F3DDABC9B7C4D3E81DF54F43D9729538,
	NULL,
	NULL,
	NULL,
	NULL,
	PostProcessEffectSettings_OnEnable_m53B71C90196D55E0557B09526B62F1964F90DE60,
	PostProcessEffectSettings_OnDisable_m9FAE66EBBEACD6D7BE2ECCE9FBAAEF0A0C54FDE0,
	PostProcessEffectSettings_SetAllOverridesTo_m0EFF9FC7563FE058E728801E91F01808431BF140,
	PostProcessEffectSettings_IsEnabledAndSupported_m3A0E447900FC7C1DFA163D851CACD45C7836760D,
	PostProcessEffectSettings_GetHash_m4E159616856B60028F64AB21661EBABA8072D9E8,
	PostProcessEffectSettings__ctor_mD40D0A07297AB8295240B8AFDA7A961384C4F82D,
	PostProcessEffectSettings_U3COnEnableU3Eb__3_2_mE5072CBCD4F5E941C6DE52C0A9EC5BEAD6A7AE9A,
	U3CU3Ec__cctor_mE68E1D1B36956CC366071123266B1ED4686FE489,
	U3CU3Ec__ctor_mF340714BAF696077FEEB50C8F0921CBADB691665,
	U3CU3Ec_U3COnEnableU3Eb__3_0_m85647CDDCD9A9892F0F99E85586B15BF0AD725F3,
	U3CU3Ec_U3COnEnableU3Eb__3_1_m50822ACB3F5C9C9F17FB2CD5DD51E85C307D1ED0,
	PostProcessEventComparer_Equals_m2166068BCF36DB77A9E738FC50C0A77AC8C4C96D,
	PostProcessEventComparer_GetHashCode_mDF3708FBF53C71BC1FA3024694AB19BD0553D45A,
	PostProcessLayer_get_sortedBundles_m1C832D9F4C17EBC0E9E8704923DE1876634588B9,
	PostProcessLayer_set_sortedBundles_mF014B030591011A9935170FCC8747EDD3F6075FF,
	PostProcessLayer_get_cameraDepthFlags_m7B14DC4DFC770826A06AF45843738D2EC68EDD01,
	PostProcessLayer_set_cameraDepthFlags_mBC9A8CE97A08E406A3DF3F3F6AAA157330316578,
	PostProcessLayer_get_haveBundlesBeenInited_m8FC1BB2B461C9427AE234EAB8B3C83339B0C544B,
	PostProcessLayer_set_haveBundlesBeenInited_mC73845F7B1DF581C092B4A735244B17E75D979FA,
	PostProcessLayer_OnEnable_m9CF9B052529EEF6E2016CE00FFBA81C130342EE2,
	PostProcessLayer_InitLegacy_m354AC408E683A1B536D8C30BA93720037025BB5E,
	PostProcessLayer_DynamicResolutionAllowsFinalBlitToCameraTarget_m9AACAA266C7632AF1B271CA5A4B3ADB8713BF199,
	PostProcessLayer_OnRenderImage_m88AE7F294CBCA2042C76BC0B45544109752B4FA4,
	PostProcessLayer_Init_m0FDB593A6B13381DAFAA2078CCA2F913AAC983F9,
	PostProcessLayer_InitBundles_mF98EC0C218D2F1E0D21B643EEBE109CA4F8C71B3,
	PostProcessLayer_UpdateBundleSortList_m5FC7EB336E878E425F971CB0BDB37FC55CF40A62,
	PostProcessLayer_OnDisable_m8F51B4254A45A8E2A323478871B69427A98F7094,
	PostProcessLayer_Reset_m96933BBE49EB42C0B5B754EEF3B22B33C0B20B1C,
	PostProcessLayer_OnPreCull_m0E5B78C5458852CDF36759FA5E785109E23FA426,
	PostProcessLayer_OnPreRender_mB714954409087F5BA3EF538B47338B77F5E20B2E,
	PostProcessLayer_RequiresInitialBlit_m66CC1F4AC2DC78365D1F134E091EE989EF3224DF,
	PostProcessLayer_UpdateSrcDstForOpaqueOnly_m06DEFDC679EDCE932F13D9258CD4B159C7172DF3,
	PostProcessLayer_BuildCommandBuffers_m8C6C6754F27A786AD80D12898F26711769FCE03D,
	PostProcessLayer_OnPostRender_m3EF2E660A385A6FE533503FCFBAE2481AB2767F8,
	NULL,
	PostProcessLayer_GetBundle_mFE6B4B8D6F1300AB3A011A60E25FB3A9C7EF4A67,
	NULL,
	PostProcessLayer_BakeMSVOMap_m2103A1A15DE80E0036FE655A19740615E9196F7D,
	PostProcessLayer_OverrideSettings_m49989C317280F87560366678EBCFF62739921F7C,
	PostProcessLayer_SetLegacyCameraFlags_m72D14BAB60AC3CEE06CEFF07D112D46486A2F958,
	PostProcessLayer_ResetHistory_mB886C39683321E5A65C86CE6D13860B7DEA79100,
	PostProcessLayer_HasOpaqueOnlyEffects_mFD8E5AB8219E71D4464704477E0EFF8A42D94F6B,
	PostProcessLayer_HasActiveEffects_mE7155905AC4D8288AF4CF87F542511377F110AB7,
	PostProcessLayer_SetupContext_mB51E288FAF90059C947F7B56982611A62D92B190,
	PostProcessLayer_UpdateVolumeSystem_m67096C3D2687CA32B0CAA5EEC3EFDBDCD1156DD2,
	PostProcessLayer_RenderOpaqueOnly_m3A58A1E06E6DBA8D582F18BBCB1BCF58B702B508,
	PostProcessLayer_Render_m9AD8F461328CF3AAD58078BE9959D39A5DB20191,
	PostProcessLayer_RenderInjectionPoint_m42D83392ABD881E8434BABB7F8BC46E548DE8EBB,
	PostProcessLayer_RenderList_m478A2ECAB77361DB4A85AE3B2A5FD55A65D6D851,
	PostProcessLayer_ApplyFlip_m64131F71DAA216E393712E1B43656056B16F6DFC,
	PostProcessLayer_ApplyDefaultFlip_mA6FF046E1DC3BABDAE6ABEB463F139329516B2DD,
	PostProcessLayer_RenderBuiltins_mB9B7DA7EE55CAC51BF153F488BFCAF5800D3FFB0,
	PostProcessLayer_RenderFinalPass_mA79AF4EF4D050FED050FF29356384FE8F681044F,
	NULL,
	PostProcessLayer_ShouldGenerateLogHistogram_m69235046FAF79ECE1E98A97DB703550D1AF56BDD,
	PostProcessLayer__ctor_mD8712392B04023FC43F79821EC8A498B33043683,
	SerializedBundleRef__ctor_mF79561705E11260D6DE0F7022B78D6CBFCD75BC9,
	U3CU3Ec__cctor_m7DC9E4B7F3BA5872200F1537B2F1F5FEB649C436,
	U3CU3Ec__ctor_m14CE645D40F0ED598B358C218449CC214E67F6D5,
	U3CU3Ec_U3CUpdateBundleSortListU3Eb__54_1_m6F744BBAF634606AB77DCEFB1F8645805257300D,
	U3CU3Ec__DisplayClass54_0__ctor_mE4663058DF29F83DC658899887FEA21A00782E5C,
	U3CU3Ec__DisplayClass54_0_U3CUpdateBundleSortListU3Eb__0_m68FE9719E12A93BBC74A3446509B93A404D9E081,
	U3CU3Ec__DisplayClass54_0_U3CUpdateBundleSortListU3Eb__2_mD7BD333124C1F57B7842028BF5E1B53EB8BBF368,
	U3CU3Ec__DisplayClass54_1__ctor_mAC82F6C1E46CBDE83CFF53574D9910FADB232983,
	U3CU3Ec__DisplayClass54_1_U3CUpdateBundleSortListU3Eb__3_m240E53745432F10C5F771D79E4B629B46E665D41,
	U3CU3Ec__DisplayClass54_2__ctor_m36472C800FCB96E7021A9ED363DBCDFE8102F00F,
	U3CU3Ec__DisplayClass54_2_U3CUpdateBundleSortListU3Eb__4_mDAA5434E8160298ACAEE90154CA37D7DE54DEC84,
	U3CU3Ec__DisplayClass54_3__ctor_mDF80A7734D48DF6C65686CEFA8DEFEE73C64EC51,
	U3CU3Ec__DisplayClass54_3_U3CUpdateBundleSortListU3Eb__5_mEF229FE5216D4F13351BCF429CAB0C4B83462935,
	PostProcessManager_get_instance_m8C56D207C6B453F8E34E8849FA2BDF8BDF19CA9C,
	PostProcessManager__ctor_mE18C53FBF36094C52BF5E888AA8E399423BE6688,
	PostProcessManager_CleanBaseTypes_m3CE8A80B457017D42537722008D62AD735AD7E02,
	PostProcessManager_ReloadBaseTypes_m1945136D79E4679F586C858EE2A72631A3FF9360,
	PostProcessManager_SetLayerDirty_m85F4D44F755A2E86AF17993DDA7A2CD47ECF568B,
	PostProcessManager_UpdateVolumeLayer_mE7C602B2A996873EFEAE5619F4E410AC55CDF9F9,
	PostProcessManager_Register_m1C449C90BAD2503D192D361F09DE5B8FD4E8D1D0,
	PostProcessManager_Register_m8A5D93C913BA9E62722006B1ED8C5C7BD2A5C36A,
	PostProcessManager_Unregister_m96F25B0BB879AA90EFAFA0CC051EFC96B3B8982F,
	PostProcessManager_Unregister_m49EE0FFAD8D79AB25CCF12C82C17C831ADE9C79F,
	PostProcessManager_ReplaceData_mFCAC48F8D1C2581BDB1A72670EEB91DB2DCF5426,
	PostProcessManager_UpdateSettings_m4B9B9670DEDA150A20023F98DEB45DA98F120EEA,
	PostProcessManager_GrabVolumes_m1AF11581681DB4092AE61C86D06F2463A36F7814,
	PostProcessManager_SortByPriority_mC460E161FD3D1293B46CFC546A8E60E0C53B7B15,
	U3CU3Ec__cctor_m72950DE1518E19ECC6261EA8098C97085DA5BD25,
	U3CU3Ec__ctor_mBDFDD56BBE85E5B393B14B106BAEB772C3CA95D4,
	U3CU3Ec_U3CReloadBaseTypesU3Eb__12_0_m865537CCEE58BF53B4AC1A1EF580E114A31BAE85,
	PostProcessProfile_OnEnable_m23364CDE321796BC226CBEFAF9DC6F1323938CDC,
	NULL,
	PostProcessProfile_AddSettings_m610279C7E82C2F2E9156FF80FE7F6C3A2FC1F1C3,
	PostProcessProfile_AddSettings_m7446918C8FB730367D486136D3DF233B57A5FCD8,
	NULL,
	PostProcessProfile_RemoveSettings_mA0FB19DEC43521661C67F12EF6A13DB3F8D3B9EC,
	NULL,
	PostProcessProfile_HasSettings_mAAAD536DF4C2CBD9EB73C2683D007F65297BD8BC,
	NULL,
	NULL,
	PostProcessProfile__ctor_m665590DA298A7A9BB57B1C54FE52F252C1B399DE,
	U3CU3Ec__cctor_m78C889B816DFCBD83DB0DB0C43FC57BC10969904,
	U3CU3Ec__ctor_m28471842FE201AEF91CB04F5F8FBABF89DA37BB7,
	U3CU3Ec_U3COnEnableU3Eb__2_0_mEBC7A0AA00EBF9BFA5042D81206E56B406A1FCD7,
	PostProcessRenderContext_get_camera_m03C2533C3B4CC5300541CEC486849C58B1F8BBAE,
	PostProcessRenderContext_set_camera_m616159FD16EF298057D8C92C9D4E04DC583A35CF,
	PostProcessRenderContext_get_command_m028BE33B6194640A1DE901A6F935658034A3E2CD,
	PostProcessRenderContext_set_command_m383214635AE4777FFE746270157B265E6CB3A799,
	PostProcessRenderContext_get_source_mD66060FCBE2897F0510AE91E86370440226FA430,
	PostProcessRenderContext_set_source_m8747B57F6213BC84C7C2990508D029FB4D31C3DE,
	PostProcessRenderContext_get_destination_m9A1924F891A773105919295D97A474741CAB9F28,
	PostProcessRenderContext_set_destination_mED49AE5FCE8CEB9D8B64AA82E1693D4913D48759,
	PostProcessRenderContext_get_sourceFormat_m5EB6DD7D586BC0E5B3E3C385CF96AC63575ED61B,
	PostProcessRenderContext_set_sourceFormat_mFBDC1C325F8E32DA2A40C784659D2DD8BA057258,
	PostProcessRenderContext_get_flip_mFE0933034DE2E1C6174C09EED54F03C472D29C32,
	PostProcessRenderContext_set_flip_m91DFA83ED3118BB02CF2849D2177C65D186F3E11,
	PostProcessRenderContext_get_resources_m89879DF69E4B910F9EE3008AB8DC60B732ABF02A,
	PostProcessRenderContext_set_resources_mD928596BD2CF34330CA3F8FA83845C9BB39E5651,
	PostProcessRenderContext_get_propertySheets_m60E7825143611FEC183803150D8F7C2785514D79,
	PostProcessRenderContext_set_propertySheets_m06436C85901FFAF383447F35F3A19909E1C60344,
	PostProcessRenderContext_get_userData_m9C88F1615E7C029E65F8A1BA3C0D22986FE27B98,
	PostProcessRenderContext_set_userData_m1D9CEBBB566FC3841231E033269B030FDBE98431,
	PostProcessRenderContext_get_debugLayer_mD44CD6FBE77E24B243E0EDE0A332A6BB32AA6C25,
	PostProcessRenderContext_set_debugLayer_mF9A346FF61AD7CD0AC5605BD3B2A2007084CFA5A,
	PostProcessRenderContext_get_width_m551FAECA1D38B547E9337BEC7316D6B113B00F03,
	PostProcessRenderContext_set_width_m1613BDCAF13B96F72BE7A91E82E9EB6D1C4C39EF,
	PostProcessRenderContext_get_height_mECB24899496181711525B9E8204F8A85AAA84181,
	PostProcessRenderContext_set_height_m4CAF4DB1F50557C276D2683C799FC9CFC19B357F,
	PostProcessRenderContext_get_stereoActive_m54D0889CF302150E3850FD7BA9AF72BB9EE94C7B,
	PostProcessRenderContext_set_stereoActive_m133C99DFFB79D8C1D33176258C5A96D8587CDEB0,
	PostProcessRenderContext_get_xrActiveEye_m1ED0BDA4DC07BAD4096F3B66DF35520815C7F208,
	PostProcessRenderContext_set_xrActiveEye_mA344A4FBC049CA99096413FBE81F0F8F9A12D9A8,
	PostProcessRenderContext_get_numberOfEyes_m172652A2003AFD49FB6ED64C3F46249B92D21AE4,
	PostProcessRenderContext_set_numberOfEyes_m047ECAB2F0CE41026EECD96F2FDAC3B49AB178B9,
	PostProcessRenderContext_get_stereoRenderingMode_m674A9251277DC05913F1EA02B6E5ADA1AE4E00F5,
	PostProcessRenderContext_set_stereoRenderingMode_mCCE43DEFBEFEEAE8D4512991FA5117C781710287,
	PostProcessRenderContext_get_screenWidth_mF6EF29A7E49F6132C7376A841571FB65A1573147,
	PostProcessRenderContext_set_screenWidth_m2472914DC0C907A2A78B7BB990F3DBE9DE785C5C,
	PostProcessRenderContext_get_screenHeight_mFF2D3EF9A84D53D9125EF96DD746CF8B257687FD,
	PostProcessRenderContext_set_screenHeight_m3D69896EFE6A480285D300EA3E99164661144FC4,
	PostProcessRenderContext_get_isSceneView_mADB2A9705C42D62BCB2DD13C1A64202A7BF32801,
	PostProcessRenderContext_set_isSceneView_m25BC585A98717333E55435BF060FBC8352979120,
	PostProcessRenderContext_get_antialiasing_m8FA450C8086C96DB987CF8F9B707DA5CFB7AA108,
	PostProcessRenderContext_set_antialiasing_m487F83A7E53C46AE4C2015794DA6568950E3C181,
	PostProcessRenderContext_get_temporalAntialiasing_m18BFEF6B423A28BD505D611A15643580B8E6CCD7,
	PostProcessRenderContext_set_temporalAntialiasing_mF2B2A0D8F39F49690FD4F76B3347C4D168B22597,
	PostProcessRenderContext_Reset_m69F96AA7B7CCE9927AB6061B887CF81F8AF06BCB,
	PostProcessRenderContext_IsTemporalAntialiasingActive_m835118A884E10E19B5625C77EC029B088ECE11E3,
	PostProcessRenderContext_IsDebugOverlayEnabled_m10FA8ED9E7FE01A6B9FB48BFF9B19A5F70908927,
	PostProcessRenderContext_PushDebugOverlay_m543E6F22232FAC40200E8D93FE089CFE05FF2860,
	PostProcessRenderContext_GetDescriptor_m60FCFCD9D6751F4A6F6A3CC93D3630B8052BA662,
	PostProcessRenderContext_GetScreenSpaceTemporaryRT_m57C7BBE2412BE154A54A57A7794A157BEB033E83,
	PostProcessRenderContext_GetScreenSpaceTemporaryRT_m74A46F835124FF373853218F774E7A178B80145C,
	PostProcessRenderContext_UpdateSinglePassStereoState_m8BF9AC8139E61F9799B33B8E5B9D989E7A493D98,
	PostProcessRenderContext__ctor_m3475CD5D0C8B8A1BCEAC80C4FA8DBBA15A4EEF45,
	PostProcessResources__ctor_mD8FED6086600D1B1502981BB6653372136779B5F,
	Shaders__ctor_mBA5C3BD6B1ACC5F0CA9824495FC283988F5EAEB3,
	ComputeShaders__ctor_m311C126CA5133910F014F4FEA6E3ABE8BE1300F1,
	SMAALuts__ctor_m687B0322BBE8261955C375339262DDC04550A029,
	PostProcessVolume_get_profile_m0055B48AC6C698FDD30573AA75D09D4BE1B2727D,
	PostProcessVolume_set_profile_m45BDE65CB13CB5AE6C769F117C75563A78BD7BF8,
	PostProcessVolume_get_profileRef_m94E97BE839967F36E42FA05C626111242638CFEE,
	PostProcessVolume_HasInstantiatedProfile_mABB6FB57F696D6B8232710F2E97D2B0E6FB48663,
	PostProcessVolume_get_previousLayer_mDD1CFB67F2C14C50E3DE2430D1631145BE7079B2,
	PostProcessVolume_OnEnable_m538EE2AF9AB4EAC16D218CFB752A5DDC47D06FFB,
	PostProcessVolume_OnDisable_m6EC44652B2F1F35FBE42213FBF5F432B594F5138,
	PostProcessVolume_Update_m36216CCB5CE0AB129BE95B8FC92E5A13F8A0FC36,
	PostProcessVolume_OnDrawGizmos_mB93DE796603855DF433CF9C12D18946CB29FE4BB,
	PostProcessVolume__ctor_m8F2103B14292EE670A3E07AAC16A7CBF8141EA1E,
	ColorUtilities_StandardIlluminantY_m1342E9911154F3AD4BFCFD222AAFA54155B6A482,
	ColorUtilities_CIExyToLMS_m2FDAC2981ACFA5CB6A3C6ED5F84000B79EEFBBCD,
	ColorUtilities_ComputeColorBalance_m971A8F4FA36252030994043BECC65CB8BB2C3589,
	ColorUtilities_ColorToLift_m006E4E4603E7479E185EA8C7C7BBC9E692D27C7F,
	ColorUtilities_ColorToInverseGamma_m41A4445E4A50744AD96BD2C8A3AFDDE89B9538C5,
	ColorUtilities_ColorToGain_m9A2EE627C2DE6679C170C8CFB25F3DAC11508003,
	HableCurve_set_whitePoint_m98FA0F3792036AA3F539010B465DF48676052109,
	HableCurve_get_inverseWhitePoint_mDC81A80F665704FE457ACC2CC735D7DBB883F7F4,
	HableCurve_set_inverseWhitePoint_m647D3BF345A9A0AB6DC32E044593286CD08F78F6,
	HableCurve_get_x0_m6B20430F19A2692748015AAD52E22CF6F19021FF,
	HableCurve_set_x0_mFF0E70FF9B980FE4A13468E5A4C7B755CA290CB3,
	HableCurve_get_x1_mCC8296F534ABB9B938D803FD24218DDD3A1D8FE0,
	HableCurve_set_x1_m6EFC16C8CC1F0B430D4D3436FD736B20841AEE32,
	HableCurve__ctor_m8E74EA1950C5E352E36A4AC15BE688FC1A17F2A3,
	HableCurve_Init_m0331C88204E2EEB65A8727E580614B3733033EF1,
	HableCurve_InitSegments_m3E6803A12AF50C93D0D052CCECED893CD85E3C37,
	HableCurve_SolveAB_m6CDF86F0CF4191F0BBE8BA819DCA716EAA2439AA,
	HableCurve_AsSlopeIntercept_mCAF8DEAEB3E843025E1F817421EBE951E54A0B5B,
	HableCurve_EvalDerivativeLinearGamma_m2B9B56FAB2A3C3C2C5A39BCDC4C1A4FDE0AC7BCA,
	Segment_Eval_mCDF8072217E3B3B5FD7EAE9716D4FC5B7B5EA83B,
	Segment__ctor_mB049A24BFA0A15439F0D5949A7AE896695D0D120,
	Uniforms__ctor_mA66464181B9A9090B823A9803BD11699B9B7BA74,
	Uniforms_get_curve_mD9034647B7B7C3A8B47B414FB7C5B821200AA32E,
	Uniforms_get_toeSegmentA_m1316C3F6B4E6B82839C9CDACD398FA33D88964BA,
	Uniforms_get_toeSegmentB_mEC3F93FF1CF8399FDE4AD8FF6355F4D5B9C5D42F,
	Uniforms_get_midSegmentA_m5A302D93655083BD41167F50535C0B6AA0C9DA6D,
	Uniforms_get_midSegmentB_m1478AA904368A2DEDAAF4C1BCF343B0566494E21,
	Uniforms_get_shoSegmentA_m06733BA44C5FB0E3A7E692DC40252468694D5965,
	Uniforms_get_shoSegmentB_m77AB80D8ABA78E633A8BB96CF76149679685184B,
	HaltonSeq_Get_m6523C48F1CB5F7C3488B47DCB3CDD53B668BC819,
	LogHistogram_get_data_m3F3310AAF8C2368E2D3D724BF95E1632F52E8684,
	LogHistogram_set_data_mB1A03174A98EFFE606B50D8AB90A73DC141B2435,
	LogHistogram_Generate_m5C64740C383CDB7BFD1E459A8066F16BB1FCC6D4,
	LogHistogram_GetHistogramScaleOffsetRes_m6FA5B33FBE2EABD16713EFC2C7A9F61E201AA2E8,
	LogHistogram_Release_m0CF4E13BFC28F7515A88B4B96B377D1496F3D4B6,
	LogHistogram__ctor_mF476B2492B4A33D5D402DC14EB9671CE61364CC7,
	PostProcessEffectRendererExtensions_RenderOrLog_mE731AD6E7B4C206271AB39985729F8F4EA49BF15,
	PropertySheet_get_properties_m3F54B6A690186CF8AE8CCD585068A4DB80AA50F5,
	PropertySheet_set_properties_mAAFE39A44E47611F99AAB7CEF4F189CAD248D2EB,
	PropertySheet_get_material_m04C2AF7A0AFC078C1477EAE39535742E746118AF,
	PropertySheet_set_material_m73FB05230DB52CF4F4086E7165FDE8EEE334F709,
	PropertySheet__ctor_m856578925217FFB32911485BC651613C936B0E09,
	PropertySheet_ClearKeywords_m3A886177632A2DF75DC115107A2BE995375C79E4,
	PropertySheet_EnableKeyword_mE87386C289094FE73D84061BE6F716BAE6F43062,
	PropertySheet_Release_mE8C333C8D788A25012EB4EC66C855232F0A93653,
	PropertySheetFactory__ctor_mA96B46A130766B88D431ECDC3A6B5838DCF916D7,
	PropertySheetFactory_Get_mCFDB007DD001F66FCC0EAD7549B63C74857569FC,
	PropertySheetFactory_Release_mDDF78ACFFC94C6E7015461F0B248E3F93E906CA1,
	RuntimeUtilities_get_whiteTexture_mC7E015E50510EE1841B8BC3BD529CCAF22086DB4,
	RuntimeUtilities_get_blackTexture_m08881D9E876F9549CFDA7C6B06B3DEA85EF5D11F,
	RuntimeUtilities_GetLutStrip_m0817F688637D9DEBACCB1388BC6B95E82E2063D3,
	RuntimeUtilities_get_fullscreenTriangle_m8C0D0D6F328EC0F374254A4B7E65DF5782465943,
	RuntimeUtilities_get_copyStdMaterial_mD30BFA982998C8AD7E2C489DF3416AF8564124D0,
	RuntimeUtilities_get_copyStdFromDoubleWideMaterial_m21BBAF82C52F21FF38FF6646CB75C64FAA1E117B,
	RuntimeUtilities_get_copyMaterial_m727A9F794F0375D736CBA99C9D49F1F6DE002580,
	RuntimeUtilities_get_copyFromTexArrayMaterial_m3A4C36563883452C4A44761A8B70C1C08500C0D1,
	RuntimeUtilities_get_copySheet_mFDA1B83FCF385E40BFC7659DF705A15423662B80,
	RuntimeUtilities_get_copyFromTexArraySheet_m7E8074DD22B6AECC91E832D4A2811364353C03B2,
	RuntimeUtilities_isValidResources_m62370A08CF85A9B49B0BF70F4BCFC3940179B9FA,
	RuntimeUtilities_UpdateResources_mBE0F3DBAC0FFC83774E6B3FE4B792D79F2D2190C,
	RuntimeUtilities_SetRenderTargetWithLoadStoreAction_mEE3E2CE42BCCD7E75243AED7EAD8D1089E1C69C1,
	RuntimeUtilities_SetRenderTargetWithLoadStoreAction_m24F0073A8D00CDF6C78C065C4CC15604079236CE,
	RuntimeUtilities_BlitFullscreenTriangle_mEC9B2A7C03407D8D98E54B9596664D7AD72E2797,
	RuntimeUtilities_BlitFullscreenTriangle_m06AA564FF5237EC15E323082548A497C6DAE4DA9,
	RuntimeUtilities_BlitFullscreenTriangle_m5E84F777CA552E3540C83CDAEB6C2075F8406E16,
	RuntimeUtilities_BlitFullscreenTriangleFromDoubleWide_mF70FFB7FA914A8F5B41B6B613ED0C69EED52D054,
	RuntimeUtilities_BlitFullscreenTriangleToDoubleWide_m519121ABE1997237F33909768F4BEFBA94435E8B,
	RuntimeUtilities_BlitFullscreenTriangleFromTexArray_m0F023533F28CBAA19FBCC967148FA3E9664DEAA4,
	RuntimeUtilities_BlitFullscreenTriangleToTexArray_mBD1CA564E22FA104F9374C123EA833DDD8836B0F,
	RuntimeUtilities_BlitFullscreenTriangle_mD14ACB5B5332363E7B5D4EE478EB26203FFA6649,
	RuntimeUtilities_BuiltinBlit_m551153B7E44E418912ACFCCA2CE92FF20093C5D6,
	RuntimeUtilities_CopyTexture_m53717D9C6C018C88760A2A3BD1B6CED4F91C4E2D,
	RuntimeUtilities_get_scriptableRenderPipelineActive_mB267E1E07B5ADF4398A57180A712752A61A74107,
	RuntimeUtilities_get_isSinglePassStereoEnabled_mE604A4AFD0B1368350F752A5769B2E471E923523,
	RuntimeUtilities_get_isAndroidOpenGL_m0263F0811E8C77CD6230B4FF6DAE96C3C879C98B,
	RuntimeUtilities_get_isWebNonWebGPU_mE952C5526AE2FA4BAE0CBCDAA8D2FBF20F17E208,
	RuntimeUtilities_get_defaultHDRRenderTextureFormat_m2C5EFD5C7311352FE16AA983D92A89A9C7BB34C8,
	RuntimeUtilities_isFloatingPointFormat_mBE4CC69E3E39C3F9993F5E53433E2C0900E4F40C,
	RuntimeUtilities_hasAlpha_m043D0727DF630E8F2637DB729433B7E55996134E,
	RuntimeUtilities_Destroy_m88860DDA45529FA1193643863F052D709087B493,
	RuntimeUtilities_get_isLinearColorSpace_m101B96DF722CF95AD86CE3E456CACC3D5609E64C,
	RuntimeUtilities_IsResolvedDepthAvailable_m608BC073F12B7301785E9F6C7761701870E1D6A4,
	RuntimeUtilities_IsDynamicResolutionEnabled_m32816C2627738CF63AC0E6732A8EF0FF05147E1F,
	NULL,
	RuntimeUtilities_Exp2_mDBD94F18960A4067EDA2A0F6C8FBD6C03276220C,
	RuntimeUtilities_GetJitteredPerspectiveProjectionMatrix_m3FAA99FBEB72A8ADACAB48B86EB551E3E5EF7D18,
	RuntimeUtilities_GetJitteredOrthographicProjectionMatrix_m74150797C451CCE5EAC7EACF5E5252E58E1139EC,
	RuntimeUtilities_GenerateJitteredProjectionMatrixFromOriginal_mD665A981362F8F364ABE3250B901BEF0F693776F,
	RuntimeUtilities_GetAllAssemblyTypes_mD7E70EABC0276D20B87C9F4094E5504D8FBCEA52,
	NULL,
	NULL,
	RuntimeUtilities__cctor_mF5DA1EDCB3AA1A6B0F034A78B049884011515FE4,
	U3CU3Ec__cctor_mCCFF835D35938095FEC132A55E85CA7B66F63E27,
	U3CU3Ec__ctor_m90691D1FA1D38AC16E174EC5EA02291F7F1FDD7B,
	U3CU3Ec_U3CGetAllAssemblyTypesU3Eb__93_0_m4CAF0A9205E2356BF3F0C1C58B9F60FDA601B8CB,
	NULL,
	NULL,
	NULL,
	ShaderIDs__cctor_m97DF40DA87FD5234D3B8047A8995049090267DE7,
	Spline__ctor_mF4CB5FE207BD36B6A8B1425A3CEC164DD61B74B3,
	Spline_Cache_mF98EF50CC2A26EC6716EB5452FD4B88641A7F833,
	Spline_Evaluate_m908B44D36171764274B429D923E47F36BF972516,
	Spline_GetHashCode_mF43C4207D64C2A5725F5341460012D79371FBFD7,
	TargetPool__ctor_m00A7A8FD2FB5F0FD7BF02F424D32A9FB6B365199,
	TargetPool_Get_m0547F5C89835B0586BE4B46593AD5A388FF434B0,
	TargetPool_Get_m3E7EF2A9C513F78863718A8237F3A0277A33DC1A,
	TargetPool_Reset_m2D79A232CECE4951254E2EDA0529F0C877B97BB3,
	TextureFormatUtilities__cctor_m38CEE5DD353AAD05D0AC95A00B923120A2706EC8,
	TextureFormatUtilities_IsObsolete_m6196F51252D89FDE496B58689F62279DD86D23BC,
	TextureFormatUtilities_GetUncompressedRenderTextureFormat_mCA81A8A50F7AF487D8D4E2AC8363A5FA6D1ECA1A,
	TextureFormatUtilities_IsSupported_mB19AE8F3F583E6286877F54F90E0A90676EBA173,
	TextureFormatUtilities_IsSupported_m36A432AF0FF57C130ACB43EC4FDE96452003CE5B,
	TextureLerper_get_instance_m9FBD47F5E67E2E692847BCD402084526D6AF8F37,
	TextureLerper__ctor_m326A48946F64C5733E75607AE62A9D247FC7E67D,
	TextureLerper_BeginFrame_mF2957CC10D96B1A568D284E0A7DA7F4E0CFE0558,
	TextureLerper_EndFrame_m827E01313363D563B9D08D245272A162D1872745,
	TextureLerper_Get_mD700555241B9F131B78A0302883C35AD0936F120,
	TextureLerper_Lerp_m675AC3EF5BB02D904E0988F874D1F6F9AF9FB257,
	TextureLerper_Lerp_mF45DC5D430B5C7B0C0067B55F390910DE27232DA,
	TextureLerper_Clear_mF79AC272E135A60DFE6AB2012B81905934090214,
};
extern void PostProcessEventComparer_Equals_m2166068BCF36DB77A9E738FC50C0A77AC8C4C96D_AdjustorThunk (void);
extern void PostProcessEventComparer_GetHashCode_mDF3708FBF53C71BC1FA3024694AB19BD0553D45A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000107, PostProcessEventComparer_Equals_m2166068BCF36DB77A9E738FC50C0A77AC8C4C96D_AdjustorThunk },
	{ 0x06000108, PostProcessEventComparer_GetHashCode_mDF3708FBF53C71BC1FA3024694AB19BD0553D45A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[536] = 
{
	8627,
	8700,
	4735,
	2185,
	8568,
	10870,
	10870,
	6166,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	10870,
	6166,
	10698,
	10637,
	10870,
	10698,
	8627,
	10870,
	10870,
	6166,
	10870,
	10870,
	4217,
	8627,
	10870,
	6166,
	10870,
	10870,
	8627,
	10870,
	6166,
	10870,
	8627,
	10870,
	10870,
	10870,
	10870,
	6166,
	10870,
	8627,
	8627,
	8627,
	8627,
	8627,
	10870,
	10870,
	7472,
	15447,
	16321,
	16321,
	10870,
	10870,
	10870,
	6166,
	10870,
	10870,
	10637,
	3195,
	7632,
	1282,
	8627,
	10870,
	8627,
	10870,
	10870,
	10637,
	6166,
	8627,
	10870,
	6166,
	10870,
	8627,
	10637,
	10870,
	10870,
	6166,
	10870,
	8627,
	10870,
	6166,
	10870,
	10637,
	672,
	8627,
	10870,
	8627,
	10637,
	8627,
	316,
	316,
	4628,
	7800,
	7634,
	7781,
	7790,
	347,
	2146,
	1507,
	321,
	57,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	8627,
	10870,
	8627,
	10637,
	8627,
	2189,
	8627,
	8627,
	8627,
	10870,
	10870,
	10870,
	6166,
	10870,
	10637,
	622,
	8627,
	10870,
	10870,
	10870,
	10537,
	8627,
	10870,
	10859,
	8781,
	10637,
	8568,
	10537,
	10637,
	10870,
	10859,
	7422,
	8627,
	8627,
	2164,
	3515,
	8627,
	10870,
	10870,
	10870,
	6166,
	10870,
	8627,
	10870,
	10870,
	10537,
	6166,
	8627,
	10870,
	6166,
	8627,
	10870,
	10698,
	8627,
	6166,
	-1,
	10537,
	4217,
	10870,
	10870,
	-1,
	10870,
	10870,
	10537,
	6166,
	8627,
	10870,
	10870,
	10537,
	6166,
	8627,
	10870,
	-1,
	-1,
	-1,
	10870,
	10870,
	-1,
	10870,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	2254,
	10870,
	2101,
	10870,
	10870,
	2045,
	10870,
	2271,
	10870,
	2293,
	10870,
	10870,
	8627,
	2196,
	10870,
	2196,
	10870,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10870,
	10870,
	-1,
	-1,
	10698,
	8627,
	10537,
	8468,
	10637,
	8568,
	10870,
	10870,
	10870,
	4217,
	1537,
	10637,
	8627,
	8627,
	10870,
	10870,
	10870,
	10870,
	10637,
	10870,
	10870,
	-1,
	-1,
	10870,
	-1,
	-1,
	-1,
	-1,
	10870,
	10870,
	3833,
	6166,
	10637,
	10870,
	7489,
	16420,
	10870,
	6166,
	7062,
	2745,
	7016,
	10698,
	8627,
	10637,
	8568,
	10537,
	8468,
	10870,
	10870,
	10537,
	4638,
	8627,
	10870,
	4628,
	10870,
	10870,
	10870,
	10870,
	13993,
	620,
	10870,
	10870,
	-1,
	7489,
	-1,
	347,
	4651,
	8627,
	10870,
	6166,
	2748,
	8627,
	4638,
	8627,
	8627,
	985,
	2193,
	4638,
	8627,
	1042,
	2160,
	-1,
	6166,
	10870,
	10870,
	16420,
	10870,
	7450,
	10870,
	5670,
	6166,
	10870,
	6166,
	10870,
	6166,
	10870,
	6166,
	16341,
	10870,
	10870,
	10870,
	8568,
	2160,
	4628,
	8627,
	4628,
	8627,
	8627,
	4638,
	7485,
	15983,
	16420,
	10870,
	6166,
	10870,
	-1,
	7489,
	7489,
	-1,
	8627,
	-1,
	6166,
	-1,
	-1,
	10870,
	16420,
	10870,
	6166,
	10698,
	8627,
	10698,
	8627,
	10746,
	8671,
	10746,
	8671,
	10637,
	8568,
	10537,
	8468,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10698,
	8627,
	10637,
	8568,
	10637,
	8568,
	10537,
	8468,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10637,
	8568,
	10537,
	8468,
	10637,
	8568,
	10698,
	8627,
	10870,
	10537,
	6112,
	1537,
	1907,
	134,
	581,
	2030,
	10870,
	10870,
	10870,
	10870,
	10870,
	10698,
	8627,
	10698,
	10537,
	10637,
	10870,
	10870,
	10870,
	10870,
	10870,
	15835,
	14516,
	14516,
	15957,
	15957,
	15957,
	8700,
	10781,
	8700,
	10781,
	8700,
	10781,
	8700,
	10870,
	362,
	8852,
	621,
	285,
	1336,
	7636,
	10870,
	8627,
	10863,
	10863,
	10863,
	10863,
	10863,
	10863,
	10863,
	14427,
	10698,
	8627,
	8627,
	7800,
	10870,
	10870,
	14317,
	10698,
	8627,
	10698,
	8627,
	8627,
	10870,
	8627,
	10870,
	10870,
	7489,
	10870,
	16341,
	16341,
	15705,
	16341,
	16341,
	16341,
	16341,
	16341,
	16341,
	16341,
	16291,
	15983,
	12819,
	11505,
	11506,
	11256,
	11255,
	11508,
	11508,
	11326,
	11326,
	11253,
	12147,
	13689,
	16291,
	16291,
	16291,
	16291,
	16321,
	15447,
	15447,
	15983,
	16291,
	15451,
	15451,
	-1,
	15835,
	14266,
	14266,
	13240,
	16341,
	-1,
	-1,
	16420,
	16420,
	10870,
	7489,
	-1,
	-1,
	-1,
	16420,
	1541,
	8568,
	3603,
	10637,
	10870,
	10637,
	7016,
	10870,
	16420,
	15451,
	15583,
	15447,
	15447,
	16341,
	10870,
	8627,
	10870,
	269,
	1839,
	1826,
	10870,
};
static const Il2CppTokenRangePair s_rgctxIndices[17] = 
{
	{ 0x0200004D, { 2, 6 } },
	{ 0x0200005D, { 10, 3 } },
	{ 0x02000081, { 34, 4 } },
	{ 0x060000B9, { 0, 2 } },
	{ 0x060000DE, { 8, 1 } },
	{ 0x060000DF, { 9, 1 } },
	{ 0x0600011E, { 13, 1 } },
	{ 0x06000120, { 14, 3 } },
	{ 0x06000131, { 17, 1 } },
	{ 0x06000153, { 18, 2 } },
	{ 0x06000156, { 20, 1 } },
	{ 0x06000158, { 21, 1 } },
	{ 0x0600015A, { 22, 1 } },
	{ 0x0600015B, { 23, 3 } },
	{ 0x060001F4, { 26, 3 } },
	{ 0x060001FA, { 29, 3 } },
	{ 0x060001FB, { 32, 2 } },
};
extern const uint32_t g_rgctx_ParameterOverride_1_t851F8FAF457076A957D8F469496E581551BF3458;
extern const uint32_t g_rgctx_T_tE59F76EA30FD0313385F51EEE02F78BC676E4EA2;
extern const uint32_t g_rgctx_T_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246;
extern const uint32_t g_rgctx_ParameterOverride_1__ctor_m3B4D09AAAE3E4FD2B423C485B4512CD2DC308FB0;
extern const uint32_t g_rgctx_ParameterOverride_1_t800539A2FDED702BFAF17DFE5D09357F5BB34C3E;
extern const uint32_t g_rgctx_ParameterOverride_GetValue_TisT_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246_m136547529B242865D367D70F7562E55C63AA74ED;
extern const uint32_t g_rgctx_ParameterOverride_1_Interp_m094F7E2AAFED335EF632649D3DF59B1AF40493D3;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_T_t58ACEC224C010C0E8AADBC708F0A3297086214E4;
extern const uint32_t g_rgctx_T_t6BD2B403215AF6A0B06B042F56B27821CBB492BF;
extern const uint32_t g_rgctx_PostProcessEffectRenderer_1_t39BEBB683083BBAA6E4D48E83B0766C4925E8E27;
extern const uint32_t g_rgctx_T_t3EFA4867E2D9B672F2AFA9715F65189CEDCFAB0B;
extern const uint32_t g_rgctx_PostProcessEffectRenderer_1_set_settings_mC5CC2F0B6BE390DBD5B0609B030DE066AD4EAA23;
extern const uint32_t g_rgctx_T_t0CEC14A755E76AFCB00B7328A16C9C661B4890C4;
extern const uint32_t g_rgctx_PostProcessLayer_GetBundle_TisT_t0D4730ECF1763E33275FBDE9311686C2D9F37E89_m1826B43E3B5EB5CC75C500A9CC1B75A47C1FDA9B;
extern const uint32_t g_rgctx_PostProcessBundle_CastSettings_TisT_t0D4730ECF1763E33275FBDE9311686C2D9F37E89_m4D96FA7799EC8A98E5E493DF5CFFAFBAB6A2156F;
extern const uint32_t g_rgctx_T_t0D4730ECF1763E33275FBDE9311686C2D9F37E89;
extern const uint32_t g_rgctx_PostProcessLayer_GetBundle_TisT_t1865F64A2B3C97F55C1CC446C763BD79EA49A22B_mB893F5E792A969CDC14E4FAF2F119F15048289BA;
extern const uint32_t g_rgctx_T_t518029E80EB5D00BCBF9BDFF6346F8FAC1ACE593;
extern const uint32_t g_rgctx_T_t518029E80EB5D00BCBF9BDFF6346F8FAC1ACE593;
extern const uint32_t g_rgctx_T_tA42B7E1F723DBE344EA078A143186AC86F610E5A;
extern const uint32_t g_rgctx_T_tF2245FFC9693AAF3CA16C500BE68C591FB4343CB;
extern const uint32_t g_rgctx_T_t83F02F123608421C5DA113BD09FC8C139A733E32;
extern const uint32_t g_rgctx_T_t62E6F4001A4FF2B8FF118F1EE056A17BEB756505;
extern const uint32_t g_rgctx_TU26_t1A5750D2EAC8FDDD2089749BBA6ECB4F1471B83C;
extern const uint32_t g_rgctx_T_t62E6F4001A4FF2B8FF118F1EE056A17BEB756505;
extern const uint32_t g_rgctx_TU26_t17135E635E82A723E5CEFD24391404EE6E76412A;
extern const uint32_t g_rgctx_T_t43E547515B25A30FB23CD522E3C03E7884DD8E78;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t43E547515B25A30FB23CD522E3C03E7884DD8E78_mD4D968403853D7C6C0827490D534DE7A89CFFA9D;
extern const uint32_t g_rgctx_U3CU3Ec__94_1_t453AD660C4B644E9B5E265D034BF7C07EAFFEC03;
extern const uint32_t g_rgctx_U3CU3Ec__94_1_t453AD660C4B644E9B5E265D034BF7C07EAFFEC03;
extern const uint32_t g_rgctx_U3CU3Ec__94_1_U3CGetAllTypesDerivedFromU3Eb__94_0_mA6475716CD1E0C3F3BD213572D2914948240DACB;
extern const uint32_t g_rgctx_T_t1C9B9FF3C80E985892319C304F8277D041FD9DD1;
extern const uint32_t g_rgctx_T_t1C9B9FF3C80E985892319C304F8277D041FD9DD1;
extern const uint32_t g_rgctx_U3CU3Ec__94_1_t9A715D6983B4903932A6A74231ADB366805E99B3;
extern const uint32_t g_rgctx_U3CU3Ec__94_1__ctor_mF8D9751155B83DE13C966DB1FC5672D0F8122D8D;
extern const uint32_t g_rgctx_U3CU3Ec__94_1_t9A715D6983B4903932A6A74231ADB366805E99B3;
extern const uint32_t g_rgctx_T_t2E3FE41C7625641E2A9D9A6056387E0385B695CF;
static const Il2CppRGCTXDefinition s_rgctxValues[38] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParameterOverride_1_t851F8FAF457076A957D8F469496E581551BF3458 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE59F76EA30FD0313385F51EEE02F78BC676E4EA2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParameterOverride_1__ctor_m3B4D09AAAE3E4FD2B423C485B4512CD2DC308FB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParameterOverride_1_t800539A2FDED702BFAF17DFE5D09357F5BB34C3E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParameterOverride_GetValue_TisT_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246_m136547529B242865D367D70F7562E55C63AA74ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParameterOverride_1_Interp_m094F7E2AAFED335EF632649D3DF59B1AF40493D3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t4B99FE542949CF1BDC32D0D3ADC6E46FC8694246_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t58ACEC224C010C0E8AADBC708F0A3297086214E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6BD2B403215AF6A0B06B042F56B27821CBB492BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PostProcessEffectRenderer_1_t39BEBB683083BBAA6E4D48E83B0766C4925E8E27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3EFA4867E2D9B672F2AFA9715F65189CEDCFAB0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PostProcessEffectRenderer_1_set_settings_mC5CC2F0B6BE390DBD5B0609B030DE066AD4EAA23 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0CEC14A755E76AFCB00B7328A16C9C661B4890C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PostProcessLayer_GetBundle_TisT_t0D4730ECF1763E33275FBDE9311686C2D9F37E89_m1826B43E3B5EB5CC75C500A9CC1B75A47C1FDA9B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PostProcessBundle_CastSettings_TisT_t0D4730ECF1763E33275FBDE9311686C2D9F37E89_m4D96FA7799EC8A98E5E493DF5CFFAFBAB6A2156F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0D4730ECF1763E33275FBDE9311686C2D9F37E89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PostProcessLayer_GetBundle_TisT_t1865F64A2B3C97F55C1CC446C763BD79EA49A22B_mB893F5E792A969CDC14E4FAF2F119F15048289BA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t518029E80EB5D00BCBF9BDFF6346F8FAC1ACE593 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t518029E80EB5D00BCBF9BDFF6346F8FAC1ACE593 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tA42B7E1F723DBE344EA078A143186AC86F610E5A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF2245FFC9693AAF3CA16C500BE68C591FB4343CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t83F02F123608421C5DA113BD09FC8C139A733E32 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t62E6F4001A4FF2B8FF118F1EE056A17BEB756505 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1A5750D2EAC8FDDD2089749BBA6ECB4F1471B83C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t62E6F4001A4FF2B8FF118F1EE056A17BEB756505 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t17135E635E82A723E5CEFD24391404EE6E76412A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t43E547515B25A30FB23CD522E3C03E7884DD8E78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t43E547515B25A30FB23CD522E3C03E7884DD8E78_mD4D968403853D7C6C0827490D534DE7A89CFFA9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__94_1_t453AD660C4B644E9B5E265D034BF7C07EAFFEC03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__94_1_t453AD660C4B644E9B5E265D034BF7C07EAFFEC03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__94_1_U3CGetAllTypesDerivedFromU3Eb__94_0_mA6475716CD1E0C3F3BD213572D2914948240DACB },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t1C9B9FF3C80E985892319C304F8277D041FD9DD1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1C9B9FF3C80E985892319C304F8277D041FD9DD1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__94_1_t9A715D6983B4903932A6A74231ADB366805E99B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__94_1__ctor_mF8D9751155B83DE13C966DB1FC5672D0F8122D8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__94_1_t9A715D6983B4903932A6A74231ADB366805E99B3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t2E3FE41C7625641E2A9D9A6056387E0385B695CF },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Postprocessing_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Postprocessing_Runtime_CodeGenModule = 
{
	"Unity.Postprocessing.Runtime.dll",
	536,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	17,
	s_rgctxIndices,
	38,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
