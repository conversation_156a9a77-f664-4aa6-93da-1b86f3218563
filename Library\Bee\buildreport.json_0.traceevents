{ "pid": 11572, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11572, "tid": 1, "ts": 1752056628347922, "dur": 6768, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752056628354707, "dur": 48926, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752056628403636, "dur": 1158, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628426540, "dur": 26, "ph": "X", "name": "", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628347840, "dur": 43434, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391276, "dur": 34778, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391287, "dur": 42, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391332, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391335, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391611, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391703, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628391708, "dur": 3822, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395537, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395653, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395658, "dur": 60, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395722, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628395725, "dur": 997, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628396727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628396730, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628396780, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628396783, "dur": 11810, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628408600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628408603, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628408678, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752056628408682, "dur": 17363, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628426569, "dur": 58, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 47244640256, "ts": 1752056628347735, "dur": 57085, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ts": 1752056628404820, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ts": 1752056628404822, "dur": 57, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628426630, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11572, "tid": 42949672960, "ts": 1752056628341078, "dur": 85035, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752056628341340, "dur": 5203, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752056628426117, "dur": 16, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752056628426137, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628426642, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752056628391601, "dur":51, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628391710, "dur":2582, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628394310, "dur":459, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628394806, "dur":1202, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628396049, "dur":97, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628396146, "dur":11906, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628408056, "dur":764, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628408925, "dur":67, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628409138, "dur":11527, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752056628395725, "dur":430, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752056628396171, "dur":1069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1752056628397271, "dur":10785, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752056628396485, "dur":11611, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628395786, "dur":398, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752056628396185, "dur":11901, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628395847, "dur":350, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752056628396198, "dur":11856, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752056628396626, "dur":11515, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752056628396667, "dur":11448, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628395973, "dur":253, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752056628396227, "dur":11902, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752056628396750, "dur":11322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752056628396790, "dur":11272, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752056628396856, "dur":11211, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628396176, "dur":9262, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752056628407517, "dur":455, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":11, "ts":1752056628405439, "dur":2537, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752056628396255, "dur":11871, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752056628396325, "dur":11794, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752056628396363, "dur":11743, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752056628396407, "dur":11700, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752056628396470, "dur":11661, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628395770, "dur":403, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752056628396206, "dur":11844, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752056628396526, "dur":11566, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752056628396574, "dur":11508, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628395898, "dur":310, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752056628396209, "dur":11837, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628395925, "dur":291, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752056628396217, "dur":11827, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752056628396709, "dur":11367, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628396022, "dur":213, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752056628396235, "dur":11813, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628396058, "dur":185, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752056628396244, "dur":11884, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752056628425601, "dur":341, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11572, "tid": 4825, "ts": 1752056628426766, "dur": 6920, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628433739, "dur": 3292, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11572, "tid": 4825, "ts": 1752056628426535, "dur": 10543, "ph": "X", "name": "Write chrome-trace events", "args": {} },
