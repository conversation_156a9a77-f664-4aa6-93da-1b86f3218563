http://schemas.android.com/apk/res-auto;;${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values/freeformwindow.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values-v30/freeformwindow.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values-v31/styles.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values-v21/styles.xml,${\:unityLibrary*release*MAIN*sourceProvider*0*resDir*0}/values-v28/styles.xml,+color:staticSplashScreenBackgroundColor,0,V"#231F20";+id:unitySurfaceView,1,V"";+string:game_view_content_description,2,V"Game view";FreeformWindowSize_phone,3,V"";FreeformWindowSize_phone,4,V"phone";FreeformWindowOrientation_landscape,3,V"";FreeformWindowOrientation_landscape,4,V"landscape";FreeformWindowSize_tablet,3,V"";FreeformWindowSize_tablet,4,V"tablet";FreeformWindowSize_maximize,3,V"";FreeformWindowSize_maximize,4,V"maximize";FreeformWindowOrientation_portrait,3,V"";FreeformWindowOrientation_portrait,4,V"portrait";+style:UnityThemeSelector.Translucent,5,VD@style/UnityThemeSelector,android\:windowIsTranslucent:true,android\:windowBackground:@android\:color/transparent,;UnityThemeSelector,5,VDBaseUnityTheme,android\:windowBackground:@android\:color/black,;BaseUnityGameActivityTheme,5,VDTheme.AppCompat.Light.NoActionBar,android\:windowBackground:@android\:color/black,;BaseUnityGameActivityTheme,6,VDTheme.AppCompat.Light.NoActionBar,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:windowSplashScreenAnimatedIcon:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenBackground:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenIconBackgroundColor:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenAnimationDuration:0,;BaseUnityTheme,5,VDandroid\:Theme.Holo.Light.NoActionBar.Fullscreen,;BaseUnityTheme,7,VDandroid\:Theme.Material.Light.NoActionBar.Fullscreen,;BaseUnityTheme,8,VDandroid\:Theme.Material.Light.NoActionBar.Fullscreen,android\:windowLayoutInDisplayCutoutMode:default,;BaseUnityTheme,6,VDandroid\:Theme.Holo.Light.NoActionBar.Fullscreen,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:windowSplashScreenAnimatedIcon:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenBackground:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenIconBackgroundColor:@color/staticSplashScreenBackgroundColor,android\:windowSplashScreenAnimationDuration:0,;