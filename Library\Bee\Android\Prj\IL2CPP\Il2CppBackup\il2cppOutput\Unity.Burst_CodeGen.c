﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE (void);
extern void BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F (void);
extern void BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B (void);
extern void FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828 (void);
extern void BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285 (void);
extern void BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4 (void);
extern void BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849 (void);
extern void BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83 (void);
extern void BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69 (void);
extern void BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7 (void);
extern void BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821 (void);
extern void BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6 (void);
extern void BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67 (void);
extern void BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A (void);
extern void BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540 (void);
extern void BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88 (void);
extern void PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5 (void);
extern void BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16 (void);
extern void BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684 (void);
extern void BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B (void);
extern void BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C (void);
extern void BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102 (void);
extern void BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730 (void);
extern void BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000 (void);
extern void BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1 (void);
extern void BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE (void);
extern void BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844 (void);
extern void BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6 (void);
extern void BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7 (void);
extern void BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D (void);
extern void BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5 (void);
extern void BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33 (void);
extern void BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772 (void);
extern void BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8 (void);
extern void BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673 (void);
extern void BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF (void);
extern void BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313 (void);
extern void BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37 (void);
extern void BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0 (void);
extern void BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766 (void);
extern void BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1 (void);
extern void BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4 (void);
extern void BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464 (void);
extern void BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54 (void);
extern void BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1 (void);
extern void BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E (void);
extern void BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF (void);
extern void BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9 (void);
extern void BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3 (void);
extern void BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF (void);
extern void BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C (void);
extern void BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252 (void);
extern void BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E (void);
extern void BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43 (void);
extern void BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642 (void);
extern void BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD (void);
extern void BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A (void);
extern void BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9 (void);
extern void BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583 (void);
extern void BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE (void);
extern void BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979 (void);
extern void BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA (void);
extern void BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0 (void);
extern void BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78 (void);
extern void PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1 (void);
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141 (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846 (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055 (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3 (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789 (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488 (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7 (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2 (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8 (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14 (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233 (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388 (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6 (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60 (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C (void);
extern void SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD (void);
extern void PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F (void);
extern void AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7 (void);
static Il2CppMethodPointer s_methodPointers[90] = 
{
	BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE,
	BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F,
	BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B,
	FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828,
	BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285,
	BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4,
	BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849,
	BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83,
	BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69,
	BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7,
	BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821,
	BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6,
	BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67,
	BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A,
	BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540,
	BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88,
	PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5,
	BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16,
	BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684,
	BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B,
	BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C,
	BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102,
	BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730,
	BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000,
	BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1,
	BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE,
	BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844,
	BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6,
	BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7,
	BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D,
	BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5,
	BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33,
	BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772,
	BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8,
	BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673,
	BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF,
	BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313,
	BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37,
	BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0,
	BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766,
	BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1,
	BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4,
	BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464,
	BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54,
	BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1,
	BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E,
	BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF,
	BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9,
	BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3,
	BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF,
	BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C,
	BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252,
	BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E,
	BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43,
	BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642,
	BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD,
	BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A,
	BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9,
	BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583,
	BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE,
	BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979,
	BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA,
	BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0,
	BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78,
	PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1,
	NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141,
	NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846,
	FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055,
	FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3,
	FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789,
	FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488,
	tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7,
	tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2,
	tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8,
	tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14,
	tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233,
	tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388,
	tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6,
	tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF,
	tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B,
	tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60,
	tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD,
	PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F,
	AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7,
};
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[17] = 
{
	{ 0x06000042, NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk },
	{ 0x06000043, NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk },
	{ 0x06000044, FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk },
	{ 0x06000045, FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk },
	{ 0x06000046, FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk },
	{ 0x06000047, FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk },
	{ 0x06000048, tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk },
	{ 0x06000049, tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk },
	{ 0x0600004A, tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk },
	{ 0x0600004B, tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk },
	{ 0x0600004C, tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk },
	{ 0x0600004D, tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk },
	{ 0x0600004E, tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk },
	{ 0x0600004F, tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk },
	{ 0x06000050, tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk },
	{ 0x06000051, tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk },
	{ 0x06000052, tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk },
};
static const int32_t s_InvokerIndices[90] = 
{
	10870,
	16420,
	16420,
	10698,
	8468,
	10537,
	8468,
	8468,
	10698,
	10870,
	10870,
	16420,
	16291,
	12691,
	16420,
	12691,
	10870,
	12691,
	11458,
	12003,
	11995,
	11994,
	12006,
	11994,
	12006,
	12007,
	12008,
	12002,
	11997,
	11998,
	12000,
	12009,
	13154,
	12001,
	11459,
	11457,
	13964,
	11526,
	11526,
	14168,
	11457,
	13516,
	13018,
	15910,
	14165,
	13498,
	13498,
	13498,
	13498,
	13503,
	14672,
	15971,
	15971,
	16027,
	14683,
	13503,
	14683,
	14477,
	14683,
	11173,
	11461,
	12004,
	11996,
	16420,
	10870,
	636,
	10515,
	1385,
	10537,
	10637,
	10698,
	10637,
	7763,
	10537,
	8777,
	8776,
	10537,
	10855,
	10855,
	10537,
	10855,
	10856,
	-1,
	-1,
	-1,
	-1,
	-1,
	12171,
	10870,
	4560,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x02000013, { 0, 3 } },
	{ 0x02000014, { 3, 5 } },
};
extern const uint32_t g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38;
extern const uint32_t g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C;
extern const uint32_t g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783;
extern const uint32_t g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22;
extern const uint32_t g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A;
extern const uint32_t g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428;
extern const uint32_t g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C;
static const Il2CppRGCTXDefinition s_rgctxValues[8] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule = 
{
	"Unity.Burst.dll",
	90,
	s_methodPointers,
	17,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	8,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
