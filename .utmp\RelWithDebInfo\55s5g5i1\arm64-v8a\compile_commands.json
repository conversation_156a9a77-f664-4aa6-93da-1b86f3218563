[{"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAApplication.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAConfiguration.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGADebug.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAEntry.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInput.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputKeyEvent.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputMotionEvent.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp"}, {"directory": "D:/My Project/Driving Simulator Game Z TEC/.utmp/RelWithDebInfo/55s5g5i1/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.30F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android23 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGASoftKeyboard.cpp.o -c \"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp\"", "file": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp"}]