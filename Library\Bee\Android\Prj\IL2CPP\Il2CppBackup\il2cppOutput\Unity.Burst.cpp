﻿#include "pch-cpp.hpp"






struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AssumeRangeAttribute_t06ACC8EEDB5AB2CE78FDE39A4F36B674A1CE06CA;
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct BurstCompileAttribute_t35957F7418CF3B99A40C9E1C66CD3C56094A2C9D;
struct BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9;
struct BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388;
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3;
struct ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct String_t;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct FakeDelegate_t659588AB379C77AF08088ED8B2E2A5ECFF38CE16;
struct PreserveAttribute_tA1799B67558808CC16DE11D04CC1D42AAA569133;
struct PreserveAttribute_t54BBA699FC0C1DD99BED77D21CADC33A352E1999;
struct PreserveAttribute_tDEA15EF9DCAB8AC4428ED72A2A1377384FE7C27B;

IL2CPP_EXTERN_C RuntimeClass* BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NumberFormatKind_t0CCF7872121CBA35A7D6296565B4A7554FB275E4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____07DB995E8ED2CFB0AB71EBA69F3A3EC07D5C6AC10C0C64F33E94ED2949B348AA_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____C69994AC61B52FBCEA582D6CCCD595C12E00BDB18F0C6F593FB6B393CAEDB08C_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D0067CAD9A63E0813759A2BB841051CA73570C0DA2E08E840A8EB45DB6A7A010_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D5B592C05DC25B5032553F1B27F4139BE95E881F73DB33B02B05AB20C3F9981E_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral052A90A585030A2D7669CC7D2884B0D78760C742;
IL2CPP_EXTERN_C String_t* _stringLiteral095255162964C376C50DCE630D972167CA5AE0D8;
IL2CPP_EXTERN_C String_t* _stringLiteral6CE6C7F7F72B90957BFCD4BAD12273C41A1C3421;
IL2CPP_EXTERN_C String_t* _stringLiteral733F2C0F892979C2C29E7E7599E36E7BC6DA158B;
IL2CPP_EXTERN_C String_t* _stringLiteral79DA35A084D67D3A4C946D97765E49E456B15354;
IL2CPP_EXTERN_C String_t* _stringLiteral7EAAC6CF31A5C9BD93736FBA006E9BA2CA274A12;
IL2CPP_EXTERN_C String_t* _stringLiteral9CC59FAE21126961EEC9FFFF150ADD139F72F4F4;
IL2CPP_EXTERN_C String_t* _stringLiteralC00E97A4D6DA0A1E727CA6FCAC517CF439F3A016;
IL2CPP_EXTERN_C String_t* _stringLiteralEEA647B69ECF2FB3DD083E36418FF930832E0BEF;
IL2CPP_EXTERN_C String_t* _stringLiteralF944DCD635F9801F7AC90A407FBC479964DEC024;
IL2CPP_EXTERN_C String_t* _stringLiteralFFEAABBBE67A35DBB7CF309C3EC21780633775FD;
IL2CPP_EXTERN_C const RuntimeType* BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* BurstRuntime_tA87CEB6EE77F6DA708C87C3DAEC7862E3A1B0EA1_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t7A78175E99B61C7B4022EA3D1E12E92F7F669089 
{
};
struct U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F  : public RuntimeObject
{
};
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA  : public RuntimeObject
{
};
struct BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8  : public RuntimeObject
{
};
struct BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9  : public RuntimeObject
{
	bool ____enableBurstCompilation;
	bool ____enableBurstSafetyChecks;
	bool ___U3CIsGlobalU3Ek__BackingField;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___U3COptionsChangedU3Ek__BackingField;
};
struct BurstRuntime_tA87CEB6EE77F6DA708C87C3DAEC7862E3A1B0EA1  : public RuntimeObject
{
};
struct BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct SharedStatic_t83F4045688B6DB97142DC2BCAE88140D165FFE35  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct FakeDelegate_t659588AB379C77AF08088ED8B2E2A5ECFF38CE16  : public RuntimeObject
{
	MethodInfo_t* ___U3CMethodU3Ek__BackingField;
};
struct AssumeRangeAttribute_t06ACC8EEDB5AB2CE78FDE39A4F36B674A1CE06CA  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct BurstCompileAttribute_t35957F7418CF3B99A40C9E1C66CD3C56094A2C9D  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct BurstTargetCpuAttribute_t9FFC44FA1778436FADA062F78C46C3FEE9E54BA9  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	String_t* ___U3CConditionStringU3Ek__BackingField;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Hash128_t93367F504B687578F893CDBCD13FB95AC8A87A40 
{
	uint64_t ___u64_0;
	uint64_t ___u64_1;
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	Type_t* ___U3CProducerTypeU3Ek__BackingField;
};
struct MethodBase_t  : public MemberInfo_t
{
};
struct SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5 
{
	int8_t ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D256_tFFE4CE163BD2DCEAA09662C2BCC33B3C37AB0D22 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D256_tFFE4CE163BD2DCEAA09662C2BCC33B3C37AB0D22__padding[256];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D3_t2857C07F0A23FB025DA0D81FCD2BE07B4ADCC026 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D3_t2857C07F0A23FB025DA0D81FCD2BE07B4ADCC026__padding[3];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D32_tF5E240ACF4B30B5A5F8C77E9E49CC2F8559D76D9 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D32_tF5E240ACF4B30B5A5F8C77E9E49CC2F8559D76D9__padding[32];
	};
};
#pragma pack(pop, tp)
struct PreserveAttribute_tA1799B67558808CC16DE11D04CC1D42AAA569133  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct PreserveAttribute_t54BBA699FC0C1DD99BED77D21CADC33A352E1999  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			float ___m_floatingPoint;
		};
		#pragma pack(pop, tp)
		struct
		{
			float ___m_floatingPoint_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint32_t ___m_integer;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint32_t ___m_integer_forAlignmentOnly;
		};
	};
};
struct tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			double ___m_floatingPoint;
		};
		#pragma pack(pop, tp)
		struct
		{
			double ___m_floatingPoint_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint64_t ___m_integer;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint64_t ___m_integer_forAlignmentOnly;
		};
	};
};
struct PreserveAttribute_tDEA15EF9DCAB8AC4428ED72A2A1377384FE7C27B  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C 
{
	union
	{
		struct
		{
			uint32_t ___FixedElementField;
		};
		uint8_t U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C__padding[140];
	};
};
struct BindingFlags_t5DC2835E4AE9C1862B3AD172EF35B6A5F4F1812C 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct MethodInfo_t  : public MethodBase_t
{
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct BurstLogType_t32BAD4D4FA2FDDC811A5259BE8EDB83420F37651 
{
	int32_t ___value__;
};
struct CutoffMode_tD217C369361D912E502CBA1D5D85FE6E0DA0393E 
{
	int32_t ___value__;
};
struct NumberBufferKind_t73D318B7611BA8F3C16006DDDBB90959AD1C3B86 
{
	int32_t ___value__;
};
struct NumberFormatKind_t0CCF7872121CBA35A7D6296565B4A7554FB275E4 
{
	uint8_t ___value__;
};
struct tBigInt_t6A436AD3913A2950571338A5018B48B299987358 
{
	int32_t ___m_length;
	U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C ___m_blocks;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 
{
	uint8_t ___Kind;
	int8_t ___AlignAndSize;
	uint8_t ___Specifier;
	bool ___Lowercase;
};
struct FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_pinvoke
{
	uint8_t ___Kind;
	int8_t ___AlignAndSize;
	uint8_t ___Specifier;
	int32_t ___Lowercase;
};
struct FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_com
{
	uint8_t ___Kind;
	int8_t ___AlignAndSize;
	uint8_t ___Specifier;
	int32_t ___Lowercase;
};
struct NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4 
{
	uint8_t* ____buffer;
	int32_t ___Kind;
	int32_t ___DigitsCount;
	int32_t ___Scale;
	bool ___IsNegative;
};
struct NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_pinvoke
{
	uint8_t* ____buffer;
	int32_t ___Kind;
	int32_t ___DigitsCount;
	int32_t ___Scale;
	int32_t ___IsNegative;
};
struct NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_com
{
	uint8_t* ____buffer;
	int32_t ___Kind;
	int32_t ___DigitsCount;
	int32_t ___Scale;
	int32_t ___IsNegative;
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F_StaticFields
{
	__StaticArrayInitTypeSizeU3D256_tFFE4CE163BD2DCEAA09662C2BCC33B3C37AB0D22 ___07DB995E8ED2CFB0AB71EBA69F3A3EC07D5C6AC10C0C64F33E94ED2949B348AA;
	__StaticArrayInitTypeSizeU3D32_tF5E240ACF4B30B5A5F8C77E9E49CC2F8559D76D9 ___C69994AC61B52FBCEA582D6CCCD595C12E00BDB18F0C6F593FB6B393CAEDB08C;
	int64_t ___D0067CAD9A63E0813759A2BB841051CA73570C0DA2E08E840A8EB45DB6A7A010;
	__StaticArrayInitTypeSizeU3D3_t2857C07F0A23FB025DA0D81FCD2BE07B4ADCC026 ___D5B592C05DC25B5032553F1B27F4139BE95E881F73DB33B02B05AB20C3F9981E;
};
struct BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields
{
	bool ____IsEnabled;
	BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* ___Options;
	MethodInfo_t* ___DummyMethodInfo;
};
struct BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields
{
	bool ___ForceDisableBurstCompilation;
	bool ___ForceBurstCompilationSynchronously;
	bool ___IsSecondaryUnityProcess;
};
struct BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields
{
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___SplitByColon;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___logTable;
	UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* ___g_PowerOf10_U32;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___InfinityString;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___NanString;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248  : public RuntimeArray
{
	ALIGN_FIELD (8) String_t* m_Items[1];

	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA  : public RuntimeArray
{
	ALIGN_FIELD (8) uint32_t m_Items[1];

	inline uint32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint32_t value)
	{
		m_Items[index] = value;
	}
};
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB  : public RuntimeArray
{
	ALIGN_FIELD (8) Il2CppChar m_Items[1];

	inline Il2CppChar GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Il2CppChar* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Il2CppChar value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Il2CppChar GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Il2CppChar* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Il2CppChar value)
	{
		m_Items[index] = value;
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2 (Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_isGlobal, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MethodInfo_t* Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D (Type_t* __this, String_t* ___0_name, int32_t ___1_bindingAttr, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4_inline (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JobsUtility_set_JobCompilerEnabled_m14CB399441AB02D65BE11D74E9CC3E313420D72B (bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69_inline (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* Environment_GetCommandLineArgs_mD29CFA1CD3C84F9BD91152E70302E908114A831D (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Environment_GetEnvironmentVariable_mAF8CC6EC1CB916789ABB16A02C032F89E508C21A (String_t* ___0_variable, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478 (String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerService_RuntimeLog_m564B95C963C0AF9DED99AA57BE28DCC90CFE3925 (void* ___0_userData, int32_t ___1_logType, uint8_t* ___2_message, uint8_t* ___3_filename, int32_t ___4_lineNumber, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstDiscardAttribute__ctor_m0AC3131F7C5B377DCA604CD7BB8AC4AA4E161033 (BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConditionalAttribute__ctor_m948BC90599397308C76C433D98236C3BD81BF27F (ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0* __this, String_t* ___0_conditionString, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JobProducerTypeAttribute__ctor_m562A2FC62E2DF7109DD703C0270B0B372607C534 (JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847* __this, Type_t* ___0_producerType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerService_Log_m4224BFA55310174B3BD9E0A2F15F3CF85757C253 (void* ___0_userData, int32_t ___1_logType, uint8_t* ___2_message, uint8_t* ___3_filename, int32_t ___4_lineNumber, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsafeUtility_MemCpy_m5CEA91ACDADC522E584AE3A2AB2B0B74393A9177 (void* ___0_destination, void* ___1_source, int64_t ___2_size, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int32_t ___3_align, int32_t ___4_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int32_t ___3_align, int32_t ___4_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, float ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_formatOptions, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, double ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_formatOptions, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int64_t ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313 (int32_t ___0_value, bool ___1_uppercase, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* __this, int32_t ___0_kind, uint8_t* ___1_buffer, int32_t ___2_digitsCount, int32_t ___3_scale, bool ___4_isNegative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_nMaxDigits, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___5_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_zeroPadding, bool ___5_outputPositiveSign, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___0_number, int32_t ___1_pos, bool ___2_isCorrectlyRounded, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___0_number, int32_t ___1_nMaxDigits, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_nMaxDigits, uint8_t ___5_expChar, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772 (int64_t ___0_value, int32_t ___1_basis, int32_t ___2_zeroPadding, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, uint8_t ___0_kind, int8_t ___1_alignAndSize, uint8_t ___2_specifier, bool ___3_lowercase, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464 (uint8_t* ___0_dig, int32_t ___1_i, bool ___2_isCorrectlyRounded, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_pLarge, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_pSmall, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_pLarge, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_pSmall, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, uint32_t ___0_val, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR tBigInt_t6A436AD3913A2950571338A5018B48B299987358 BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43 (int32_t ___0_i, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, uint32_t ___2_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, uint64_t ___0_val, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_shift, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_exponent, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_input, uint32_t ___2_exponent, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_exponent, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_input, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_inline (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, int32_t ___0_idx, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54 (uint32_t ___0_val, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pDividend, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_divisor, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6 (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388 (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_mantissa, bool ___4_isNegative, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___5_formatOptions, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Math_Max_m530EBA549AFD98CFC2BD29FE86C6376E67DF11CF (int32_t ___0_val1, int32_t ___1_val2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE (uint64_t ___0_mantissa, int32_t ___1_exponent, uint32_t ___2_mantissaHighBitIdx, bool ___3_hasUnequalMargins, int32_t ___4_cutoffMode, uint32_t ___5_cutoffNumber, uint8_t* ___6_pOutBuffer, uint32_t ___7_bufferSize, int32_t* ___8_pOutExponent, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60 (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_m918500C1EFB475181349A79989BB79BB36102894 (String_t* ___0_format, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___1_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3 (Hash128_t93367F504B687578F893CDBCD13FB95AC8A87A40* __this, uint64_t ___0_u64_0, uint64_t ___1_u64_1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void* BurstCompilerService_GetOrCreateSharedMemory_m1293EB3119CBEE41DBCC0E3B2235601BD927BFE6 (Hash128_t93367F504B687578F893CDBCD13FB95AC8A87A40* ___0_key, uint32_t ___1_size_of, uint32_t ___2_alignment, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE (BurstCompileAttribute_t35957F7418CF3B99A40C9E1C66CD3C56094A2C9D* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F (const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral095255162964C376C50DCE630D972167CA5AE0D8);
		s_Il2CppMethodInitialized = true;
	}
	{
		BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* L_0 = (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9*)il2cpp_codegen_object_new(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var);
		BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285(L_0, (bool)1, NULL);
		((BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var))->___Options = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var))->___Options), (void*)L_0);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_1 = { reinterpret_cast<intptr_t> (BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(il2cpp_defaults.systemtype_class);
		Type_t* L_2;
		L_2 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_1, NULL);
		NullCheck(L_2);
		MethodInfo_t* L_3;
		L_3 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_2, _stringLiteral095255162964C376C50DCE630D972167CA5AE0D8, ((int32_t)40), NULL);
		((BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var))->___DummyMethodInfo = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var))->___DummyMethodInfo), (void*)L_3);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MethodInfo_t* FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828 (FakeDelegate_t659588AB379C77AF08088ED8B2E2A5ECFF38CE16* __this, const RuntimeMethod* method) 
{
	{
		MethodInfo_t* L_0 = __this->___U3CMethodU3Ek__BackingField;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_isGlobal, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		bool L_0 = ___0_isGlobal;
		__this->___U3CIsGlobalU3Ek__BackingField = L_0;
		BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849(__this, (bool)1, NULL);
		BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83(__this, (bool)1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsGlobalU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t G_B5_0 = 0;
	int32_t G_B4_0 = 0;
	{
		bool L_0;
		L_0 = BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4_inline(__this, NULL);
		if (!L_0)
		{
			goto IL_0012;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var);
		bool L_1 = ((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___ForceDisableBurstCompilation;
		if (!L_1)
		{
			goto IL_0012;
		}
	}
	{
		___0_value = (bool)0;
	}

IL_0012:
	{
		bool L_2 = __this->____enableBurstCompilation;
		bool L_3 = ___0_value;
		bool L_4 = ___0_value;
		__this->____enableBurstCompilation = L_4;
		bool L_5;
		L_5 = BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4_inline(__this, NULL);
		if (!L_5)
		{
			G_B5_0 = ((((int32_t)((((int32_t)L_2) == ((int32_t)L_3))? 1 : 0)) == ((int32_t)0))? 1 : 0);
			goto IL_0039;
		}
		G_B4_0 = ((((int32_t)((((int32_t)L_2) == ((int32_t)L_3))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
	{
		bool L_6 = ___0_value;
		JobsUtility_set_JobCompilerEnabled_m14CB399441AB02D65BE11D74E9CC3E313420D72B(L_6, NULL);
		bool L_7 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var);
		((BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompiler_t2715484E1FF256726FC4D4D8E17C35A4C8DFA2B8_il2cpp_TypeInfo_var))->____IsEnabled = L_7;
		G_B5_0 = G_B4_0;
	}

IL_0039:
	{
		if (!G_B5_0)
		{
			goto IL_0041;
		}
	}
	{
		BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7(__this, NULL);
	}

IL_0041:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____enableBurstSafetyChecks;
		bool L_1 = ___0_value;
		bool L_2 = ___0_value;
		__this->____enableBurstSafetyChecks = L_2;
		if (!((((int32_t)((((int32_t)L_0) == ((int32_t)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0))
		{
			goto IL_0021;
		}
	}
	{
		BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7(__this, NULL);
		BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821(__this, NULL);
	}

IL_0021:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___U3COptionsChangedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0;
		L_0 = BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69_inline(__this, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000b;
		}
		G_B1_0 = L_1;
	}
	{
		return;
	}

IL_000b:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821 (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral052A90A585030A2D7669CC7D2884B0D78760C742);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral79DA35A084D67D3A4C946D97765E49E456B15354);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9CC59FAE21126961EEC9FFFF150ADD139F72F4F4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF944DCD635F9801F7AC90A407FBC479964DEC024);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* V_1 = NULL;
	int32_t V_2 = 0;
	String_t* V_3 = NULL;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0;
		L_0 = Environment_GetCommandLineArgs_mD29CFA1CD3C84F9BD91152E70302E908114A831D(NULL);
		V_1 = L_0;
		V_2 = 0;
		goto IL_003c;
	}

IL_000a:
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = V_1;
		int32_t L_2 = V_2;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		String_t* L_4 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		V_3 = L_4;
		String_t* L_5 = V_3;
		bool L_6;
		L_6 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_5, _stringLiteral052A90A585030A2D7669CC7D2884B0D78760C742, NULL);
		if (L_6)
		{
			goto IL_002a;
		}
	}
	{
		String_t* L_7 = V_3;
		bool L_8;
		L_8 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_7, _stringLiteral79DA35A084D67D3A4C946D97765E49E456B15354, NULL);
		if (L_8)
		{
			goto IL_0032;
		}
	}
	{
		goto IL_0038;
	}

IL_002a:
	{
		((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___ForceDisableBurstCompilation = (bool)1;
		goto IL_0038;
	}

IL_0032:
	{
		((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___ForceBurstCompilationSynchronously = (bool)1;
	}

IL_0038:
	{
		int32_t L_9 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_003c:
	{
		int32_t L_10 = V_2;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_11 = V_1;
		NullCheck(L_11);
		if ((((int32_t)L_10) < ((int32_t)((int32_t)(((RuntimeArray*)L_11)->max_length)))))
		{
			goto IL_000a;
		}
	}
	{
		bool L_12;
		L_12 = BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67(NULL);
		if (!L_12)
		{
			goto IL_0055;
		}
	}
	{
		((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___ForceDisableBurstCompilation = (bool)1;
		((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___IsSecondaryUnityProcess = (bool)1;
	}

IL_0055:
	{
		String_t* L_13;
		L_13 = Environment_GetEnvironmentVariable_mAF8CC6EC1CB916789ABB16A02C032F89E508C21A(_stringLiteral9CC59FAE21126961EEC9FFFF150ADD139F72F4F4, NULL);
		V_0 = L_13;
		String_t* L_14 = V_0;
		bool L_15;
		L_15 = String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478(L_14, NULL);
		if (L_15)
		{
			goto IL_007b;
		}
	}
	{
		String_t* L_16 = V_0;
		bool L_17;
		L_17 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_16, _stringLiteralF944DCD635F9801F7AC90A407FBC479964DEC024, NULL);
		if (!L_17)
		{
			goto IL_007b;
		}
	}
	{
		((BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_StaticFields*)il2cpp_codegen_static_fields_for(BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9_il2cpp_TypeInfo_var))->___ForceDisableBurstCompilation = (bool)1;
	}

IL_007b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67 (const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A (uint8_t* ___0_message, int32_t ___1_logType, uint8_t* ___2_fileName, int32_t ___3_lineNumber, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___1_logType;
		uint8_t* L_1 = ___0_message;
		uint8_t* L_2 = ___2_fileName;
		int32_t L_3 = ___3_lineNumber;
		BurstCompilerService_RuntimeLog_m564B95C963C0AF9DED99AA57BE28DCC90CFE3925((void*)((intptr_t)0), L_0, L_1, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstRuntime_tA87CEB6EE77F6DA708C87C3DAEC7862E3A1B0EA1_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7EAAC6CF31A5C9BD93736FBA006E9BA2CA274A12);
		s_Il2CppMethodInitialized = true;
	}
	{
		BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388* L_0 = (BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388*)il2cpp_codegen_object_new(BurstDiscardAttribute_t860FCEA3A7BADFD735A65A36C99B894EB2AAB388_il2cpp_TypeInfo_var);
		BurstDiscardAttribute__ctor_m0AC3131F7C5B377DCA604CD7BB8AC4AA4E161033(L_0, NULL);
		ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0* L_1 = (ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0*)il2cpp_codegen_object_new(ConditionalAttribute_tBBDC0DB2EEFFA35C6A2802ADF484A1BD5B400BE0_il2cpp_TypeInfo_var);
		ConditionalAttribute__ctor_m948BC90599397308C76C433D98236C3BD81BF27F(L_1, _stringLiteral7EAAC6CF31A5C9BD93736FBA006E9BA2CA274A12, NULL);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (BurstRuntime_tA87CEB6EE77F6DA708C87C3DAEC7862E3A1B0EA1_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(il2cpp_defaults.systemtype_class);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847* L_4 = (JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847*)il2cpp_codegen_object_new(JobProducerTypeAttribute_t4F137BDC862349EC5FD1A70D1ACABEDFEF2C6847_il2cpp_TypeInfo_var);
		JobProducerTypeAttribute__ctor_m562A2FC62E2DF7109DD703C0270B0B372607C534(L_4, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88 (uint8_t* ___0_message, int32_t ___1_logType, uint8_t* ___2_fileName, int32_t ___3_lineNumber, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___1_logType;
		uint8_t* L_1 = ___0_message;
		int32_t L_2 = ___3_lineNumber;
		BurstCompilerService_Log_m4224BFA55310174B3BD9E0A2F15F3CF85757C253((void*)((intptr_t)0), L_0, L_1, (uint8_t*)((intptr_t)0), L_2, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5 (PreserveAttribute_tA1799B67558808CC16DE11D04CC1D42AAA569133* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16 (uint8_t* ___0_dest, int32_t ___1_destLength, uint8_t* ___2_src, int32_t ___3_srcLength, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t G_B3_0 = 0;
	{
		int32_t L_0 = ___3_srcLength;
		int32_t L_1 = ___1_destLength;
		if ((((int32_t)L_0) > ((int32_t)L_1)))
		{
			goto IL_0007;
		}
	}
	{
		int32_t L_2 = ___3_srcLength;
		G_B3_0 = L_2;
		goto IL_0008;
	}

IL_0007:
	{
		int32_t L_3 = ___1_destLength;
		G_B3_0 = L_3;
	}

IL_0008:
	{
		V_0 = G_B3_0;
		uint8_t* L_4 = ___0_dest;
		int32_t L_5 = V_0;
		*((int16_t*)((uint8_t*)il2cpp_codegen_subtract((intptr_t)L_4, 2))) = (int16_t)((int32_t)(uint16_t)L_5);
		uint8_t* L_6 = ___0_dest;
		int32_t L_7 = V_0;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_6, L_7))) = (int8_t)0;
		uint8_t* L_8 = ___0_dest;
		uint8_t* L_9 = ___2_src;
		int32_t L_10 = V_0;
		UnsafeUtility_MemCpy_m5CEA91ACDADC522E584AE3A2AB2B0B74393A9177((void*)L_8, (void*)L_9, ((int64_t)L_10), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint8_t* ___3_src, int32_t ___4_srcLength, int32_t ___5_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t G_B5_0 = 0;
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___5_formatOptionsRaw)));
		V_0 = L_0;
		uint8_t* L_1 = ___0_dest;
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_4 = V_0;
		int8_t L_5 = L_4.___AlignAndSize;
		int32_t L_6 = ___4_srcLength;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_1, L_2, L_3, L_5, L_6, NULL);
		if (!L_7)
		{
			goto IL_001c;
		}
	}
	{
		return;
	}

IL_001c:
	{
		int32_t L_8 = ___2_destLength;
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = *((int32_t*)L_9);
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_8, L_10));
		int32_t L_11 = ___4_srcLength;
		int32_t L_12 = V_1;
		if ((((int32_t)L_11) > ((int32_t)L_12)))
		{
			goto IL_002a;
		}
	}
	{
		int32_t L_13 = ___4_srcLength;
		G_B5_0 = L_13;
		goto IL_002b;
	}

IL_002a:
	{
		int32_t L_14 = V_1;
		G_B5_0 = L_14;
	}

IL_002b:
	{
		V_2 = G_B5_0;
		int32_t L_15 = V_2;
		if ((((int32_t)L_15) <= ((int32_t)0)))
		{
			goto IL_0053;
		}
	}
	{
		uint8_t* L_16 = ___0_dest;
		int32_t* L_17 = ___1_destIndex;
		int32_t L_18 = *((int32_t*)L_17);
		uint8_t* L_19 = ___3_src;
		int32_t L_20 = V_2;
		UnsafeUtility_MemCpy_m5CEA91ACDADC522E584AE3A2AB2B0B74393A9177((void*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_16, L_18)), (void*)L_19, ((int64_t)L_20), NULL);
		int32_t* L_21 = ___1_destIndex;
		int32_t* L_22 = ___1_destIndex;
		int32_t L_23 = *((int32_t*)L_22);
		int32_t L_24 = V_2;
		*((int32_t*)L_21) = (int32_t)((int32_t)il2cpp_codegen_add(L_23, L_24));
		uint8_t* L_25 = ___0_dest;
		int32_t* L_26 = ___1_destIndex;
		int32_t L_27 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_28 = V_0;
		int8_t L_29 = L_28.___AlignAndSize;
		int32_t L_30 = ___4_srcLength;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_31;
		L_31 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_25, L_26, L_27, L_29, L_30, NULL);
	}

IL_0053:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, float ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		uint8_t* L_1 = ___0_dest;
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = ___2_destLength;
		float L_4 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA(L_1, L_2, L_3, L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, double ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		uint8_t* L_1 = ___0_dest;
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = ___2_destLength;
		double L_4 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0(L_1, L_2, L_3, L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, bool ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	int32_t G_B3_0 = 0;
	{
		bool L_0 = ___3_value;
		if (L_0)
		{
			goto IL_0006;
		}
	}
	{
		G_B3_0 = 5;
		goto IL_0007;
	}

IL_0006:
	{
		G_B3_0 = 4;
	}

IL_0007:
	{
		V_0 = G_B3_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_1 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_1 = L_1;
		uint8_t* L_2 = ___0_dest;
		int32_t* L_3 = ___1_destIndex;
		int32_t L_4 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_5 = V_1;
		int8_t L_6 = L_5.___AlignAndSize;
		int32_t L_7 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_2, L_3, L_4, L_6, L_7, NULL);
		if (!L_8)
		{
			goto IL_0023;
		}
	}
	{
		return;
	}

IL_0023:
	{
		bool L_9 = ___3_value;
		if (!L_9)
		{
			goto IL_0078;
		}
	}
	{
		int32_t* L_10 = ___1_destIndex;
		int32_t L_11 = *((int32_t*)L_10);
		int32_t L_12 = ___2_destLength;
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_002c;
		}
	}
	{
		return;
	}

IL_002c:
	{
		uint8_t* L_13 = ___0_dest;
		int32_t* L_14 = ___1_destIndex;
		int32_t* L_15 = ___1_destIndex;
		int32_t L_16 = *((int32_t*)L_15);
		V_2 = L_16;
		int32_t L_17 = V_2;
		*((int32_t*)L_14) = (int32_t)((int32_t)il2cpp_codegen_add(L_17, 1));
		int32_t L_18 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_13, L_18))) = (int8_t)((int32_t)84);
		int32_t* L_19 = ___1_destIndex;
		int32_t L_20 = *((int32_t*)L_19);
		int32_t L_21 = ___2_destLength;
		if ((((int32_t)L_20) < ((int32_t)L_21)))
		{
			goto IL_0040;
		}
	}
	{
		return;
	}

IL_0040:
	{
		uint8_t* L_22 = ___0_dest;
		int32_t* L_23 = ___1_destIndex;
		int32_t* L_24 = ___1_destIndex;
		int32_t L_25 = *((int32_t*)L_24);
		V_2 = L_25;
		int32_t L_26 = V_2;
		*((int32_t*)L_23) = (int32_t)((int32_t)il2cpp_codegen_add(L_26, 1));
		int32_t L_27 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_22, L_27))) = (int8_t)((int32_t)114);
		int32_t* L_28 = ___1_destIndex;
		int32_t L_29 = *((int32_t*)L_28);
		int32_t L_30 = ___2_destLength;
		if ((((int32_t)L_29) < ((int32_t)L_30)))
		{
			goto IL_0054;
		}
	}
	{
		return;
	}

IL_0054:
	{
		uint8_t* L_31 = ___0_dest;
		int32_t* L_32 = ___1_destIndex;
		int32_t* L_33 = ___1_destIndex;
		int32_t L_34 = *((int32_t*)L_33);
		V_2 = L_34;
		int32_t L_35 = V_2;
		*((int32_t*)L_32) = (int32_t)((int32_t)il2cpp_codegen_add(L_35, 1));
		int32_t L_36 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_31, L_36))) = (int8_t)((int32_t)117);
		int32_t* L_37 = ___1_destIndex;
		int32_t L_38 = *((int32_t*)L_37);
		int32_t L_39 = ___2_destLength;
		if ((((int32_t)L_38) < ((int32_t)L_39)))
		{
			goto IL_0068;
		}
	}
	{
		return;
	}

IL_0068:
	{
		uint8_t* L_40 = ___0_dest;
		int32_t* L_41 = ___1_destIndex;
		int32_t* L_42 = ___1_destIndex;
		int32_t L_43 = *((int32_t*)L_42);
		V_2 = L_43;
		int32_t L_44 = V_2;
		*((int32_t*)L_41) = (int32_t)((int32_t)il2cpp_codegen_add(L_44, 1));
		int32_t L_45 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_40, L_45))) = (int8_t)((int32_t)101);
		goto IL_00dc;
	}

IL_0078:
	{
		int32_t* L_46 = ___1_destIndex;
		int32_t L_47 = *((int32_t*)L_46);
		int32_t L_48 = ___2_destLength;
		if ((((int32_t)L_47) < ((int32_t)L_48)))
		{
			goto IL_007e;
		}
	}
	{
		return;
	}

IL_007e:
	{
		uint8_t* L_49 = ___0_dest;
		int32_t* L_50 = ___1_destIndex;
		int32_t* L_51 = ___1_destIndex;
		int32_t L_52 = *((int32_t*)L_51);
		V_2 = L_52;
		int32_t L_53 = V_2;
		*((int32_t*)L_50) = (int32_t)((int32_t)il2cpp_codegen_add(L_53, 1));
		int32_t L_54 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_49, L_54))) = (int8_t)((int32_t)70);
		int32_t* L_55 = ___1_destIndex;
		int32_t L_56 = *((int32_t*)L_55);
		int32_t L_57 = ___2_destLength;
		if ((((int32_t)L_56) < ((int32_t)L_57)))
		{
			goto IL_0092;
		}
	}
	{
		return;
	}

IL_0092:
	{
		uint8_t* L_58 = ___0_dest;
		int32_t* L_59 = ___1_destIndex;
		int32_t* L_60 = ___1_destIndex;
		int32_t L_61 = *((int32_t*)L_60);
		V_2 = L_61;
		int32_t L_62 = V_2;
		*((int32_t*)L_59) = (int32_t)((int32_t)il2cpp_codegen_add(L_62, 1));
		int32_t L_63 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_58, L_63))) = (int8_t)((int32_t)97);
		int32_t* L_64 = ___1_destIndex;
		int32_t L_65 = *((int32_t*)L_64);
		int32_t L_66 = ___2_destLength;
		if ((((int32_t)L_65) < ((int32_t)L_66)))
		{
			goto IL_00a6;
		}
	}
	{
		return;
	}

IL_00a6:
	{
		uint8_t* L_67 = ___0_dest;
		int32_t* L_68 = ___1_destIndex;
		int32_t* L_69 = ___1_destIndex;
		int32_t L_70 = *((int32_t*)L_69);
		V_2 = L_70;
		int32_t L_71 = V_2;
		*((int32_t*)L_68) = (int32_t)((int32_t)il2cpp_codegen_add(L_71, 1));
		int32_t L_72 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_67, L_72))) = (int8_t)((int32_t)108);
		int32_t* L_73 = ___1_destIndex;
		int32_t L_74 = *((int32_t*)L_73);
		int32_t L_75 = ___2_destLength;
		if ((((int32_t)L_74) < ((int32_t)L_75)))
		{
			goto IL_00ba;
		}
	}
	{
		return;
	}

IL_00ba:
	{
		uint8_t* L_76 = ___0_dest;
		int32_t* L_77 = ___1_destIndex;
		int32_t* L_78 = ___1_destIndex;
		int32_t L_79 = *((int32_t*)L_78);
		V_2 = L_79;
		int32_t L_80 = V_2;
		*((int32_t*)L_77) = (int32_t)((int32_t)il2cpp_codegen_add(L_80, 1));
		int32_t L_81 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_76, L_81))) = (int8_t)((int32_t)115);
		int32_t* L_82 = ___1_destIndex;
		int32_t L_83 = *((int32_t*)L_82);
		int32_t L_84 = ___2_destLength;
		if ((((int32_t)L_83) < ((int32_t)L_84)))
		{
			goto IL_00ce;
		}
	}
	{
		return;
	}

IL_00ce:
	{
		uint8_t* L_85 = ___0_dest;
		int32_t* L_86 = ___1_destIndex;
		int32_t* L_87 = ___1_destIndex;
		int32_t L_88 = *((int32_t*)L_87);
		V_2 = L_88;
		int32_t L_89 = V_2;
		*((int32_t*)L_86) = (int32_t)((int32_t)il2cpp_codegen_add(L_89, 1));
		int32_t L_90 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_85, L_90))) = (int8_t)((int32_t)101);
	}

IL_00dc:
	{
		uint8_t* L_91 = ___0_dest;
		int32_t* L_92 = ___1_destIndex;
		int32_t L_93 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_94 = V_1;
		int8_t L_95 = L_94.___AlignAndSize;
		int32_t L_96 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_97;
		L_97 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_91, L_92, L_93, L_95, L_96, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, Il2CppChar ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	int32_t G_B5_0 = 0;
	int32_t G_B21_0 = 0;
	{
		Il2CppChar L_0 = ___3_value;
		if ((((int32_t)L_0) <= ((int32_t)((int32_t)127))))
		{
			goto IL_0013;
		}
	}
	{
		Il2CppChar L_1 = ___3_value;
		if ((((int32_t)L_1) <= ((int32_t)((int32_t)2047))))
		{
			goto IL_0010;
		}
	}
	{
		G_B5_0 = 3;
		goto IL_0014;
	}

IL_0010:
	{
		G_B5_0 = 2;
		goto IL_0014;
	}

IL_0013:
	{
		G_B5_0 = 1;
	}

IL_0014:
	{
		V_0 = G_B5_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_2 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_1 = L_2;
		uint8_t* L_3 = ___0_dest;
		int32_t* L_4 = ___1_destIndex;
		int32_t L_5 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_6 = V_1;
		int8_t L_7 = L_6.___AlignAndSize;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_3, L_4, L_5, L_7, 1, NULL);
		if (!L_8)
		{
			goto IL_0030;
		}
	}
	{
		return;
	}

IL_0030:
	{
		int32_t L_9 = V_0;
		if ((!(((uint32_t)L_9) == ((uint32_t)1))))
		{
			goto IL_004d;
		}
	}
	{
		int32_t* L_10 = ___1_destIndex;
		int32_t L_11 = *((int32_t*)L_10);
		int32_t L_12 = ___2_destLength;
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_003a;
		}
	}
	{
		return;
	}

IL_003a:
	{
		uint8_t* L_13 = ___0_dest;
		int32_t* L_14 = ___1_destIndex;
		int32_t* L_15 = ___1_destIndex;
		int32_t L_16 = *((int32_t*)L_15);
		V_2 = L_16;
		int32_t L_17 = V_2;
		*((int32_t*)L_14) = (int32_t)((int32_t)il2cpp_codegen_add(L_17, 1));
		int32_t L_18 = V_2;
		Il2CppChar L_19 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_13, L_18))) = (int8_t)((int32_t)(uint8_t)L_19);
		goto IL_014e;
	}

IL_004d:
	{
		int32_t L_20 = V_0;
		if ((!(((uint32_t)L_20) == ((uint32_t)2))))
		{
			goto IL_008f;
		}
	}
	{
		int32_t* L_21 = ___1_destIndex;
		int32_t L_22 = *((int32_t*)L_21);
		int32_t L_23 = ___2_destLength;
		if ((((int32_t)L_22) < ((int32_t)L_23)))
		{
			goto IL_0057;
		}
	}
	{
		return;
	}

IL_0057:
	{
		uint8_t* L_24 = ___0_dest;
		int32_t* L_25 = ___1_destIndex;
		int32_t* L_26 = ___1_destIndex;
		int32_t L_27 = *((int32_t*)L_26);
		V_2 = L_27;
		int32_t L_28 = V_2;
		*((int32_t*)L_25) = (int32_t)((int32_t)il2cpp_codegen_add(L_28, 1));
		int32_t L_29 = V_2;
		Il2CppChar L_30 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_24, L_29))) = (int8_t)((int32_t)(uint8_t)((int32_t)(((int32_t)((int32_t)L_30>>6))|((int32_t)192))));
		int32_t* L_31 = ___1_destIndex;
		int32_t L_32 = *((int32_t*)L_31);
		int32_t L_33 = ___2_destLength;
		if ((((int32_t)L_32) < ((int32_t)L_33)))
		{
			goto IL_0073;
		}
	}
	{
		return;
	}

IL_0073:
	{
		uint8_t* L_34 = ___0_dest;
		int32_t* L_35 = ___1_destIndex;
		int32_t* L_36 = ___1_destIndex;
		int32_t L_37 = *((int32_t*)L_36);
		V_2 = L_37;
		int32_t L_38 = V_2;
		*((int32_t*)L_35) = (int32_t)((int32_t)il2cpp_codegen_add(L_38, 1));
		int32_t L_39 = V_2;
		Il2CppChar L_40 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_34, L_39))) = (int8_t)((int32_t)(uint8_t)((int32_t)(((int32_t)((int32_t)L_40&((int32_t)63)))|((int32_t)128))));
		goto IL_014e;
	}

IL_008f:
	{
		int32_t L_41 = V_0;
		if ((!(((uint32_t)L_41) == ((uint32_t)3))))
		{
			goto IL_014e;
		}
	}
	{
		Il2CppChar L_42 = ___3_value;
		if ((((int32_t)L_42) < ((int32_t)((int32_t)55296))))
		{
			goto IL_00ab;
		}
	}
	{
		Il2CppChar L_43 = ___3_value;
		G_B21_0 = ((((int32_t)((((int32_t)L_43) > ((int32_t)((int32_t)57343)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_00ac;
	}

IL_00ab:
	{
		G_B21_0 = 0;
	}

IL_00ac:
	{
		if (!G_B21_0)
		{
			goto IL_00f5;
		}
	}
	{
		int32_t* L_44 = ___1_destIndex;
		int32_t L_45 = *((int32_t*)L_44);
		int32_t L_46 = ___2_destLength;
		if ((((int32_t)L_45) < ((int32_t)L_46)))
		{
			goto IL_00b4;
		}
	}
	{
		return;
	}

IL_00b4:
	{
		uint8_t* L_47 = ___0_dest;
		int32_t* L_48 = ___1_destIndex;
		int32_t* L_49 = ___1_destIndex;
		int32_t L_50 = *((int32_t*)L_49);
		V_2 = L_50;
		int32_t L_51 = V_2;
		*((int32_t*)L_48) = (int32_t)((int32_t)il2cpp_codegen_add(L_51, 1));
		int32_t L_52 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_47, L_52))) = (int8_t)((int32_t)239);
		int32_t* L_53 = ___1_destIndex;
		int32_t L_54 = *((int32_t*)L_53);
		int32_t L_55 = ___2_destLength;
		if ((((int32_t)L_54) < ((int32_t)L_55)))
		{
			goto IL_00cb;
		}
	}
	{
		return;
	}

IL_00cb:
	{
		uint8_t* L_56 = ___0_dest;
		int32_t* L_57 = ___1_destIndex;
		int32_t* L_58 = ___1_destIndex;
		int32_t L_59 = *((int32_t*)L_58);
		V_2 = L_59;
		int32_t L_60 = V_2;
		*((int32_t*)L_57) = (int32_t)((int32_t)il2cpp_codegen_add(L_60, 1));
		int32_t L_61 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_56, L_61))) = (int8_t)((int32_t)191);
		int32_t* L_62 = ___1_destIndex;
		int32_t L_63 = *((int32_t*)L_62);
		int32_t L_64 = ___2_destLength;
		if ((((int32_t)L_63) < ((int32_t)L_64)))
		{
			goto IL_00e2;
		}
	}
	{
		return;
	}

IL_00e2:
	{
		uint8_t* L_65 = ___0_dest;
		int32_t* L_66 = ___1_destIndex;
		int32_t* L_67 = ___1_destIndex;
		int32_t L_68 = *((int32_t*)L_67);
		V_2 = L_68;
		int32_t L_69 = V_2;
		*((int32_t*)L_66) = (int32_t)((int32_t)il2cpp_codegen_add(L_69, 1));
		int32_t L_70 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_65, L_70))) = (int8_t)((int32_t)189);
		goto IL_014e;
	}

IL_00f5:
	{
		int32_t* L_71 = ___1_destIndex;
		int32_t L_72 = *((int32_t*)L_71);
		int32_t L_73 = ___2_destLength;
		if ((((int32_t)L_72) < ((int32_t)L_73)))
		{
			goto IL_00fb;
		}
	}
	{
		return;
	}

IL_00fb:
	{
		uint8_t* L_74 = ___0_dest;
		int32_t* L_75 = ___1_destIndex;
		int32_t* L_76 = ___1_destIndex;
		int32_t L_77 = *((int32_t*)L_76);
		V_2 = L_77;
		int32_t L_78 = V_2;
		*((int32_t*)L_75) = (int32_t)((int32_t)il2cpp_codegen_add(L_78, 1));
		int32_t L_79 = V_2;
		Il2CppChar L_80 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_74, L_79))) = (int8_t)((int32_t)(uint8_t)((int32_t)(((int32_t)((int32_t)L_80>>((int32_t)12)))|((int32_t)224))));
		int32_t* L_81 = ___1_destIndex;
		int32_t L_82 = *((int32_t*)L_81);
		int32_t L_83 = ___2_destLength;
		if ((((int32_t)L_82) < ((int32_t)L_83)))
		{
			goto IL_0118;
		}
	}
	{
		return;
	}

IL_0118:
	{
		uint8_t* L_84 = ___0_dest;
		int32_t* L_85 = ___1_destIndex;
		int32_t* L_86 = ___1_destIndex;
		int32_t L_87 = *((int32_t*)L_86);
		V_2 = L_87;
		int32_t L_88 = V_2;
		*((int32_t*)L_85) = (int32_t)((int32_t)il2cpp_codegen_add(L_88, 1));
		int32_t L_89 = V_2;
		Il2CppChar L_90 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_84, L_89))) = (int8_t)((int32_t)(uint8_t)((int32_t)(((int32_t)(((int32_t)((int32_t)L_90>>6))&((int32_t)63)))|((int32_t)128))));
		int32_t* L_91 = ___1_destIndex;
		int32_t L_92 = *((int32_t*)L_91);
		int32_t L_93 = ___2_destLength;
		if ((((int32_t)L_92) < ((int32_t)L_93)))
		{
			goto IL_0137;
		}
	}
	{
		return;
	}

IL_0137:
	{
		uint8_t* L_94 = ___0_dest;
		int32_t* L_95 = ___1_destIndex;
		int32_t* L_96 = ___1_destIndex;
		int32_t L_97 = *((int32_t*)L_96);
		V_2 = L_97;
		int32_t L_98 = V_2;
		*((int32_t*)L_95) = (int32_t)((int32_t)il2cpp_codegen_add(L_98, 1));
		int32_t L_99 = V_2;
		Il2CppChar L_100 = ___3_value;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_94, L_99))) = (int8_t)((int32_t)(uint8_t)((int32_t)(((int32_t)((int32_t)L_100&((int32_t)63)))|((int32_t)128))));
	}

IL_014e:
	{
		uint8_t* L_101 = ___0_dest;
		int32_t* L_102 = ___1_destIndex;
		int32_t L_103 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_104 = V_1;
		int8_t L_105 = L_104.___AlignAndSize;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_106;
		L_106 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_101, L_102, L_103, L_105, 1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint8_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		uint8_t* L_0 = ___0_dest;
		int32_t* L_1 = ___1_destIndex;
		int32_t L_2 = ___2_destLength;
		uint8_t L_3 = ___3_value;
		int32_t L_4 = ___4_formatOptionsRaw;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844(L_0, L_1, L_2, ((int64_t)(uint64_t)L_3), L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint16_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		uint8_t* L_0 = ___0_dest;
		int32_t* L_1 = ___1_destIndex;
		int32_t L_2 = ___2_destLength;
		uint16_t L_3 = ___3_value;
		int32_t L_4 = ___4_formatOptionsRaw;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844(L_0, L_1, L_2, ((int64_t)(uint64_t)L_3), L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint32_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		uint8_t* L_1 = ___0_dest;
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = ___2_destLength;
		uint32_t L_4 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_1, L_2, L_3, ((int64_t)(uint64_t)L_4), L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		uint8_t* L_1 = ___0_dest;
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = ___2_destLength;
		uint64_t L_4 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_1, L_2, L_3, L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int8_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_1 = V_0;
		uint8_t L_2 = L_1.___Kind;
		if ((!(((uint32_t)L_2) == ((uint32_t)3))))
		{
			goto IL_001f;
		}
	}
	{
		uint8_t* L_3 = ___0_dest;
		int32_t* L_4 = ___1_destIndex;
		int32_t L_5 = ___2_destLength;
		int8_t L_6 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_7 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_3, L_4, L_5, ((int64_t)(uint64_t)((uint32_t)((int32_t)(uint8_t)L_6))), L_7, NULL);
		return;
	}

IL_001f:
	{
		uint8_t* L_8 = ___0_dest;
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = ___2_destLength;
		int8_t L_11 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_12 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8(L_8, L_9, L_10, ((int64_t)L_11), L_12, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int16_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_1 = V_0;
		uint8_t L_2 = L_1.___Kind;
		if ((!(((uint32_t)L_2) == ((uint32_t)3))))
		{
			goto IL_001f;
		}
	}
	{
		uint8_t* L_3 = ___0_dest;
		int32_t* L_4 = ___1_destIndex;
		int32_t L_5 = ___2_destLength;
		int16_t L_6 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_7 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_3, L_4, L_5, ((int64_t)(uint64_t)((uint32_t)((int32_t)(uint16_t)L_6))), L_7, NULL);
		return;
	}

IL_001f:
	{
		uint8_t* L_8 = ___0_dest;
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = ___2_destLength;
		int16_t L_11 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_12 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8(L_8, L_9, L_10, ((int64_t)L_11), L_12, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int32_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_1 = V_0;
		uint8_t L_2 = L_1.___Kind;
		if ((!(((uint32_t)L_2) == ((uint32_t)3))))
		{
			goto IL_001e;
		}
	}
	{
		uint8_t* L_3 = ___0_dest;
		int32_t* L_4 = ___1_destIndex;
		int32_t L_5 = ___2_destLength;
		int32_t L_6 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_7 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_3, L_4, L_5, ((int64_t)(uint64_t)((uint32_t)L_6)), L_7, NULL);
		return;
	}

IL_001e:
	{
		uint8_t* L_8 = ___0_dest;
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = ___2_destLength;
		int32_t L_11 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_12 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8(L_8, L_9, L_10, ((int64_t)L_11), L_12, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int64_t ___3_value, int32_t ___4_formatOptionsRaw, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_0 = (*(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*)((uintptr_t)(&___4_formatOptionsRaw)));
		V_0 = L_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_1 = V_0;
		uint8_t L_2 = L_1.___Kind;
		if ((!(((uint32_t)L_2) == ((uint32_t)3))))
		{
			goto IL_001d;
		}
	}
	{
		uint8_t* L_3 = ___0_dest;
		int32_t* L_4 = ___1_destIndex;
		int32_t L_5 = ___2_destLength;
		int64_t L_6 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_7 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33(L_3, L_4, L_5, L_6, L_7, NULL);
		return;
	}

IL_001d:
	{
		uint8_t* L_8 = ___0_dest;
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = ___2_destLength;
		int64_t L_11 = ___3_value;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_12 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8(L_8, L_9, L_10, L_11, L_12, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_options, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	int32_t V_1 = 0;
	uint64_t V_2 = 0;
	int32_t V_3 = 0;
	uint8_t* V_4 = NULL;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4 V_5;
	memset((&V_5), 0, sizeof(V_5));
	{
		int32_t L_0;
		L_0 = FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789((&___4_options), NULL);
		V_0 = L_0;
		uint32_t L_1 = V_0;
		if ((!(((uint32_t)L_1) >= ((uint32_t)2))))
		{
			goto IL_0011;
		}
	}
	{
		uint32_t L_2 = V_0;
		if ((!(((uint32_t)L_2) > ((uint32_t)((int32_t)36)))))
		{
			goto IL_0012;
		}
	}

IL_0011:
	{
		return;
	}

IL_0012:
	{
		V_1 = 0;
		uint64_t L_3 = ___3_value;
		V_2 = L_3;
	}

IL_0016:
	{
		uint64_t L_4 = V_2;
		uint32_t L_5 = V_0;
		V_2 = ((int64_t)((uint64_t)(int64_t)L_4/(uint64_t)(int64_t)((int64_t)(uint64_t)L_5)));
		int32_t L_6 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_6, 1));
		uint64_t L_7 = V_2;
		if (L_7)
		{
			goto IL_0016;
		}
	}
	{
		int32_t L_8 = V_1;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_8, 1));
		int32_t L_9 = V_1;
		uintptr_t L_10 = ((uintptr_t)((int32_t)il2cpp_codegen_add(L_9, 1)));
		int8_t* L_11 = (int8_t*) (L_10 ? alloca(L_10) : NULL);
		memset(L_11, 0, L_10);
		V_4 = (uint8_t*)(L_11);
		uint64_t L_12 = ___3_value;
		V_2 = L_12;
	}

IL_0030:
	{
		uint8_t* L_13 = V_4;
		int32_t L_14 = V_3;
		int32_t L_15 = L_14;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_15, 1));
		uint64_t L_16 = V_2;
		uint32_t L_17 = V_0;
		bool L_18;
		L_18 = FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3((&___4_options), NULL);
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint8_t L_19;
		L_19 = BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313(((int32_t)((int64_t)((uint64_t)(int64_t)L_16%(uint64_t)(int64_t)((int64_t)(uint64_t)L_17)))), L_18, NULL);
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_13, L_15))) = (int8_t)L_19;
		uint64_t L_20 = V_2;
		uint32_t L_21 = V_0;
		V_2 = ((int64_t)((uint64_t)(int64_t)L_20/(uint64_t)(int64_t)((int64_t)(uint64_t)L_21)));
		uint64_t L_22 = V_2;
		if (L_22)
		{
			goto IL_0030;
		}
	}
	{
		uint8_t* L_23 = V_4;
		int32_t L_24 = V_1;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_23, L_24))) = (int8_t)0;
		uint8_t* L_25 = V_4;
		int32_t L_26 = V_1;
		int32_t L_27 = V_1;
		NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141((&V_5), 0, L_25, L_26, L_27, (bool)0, NULL);
		uint8_t* L_28 = ___0_dest;
		int32_t* L_29 = ___1_destIndex;
		int32_t L_30 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_31 = ___4_options;
		uint8_t L_32 = L_31.___Specifier;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_33 = ___4_options;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673(L_28, L_29, L_30, (&V_5), L_32, L_33, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772 (int64_t ___0_value, int32_t ___1_basis, int32_t ___2_zeroPadding, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int64_t V_1 = 0;
	{
		V_0 = 0;
		int64_t L_0 = ___0_value;
		V_1 = L_0;
	}

IL_0004:
	{
		int64_t L_1 = V_1;
		int32_t L_2 = ___1_basis;
		V_1 = ((int64_t)(L_1/((int64_t)L_2)));
		int32_t L_3 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_3, 1));
		int64_t L_4 = V_1;
		if (L_4)
		{
			goto IL_0004;
		}
	}
	{
		int32_t L_5 = V_0;
		int32_t L_6 = ___2_zeroPadding;
		if ((((int32_t)L_5) >= ((int32_t)L_6)))
		{
			goto IL_0016;
		}
	}
	{
		int32_t L_7 = ___2_zeroPadding;
		V_0 = L_7;
	}

IL_0016:
	{
		int64_t L_8 = ___0_value;
		if ((((int64_t)L_8) >= ((int64_t)((int64_t)0))))
		{
			goto IL_001f;
		}
	}
	{
		int32_t L_9 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_001f:
	{
		int32_t L_10 = V_0;
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int64_t ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_options, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int64_t V_2 = 0;
	uint8_t* V_3 = NULL;
	int32_t V_4 = 0;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4 V_5;
	memset((&V_5), 0, sizeof(V_5));
	{
		int32_t L_0;
		L_0 = FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789((&___4_options), NULL);
		V_0 = L_0;
		int32_t L_1 = V_0;
		if ((((int32_t)L_1) < ((int32_t)2)))
		{
			goto IL_0011;
		}
	}
	{
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) <= ((int32_t)((int32_t)36))))
		{
			goto IL_0012;
		}
	}

IL_0011:
	{
		return;
	}

IL_0012:
	{
		V_1 = 0;
		int64_t L_3 = ___3_value;
		V_2 = L_3;
	}

IL_0016:
	{
		int64_t L_4 = V_2;
		int32_t L_5 = V_0;
		V_2 = ((int64_t)(L_4/((int64_t)L_5)));
		int32_t L_6 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_6, 1));
		int64_t L_7 = V_2;
		if (L_7)
		{
			goto IL_0016;
		}
	}
	{
		int32_t L_8 = V_1;
		uintptr_t L_9 = ((uintptr_t)((int32_t)il2cpp_codegen_add(L_8, 1)));
		int8_t* L_10 = (int8_t*) (L_9 ? alloca(L_9) : NULL);
		memset(L_10, 0, L_9);
		V_3 = (uint8_t*)(L_10);
		int64_t L_11 = ___3_value;
		V_2 = L_11;
		int32_t L_12 = V_1;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_12, 1));
	}

IL_0030:
	{
		uint8_t* L_13 = V_3;
		int32_t L_14 = V_4;
		int32_t L_15 = L_14;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_15, 1));
		int64_t L_16 = V_2;
		int32_t L_17 = V_0;
		bool L_18;
		L_18 = FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3((&___4_options), NULL);
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint8_t L_19;
		L_19 = BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313(((int32_t)((int64_t)(L_16%((int64_t)L_17)))), L_18, NULL);
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_13, L_15))) = (int8_t)L_19;
		int64_t L_20 = V_2;
		int32_t L_21 = V_0;
		V_2 = ((int64_t)(L_20/((int64_t)L_21)));
		int64_t L_22 = V_2;
		if (L_22)
		{
			goto IL_0030;
		}
	}
	{
		uint8_t* L_23 = V_3;
		int32_t L_24 = V_1;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_23, L_24))) = (int8_t)0;
		uint8_t* L_25 = V_3;
		int32_t L_26 = V_1;
		int32_t L_27 = V_1;
		int64_t L_28 = ___3_value;
		NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141((&V_5), 0, L_25, L_26, L_27, (bool)((((int64_t)L_28) < ((int64_t)((int64_t)0)))? 1 : 0), NULL);
		uint8_t* L_29 = ___0_dest;
		int32_t* L_30 = ___1_destIndex;
		int32_t L_31 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_32 = ___4_options;
		uint8_t L_33 = L_32.___Specifier;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_34 = ___4_options;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673(L_29, L_30, L_31, (&V_5), L_33, L_34, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_nMaxDigits, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___5_options, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	bool V_4 = false;
	uint8_t V_5 = 0;
	int32_t G_B10_0 = 0;
	int32_t G_B9_0 = 0;
	int32_t G_B11_0 = 0;
	int32_t G_B11_1 = 0;
	int32_t G_B20_0 = 0;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* G_B20_1 = NULL;
	int32_t G_B20_2 = 0;
	int32_t* G_B20_3 = NULL;
	uint8_t* G_B20_4 = NULL;
	int32_t G_B19_0 = 0;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* G_B19_1 = NULL;
	int32_t G_B19_2 = 0;
	int32_t* G_B19_3 = NULL;
	uint8_t* G_B19_4 = NULL;
	int32_t G_B21_0 = 0;
	int32_t G_B21_1 = 0;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* G_B21_2 = NULL;
	int32_t G_B21_3 = 0;
	int32_t* G_B21_4 = NULL;
	uint8_t* G_B21_5 = NULL;
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_0 = ___3_number;
		int32_t L_1 = L_0->___Kind;
		V_0 = (bool)((((int32_t)L_1) == ((int32_t)1))? 1 : 0);
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_2 = ___3_number;
		int32_t L_3 = L_2->___Kind;
		if (L_3)
		{
			goto IL_002c;
		}
	}
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_4 = ___5_options;
		uint8_t L_5 = L_4.___Kind;
		if (L_5)
		{
			goto IL_002c;
		}
	}
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_6 = ___5_options;
		uint8_t L_7 = L_6.___Specifier;
		if (L_7)
		{
			goto IL_002c;
		}
	}
	{
		(&___5_options)->___Kind = 1;
	}

IL_002c:
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_8 = ___5_options;
		uint8_t L_9 = L_8.___Kind;
		V_5 = L_9;
		uint8_t L_10 = V_5;
		if (!L_10)
		{
			goto IL_00aa;
		}
	}
	{
		uint8_t L_11 = V_5;
		if ((!(((uint32_t)((int32_t)il2cpp_codegen_subtract((int32_t)L_11, 1))) <= ((uint32_t)2))))
		{
			goto IL_00aa;
		}
	}
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_12 = ___3_number;
		int32_t L_13 = L_12->___DigitsCount;
		V_1 = L_13;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_14 = ___5_options;
		uint8_t L_15 = L_14.___Specifier;
		V_2 = L_15;
		V_3 = 0;
		int32_t L_16 = V_1;
		int32_t L_17 = V_2;
		if ((((int32_t)L_16) >= ((int32_t)L_17)))
		{
			goto IL_005b;
		}
	}
	{
		int32_t L_18 = V_2;
		int32_t L_19 = V_1;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_18, L_19));
		int32_t L_20 = V_2;
		V_1 = L_20;
	}

IL_005b:
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_21 = ___5_options;
		uint8_t L_22 = L_21.___Kind;
		V_4 = (bool)((((int32_t)L_22) == ((int32_t)2))? 1 : 0);
		int32_t L_23 = V_1;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_24 = ___3_number;
		bool L_25 = L_24->___IsNegative;
		bool L_26 = V_4;
		if (((int32_t)((int32_t)L_25|(int32_t)L_26)))
		{
			G_B10_0 = L_23;
			goto IL_0076;
		}
		G_B9_0 = L_23;
	}
	{
		G_B11_0 = 0;
		G_B11_1 = G_B9_0;
		goto IL_0077;
	}

IL_0076:
	{
		G_B11_0 = 1;
		G_B11_1 = G_B10_0;
	}

IL_0077:
	{
		V_1 = ((int32_t)il2cpp_codegen_add(G_B11_1, G_B11_0));
		uint8_t* L_27 = ___0_dest;
		int32_t* L_28 = ___1_destIndex;
		int32_t L_29 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_30 = ___5_options;
		int8_t L_31 = L_30.___AlignAndSize;
		int32_t L_32 = V_1;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_33;
		L_33 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_27, L_28, L_29, L_31, L_32, NULL);
		if (!L_33)
		{
			goto IL_008c;
		}
	}
	{
		return;
	}

IL_008c:
	{
		uint8_t* L_34 = ___0_dest;
		int32_t* L_35 = ___1_destIndex;
		int32_t L_36 = ___2_destLength;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_37 = ___3_number;
		int32_t L_38 = V_3;
		bool L_39 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF(L_34, L_35, L_36, L_37, L_38, L_39, NULL);
		uint8_t* L_40 = ___0_dest;
		int32_t* L_41 = ___1_destIndex;
		int32_t L_42 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_43 = ___5_options;
		int8_t L_44 = L_43.___AlignAndSize;
		int32_t L_45 = V_1;
		bool L_46;
		L_46 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_40, L_41, L_42, L_44, L_45, NULL);
		return;
	}

IL_00aa:
	{
		int32_t L_47 = ___4_nMaxDigits;
		if ((((int32_t)L_47) >= ((int32_t)1)))
		{
			goto IL_00b7;
		}
	}
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_48 = ___3_number;
		int32_t L_49 = L_48->___DigitsCount;
		___4_nMaxDigits = L_49;
	}

IL_00b7:
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_50 = ___3_number;
		int32_t L_51 = ___4_nMaxDigits;
		bool L_52 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4(L_50, L_51, L_52, NULL);
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_53 = ___3_number;
		int32_t L_54 = ___4_nMaxDigits;
		int32_t L_55;
		L_55 = BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766(L_53, L_54, NULL);
		V_1 = L_55;
		uint8_t* L_56 = ___0_dest;
		int32_t* L_57 = ___1_destIndex;
		int32_t L_58 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_59 = ___5_options;
		int8_t L_60 = L_59.___AlignAndSize;
		int32_t L_61 = V_1;
		bool L_62;
		L_62 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_56, L_57, L_58, L_60, L_61, NULL);
		if (!L_62)
		{
			goto IL_00dc;
		}
	}
	{
		return;
	}

IL_00dc:
	{
		uint8_t* L_63 = ___0_dest;
		int32_t* L_64 = ___1_destIndex;
		int32_t L_65 = ___2_destLength;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_66 = ___3_number;
		int32_t L_67 = ___4_nMaxDigits;
		bool L_68;
		L_68 = FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3((&___5_options), NULL);
		if (L_68)
		{
			G_B20_0 = L_67;
			G_B20_1 = L_66;
			G_B20_2 = L_65;
			G_B20_3 = L_64;
			G_B20_4 = L_63;
			goto IL_00ef;
		}
		G_B19_0 = L_67;
		G_B19_1 = L_66;
		G_B19_2 = L_65;
		G_B19_3 = L_64;
		G_B19_4 = L_63;
	}
	{
		G_B21_0 = ((int32_t)101);
		G_B21_1 = G_B19_0;
		G_B21_2 = G_B19_1;
		G_B21_3 = G_B19_2;
		G_B21_4 = G_B19_3;
		G_B21_5 = G_B19_4;
		goto IL_00f1;
	}

IL_00ef:
	{
		G_B21_0 = ((int32_t)69);
		G_B21_1 = G_B20_0;
		G_B21_2 = G_B20_1;
		G_B21_3 = G_B20_2;
		G_B21_4 = G_B20_3;
		G_B21_5 = G_B20_4;
	}

IL_00f1:
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1(G_B21_5, G_B21_4, G_B21_3, G_B21_2, G_B21_1, (uint8_t)G_B21_0, NULL);
		uint8_t* L_69 = ___0_dest;
		int32_t* L_70 = ___1_destIndex;
		int32_t L_71 = ___2_destLength;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_72 = ___5_options;
		int8_t L_73 = L_72.___AlignAndSize;
		int32_t L_74 = V_1;
		bool L_75;
		L_75 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_69, L_70, L_71, L_73, L_74, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_zeroPadding, bool ___5_outputPositiveSign, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	uint8_t* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_0 = ___3_number;
		bool L_1 = L_0->___IsNegative;
		if (!L_1)
		{
			goto IL_001e;
		}
	}
	{
		int32_t* L_2 = ___1_destIndex;
		int32_t L_3 = *((int32_t*)L_2);
		int32_t L_4 = ___2_destLength;
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_000e;
		}
	}
	{
		return;
	}

IL_000e:
	{
		uint8_t* L_5 = ___0_dest;
		int32_t* L_6 = ___1_destIndex;
		int32_t* L_7 = ___1_destIndex;
		int32_t L_8 = *((int32_t*)L_7);
		V_2 = L_8;
		int32_t L_9 = V_2;
		*((int32_t*)L_6) = (int32_t)((int32_t)il2cpp_codegen_add(L_9, 1));
		int32_t L_10 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_10))) = (int8_t)((int32_t)45);
		goto IL_0036;
	}

IL_001e:
	{
		bool L_11 = ___5_outputPositiveSign;
		if (!L_11)
		{
			goto IL_0036;
		}
	}
	{
		int32_t* L_12 = ___1_destIndex;
		int32_t L_13 = *((int32_t*)L_12);
		int32_t L_14 = ___2_destLength;
		if ((((int32_t)L_13) < ((int32_t)L_14)))
		{
			goto IL_0028;
		}
	}
	{
		return;
	}

IL_0028:
	{
		uint8_t* L_15 = ___0_dest;
		int32_t* L_16 = ___1_destIndex;
		int32_t* L_17 = ___1_destIndex;
		int32_t L_18 = *((int32_t*)L_17);
		V_2 = L_18;
		int32_t L_19 = V_2;
		*((int32_t*)L_16) = (int32_t)((int32_t)il2cpp_codegen_add(L_19, 1));
		int32_t L_20 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_15, L_20))) = (int8_t)((int32_t)43);
	}

IL_0036:
	{
		V_3 = 0;
		goto IL_0052;
	}

IL_003a:
	{
		int32_t* L_21 = ___1_destIndex;
		int32_t L_22 = *((int32_t*)L_21);
		int32_t L_23 = ___2_destLength;
		if ((((int32_t)L_22) < ((int32_t)L_23)))
		{
			goto IL_0040;
		}
	}
	{
		return;
	}

IL_0040:
	{
		uint8_t* L_24 = ___0_dest;
		int32_t* L_25 = ___1_destIndex;
		int32_t* L_26 = ___1_destIndex;
		int32_t L_27 = *((int32_t*)L_26);
		V_2 = L_27;
		int32_t L_28 = V_2;
		*((int32_t*)L_25) = (int32_t)((int32_t)il2cpp_codegen_add(L_28, 1));
		int32_t L_29 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_24, L_29))) = (int8_t)((int32_t)48);
		int32_t L_30 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_30, 1));
	}

IL_0052:
	{
		int32_t L_31 = V_3;
		int32_t L_32 = ___4_zeroPadding;
		if ((((int32_t)L_31) < ((int32_t)L_32)))
		{
			goto IL_003a;
		}
	}
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_33 = ___3_number;
		int32_t L_34 = L_33->___DigitsCount;
		V_0 = L_34;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_35 = ___3_number;
		uint8_t* L_36;
		L_36 = NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline(L_35, NULL);
		V_1 = L_36;
		V_4 = 0;
		goto IL_0087;
	}

IL_006a:
	{
		int32_t* L_37 = ___1_destIndex;
		int32_t L_38 = *((int32_t*)L_37);
		int32_t L_39 = ___2_destLength;
		if ((((int32_t)L_38) < ((int32_t)L_39)))
		{
			goto IL_0070;
		}
	}
	{
		return;
	}

IL_0070:
	{
		uint8_t* L_40 = ___0_dest;
		int32_t* L_41 = ___1_destIndex;
		int32_t* L_42 = ___1_destIndex;
		int32_t L_43 = *((int32_t*)L_42);
		V_2 = L_43;
		int32_t L_44 = V_2;
		*((int32_t*)L_41) = (int32_t)((int32_t)il2cpp_codegen_add(L_44, 1));
		int32_t L_45 = V_2;
		uint8_t* L_46 = V_1;
		int32_t L_47 = V_4;
		int32_t L_48 = (*(((uint8_t*)il2cpp_codegen_add((intptr_t)L_46, L_47))));
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_40, L_45))) = (int8_t)L_48;
		int32_t L_49 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_49, 1));
	}

IL_0087:
	{
		int32_t L_50 = V_4;
		int32_t L_51 = V_0;
		if ((((int32_t)L_50) < ((int32_t)L_51)))
		{
			goto IL_006a;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313 (int32_t ___0_value, bool ___1_uppercase, const RuntimeMethod* method) 
{
	int32_t G_B3_0 = 0;
	int32_t G_B9_0 = 0;
	{
		int32_t L_0 = ___0_value;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0007;
		}
	}
	{
		int32_t L_1 = ___0_value;
		G_B3_0 = L_1;
		goto IL_0009;
	}

IL_0007:
	{
		int32_t L_2 = ___0_value;
		G_B3_0 = ((-L_2));
	}

IL_0009:
	{
		___0_value = G_B3_0;
		int32_t L_3 = ___0_value;
		if ((((int32_t)L_3) > ((int32_t)((int32_t)9))))
		{
			goto IL_0016;
		}
	}
	{
		int32_t L_4 = ___0_value;
		return (uint8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)48), L_4)));
	}

IL_0016:
	{
		int32_t L_5 = ___0_value;
		if ((((int32_t)L_5) >= ((int32_t)((int32_t)36))))
		{
			goto IL_002b;
		}
	}
	{
		bool L_6 = ___1_uppercase;
		if (L_6)
		{
			goto IL_0022;
		}
	}
	{
		G_B9_0 = ((int32_t)97);
		goto IL_0024;
	}

IL_0022:
	{
		G_B9_0 = ((int32_t)65);
	}

IL_0024:
	{
		int32_t L_7 = ___0_value;
		return (uint8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(G_B9_0, ((int32_t)il2cpp_codegen_subtract(L_7, ((int32_t)10))))));
	}

IL_002b:
	{
		return (uint8_t)((int32_t)63);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int32_t ___3_align, int32_t ___4_length, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___3_align;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0014;
		}
	}
	{
		int32_t L_1 = ___3_align;
		___3_align = ((-L_1));
		uint8_t* L_2 = ___0_dest;
		int32_t* L_3 = ___1_destIndex;
		int32_t L_4 = ___2_destLength;
		int32_t L_5 = ___3_align;
		int32_t L_6 = ___4_length;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_2, L_3, L_4, L_5, L_6, NULL);
		return L_7;
	}

IL_0014:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, int32_t ___3_align, int32_t ___4_length, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = ___3_align;
		if ((((int32_t)L_0) <= ((int32_t)0)))
		{
			goto IL_0026;
		}
	}
	{
		goto IL_0021;
	}

IL_0006:
	{
		int32_t* L_1 = ___1_destIndex;
		int32_t L_2 = *((int32_t*)L_1);
		int32_t L_3 = ___2_destLength;
		if ((((int32_t)L_2) < ((int32_t)L_3)))
		{
			goto IL_000d;
		}
	}
	{
		return (bool)1;
	}

IL_000d:
	{
		uint8_t* L_4 = ___0_dest;
		int32_t* L_5 = ___1_destIndex;
		int32_t* L_6 = ___1_destIndex;
		int32_t L_7 = *((int32_t*)L_6);
		V_0 = L_7;
		int32_t L_8 = V_0;
		*((int32_t*)L_5) = (int32_t)((int32_t)il2cpp_codegen_add(L_8, 1));
		int32_t L_9 = V_0;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_4, L_9))) = (int8_t)((int32_t)32);
		int32_t L_10 = ___4_length;
		___4_length = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0021:
	{
		int32_t L_11 = ___4_length;
		int32_t L_12 = ___3_align;
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_0006;
		}
	}

IL_0026:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___0_number, int32_t ___1_nMaxDigits, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	bool V_2 = false;
	uint8_t* V_3 = NULL;
	int32_t V_4 = 0;
	{
		V_0 = 0;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_0 = ___0_number;
		int32_t L_1 = L_0->___Scale;
		V_1 = L_1;
		V_2 = (bool)0;
		int32_t L_2 = V_1;
		int32_t L_3 = ___1_nMaxDigits;
		if ((((int32_t)L_2) > ((int32_t)L_3)))
		{
			goto IL_0014;
		}
	}
	{
		int32_t L_4 = V_1;
		if ((((int32_t)L_4) >= ((int32_t)((int32_t)-3))))
		{
			goto IL_0018;
		}
	}

IL_0014:
	{
		V_1 = 1;
		V_2 = (bool)1;
	}

IL_0018:
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_5 = ___0_number;
		uint8_t* L_6;
		L_6 = NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline(L_5, NULL);
		V_3 = L_6;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_7 = ___0_number;
		bool L_8 = L_7->___IsNegative;
		if (!L_8)
		{
			goto IL_002b;
		}
	}
	{
		int32_t L_9 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_002b:
	{
		int32_t L_10 = V_1;
		if ((((int32_t)L_10) <= ((int32_t)0)))
		{
			goto IL_0045;
		}
	}

IL_002f:
	{
		uint8_t* L_11 = V_3;
		int32_t L_12 = (*(L_11));
		if (!L_12)
		{
			goto IL_0037;
		}
	}
	{
		uint8_t* L_13 = V_3;
		V_3 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_13, 1));
	}

IL_0037:
	{
		int32_t L_14 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_14, 1));
		int32_t L_15 = V_1;
		int32_t L_16 = ((int32_t)il2cpp_codegen_subtract(L_15, 1));
		V_1 = L_16;
		if ((((int32_t)L_16) > ((int32_t)0)))
		{
			goto IL_002f;
		}
	}
	{
		goto IL_0049;
	}

IL_0045:
	{
		int32_t L_17 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_17, 1));
	}

IL_0049:
	{
		uint8_t* L_18 = V_3;
		int32_t L_19 = (*(L_18));
		if (L_19)
		{
			goto IL_0051;
		}
	}
	{
		int32_t L_20 = V_1;
		if ((((int32_t)L_20) >= ((int32_t)0)))
		{
			goto IL_0071;
		}
	}

IL_0051:
	{
		int32_t L_21 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_21, 1));
		goto IL_005f;
	}

IL_0057:
	{
		int32_t L_22 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_22, 1));
		int32_t L_23 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_23, 1));
	}

IL_005f:
	{
		int32_t L_24 = V_1;
		if ((((int32_t)L_24) < ((int32_t)0)))
		{
			goto IL_0057;
		}
	}
	{
		goto IL_006d;
	}

IL_0065:
	{
		int32_t L_25 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_25, 1));
		uint8_t* L_26 = V_3;
		V_3 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_26, 1));
	}

IL_006d:
	{
		uint8_t* L_27 = V_3;
		int32_t L_28 = (*(L_27));
		if (L_28)
		{
			goto IL_0065;
		}
	}

IL_0071:
	{
		bool L_29 = V_2;
		if (!L_29)
		{
			goto IL_0099;
		}
	}
	{
		int32_t L_30 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_30, 1));
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_31 = ___0_number;
		int32_t L_32 = L_31->___Scale;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_32, 1));
		int32_t L_33 = V_4;
		if ((((int32_t)L_33) < ((int32_t)0)))
		{
			goto IL_008b;
		}
	}
	{
		int32_t L_34 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_34, 1));
	}

IL_008b:
	{
		int32_t L_35 = V_0;
		int32_t L_36 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		int32_t L_37;
		L_37 = BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772(((int64_t)L_36), ((int32_t)10), 2, NULL);
		V_0 = ((int32_t)il2cpp_codegen_add(L_35, L_37));
	}

IL_0099:
	{
		int32_t L_38 = V_0;
		return L_38;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___3_number, int32_t ___4_nMaxDigits, uint8_t ___5_expChar, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	uint8_t* V_2 = NULL;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 V_5;
	memset((&V_5), 0, sizeof(V_5));
	uint8_t* G_B12_0 = NULL;
	uint8_t* G_B11_0 = NULL;
	int32_t G_B13_0 = 0;
	uint8_t* G_B13_1 = NULL;
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_0 = ___3_number;
		int32_t L_1 = L_0->___Scale;
		V_0 = L_1;
		V_1 = (bool)0;
		int32_t L_2 = V_0;
		int32_t L_3 = ___4_nMaxDigits;
		if ((((int32_t)L_2) > ((int32_t)L_3)))
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_4 = V_0;
		if ((((int32_t)L_4) >= ((int32_t)((int32_t)-3))))
		{
			goto IL_0017;
		}
	}

IL_0013:
	{
		V_0 = 1;
		V_1 = (bool)1;
	}

IL_0017:
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_5 = ___3_number;
		uint8_t* L_6;
		L_6 = NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline(L_5, NULL);
		V_2 = L_6;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_7 = ___3_number;
		bool L_8 = L_7->___IsNegative;
		if (!L_8)
		{
			goto IL_003a;
		}
	}
	{
		int32_t* L_9 = ___1_destIndex;
		int32_t L_10 = *((int32_t*)L_9);
		int32_t L_11 = ___2_destLength;
		if ((((int32_t)L_10) < ((int32_t)L_11)))
		{
			goto IL_002c;
		}
	}
	{
		return;
	}

IL_002c:
	{
		uint8_t* L_12 = ___0_dest;
		int32_t* L_13 = ___1_destIndex;
		int32_t* L_14 = ___1_destIndex;
		int32_t L_15 = *((int32_t*)L_14);
		V_3 = L_15;
		int32_t L_16 = V_3;
		*((int32_t*)L_13) = (int32_t)((int32_t)il2cpp_codegen_add(L_16, 1));
		int32_t L_17 = V_3;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_12, L_17))) = (int8_t)((int32_t)45);
	}

IL_003a:
	{
		int32_t L_18 = V_0;
		if ((((int32_t)L_18) <= ((int32_t)0)))
		{
			goto IL_0068;
		}
	}

IL_003e:
	{
		int32_t* L_19 = ___1_destIndex;
		int32_t L_20 = *((int32_t*)L_19);
		int32_t L_21 = ___2_destLength;
		if ((((int32_t)L_20) < ((int32_t)L_21)))
		{
			goto IL_0044;
		}
	}
	{
		return;
	}

IL_0044:
	{
		uint8_t* L_22 = ___0_dest;
		int32_t* L_23 = ___1_destIndex;
		int32_t* L_24 = ___1_destIndex;
		int32_t L_25 = *((int32_t*)L_24);
		V_3 = L_25;
		int32_t L_26 = V_3;
		*((int32_t*)L_23) = (int32_t)((int32_t)il2cpp_codegen_add(L_26, 1));
		int32_t L_27 = V_3;
		uint8_t* L_28 = V_2;
		int32_t L_29 = (*(L_28));
		if (L_29)
		{
			G_B12_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_22, L_27));
			goto IL_0057;
		}
		G_B11_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_22, L_27));
	}
	{
		G_B13_0 = ((int32_t)48);
		G_B13_1 = G_B11_0;
		goto IL_005d;
	}

IL_0057:
	{
		uint8_t* L_30 = V_2;
		uint8_t* L_31 = L_30;
		V_2 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_31, 1));
		int32_t L_32 = (*(L_31));
		G_B13_0 = L_32;
		G_B13_1 = G_B12_0;
	}

IL_005d:
	{
		*((int8_t*)G_B13_1) = (int8_t)G_B13_0;
		int32_t L_33 = V_0;
		int32_t L_34 = ((int32_t)il2cpp_codegen_subtract(L_33, 1));
		V_0 = L_34;
		if ((((int32_t)L_34) > ((int32_t)0)))
		{
			goto IL_003e;
		}
	}
	{
		goto IL_007c;
	}

IL_0068:
	{
		int32_t* L_35 = ___1_destIndex;
		int32_t L_36 = *((int32_t*)L_35);
		int32_t L_37 = ___2_destLength;
		if ((((int32_t)L_36) < ((int32_t)L_37)))
		{
			goto IL_006e;
		}
	}
	{
		return;
	}

IL_006e:
	{
		uint8_t* L_38 = ___0_dest;
		int32_t* L_39 = ___1_destIndex;
		int32_t* L_40 = ___1_destIndex;
		int32_t L_41 = *((int32_t*)L_40);
		V_3 = L_41;
		int32_t L_42 = V_3;
		*((int32_t*)L_39) = (int32_t)((int32_t)il2cpp_codegen_add(L_42, 1));
		int32_t L_43 = V_3;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_38, L_43))) = (int8_t)((int32_t)48);
	}

IL_007c:
	{
		uint8_t* L_44 = V_2;
		int32_t L_45 = (*(L_44));
		if (L_45)
		{
			goto IL_0084;
		}
	}
	{
		int32_t L_46 = V_0;
		if ((((int32_t)L_46) >= ((int32_t)0)))
		{
			goto IL_00d4;
		}
	}

IL_0084:
	{
		int32_t* L_47 = ___1_destIndex;
		int32_t L_48 = *((int32_t*)L_47);
		int32_t L_49 = ___2_destLength;
		if ((((int32_t)L_48) < ((int32_t)L_49)))
		{
			goto IL_008a;
		}
	}
	{
		return;
	}

IL_008a:
	{
		uint8_t* L_50 = ___0_dest;
		int32_t* L_51 = ___1_destIndex;
		int32_t* L_52 = ___1_destIndex;
		int32_t L_53 = *((int32_t*)L_52);
		V_3 = L_53;
		int32_t L_54 = V_3;
		*((int32_t*)L_51) = (int32_t)((int32_t)il2cpp_codegen_add(L_54, 1));
		int32_t L_55 = V_3;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_50, L_55))) = (int8_t)((int32_t)46);
		goto IL_00b2;
	}

IL_009a:
	{
		int32_t* L_56 = ___1_destIndex;
		int32_t L_57 = *((int32_t*)L_56);
		int32_t L_58 = ___2_destLength;
		if ((((int32_t)L_57) < ((int32_t)L_58)))
		{
			goto IL_00a0;
		}
	}
	{
		return;
	}

IL_00a0:
	{
		uint8_t* L_59 = ___0_dest;
		int32_t* L_60 = ___1_destIndex;
		int32_t* L_61 = ___1_destIndex;
		int32_t L_62 = *((int32_t*)L_61);
		V_3 = L_62;
		int32_t L_63 = V_3;
		*((int32_t*)L_60) = (int32_t)((int32_t)il2cpp_codegen_add(L_63, 1));
		int32_t L_64 = V_3;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_59, L_64))) = (int8_t)((int32_t)48);
		int32_t L_65 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_65, 1));
	}

IL_00b2:
	{
		int32_t L_66 = V_0;
		if ((((int32_t)L_66) < ((int32_t)0)))
		{
			goto IL_009a;
		}
	}
	{
		goto IL_00d0;
	}

IL_00b8:
	{
		int32_t* L_67 = ___1_destIndex;
		int32_t L_68 = *((int32_t*)L_67);
		int32_t L_69 = ___2_destLength;
		if ((((int32_t)L_68) < ((int32_t)L_69)))
		{
			goto IL_00be;
		}
	}
	{
		return;
	}

IL_00be:
	{
		uint8_t* L_70 = ___0_dest;
		int32_t* L_71 = ___1_destIndex;
		int32_t* L_72 = ___1_destIndex;
		int32_t L_73 = *((int32_t*)L_72);
		V_3 = L_73;
		int32_t L_74 = V_3;
		*((int32_t*)L_71) = (int32_t)((int32_t)il2cpp_codegen_add(L_74, 1));
		int32_t L_75 = V_3;
		uint8_t* L_76 = V_2;
		uint8_t* L_77 = L_76;
		V_2 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_77, 1));
		int32_t L_78 = (*(L_77));
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_70, L_75))) = (int8_t)L_78;
	}

IL_00d0:
	{
		uint8_t* L_79 = V_2;
		int32_t L_80 = (*(L_79));
		if (L_80)
		{
			goto IL_00b8;
		}
	}

IL_00d4:
	{
		bool L_81 = V_1;
		if (!L_81)
		{
			goto IL_010d;
		}
	}
	{
		int32_t* L_82 = ___1_destIndex;
		int32_t L_83 = *((int32_t*)L_82);
		int32_t L_84 = ___2_destLength;
		if ((((int32_t)L_83) < ((int32_t)L_84)))
		{
			goto IL_00dd;
		}
	}
	{
		return;
	}

IL_00dd:
	{
		uint8_t* L_85 = ___0_dest;
		int32_t* L_86 = ___1_destIndex;
		int32_t* L_87 = ___1_destIndex;
		int32_t L_88 = *((int32_t*)L_87);
		V_3 = L_88;
		int32_t L_89 = V_3;
		*((int32_t*)L_86) = (int32_t)((int32_t)il2cpp_codegen_add(L_89, 1));
		int32_t L_90 = V_3;
		uint8_t L_91 = ___5_expChar;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_85, L_90))) = (int8_t)L_91;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_92 = ___3_number;
		int32_t L_93 = L_92->___Scale;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_93, 1));
		FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055((&V_5), 2, (int8_t)0, (uint8_t)2, (bool)0, NULL);
		uint8_t* L_94 = ___0_dest;
		int32_t* L_95 = ___1_destIndex;
		int32_t L_96 = ___2_destLength;
		int32_t L_97 = V_4;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_98 = V_5;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8(L_94, L_95, L_96, ((int64_t)L_97), L_98, NULL);
	}

IL_010d:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* ___0_number, int32_t ___1_pos, bool ___2_isCorrectlyRounded, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	int32_t V_1 = 0;
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_0 = ___0_number;
		uint8_t* L_1;
		L_1 = NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline(L_0, NULL);
		V_0 = L_1;
		V_1 = 0;
		goto IL_000f;
	}

IL_000b:
	{
		int32_t L_2 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_2, 1));
	}

IL_000f:
	{
		int32_t L_3 = V_1;
		int32_t L_4 = ___1_pos;
		if ((((int32_t)L_3) >= ((int32_t)L_4)))
		{
			goto IL_0019;
		}
	}
	{
		uint8_t* L_5 = V_0;
		int32_t L_6 = V_1;
		int32_t L_7 = (*(((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_6))));
		if (L_7)
		{
			goto IL_000b;
		}
	}

IL_0019:
	{
		int32_t L_8 = V_1;
		int32_t L_9 = ___1_pos;
		if ((!(((uint32_t)L_8) == ((uint32_t)L_9))))
		{
			goto IL_0063;
		}
	}
	{
		uint8_t* L_10 = V_0;
		int32_t L_11 = V_1;
		bool L_12 = ___2_isCorrectlyRounded;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_13;
		L_13 = BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464(L_10, L_11, L_12, NULL);
		if (!L_13)
		{
			goto IL_0063;
		}
	}
	{
		goto IL_002d;
	}

IL_0029:
	{
		int32_t L_14 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_14, 1));
	}

IL_002d:
	{
		int32_t L_15 = V_1;
		if ((((int32_t)L_15) <= ((int32_t)0)))
		{
			goto IL_003b;
		}
	}
	{
		uint8_t* L_16 = V_0;
		int32_t L_17 = V_1;
		int32_t L_18 = (*(((uint8_t*)il2cpp_codegen_add((intptr_t)L_16, ((int32_t)il2cpp_codegen_subtract(L_17, 1))))));
		if ((((int32_t)L_18) == ((int32_t)((int32_t)57))))
		{
			goto IL_0029;
		}
	}

IL_003b:
	{
		int32_t L_19 = V_1;
		if ((((int32_t)L_19) <= ((int32_t)0)))
		{
			goto IL_004c;
		}
	}
	{
		uint8_t* L_20 = V_0;
		int32_t L_21 = V_1;
		uint8_t* L_22 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_20, ((int32_t)il2cpp_codegen_subtract(L_21, 1))));
		int32_t L_23 = (*(L_22));
		*((int8_t*)L_22) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(L_23, 1)));
		goto IL_0071;
	}

IL_004c:
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_24 = ___0_number;
		int32_t* L_25 = (int32_t*)(&L_24->___Scale);
		int32_t* L_26 = L_25;
		int32_t L_27 = *((int32_t*)L_26);
		*((int32_t*)L_26) = (int32_t)((int32_t)il2cpp_codegen_add(L_27, 1));
		uint8_t* L_28 = V_0;
		*((int8_t*)L_28) = (int8_t)((int32_t)49);
		V_1 = 1;
		goto IL_0071;
	}

IL_005f:
	{
		int32_t L_29 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_29, 1));
	}

IL_0063:
	{
		int32_t L_30 = V_1;
		if ((((int32_t)L_30) <= ((int32_t)0)))
		{
			goto IL_0071;
		}
	}
	{
		uint8_t* L_31 = V_0;
		int32_t L_32 = V_1;
		int32_t L_33 = (*(((uint8_t*)il2cpp_codegen_add((intptr_t)L_31, ((int32_t)il2cpp_codegen_subtract(L_32, 1))))));
		if ((((int32_t)L_33) == ((int32_t)((int32_t)48))))
		{
			goto IL_005f;
		}
	}

IL_0071:
	{
		int32_t L_34 = V_1;
		if (L_34)
		{
			goto IL_007b;
		}
	}
	{
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_35 = ___0_number;
		L_35->___Scale = 0;
	}

IL_007b:
	{
		uint8_t* L_36 = V_0;
		int32_t L_37 = V_1;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_36, L_37))) = (int8_t)0;
		NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* L_38 = ___0_number;
		int32_t L_39 = V_1;
		L_38->___DigitsCount = L_39;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464 (uint8_t* ___0_dig, int32_t ___1_i, bool ___2_isCorrectlyRounded, const RuntimeMethod* method) 
{
	uint8_t V_0 = 0x0;
	{
		uint8_t* L_0 = ___0_dig;
		int32_t L_1 = ___1_i;
		int32_t L_2 = (*(((uint8_t*)il2cpp_codegen_add((intptr_t)L_0, L_1))));
		V_0 = (uint8_t)L_2;
		uint8_t L_3 = V_0;
		bool L_4 = ___2_isCorrectlyRounded;
		if (!((int32_t)(((((int32_t)L_3) == ((int32_t)0))? 1 : 0)|(int32_t)L_4)))
		{
			goto IL_000f;
		}
	}
	{
		return (bool)0;
	}

IL_000f:
	{
		uint8_t L_5 = V_0;
		return (bool)((((int32_t)((((int32_t)L_5) < ((int32_t)((int32_t)53)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54 (uint32_t ___0_val, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	{
		uint32_t L_0 = ___0_val;
		V_0 = ((int32_t)((uint32_t)L_0>>((int32_t)24)));
		uint32_t L_1 = V_0;
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable;
		uint32_t L_3 = V_0;
		NullCheck(L_2);
		uint32_t L_4 = L_3;
		uint8_t L_5 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		return ((int32_t)il2cpp_codegen_add(((int32_t)24), (int32_t)L_5));
	}

IL_0013:
	{
		uint32_t L_6 = ___0_val;
		V_0 = ((int32_t)((uint32_t)L_6>>((int32_t)16)));
		uint32_t L_7 = V_0;
		if (!L_7)
		{
			goto IL_0026;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable;
		uint32_t L_9 = V_0;
		NullCheck(L_8);
		uint32_t L_10 = L_9;
		uint8_t L_11 = (L_8)->GetAt(static_cast<il2cpp_array_size_t>(L_10));
		return ((int32_t)il2cpp_codegen_add(((int32_t)16), (int32_t)L_11));
	}

IL_0026:
	{
		uint32_t L_12 = ___0_val;
		V_0 = ((int32_t)((uint32_t)L_12>>8));
		uint32_t L_13 = V_0;
		if (!L_13)
		{
			goto IL_0037;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_14 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable;
		uint32_t L_15 = V_0;
		NullCheck(L_14);
		uint32_t L_16 = L_15;
		uint8_t L_17 = (L_14)->GetAt(static_cast<il2cpp_array_size_t>(L_16));
		return ((int32_t)il2cpp_codegen_add(8, (int32_t)L_17));
	}

IL_0037:
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_18 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable;
		uint32_t L_19 = ___0_val;
		NullCheck(L_18);
		uint32_t L_20 = L_19;
		uint8_t L_21 = (L_18)->GetAt(static_cast<il2cpp_array_size_t>(L_20));
		return L_21;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_rhs, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___0_lhs;
		int32_t L_1 = L_0->___m_length;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___1_rhs;
		int32_t L_3 = L_2->___m_length;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, L_3));
		int32_t L_4 = V_0;
		if (!L_4)
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_5 = V_0;
		return L_5;
	}

IL_0013:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_6 = ___0_lhs;
		int32_t L_7 = L_6->___m_length;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_7, 1));
		goto IL_006e;
	}

IL_001e:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = ___0_lhs;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_9 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_8->___m_blocks);
		uint32_t* L_10 = (uint32_t*)(&L_9->___FixedElementField);
		int32_t L_11 = V_1;
		int32_t L_12 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_10, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_11), 4)))));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_13 = ___1_rhs;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_14 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_13->___m_blocks);
		uint32_t* L_15 = (uint32_t*)(&L_14->___FixedElementField);
		int32_t L_16 = V_1;
		int32_t L_17 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_15, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_16), 4)))));
		if ((((int32_t)L_12) == ((int32_t)L_17)))
		{
			goto IL_006a;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_18 = ___0_lhs;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_19 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_18->___m_blocks);
		uint32_t* L_20 = (uint32_t*)(&L_19->___FixedElementField);
		int32_t L_21 = V_1;
		int32_t L_22 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_20, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_21), 4)))));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_23 = ___1_rhs;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_24 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_23->___m_blocks);
		uint32_t* L_25 = (uint32_t*)(&L_24->___FixedElementField);
		int32_t L_26 = V_1;
		int32_t L_27 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_25, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_26), 4)))));
		if ((!(((uint32_t)L_22) > ((uint32_t)L_27))))
		{
			goto IL_0068;
		}
	}
	{
		return 1;
	}

IL_0068:
	{
		return (-1);
	}

IL_006a:
	{
		int32_t L_28 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_28, 1));
	}

IL_006e:
	{
		int32_t L_29 = V_1;
		if ((((int32_t)L_29) >= ((int32_t)0)))
		{
			goto IL_001e;
		}
	}
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_rhs, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___1_lhs;
		int32_t L_1 = L_0->___m_length;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___2_rhs;
		int32_t L_3 = L_2->___m_length;
		if ((((int32_t)L_1) >= ((int32_t)L_3)))
		{
			goto IL_0017;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_5 = ___2_rhs;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_6 = ___1_lhs;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF(L_4, L_5, L_6, NULL);
		return;
	}

IL_0017:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_7 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = ___1_lhs;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_9 = ___2_rhs;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF(L_7, L_8, L_9, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_pLarge, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_pSmall, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	uint64_t V_2 = 0;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint32_t* V_7 = NULL;
	uint32_t* V_8 = NULL;
	uint32_t* V_9 = NULL;
	uint32_t* V_10 = NULL;
	uint32_t* V_11 = NULL;
	uint32_t* V_12 = NULL;
	uint64_t V_13 = 0;
	uint64_t V_14 = 0;
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___1_pLarge;
		int32_t L_1 = L_0->___m_length;
		V_0 = L_1;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___2_pSmall;
		int32_t L_3 = L_2->___m_length;
		V_1 = L_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___0_pResult;
		int32_t L_5 = V_0;
		L_4->___m_length = L_5;
		V_2 = ((int64_t)0);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_6 = ___1_pLarge;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_7 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_6->___m_blocks);
		uint32_t* L_8 = (uint32_t*)(&L_7->___FixedElementField);
		V_4 = L_8;
		uint32_t* L_9 = V_4;
		V_3 = (uint32_t*)((uintptr_t)L_9);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_10 = ___2_pSmall;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_11 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_10->___m_blocks);
		uint32_t* L_12 = (uint32_t*)(&L_11->___FixedElementField);
		V_6 = L_12;
		uint32_t* L_13 = V_6;
		V_5 = (uint32_t*)((uintptr_t)L_13);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_14 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_15 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_14->___m_blocks);
		uint32_t* L_16 = (uint32_t*)(&L_15->___FixedElementField);
		V_7 = L_16;
		uint32_t* L_17 = V_7;
		uint32_t* L_18 = V_3;
		V_8 = L_18;
		uint32_t* L_19 = V_5;
		V_9 = L_19;
		V_10 = (uint32_t*)((uintptr_t)L_17);
		uint32_t* L_20 = V_8;
		int32_t L_21 = V_0;
		V_11 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_20, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_21), 4))));
		uint32_t* L_22 = V_9;
		int32_t L_23 = V_1;
		V_12 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_22, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_23), 4))));
		goto IL_0096;
	}

IL_0068:
	{
		uint64_t L_24 = V_2;
		uint32_t* L_25 = V_8;
		int32_t L_26 = (*(L_25));
		uint32_t* L_27 = V_9;
		int32_t L_28 = (*(L_27));
		V_13 = ((int64_t)il2cpp_codegen_add(((int64_t)il2cpp_codegen_add((int64_t)L_24, ((int64_t)(uint64_t)((uint32_t)L_26)))), ((int64_t)(uint64_t)((uint32_t)L_28))));
		uint64_t L_29 = V_13;
		V_2 = ((int64_t)((uint64_t)L_29>>((int32_t)32)));
		uint32_t* L_30 = V_10;
		uint64_t L_31 = V_13;
		*((int32_t*)L_30) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_31&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint32_t* L_32 = V_8;
		V_8 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_32, 4));
		uint32_t* L_33 = V_9;
		V_9 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_33, 4));
		uint32_t* L_34 = V_10;
		V_10 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_34, 4));
	}

IL_0096:
	{
		uint32_t* L_35 = V_9;
		uint32_t* L_36 = V_12;
		if ((!(((uintptr_t)L_35) == ((uintptr_t)L_36))))
		{
			goto IL_0068;
		}
	}
	{
		goto IL_00c1;
	}

IL_009e:
	{
		uint64_t L_37 = V_2;
		uint32_t* L_38 = V_8;
		int32_t L_39 = (*(L_38));
		V_14 = ((int64_t)il2cpp_codegen_add((int64_t)L_37, ((int64_t)(uint64_t)((uint32_t)L_39))));
		uint64_t L_40 = V_14;
		V_2 = ((int64_t)((uint64_t)L_40>>((int32_t)32)));
		uint32_t* L_41 = V_10;
		uint64_t L_42 = V_14;
		*((int32_t*)L_41) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_42&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint32_t* L_43 = V_8;
		V_8 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_43, 4));
		uint32_t* L_44 = V_10;
		V_10 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_44, 4));
	}

IL_00c1:
	{
		uint32_t* L_45 = V_8;
		uint32_t* L_46 = V_11;
		if ((!(((uintptr_t)L_45) == ((uintptr_t)L_46))))
		{
			goto IL_009e;
		}
	}
	{
		uint64_t L_47 = V_2;
		if (!L_47)
		{
			goto IL_00d9;
		}
	}
	{
		uint32_t* L_48 = V_10;
		*((int32_t*)L_48) = (int32_t)1;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_49 = ___0_pResult;
		int32_t L_50 = V_0;
		L_49->___m_length = ((int32_t)il2cpp_codegen_add(L_50, 1));
		goto IL_00e0;
	}

IL_00d9:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_51 = ___0_pResult;
		int32_t L_52 = V_0;
		L_51->___m_length = L_52;
	}

IL_00e0:
	{
		V_7 = (uint32_t*)((uintptr_t)0);
		V_6 = (uint32_t*)((uintptr_t)0);
		V_4 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_rhs, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___1_lhs;
		int32_t L_1 = L_0->___m_length;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___2_rhs;
		int32_t L_3 = L_2->___m_length;
		if ((((int32_t)L_1) >= ((int32_t)L_3)))
		{
			goto IL_0017;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_5 = ___2_rhs;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_6 = ___1_lhs;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3(L_4, L_5, L_6, NULL);
		return;
	}

IL_0017:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_7 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = ___1_lhs;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_9 = ___2_rhs;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3(L_7, L_8, L_9, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_pLarge, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___2_pSmall, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint32_t* V_7 = NULL;
	uint32_t* V_8 = NULL;
	uint32_t* V_9 = NULL;
	uint32_t V_10 = 0;
	uint32_t* V_11 = NULL;
	uint32_t* V_12 = NULL;
	uint64_t V_13 = 0;
	uint64_t V_14 = 0;
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___1_pLarge;
		int32_t L_1 = L_0->___m_length;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___2_pSmall;
		int32_t L_3 = L_2->___m_length;
		V_0 = ((int32_t)il2cpp_codegen_add(L_1, L_3));
		V_1 = 0;
		goto IL_0028;
	}

IL_0012:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_5 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_4->___m_blocks);
		uint32_t* L_6 = (uint32_t*)(&L_5->___FixedElementField);
		int32_t L_7 = V_1;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_6, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_7), 4))))) = (int32_t)0;
		int32_t L_8 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_8, 1));
	}

IL_0028:
	{
		int32_t L_9 = V_1;
		int32_t L_10 = V_0;
		if ((((int32_t)L_9) < ((int32_t)L_10)))
		{
			goto IL_0012;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_11 = ___1_pLarge;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_12 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_11->___m_blocks);
		uint32_t* L_13 = (uint32_t*)(&L_12->___FixedElementField);
		V_2 = L_13;
		uint32_t* L_14 = V_2;
		V_3 = (uint32_t*)((uintptr_t)L_14);
		uint32_t* L_15 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_16 = ___1_pLarge;
		int32_t L_17 = L_16->___m_length;
		V_4 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_15, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_17), 4))));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_18 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_19 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_18->___m_blocks);
		uint32_t* L_20 = (uint32_t*)(&L_19->___FixedElementField);
		V_5 = L_20;
		uint32_t* L_21 = V_5;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_22 = ___2_pSmall;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_23 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_22->___m_blocks);
		uint32_t* L_24 = (uint32_t*)(&L_23->___FixedElementField);
		V_6 = L_24;
		uint32_t* L_25 = V_6;
		V_7 = (uint32_t*)((uintptr_t)L_25);
		uint32_t* L_26 = V_7;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_27 = ___2_pSmall;
		int32_t L_28 = L_27->___m_length;
		V_8 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_26, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_28), 4))));
		V_9 = (uint32_t*)((uintptr_t)L_21);
		goto IL_00d9;
	}

IL_007c:
	{
		uint32_t* L_29 = V_7;
		int32_t L_30 = (*(L_29));
		V_10 = L_30;
		uint32_t L_31 = V_10;
		if (!L_31)
		{
			goto IL_00cd;
		}
	}
	{
		uint32_t* L_32 = V_3;
		V_11 = L_32;
		uint32_t* L_33 = V_9;
		V_12 = L_33;
		V_13 = ((int64_t)0);
	}

IL_0090:
	{
		uint32_t* L_34 = V_12;
		int32_t L_35 = (*(L_34));
		uint32_t* L_36 = V_11;
		int32_t L_37 = (*(L_36));
		uint32_t L_38 = V_10;
		uint64_t L_39 = V_13;
		V_14 = ((int64_t)il2cpp_codegen_add(((int64_t)il2cpp_codegen_add(((int64_t)(uint64_t)((uint32_t)L_35)), ((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)((uint32_t)L_37)), ((int64_t)(uint64_t)L_38))))), (int64_t)L_39));
		uint64_t L_40 = V_14;
		V_13 = ((int64_t)((uint64_t)L_40>>((int32_t)32)));
		uint32_t* L_41 = V_12;
		uint64_t L_42 = V_14;
		*((int32_t*)L_41) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_42&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint32_t* L_43 = V_11;
		V_11 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_43, 4));
		uint32_t* L_44 = V_12;
		V_12 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_44, 4));
		uint32_t* L_45 = V_11;
		uint32_t* L_46 = V_4;
		if ((!(((uintptr_t)L_45) == ((uintptr_t)L_46))))
		{
			goto IL_0090;
		}
	}
	{
		uint32_t* L_47 = V_12;
		uint64_t L_48 = V_13;
		*((int32_t*)L_47) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_48&((int64_t)(uint64_t)((uint32_t)(-1))))));
	}

IL_00cd:
	{
		uint32_t* L_49 = V_7;
		V_7 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_49, 4));
		uint32_t* L_50 = V_9;
		V_9 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_50, 4));
	}

IL_00d9:
	{
		uint32_t* L_51 = V_7;
		uint32_t* L_52 = V_8;
		if ((!(((uintptr_t)L_51) == ((uintptr_t)L_52))))
		{
			goto IL_007c;
		}
	}
	{
		int32_t L_53 = V_0;
		if ((((int32_t)L_53) <= ((int32_t)0)))
		{
			goto IL_0103;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_54 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_55 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_54->___m_blocks);
		uint32_t* L_56 = (uint32_t*)(&L_55->___FixedElementField);
		int32_t L_57 = V_0;
		int32_t L_58 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_56, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)il2cpp_codegen_subtract(L_57, 1))), 4)))));
		if (L_58)
		{
			goto IL_0103;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_59 = ___0_pResult;
		int32_t L_60 = V_0;
		L_59->___m_length = ((int32_t)il2cpp_codegen_subtract(L_60, 1));
		goto IL_010a;
	}

IL_0103:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_61 = ___0_pResult;
		int32_t L_62 = V_0;
		L_61->___m_length = L_62;
	}

IL_010a:
	{
		V_6 = (uint32_t*)((uintptr_t)0);
		V_5 = (uint32_t*)((uintptr_t)0);
		V_2 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_lhs, uint32_t ___2_rhs, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t* V_1 = NULL;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint64_t V_7 = 0;
	{
		V_0 = 0;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_0->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		V_2 = L_2;
		uint32_t* L_3 = V_2;
		V_1 = (uint32_t*)((uintptr_t)L_3);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___1_lhs;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_5 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_4->___m_blocks);
		uint32_t* L_6 = (uint32_t*)(&L_5->___FixedElementField);
		V_3 = L_6;
		uint32_t* L_7 = V_3;
		uint32_t* L_8 = V_1;
		V_4 = L_8;
		V_5 = (uint32_t*)((uintptr_t)L_7);
		uint32_t* L_9 = V_5;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_10 = ___1_lhs;
		int32_t L_11 = L_10->___m_length;
		V_6 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_9, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_11), 4))));
		goto IL_005c;
	}

IL_0034:
	{
		uint32_t* L_12 = V_5;
		int32_t L_13 = (*(L_12));
		uint32_t L_14 = ___2_rhs;
		uint32_t L_15 = V_0;
		V_7 = ((int64_t)il2cpp_codegen_add(((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)((uint32_t)L_13)), ((int64_t)(uint64_t)L_14))), ((int64_t)(uint64_t)L_15)));
		uint32_t* L_16 = V_4;
		uint64_t L_17 = V_7;
		*((int32_t*)L_16) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_17&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint64_t L_18 = V_7;
		V_0 = ((int32_t)(uint32_t)((int64_t)((uint64_t)L_18>>((int32_t)32))));
		uint32_t* L_19 = V_5;
		V_5 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_19, 4));
		uint32_t* L_20 = V_4;
		V_4 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_20, 4));
	}

IL_005c:
	{
		uint32_t* L_21 = V_5;
		uint32_t* L_22 = V_6;
		if ((!(((uintptr_t)L_21) == ((uintptr_t)L_22))))
		{
			goto IL_0034;
		}
	}
	{
		uint32_t L_23 = V_0;
		if (!L_23)
		{
			goto IL_0079;
		}
	}
	{
		uint32_t* L_24 = V_4;
		uint32_t L_25 = V_0;
		*((int32_t*)L_24) = (int32_t)L_25;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_26 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_27 = ___1_lhs;
		int32_t L_28 = L_27->___m_length;
		L_26->___m_length = ((int32_t)il2cpp_codegen_add(L_28, 1));
		goto IL_0085;
	}

IL_0079:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_29 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_30 = ___1_lhs;
		int32_t L_31 = L_30->___m_length;
		L_29->___m_length = L_31;
	}

IL_0085:
	{
		V_3 = (uint32_t*)((uintptr_t)0);
		V_2 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_input, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t* V_1 = NULL;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint32_t V_7 = 0;
	{
		V_0 = 0;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_0->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		V_2 = L_2;
		uint32_t* L_3 = V_2;
		V_1 = (uint32_t*)((uintptr_t)L_3);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___1_input;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_5 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_4->___m_blocks);
		uint32_t* L_6 = (uint32_t*)(&L_5->___FixedElementField);
		V_3 = L_6;
		uint32_t* L_7 = V_3;
		uint32_t* L_8 = V_1;
		V_4 = L_8;
		V_5 = (uint32_t*)((uintptr_t)L_7);
		uint32_t* L_9 = V_5;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_10 = ___1_input;
		int32_t L_11 = L_10->___m_length;
		V_6 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_9, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_11), 4))));
		goto IL_0054;
	}

IL_0034:
	{
		uint32_t* L_12 = V_5;
		int32_t L_13 = (*(L_12));
		V_7 = L_13;
		uint32_t* L_14 = V_4;
		uint32_t L_15 = V_7;
		uint32_t L_16 = V_0;
		*((int32_t*)L_14) = (int32_t)((int32_t)(((int32_t)((int32_t)L_15<<1))|(int32_t)L_16));
		uint32_t L_17 = V_7;
		V_0 = ((int32_t)((uint32_t)L_17>>((int32_t)31)));
		uint32_t* L_18 = V_5;
		V_5 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_18, 4));
		uint32_t* L_19 = V_4;
		V_4 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_19, 4));
	}

IL_0054:
	{
		uint32_t* L_20 = V_5;
		uint32_t* L_21 = V_6;
		if ((!(((uintptr_t)L_20) == ((uintptr_t)L_21))))
		{
			goto IL_0034;
		}
	}
	{
		uint32_t L_22 = V_0;
		if (!L_22)
		{
			goto IL_0071;
		}
	}
	{
		uint32_t* L_23 = V_4;
		uint32_t L_24 = V_0;
		*((int32_t*)L_23) = (int32_t)L_24;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_25 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_26 = ___1_input;
		int32_t L_27 = L_26->___m_length;
		L_25->___m_length = ((int32_t)il2cpp_codegen_add(L_27, 1));
		goto IL_007d;
	}

IL_0071:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_28 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_29 = ___1_input;
		int32_t L_30 = L_29->___m_length;
		L_28->___m_length = L_30;
	}

IL_007d:
	{
		V_3 = (uint32_t*)((uintptr_t)0);
		V_2 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t* V_1 = NULL;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint32_t V_4 = 0;
	{
		V_0 = 0;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_0->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		V_1 = L_2;
		uint32_t* L_3 = V_1;
		V_2 = (uint32_t*)((uintptr_t)L_3);
		uint32_t* L_4 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_5 = ___0_pResult;
		int32_t L_6 = L_5->___m_length;
		V_3 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_4, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_6), 4))));
		goto IL_0035;
	}

IL_001f:
	{
		uint32_t* L_7 = V_2;
		int32_t L_8 = (*(L_7));
		V_4 = L_8;
		uint32_t* L_9 = V_2;
		uint32_t L_10 = V_4;
		uint32_t L_11 = V_0;
		*((int32_t*)L_9) = (int32_t)((int32_t)(((int32_t)((int32_t)L_10<<1))|(int32_t)L_11));
		uint32_t L_12 = V_4;
		V_0 = ((int32_t)((uint32_t)L_12>>((int32_t)31)));
		uint32_t* L_13 = V_2;
		V_2 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_13, 4));
	}

IL_0035:
	{
		uint32_t* L_14 = V_2;
		uint32_t* L_15 = V_3;
		if ((!(((uintptr_t)L_14) == ((uintptr_t)L_15))))
		{
			goto IL_001f;
		}
	}
	{
		uint32_t L_16 = V_0;
		if (!L_16)
		{
			goto IL_004a;
		}
	}
	{
		uint32_t* L_17 = V_2;
		uint32_t L_18 = V_0;
		*((int32_t*)L_17) = (int32_t)L_18;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_19 = ___0_pResult;
		int32_t* L_20 = (int32_t*)(&L_19->___m_length);
		int32_t* L_21 = L_20;
		int32_t L_22 = *((int32_t*)L_21);
		*((int32_t*)L_21) = (int32_t)((int32_t)il2cpp_codegen_add(L_22, 1));
	}

IL_004a:
	{
		V_1 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, const RuntimeMethod* method) 
{
	uint64_t V_0 = 0;
	uint32_t* V_1 = NULL;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint64_t V_4 = 0;
	{
		V_0 = ((int64_t)0);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_0->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		V_1 = L_2;
		uint32_t* L_3 = V_1;
		V_2 = (uint32_t*)((uintptr_t)L_3);
		uint32_t* L_4 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_5 = ___0_pResult;
		int32_t L_6 = L_5->___m_length;
		V_3 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_4, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_6), 4))));
		goto IL_003d;
	}

IL_0020:
	{
		uint32_t* L_7 = V_2;
		int32_t L_8 = (*(L_7));
		uint64_t L_9 = V_0;
		V_4 = ((int64_t)il2cpp_codegen_add(((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)((uint32_t)L_8)), ((int64_t)((int32_t)10)))), (int64_t)L_9));
		uint32_t* L_10 = V_2;
		uint64_t L_11 = V_4;
		*((int32_t*)L_10) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_11&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint64_t L_12 = V_4;
		V_0 = ((int64_t)((uint64_t)L_12>>((int32_t)32)));
		uint32_t* L_13 = V_2;
		V_2 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_13, 4));
	}

IL_003d:
	{
		uint32_t* L_14 = V_2;
		uint32_t* L_15 = V_3;
		if ((!(((uintptr_t)L_14) == ((uintptr_t)L_15))))
		{
			goto IL_0020;
		}
	}
	{
		uint64_t L_16 = V_0;
		if (!L_16)
		{
			goto IL_0053;
		}
	}
	{
		uint32_t* L_17 = V_2;
		uint64_t L_18 = V_0;
		*((int32_t*)L_17) = (int32_t)((int32_t)(uint32_t)L_18);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_19 = ___0_pResult;
		int32_t* L_20 = (int32_t*)(&L_19->___m_length);
		int32_t* L_21 = L_20;
		int32_t L_22 = *((int32_t*)L_21);
		*((int32_t*)L_21) = (int32_t)((int32_t)il2cpp_codegen_add(L_22, 1));
	}

IL_0053:
	{
		V_1 = (uint32_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR tBigInt_t6A436AD3913A2950571338A5018B48B299987358 BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43 (int32_t ___0_i, const RuntimeMethod* method) 
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_i;
		if (L_0)
		{
			goto IL_0022;
		}
	}
	{
		(&V_0)->___m_length = 1;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		*((int32_t*)L_2) = (int32_t)((int32_t)100000000);
		goto IL_0503;
	}

IL_0022:
	{
		int32_t L_3 = ___0_i;
		if ((!(((uint32_t)L_3) == ((uint32_t)1))))
		{
			goto IL_0059;
		}
	}
	{
		(&V_0)->___m_length = 2;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_4 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_5 = (uint32_t*)(&L_4->___FixedElementField);
		*((int32_t*)L_5) = (int32_t)((int32_t)1874919424);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_6 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_7 = (uint32_t*)(&L_6->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_7, 4))) = (int32_t)((int32_t)2328306);
		goto IL_0503;
	}

IL_0059:
	{
		int32_t L_8 = ___0_i;
		if ((!(((uint32_t)L_8) == ((uint32_t)2))))
		{
			goto IL_00ba;
		}
	}
	{
		(&V_0)->___m_length = 4;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_9 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_10 = (uint32_t*)(&L_9->___FixedElementField);
		*((int32_t*)L_10) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_11 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_12 = (uint32_t*)(&L_11->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_12, 4))) = (int32_t)((int32_t)-2052264063);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_13 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_14 = (uint32_t*)(&L_13->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_14, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)2), 4))))) = (int32_t)((int32_t)762134875);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_15 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_16 = (uint32_t*)(&L_15->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_16, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)3), 4))))) = (int32_t)((int32_t)1262);
		goto IL_0503;
	}

IL_00ba:
	{
		int32_t L_17 = ___0_i;
		if ((!(((uint32_t)L_17) == ((uint32_t)3))))
		{
			goto IL_015f;
		}
	}
	{
		(&V_0)->___m_length = 7;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_18 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_19 = (uint32_t*)(&L_18->___FixedElementField);
		*((int32_t*)L_19) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_20 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_21 = (uint32_t*)(&L_20->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_21, 4))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_22 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_23 = (uint32_t*)(&L_22->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_23, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)2), 4))))) = (int32_t)((int32_t)-1083564287);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_24 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_25 = (uint32_t*)(&L_24->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_25, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)3), 4))))) = (int32_t)((int32_t)1849224548);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_26 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_27 = (uint32_t*)(&L_26->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_27, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)4), 4))))) = (int32_t)((int32_t)-626550803);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_28 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_29 = (uint32_t*)(&L_28->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_29, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)5), 4))))) = (int32_t)((int32_t)-381683212);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_30 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_31 = (uint32_t*)(&L_30->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_31, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)6), 4))))) = (int32_t)((int32_t)1593091);
		goto IL_0503;
	}

IL_015f:
	{
		int32_t L_32 = ___0_i;
		if ((!(((uint32_t)L_32) == ((uint32_t)4))))
		{
			goto IL_02a3;
		}
	}
	{
		(&V_0)->___m_length = ((int32_t)14);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_33 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_34 = (uint32_t*)(&L_33->___FixedElementField);
		*((int32_t*)L_34) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_35 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_36 = (uint32_t*)(&L_35->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_36, 4))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_37 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_38 = (uint32_t*)(&L_37->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_38, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)2), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_39 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_40 = (uint32_t*)(&L_39->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_40, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)3), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_41 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_42 = (uint32_t*)(&L_41->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_42, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)4), 4))))) = (int32_t)((int32_t)781532673);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_43 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_44 = (uint32_t*)(&L_43->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_44, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)5), 4))))) = (int32_t)((int32_t)64985353);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_45 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_46 = (uint32_t*)(&L_45->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_46, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)6), 4))))) = (int32_t)((int32_t)253049085);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_47 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_48 = (uint32_t*)(&L_47->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_48, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)7), 4))))) = (int32_t)((int32_t)594863151);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_49 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_50 = (uint32_t*)(&L_49->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_50, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)8), 4))))) = (int32_t)((int32_t)-741345812);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_51 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_52 = (uint32_t*)(&L_51->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_52, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)9)), 4))))) = (int32_t)((int32_t)-1006314488);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_53 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_54 = (uint32_t*)(&L_53->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_54, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)10)), 4))))) = (int32_t)((int32_t)-1127370534);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_55 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_56 = (uint32_t*)(&L_55->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_56, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)11)), 4))))) = (int32_t)((int32_t)-1506574567);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_57 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_58 = (uint32_t*)(&L_57->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_58, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)12)), 4))))) = (int32_t)((int32_t)-383834621);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_59 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_60 = (uint32_t*)(&L_59->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_60, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)13)), 4))))) = (int32_t)((int32_t)590);
		goto IL_0503;
	}

IL_02a3:
	{
		(&V_0)->___m_length = ((int32_t)27);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_61 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_62 = (uint32_t*)(&L_61->___FixedElementField);
		*((int32_t*)L_62) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_63 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_64 = (uint32_t*)(&L_63->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_64, 4))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_65 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_66 = (uint32_t*)(&L_65->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_66, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)2), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_67 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_68 = (uint32_t*)(&L_67->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_68, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)3), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_69 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_70 = (uint32_t*)(&L_69->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_70, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)4), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_71 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_72 = (uint32_t*)(&L_71->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_72, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)5), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_73 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_74 = (uint32_t*)(&L_73->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_74, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)6), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_75 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_76 = (uint32_t*)(&L_75->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_76, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)7), 4))))) = (int32_t)0;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_77 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_78 = (uint32_t*)(&L_77->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_78, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)8), 4))))) = (int32_t)((int32_t)-1741784063);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_79 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_80 = (uint32_t*)(&L_79->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_80, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)9)), 4))))) = (int32_t)((int32_t)-1093433509);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_81 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_82 = (uint32_t*)(&L_81->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_82, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)10)), 4))))) = (int32_t)((int32_t)-656826510);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_83 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_84 = (uint32_t*)(&L_83->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_84, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)11)), 4))))) = (int32_t)((int32_t)303378311);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_85 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_86 = (uint32_t*)(&L_85->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_86, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)12)), 4))))) = (int32_t)((int32_t)1809731782);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_87 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_88 = (uint32_t*)(&L_87->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_88, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)13)), 4))))) = (int32_t)((int32_t)-817205648);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_89 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_90 = (uint32_t*)(&L_89->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_90, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)14)), 4))))) = (int32_t)((int32_t)-711600113);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_91 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_92 = (uint32_t*)(&L_91->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_92, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)15)), 4))))) = (int32_t)((int32_t)649228654);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_93 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_94 = (uint32_t*)(&L_93->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_94, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)16)), 4))))) = (int32_t)((int32_t)-1379506512);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_95 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_96 = (uint32_t*)(&L_95->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_96, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)17)), 4))))) = (int32_t)((int32_t)487929380);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_97 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_98 = (uint32_t*)(&L_97->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_98, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)18)), 4))))) = (int32_t)((int32_t)1011012442);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_99 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_100 = (uint32_t*)(&L_99->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_100, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)19)), 4))))) = (int32_t)((int32_t)1677677582);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_101 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_102 = (uint32_t*)(&L_101->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_102, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)20)), 4))))) = (int32_t)((int32_t)-866815040);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_103 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_104 = (uint32_t*)(&L_103->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_104, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)21)), 4))))) = (int32_t)((int32_t)1710878487);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_105 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_106 = (uint32_t*)(&L_105->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_106, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)22)), 4))))) = (int32_t)((int32_t)1438394610);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_107 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_108 = (uint32_t*)(&L_107->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_108, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)23)), 4))))) = (int32_t)((int32_t)-2133014537);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_109 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_110 = (uint32_t*)(&L_109->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_110, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)24)), 4))))) = (int32_t)((int32_t)-194056740);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_111 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_112 = (uint32_t*)(&L_111->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_112, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)25)), 4))))) = (int32_t)((int32_t)1608314830);
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_113 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&(&V_0)->___m_blocks);
		uint32_t* L_114 = (uint32_t*)(&L_113->___FixedElementField);
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_114, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)26)), 4))))) = (int32_t)((int32_t)349175);
	}

IL_0503:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_115 = V_0;
		return L_115;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_exponent, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_0;
	memset((&V_0), 0, sizeof(V_0));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_1;
	memset((&V_1), 0, sizeof(V_1));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_2 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_3 = NULL;
	uint32_t V_4 = 0;
	int32_t V_5 = 0;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_6 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_7;
	memset((&V_7), 0, sizeof(V_7));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		il2cpp_codegen_initobj((&V_1), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		V_2 = (&V_0);
		V_3 = (&V_1);
		uint32_t L_0 = ___1_exponent;
		V_4 = ((int32_t)((int32_t)L_0&7));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_1 = V_2;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* L_2 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___g_PowerOf10_U32;
		uint32_t L_3 = V_4;
		NullCheck(L_2);
		uint32_t L_4 = L_3;
		uint32_t L_5 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233(L_1, L_5, NULL);
		uint32_t L_6 = ___1_exponent;
		___1_exponent = ((int32_t)((uint32_t)L_6>>3));
		V_5 = 0;
		goto IL_0071;
	}

IL_0033:
	{
		uint32_t L_7 = ___1_exponent;
		if (!((int32_t)((int32_t)L_7&1)))
		{
			goto IL_0066;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_9 = V_2;
		int32_t L_10 = V_5;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_11;
		L_11 = BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43(L_10, NULL);
		V_7 = L_11;
		BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9(L_8, L_9, (&V_7), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_12 = V_2;
		V_6 = L_12;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_13 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_14 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_15 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_14);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_13 = L_15;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_16 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_17 = V_6;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_18 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_17);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_16 = L_18;
	}

IL_0066:
	{
		int32_t L_19 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_19, 1));
		uint32_t L_20 = ___1_exponent;
		___1_exponent = ((int32_t)((uint32_t)L_20>>1));
	}

IL_0071:
	{
		uint32_t L_21 = ___1_exponent;
		if (L_21)
		{
			goto IL_0033;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_22 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_23 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_24 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_23);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_22 = L_24;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_input, uint32_t ___2_exponent, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_0;
	memset((&V_0), 0, sizeof(V_0));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_1;
	memset((&V_1), 0, sizeof(V_1));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_2 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_3 = NULL;
	uint32_t V_4 = 0;
	int32_t V_5 = 0;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_6 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_7;
	memset((&V_7), 0, sizeof(V_7));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		il2cpp_codegen_initobj((&V_1), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		V_2 = (&V_0);
		V_3 = (&V_1);
		uint32_t L_0 = ___2_exponent;
		V_4 = ((int32_t)((int32_t)L_0&7));
		uint32_t L_1 = V_4;
		if (!L_1)
		{
			goto IL_0030;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_3 = ___1_input;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* L_4 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___g_PowerOf10_U32;
		uint32_t L_5 = V_4;
		NullCheck(L_4);
		uint32_t L_6 = L_5;
		uint32_t L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF(L_2, L_3, L_7, NULL);
		goto IL_003c;
	}

IL_0030:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_9 = ___1_input;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_10 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_9);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_8 = L_10;
	}

IL_003c:
	{
		uint32_t L_11 = ___2_exponent;
		___2_exponent = ((int32_t)((uint32_t)L_11>>3));
		V_5 = 0;
		goto IL_0084;
	}

IL_0046:
	{
		uint32_t L_12 = ___2_exponent;
		if (!((int32_t)((int32_t)L_12&1)))
		{
			goto IL_0079;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_13 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_14 = V_2;
		int32_t L_15 = V_5;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_16;
		L_16 = BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43(L_15, NULL);
		V_7 = L_16;
		BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9(L_13, L_14, (&V_7), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_17 = V_2;
		V_6 = L_17;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_18 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_19 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_20 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_19);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_18 = L_20;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_21 = V_3;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_22 = V_6;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_23 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_22);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_21 = L_23;
	}

IL_0079:
	{
		int32_t L_24 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_24, 1));
		uint32_t L_25 = ___2_exponent;
		___2_exponent = ((int32_t)((uint32_t)L_25>>1));
	}

IL_0084:
	{
		uint32_t L_26 = ___2_exponent;
		if (L_26)
		{
			goto IL_0046;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_27 = ___0_pResult;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_28 = V_2;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_29 = (*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_28);
		*(tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_27 = L_29;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_exponent, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	uint32_t V_2 = 0;
	{
		uint32_t L_0 = ___1_exponent;
		V_0 = ((int32_t)((int32_t)L_0/((int32_t)32)));
		V_2 = 0;
		goto IL_0021;
	}

IL_0009:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_1 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_2 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_1->___m_blocks);
		uint32_t* L_3 = (uint32_t*)(&L_2->___FixedElementField);
		uint32_t L_4 = V_2;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_3, ((intptr_t)((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)L_4), ((int64_t)4))))))) = (int32_t)0;
		uint32_t L_5 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add((int32_t)L_5, 1));
	}

IL_0021:
	{
		uint32_t L_6 = V_2;
		int32_t L_7 = V_0;
		if ((((int64_t)((int64_t)(uint64_t)L_6)) <= ((int64_t)((int64_t)L_7))))
		{
			goto IL_0009;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_8 = ___0_pResult;
		int32_t L_9 = V_0;
		L_8->___m_length = ((int32_t)il2cpp_codegen_add(L_9, 1));
		uint32_t L_10 = ___1_exponent;
		V_1 = ((int32_t)((int32_t)L_10%((int32_t)32)));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_11 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_12 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_11->___m_blocks);
		uint32_t* L_13 = (uint32_t*)(&L_12->___FixedElementField);
		int32_t L_14 = V_0;
		uint32_t* L_15 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_13, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_14), 4))));
		int32_t L_16 = *((uint32_t*)L_15);
		int32_t L_17 = V_1;
		*((int32_t*)L_15) = (int32_t)((int32_t)(L_16|((int32_t)(1<<((int32_t)(L_17&((int32_t)31)))))));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pDividend, tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___1_divisor, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	uint32_t* V_1 = NULL;
	uint32_t* V_2 = NULL;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint32_t* V_7 = NULL;
	uint32_t V_8 = 0;
	uint64_t V_9 = 0;
	uint64_t V_10 = 0;
	uint64_t V_11 = 0;
	uint64_t V_12 = 0;
	uint64_t V_13 = 0;
	uint64_t V_14 = 0;
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_0 = ___1_divisor;
		int32_t L_1 = L_0->___m_length;
		V_0 = L_1;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___0_pDividend;
		int32_t L_3 = L_2->___m_length;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_4 = ___1_divisor;
		int32_t L_5 = L_4->___m_length;
		if ((((int32_t)L_3) >= ((int32_t)L_5)))
		{
			goto IL_0017;
		}
	}
	{
		return 0;
	}

IL_0017:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_6 = ___1_divisor;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_7 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_6->___m_blocks);
		uint32_t* L_8 = (uint32_t*)(&L_7->___FixedElementField);
		V_2 = L_8;
		uint32_t* L_9 = V_2;
		V_1 = (uint32_t*)((uintptr_t)L_9);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_10 = ___0_pDividend;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_11 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_10->___m_blocks);
		uint32_t* L_12 = (uint32_t*)(&L_11->___FixedElementField);
		V_4 = L_12;
		uint32_t* L_13 = V_4;
		V_3 = (uint32_t*)((uintptr_t)L_13);
		uint32_t* L_14 = V_1;
		V_5 = L_14;
		uint32_t* L_15 = V_3;
		V_6 = L_15;
		uint32_t* L_16 = V_5;
		int32_t L_17 = V_0;
		V_7 = ((uint32_t*)il2cpp_codegen_subtract((intptr_t)((uint32_t*)il2cpp_codegen_add((intptr_t)L_16, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_17), 4)))), 4));
		uint32_t* L_18 = V_6;
		int32_t L_19 = V_0;
		int32_t L_20 = (*(((uint32_t*)il2cpp_codegen_subtract((intptr_t)((uint32_t*)il2cpp_codegen_add((intptr_t)L_18, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_19), 4)))), 4))));
		uint32_t* L_21 = V_7;
		int32_t L_22 = (*(L_21));
		V_8 = ((int32_t)((uint32_t)(int32_t)L_20/(uint32_t)(int32_t)((int32_t)il2cpp_codegen_add(L_22, 1))));
		uint32_t L_23 = V_8;
		if (!L_23)
		{
			goto IL_00d4;
		}
	}
	{
		V_9 = ((int64_t)0);
		V_10 = ((int64_t)0);
	}

IL_0066:
	{
		uint32_t* L_24 = V_5;
		int32_t L_25 = (*(L_24));
		uint32_t L_26 = V_8;
		uint64_t L_27 = V_10;
		V_11 = ((int64_t)il2cpp_codegen_add(((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)((uint32_t)L_25)), ((int64_t)(uint64_t)L_26))), (int64_t)L_27));
		uint64_t L_28 = V_11;
		V_10 = ((int64_t)((uint64_t)L_28>>((int32_t)32)));
		uint32_t* L_29 = V_6;
		int32_t L_30 = (*(L_29));
		uint64_t L_31 = V_11;
		uint64_t L_32 = V_9;
		V_12 = ((int64_t)il2cpp_codegen_subtract(((int64_t)il2cpp_codegen_subtract(((int64_t)(uint64_t)((uint32_t)L_30)), ((int64_t)((int64_t)L_31&((int64_t)(uint64_t)((uint32_t)(-1))))))), (int64_t)L_32));
		uint64_t L_33 = V_12;
		V_9 = ((int64_t)(((int64_t)((uint64_t)L_33>>((int32_t)32)))&((int64_t)1)));
		uint32_t* L_34 = V_6;
		uint64_t L_35 = V_12;
		*((int32_t*)L_34) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_35&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint32_t* L_36 = V_5;
		V_5 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_36, 4));
		uint32_t* L_37 = V_6;
		V_6 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_37, 4));
		uint32_t* L_38 = V_5;
		uint32_t* L_39 = V_7;
		if ((!(((uintptr_t)L_38) > ((uintptr_t)L_39))))
		{
			goto IL_0066;
		}
	}
	{
		goto IL_00b4;
	}

IL_00b0:
	{
		int32_t L_40 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_40, 1));
	}

IL_00b4:
	{
		int32_t L_41 = V_0;
		if ((((int32_t)L_41) <= ((int32_t)0)))
		{
			goto IL_00cd;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_42 = ___0_pDividend;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_43 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_42->___m_blocks);
		uint32_t* L_44 = (uint32_t*)(&L_43->___FixedElementField);
		int32_t L_45 = V_0;
		int32_t L_46 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_44, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)il2cpp_codegen_subtract(L_45, 1))), 4)))));
		if (!L_46)
		{
			goto IL_00b0;
		}
	}

IL_00cd:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_47 = ___0_pDividend;
		int32_t L_48 = V_0;
		L_47->___m_length = L_48;
	}

IL_00d4:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_49 = ___0_pDividend;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_50 = ___1_divisor;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		int32_t L_51;
		L_51 = BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1(L_49, L_50, NULL);
		if ((((int32_t)L_51) < ((int32_t)0)))
		{
			goto IL_0147;
		}
	}
	{
		uint32_t L_52 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add((int32_t)L_52, 1));
		uint32_t* L_53 = V_1;
		V_5 = L_53;
		uint32_t* L_54 = V_3;
		V_6 = L_54;
		V_13 = ((int64_t)0);
	}

IL_00ee:
	{
		uint32_t* L_55 = V_6;
		int32_t L_56 = (*(L_55));
		uint32_t* L_57 = V_5;
		int32_t L_58 = (*(L_57));
		uint64_t L_59 = V_13;
		V_14 = ((int64_t)il2cpp_codegen_subtract(((int64_t)il2cpp_codegen_subtract(((int64_t)(uint64_t)((uint32_t)L_56)), ((int64_t)(uint64_t)((uint32_t)L_58)))), (int64_t)L_59));
		uint64_t L_60 = V_14;
		V_13 = ((int64_t)(((int64_t)((uint64_t)L_60>>((int32_t)32)))&((int64_t)1)));
		uint32_t* L_61 = V_6;
		uint64_t L_62 = V_14;
		*((int32_t*)L_61) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_62&((int64_t)(uint64_t)((uint32_t)(-1))))));
		uint32_t* L_63 = V_5;
		V_5 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_63, 4));
		uint32_t* L_64 = V_6;
		V_6 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_64, 4));
		uint32_t* L_65 = V_5;
		uint32_t* L_66 = V_7;
		if ((!(((uintptr_t)L_65) > ((uintptr_t)L_66))))
		{
			goto IL_00ee;
		}
	}
	{
		goto IL_0127;
	}

IL_0123:
	{
		int32_t L_67 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_67, 1));
	}

IL_0127:
	{
		int32_t L_68 = V_0;
		if ((((int32_t)L_68) <= ((int32_t)0)))
		{
			goto IL_0140;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_69 = ___0_pDividend;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_70 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_69->___m_blocks);
		uint32_t* L_71 = (uint32_t*)(&L_70->___FixedElementField);
		int32_t L_72 = V_0;
		int32_t L_73 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_71, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)il2cpp_codegen_subtract(L_72, 1))), 4)))));
		if (!L_73)
		{
			goto IL_0123;
		}
	}

IL_0140:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_74 = ___0_pDividend;
		int32_t L_75 = V_0;
		L_74->___m_length = L_75;
	}

IL_0147:
	{
		uint32_t L_76 = V_8;
		return L_76;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* ___0_pResult, uint32_t ___1_shift, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	uint32_t* V_3 = NULL;
	uint32_t* V_4 = NULL;
	uint32_t* V_5 = NULL;
	uint32_t* V_6 = NULL;
	uint32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	uint32_t V_11 = 0;
	uint32_t V_12 = 0;
	uint32_t V_13 = 0;
	uint32_t V_14 = 0;
	{
		uint32_t L_0 = ___1_shift;
		V_0 = ((int32_t)((int32_t)L_0/((int32_t)32)));
		uint32_t L_1 = ___1_shift;
		V_1 = ((int32_t)((int32_t)L_1%((int32_t)32)));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_2 = ___0_pResult;
		int32_t L_3 = L_2->___m_length;
		V_2 = L_3;
		int32_t L_4 = V_1;
		if (L_4)
		{
			goto IL_0088;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_5 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_6 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_5->___m_blocks);
		uint32_t* L_7 = (uint32_t*)(&L_6->___FixedElementField);
		V_3 = L_7;
		uint32_t* L_8 = V_3;
		V_4 = (uint32_t*)((uintptr_t)L_8);
		uint32_t* L_9 = V_4;
		int32_t L_10 = V_2;
		V_5 = ((uint32_t*)il2cpp_codegen_subtract((intptr_t)((uint32_t*)il2cpp_codegen_add((intptr_t)L_9, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_10), 4)))), 4));
		uint32_t* L_11 = V_5;
		int32_t L_12 = V_0;
		V_6 = ((uint32_t*)il2cpp_codegen_add((intptr_t)L_11, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_12), 4))));
		goto IL_004c;
	}

IL_003a:
	{
		uint32_t* L_13 = V_6;
		uint32_t* L_14 = V_5;
		int32_t L_15 = (*(L_14));
		*((int32_t*)L_13) = (int32_t)L_15;
		uint32_t* L_16 = V_5;
		V_5 = ((uint32_t*)il2cpp_codegen_subtract((intptr_t)L_16, 4));
		uint32_t* L_17 = V_6;
		V_6 = ((uint32_t*)il2cpp_codegen_subtract((intptr_t)L_17, 4));
	}

IL_004c:
	{
		uint32_t* L_18 = V_5;
		uint32_t* L_19 = V_4;
		if ((!(((uintptr_t)L_18) < ((uintptr_t)L_19))))
		{
			goto IL_003a;
		}
	}
	{
		V_3 = (uint32_t*)((uintptr_t)0);
		V_7 = 0;
		goto IL_0075;
	}

IL_005a:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_20 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_21 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_20->___m_blocks);
		uint32_t* L_22 = (uint32_t*)(&L_21->___FixedElementField);
		uint32_t L_23 = V_7;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_22, ((intptr_t)((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)L_23), ((int64_t)4))))))) = (int32_t)0;
		uint32_t L_24 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_add((int32_t)L_24, 1));
	}

IL_0075:
	{
		uint32_t L_25 = V_7;
		int32_t L_26 = V_0;
		if ((((int64_t)((int64_t)(uint64_t)L_25)) < ((int64_t)((int64_t)L_26))))
		{
			goto IL_005a;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_27 = ___0_pResult;
		int32_t* L_28 = (int32_t*)(&L_27->___m_length);
		int32_t* L_29 = L_28;
		int32_t L_30 = *((int32_t*)L_29);
		int32_t L_31 = V_0;
		*((int32_t*)L_29) = (int32_t)((int32_t)il2cpp_codegen_add(L_30, L_31));
		return;
	}

IL_0088:
	{
		int32_t L_32 = V_2;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_32, 1));
		int32_t L_33 = V_2;
		int32_t L_34 = V_0;
		V_9 = ((int32_t)il2cpp_codegen_add(L_33, L_34));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_35 = ___0_pResult;
		int32_t L_36 = V_9;
		L_35->___m_length = ((int32_t)il2cpp_codegen_add(L_36, 1));
		int32_t L_37 = V_1;
		V_10 = ((int32_t)il2cpp_codegen_subtract(((int32_t)32), L_37));
		V_11 = 0;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_38 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_39 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_38->___m_blocks);
		uint32_t* L_40 = (uint32_t*)(&L_39->___FixedElementField);
		int32_t L_41 = V_8;
		int32_t L_42 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_40, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_41), 4)))));
		V_12 = L_42;
		uint32_t L_43 = V_12;
		int32_t L_44 = V_10;
		V_13 = ((int32_t)((uint32_t)L_43>>((int32_t)(L_44&((int32_t)31)))));
		goto IL_010f;
	}

IL_00c5:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_45 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_46 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_45->___m_blocks);
		uint32_t* L_47 = (uint32_t*)(&L_46->___FixedElementField);
		int32_t L_48 = V_9;
		uint32_t L_49 = V_11;
		uint32_t L_50 = V_13;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_47, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_48), 4))))) = (int32_t)((int32_t)((int32_t)L_49|(int32_t)L_50));
		uint32_t L_51 = V_12;
		int32_t L_52 = V_1;
		V_11 = ((int32_t)((int32_t)L_51<<((int32_t)(L_52&((int32_t)31)))));
		int32_t L_53 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_53, 1));
		int32_t L_54 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_54, 1));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_55 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_56 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_55->___m_blocks);
		uint32_t* L_57 = (uint32_t*)(&L_56->___FixedElementField);
		int32_t L_58 = V_8;
		int32_t L_59 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_57, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_58), 4)))));
		V_12 = L_59;
		uint32_t L_60 = V_12;
		int32_t L_61 = V_10;
		V_13 = ((int32_t)((uint32_t)L_60>>((int32_t)(L_61&((int32_t)31)))));
	}

IL_010f:
	{
		int32_t L_62 = V_8;
		if ((((int32_t)L_62) > ((int32_t)0)))
		{
			goto IL_00c5;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_63 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_64 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_63->___m_blocks);
		uint32_t* L_65 = (uint32_t*)(&L_64->___FixedElementField);
		int32_t L_66 = V_9;
		uint32_t L_67 = V_11;
		uint32_t L_68 = V_13;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_65, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_66), 4))))) = (int32_t)((int32_t)((int32_t)L_67|(int32_t)L_68));
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_69 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_70 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_69->___m_blocks);
		uint32_t* L_71 = (uint32_t*)(&L_70->___FixedElementField);
		int32_t L_72 = V_9;
		uint32_t L_73 = V_12;
		int32_t L_74 = V_1;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_71, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)il2cpp_codegen_subtract(L_72, 1))), 4))))) = (int32_t)((int32_t)((int32_t)L_73<<((int32_t)(L_74&((int32_t)31)))));
		V_14 = 0;
		goto IL_0166;
	}

IL_014b:
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_75 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_76 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_75->___m_blocks);
		uint32_t* L_77 = (uint32_t*)(&L_76->___FixedElementField);
		uint32_t L_78 = V_14;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_77, ((intptr_t)((int64_t)il2cpp_codegen_multiply(((int64_t)(uint64_t)L_78), ((int64_t)4))))))) = (int32_t)0;
		uint32_t L_79 = V_14;
		V_14 = ((int32_t)il2cpp_codegen_add((int32_t)L_79, 1));
	}

IL_0166:
	{
		uint32_t L_80 = V_14;
		int32_t L_81 = V_0;
		if ((((int64_t)((int64_t)(uint64_t)L_80)) < ((int64_t)((int64_t)L_81))))
		{
			goto IL_014b;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_82 = ___0_pResult;
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_83 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&L_82->___m_blocks);
		uint32_t* L_84 = (uint32_t*)(&L_83->___FixedElementField);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_85 = ___0_pResult;
		int32_t L_86 = L_85->___m_length;
		int32_t L_87 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_84, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)((int32_t)il2cpp_codegen_subtract(L_86, 1))), 4)))));
		if (L_87)
		{
			goto IL_0192;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_88 = ___0_pResult;
		int32_t* L_89 = (int32_t*)(&L_88->___m_length);
		int32_t* L_90 = L_89;
		int32_t L_91 = *((int32_t*)L_90);
		*((int32_t*)L_90) = (int32_t)((int32_t)il2cpp_codegen_subtract(L_91, 1));
	}

IL_0192:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE (uint64_t ___0_mantissa, int32_t ___1_exponent, uint32_t ___2_mantissaHighBitIdx, bool ___3_hasUnequalMargins, int32_t ___4_cutoffMode, uint32_t ___5_cutoffNumber, uint8_t* ___6_pOutBuffer, uint32_t ___7_bufferSize, int32_t* ___8_pOutExponent, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_1;
	memset((&V_1), 0, sizeof(V_1));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_2;
	memset((&V_2), 0, sizeof(V_2));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_3;
	memset((&V_3), 0, sizeof(V_3));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* V_4 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_5;
	memset((&V_5), 0, sizeof(V_5));
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	uint32_t V_8 = 0;
	bool V_9 = false;
	bool V_10 = false;
	uint32_t V_11 = 0;
	bool V_12 = false;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_13;
	memset((&V_13), 0, sizeof(V_13));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_14;
	memset((&V_14), 0, sizeof(V_14));
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_15;
	memset((&V_15), 0, sizeof(V_15));
	int32_t V_16 = 0;
	int32_t V_17 = 0;
	uint32_t V_18 = 0;
	uint32_t V_19 = 0;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358 V_20;
	memset((&V_20), 0, sizeof(V_20));
	{
		uint8_t* L_0 = ___6_pOutBuffer;
		V_0 = L_0;
		uint64_t L_1 = ___0_mantissa;
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		uint8_t* L_2 = V_0;
		*((int8_t*)L_2) = (int8_t)((int32_t)48);
		int32_t* L_3 = ___8_pOutExponent;
		*((int32_t*)L_3) = (int32_t)0;
		return 1;
	}

IL_0010:
	{
		il2cpp_codegen_initobj((&V_1), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		il2cpp_codegen_initobj((&V_2), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		il2cpp_codegen_initobj((&V_3), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		il2cpp_codegen_initobj((&V_5), sizeof(tBigInt_t6A436AD3913A2950571338A5018B48B299987358));
		bool L_4 = ___3_hasUnequalMargins;
		if (!L_4)
		{
			goto IL_0093;
		}
	}
	{
		int32_t L_5 = ___1_exponent;
		if ((((int32_t)L_5) <= ((int32_t)0)))
		{
			goto IL_0066;
		}
	}
	{
		uint64_t L_6 = ___0_mantissa;
		tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14((&V_2), ((int64_t)il2cpp_codegen_multiply(((int64_t)4), (int64_t)L_6)), NULL);
		int32_t L_7 = ___1_exponent;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583((&V_2), L_7, NULL);
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233((&V_1), 4, NULL);
		int32_t L_8 = ___1_exponent;
		BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A((&V_3), L_8, NULL);
		int32_t L_9 = ___1_exponent;
		BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A((&V_5), ((int32_t)il2cpp_codegen_add(L_9, 1)), NULL);
		goto IL_008c;
	}

IL_0066:
	{
		uint64_t L_10 = ___0_mantissa;
		tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14((&V_2), ((int64_t)il2cpp_codegen_multiply(((int64_t)4), (int64_t)L_10)), NULL);
		int32_t L_11 = ___1_exponent;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A((&V_1), ((int32_t)il2cpp_codegen_add(((-L_11)), 2)), NULL);
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233((&V_3), 1, NULL);
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233((&V_5), 2, NULL);
	}

IL_008c:
	{
		V_4 = (tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)((uintptr_t)(&V_5));
		goto IL_00df;
	}

IL_0093:
	{
		int32_t L_12 = ___1_exponent;
		if ((((int32_t)L_12) <= ((int32_t)0)))
		{
			goto IL_00bc;
		}
	}
	{
		uint64_t L_13 = ___0_mantissa;
		tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14((&V_2), ((int64_t)il2cpp_codegen_multiply(((int64_t)2), (int64_t)L_13)), NULL);
		int32_t L_14 = ___1_exponent;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583((&V_2), L_14, NULL);
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233((&V_1), 2, NULL);
		int32_t L_15 = ___1_exponent;
		BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A((&V_3), L_15, NULL);
		goto IL_00da;
	}

IL_00bc:
	{
		uint64_t L_16 = ___0_mantissa;
		tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14((&V_2), ((int64_t)il2cpp_codegen_multiply(((int64_t)2), (int64_t)L_16)), NULL);
		int32_t L_17 = ___1_exponent;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A((&V_1), ((int32_t)il2cpp_codegen_add(((-L_17)), 1)), NULL);
		tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233((&V_3), 1, NULL);
	}

IL_00da:
	{
		V_4 = (tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)((uintptr_t)(&V_3));
	}

IL_00df:
	{
		uint32_t L_18 = ___2_mantissaHighBitIdx;
		int32_t L_19 = ___1_exponent;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_20;
		L_20 = ceil(((double)il2cpp_codegen_subtract(((double)il2cpp_codegen_multiply(((double)((int32_t)il2cpp_codegen_add((int32_t)L_18, L_19))), (0.3010299956639812))), (0.68999999999999995))));
		V_6 = il2cpp_codegen_cast_double_to_int<int32_t>(L_20);
		int32_t L_21 = ___4_cutoffMode;
		if ((!(((uint32_t)L_21) == ((uint32_t)2))))
		{
			goto IL_0112;
		}
	}
	{
		int32_t L_22 = V_6;
		uint32_t L_23 = ___5_cutoffNumber;
		if ((((int32_t)L_22) > ((int32_t)((-((int32_t)L_23))))))
		{
			goto IL_0112;
		}
	}
	{
		uint32_t L_24 = ___5_cutoffNumber;
		V_6 = ((int32_t)il2cpp_codegen_add(((-((int32_t)L_24))), 1));
	}

IL_0112:
	{
		int32_t L_25 = V_6;
		if ((((int32_t)L_25) <= ((int32_t)0)))
		{
			goto IL_0127;
		}
	}
	{
		int32_t L_26 = V_6;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD((&V_13), (&V_1), L_26, NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_27 = V_13;
		V_1 = L_27;
		goto IL_0162;
	}

IL_0127:
	{
		int32_t L_28 = V_6;
		if ((((int32_t)L_28) >= ((int32_t)0)))
		{
			goto IL_0162;
		}
	}
	{
		int32_t L_29 = V_6;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642((&V_14), ((-L_29)), NULL);
		BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9((&V_15), (&V_2), (&V_14), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_30 = V_15;
		V_2 = L_30;
		BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9((&V_15), (&V_3), (&V_14), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358 L_31 = V_15;
		V_3 = L_31;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_32 = V_4;
		if ((((intptr_t)L_32) == ((intptr_t)((uintptr_t)(&V_3)))))
		{
			goto IL_0162;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_33 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C((tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_33, (&V_3), NULL);
	}

IL_0162:
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		int32_t L_34;
		L_34 = BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1((&V_2), (&V_1), NULL);
		if ((((int32_t)L_34) < ((int32_t)0)))
		{
			goto IL_0176;
		}
	}
	{
		int32_t L_35 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_35, 1));
		goto IL_0194;
	}

IL_0176:
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E((&V_2), NULL);
		BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E((&V_3), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_36 = V_4;
		if ((((intptr_t)L_36) == ((intptr_t)((uintptr_t)(&V_3)))))
		{
			goto IL_0194;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_37 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C((tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_37, (&V_3), NULL);
	}

IL_0194:
	{
		int32_t L_38 = V_6;
		uint32_t L_39 = ___7_bufferSize;
		V_7 = ((int32_t)il2cpp_codegen_subtract(L_38, (int32_t)L_39));
		int32_t L_40 = ___4_cutoffMode;
		switch (L_40)
		{
			case 0:
			{
				goto IL_01d2;
			}
			case 1:
			{
				goto IL_01b0;
			}
			case 2:
			{
				goto IL_01c3;
			}
		}
	}
	{
		goto IL_01d2;
	}

IL_01b0:
	{
		int32_t L_41 = V_6;
		uint32_t L_42 = ___5_cutoffNumber;
		V_16 = ((int32_t)il2cpp_codegen_subtract(L_41, (int32_t)L_42));
		int32_t L_43 = V_16;
		int32_t L_44 = V_7;
		if ((((int32_t)L_43) <= ((int32_t)L_44)))
		{
			goto IL_01d2;
		}
	}
	{
		int32_t L_45 = V_16;
		V_7 = L_45;
		goto IL_01d2;
	}

IL_01c3:
	{
		uint32_t L_46 = ___5_cutoffNumber;
		V_17 = ((-((int32_t)L_46)));
		int32_t L_47 = V_17;
		int32_t L_48 = V_7;
		if ((((int32_t)L_47) <= ((int32_t)L_48)))
		{
			goto IL_01d2;
		}
	}
	{
		int32_t L_49 = V_17;
		V_7 = L_49;
	}

IL_01d2:
	{
		int32_t* L_50 = ___8_pOutExponent;
		int32_t L_51 = V_6;
		*((int32_t*)L_50) = (int32_t)((int32_t)il2cpp_codegen_subtract(L_51, 1));
		int32_t L_52;
		L_52 = tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_inline((&V_1), NULL);
		uint32_t L_53;
		L_53 = tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2((&V_1), ((int32_t)il2cpp_codegen_subtract(L_52, 1)), NULL);
		V_8 = L_53;
		uint32_t L_54 = V_8;
		if ((!(((uint32_t)L_54) >= ((uint32_t)8))))
		{
			goto IL_01f9;
		}
	}
	{
		uint32_t L_55 = V_8;
		if ((!(((uint32_t)L_55) > ((uint32_t)((int32_t)429496729)))))
		{
			goto IL_0237;
		}
	}

IL_01f9:
	{
		uint32_t L_56 = V_8;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_57;
		L_57 = BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54(L_56, NULL);
		V_18 = L_57;
		uint32_t L_58 = V_18;
		V_19 = ((int32_t)((uint32_t)(int32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)59), (int32_t)L_58))%(uint32_t)(int32_t)((int32_t)32)));
		uint32_t L_59 = V_19;
		BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583((&V_1), L_59, NULL);
		uint32_t L_60 = V_19;
		BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583((&V_2), L_60, NULL);
		uint32_t L_61 = V_19;
		BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583((&V_3), L_61, NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_62 = V_4;
		if ((((intptr_t)L_62) == ((intptr_t)((uintptr_t)(&V_3)))))
		{
			goto IL_0237;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_63 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C((tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_63, (&V_3), NULL);
	}

IL_0237:
	{
		int32_t L_64 = ___4_cutoffMode;
		if (L_64)
		{
			goto IL_02ad;
		}
	}

IL_023b:
	{
		int32_t L_65 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_65, 1));
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_66;
		L_66 = BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9((&V_2), (&V_1), NULL);
		V_11 = L_66;
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_67 = V_4;
		BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E((&V_20), (&V_2), (tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_67, NULL);
		int32_t L_68;
		L_68 = BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1((&V_2), (&V_3), NULL);
		V_9 = (bool)((((int32_t)L_68) < ((int32_t)0))? 1 : 0);
		int32_t L_69;
		L_69 = BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1((&V_20), (&V_1), NULL);
		V_10 = (bool)((((int32_t)L_69) > ((int32_t)0))? 1 : 0);
		bool L_70 = V_9;
		bool L_71 = V_10;
		int32_t L_72 = V_6;
		int32_t L_73 = V_7;
		if (((int32_t)(((int32_t)((int32_t)L_70|(int32_t)L_71))|((((int32_t)L_72) == ((int32_t)L_73))? 1 : 0))))
		{
			goto IL_02e9;
		}
	}
	{
		uint8_t* L_74 = V_0;
		uint32_t L_75 = V_11;
		*((int8_t*)L_74) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)48), (int32_t)L_75)));
		uint8_t* L_76 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_76, 1));
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E((&V_2), NULL);
		BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E((&V_3), NULL);
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_77 = V_4;
		if ((((intptr_t)L_77) == ((intptr_t)((uintptr_t)(&V_3)))))
		{
			goto IL_023b;
		}
	}
	{
		tBigInt_t6A436AD3913A2950571338A5018B48B299987358* L_78 = V_4;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C((tBigInt_t6A436AD3913A2950571338A5018B48B299987358*)L_78, (&V_3), NULL);
		goto IL_023b;
	}

IL_02ad:
	{
		V_9 = (bool)0;
		V_10 = (bool)0;
	}

IL_02b3:
	{
		int32_t L_79 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_79, 1));
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_80;
		L_80 = BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9((&V_2), (&V_1), NULL);
		V_11 = L_80;
		bool L_81;
		L_81 = tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8((&V_2), NULL);
		int32_t L_82 = V_6;
		int32_t L_83 = V_7;
		if (((int32_t)((int32_t)L_81|((((int32_t)L_82) == ((int32_t)L_83))? 1 : 0))))
		{
			goto IL_02e9;
		}
	}
	{
		uint8_t* L_84 = V_0;
		uint32_t L_85 = V_11;
		*((int8_t*)L_84) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)48), (int32_t)L_85)));
		uint8_t* L_86 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_86, 1));
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E((&V_2), NULL);
		goto IL_02b3;
	}

IL_02e9:
	{
		bool L_87 = V_9;
		V_12 = L_87;
		bool L_88 = V_9;
		bool L_89 = V_10;
		if ((!(((uint32_t)L_88) == ((uint32_t)L_89))))
		{
			goto IL_0314;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252((&V_2), NULL);
		int32_t L_90;
		L_90 = BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1((&V_2), (&V_1), NULL);
		int32_t L_91 = L_90;
		V_12 = (bool)((((int32_t)L_91) < ((int32_t)0))? 1 : 0);
		if (L_91)
		{
			goto IL_0314;
		}
	}
	{
		uint32_t L_92 = V_11;
		V_12 = (bool)((((int32_t)((int32_t)((int32_t)L_92&1))) == ((int32_t)0))? 1 : 0);
	}

IL_0314:
	{
		bool L_93 = V_12;
		if (!L_93)
		{
			goto IL_0326;
		}
	}
	{
		uint8_t* L_94 = V_0;
		uint32_t L_95 = V_11;
		*((int8_t*)L_94) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)48), (int32_t)L_95)));
		uint8_t* L_96 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_96, 1));
		goto IL_0368;
	}

IL_0326:
	{
		uint32_t L_97 = V_11;
		if ((!(((uint32_t)L_97) == ((uint32_t)((int32_t)9)))))
		{
			goto IL_035a;
		}
	}

IL_032c:
	{
		uint8_t* L_98 = V_0;
		uint8_t* L_99 = ___6_pOutBuffer;
		if ((!(((uintptr_t)L_98) == ((uintptr_t)L_99))))
		{
			goto IL_0343;
		}
	}
	{
		uint8_t* L_100 = V_0;
		*((int8_t*)L_100) = (int8_t)((int32_t)49);
		uint8_t* L_101 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_101, 1));
		int32_t* L_102 = ___8_pOutExponent;
		int32_t* L_103 = ___8_pOutExponent;
		int32_t L_104 = *((int32_t*)L_103);
		*((int32_t*)L_102) = (int32_t)((int32_t)il2cpp_codegen_add(L_104, 1));
		goto IL_0368;
	}

IL_0343:
	{
		uint8_t* L_105 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_subtract((intptr_t)L_105, 1));
		uint8_t* L_106 = V_0;
		int32_t L_107 = (*(L_106));
		if ((((int32_t)L_107) == ((int32_t)((int32_t)57))))
		{
			goto IL_032c;
		}
	}
	{
		uint8_t* L_108 = V_0;
		uint8_t* L_109 = L_108;
		int32_t L_110 = (*(L_109));
		*((int8_t*)L_109) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(L_110, 1)));
		uint8_t* L_111 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_111, 1));
		goto IL_0368;
	}

IL_035a:
	{
		uint8_t* L_112 = V_0;
		uint32_t L_113 = V_11;
		*((int8_t*)L_112) = (int8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)48), (int32_t)L_113)), 1)));
		uint8_t* L_114 = V_0;
		V_0 = ((uint8_t*)il2cpp_codegen_add((intptr_t)L_114, 1));
	}

IL_0368:
	{
		uint8_t* L_115 = V_0;
		uint8_t* L_116 = ___6_pOutBuffer;
		return ((int32_t)(uint32_t)((int64_t)(intptr_t)((uint8_t*)((intptr_t)((uint8_t*)il2cpp_codegen_subtract((intptr_t)L_115, (intptr_t)L_116))/1))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, uint64_t ___3_mantissa, bool ___4_isNegative, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___5_formatOptions, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t G_B6_0 = 0;
	int32_t G_B4_0 = 0;
	int32_t G_B3_0 = 0;
	int32_t G_B5_0 = 0;
	int32_t G_B5_1 = 0;
	{
		uint64_t L_0 = ___3_mantissa;
		if (!L_0)
		{
			goto IL_0006;
		}
	}
	{
		G_B6_0 = 3;
		goto IL_0010;
	}

IL_0006:
	{
		bool L_1 = ___4_isNegative;
		if (L_1)
		{
			G_B4_0 = 8;
			goto IL_000e;
		}
		G_B3_0 = 8;
	}
	{
		G_B5_0 = 0;
		G_B5_1 = G_B3_0;
		goto IL_000f;
	}

IL_000e:
	{
		G_B5_0 = 1;
		G_B5_1 = G_B4_0;
	}

IL_000f:
	{
		G_B6_0 = ((int32_t)il2cpp_codegen_add(G_B5_1, G_B5_0));
	}

IL_0010:
	{
		V_0 = G_B6_0;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_2 = ___5_formatOptions;
		int8_t L_3 = L_2.___AlignAndSize;
		V_1 = L_3;
		uint8_t* L_4 = ___0_dest;
		int32_t* L_5 = ___1_destIndex;
		int32_t L_6 = ___2_destLength;
		int32_t L_7 = V_1;
		int32_t L_8 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_9;
		L_9 = BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0(L_4, L_5, L_6, L_7, L_8, NULL);
		if (!L_9)
		{
			goto IL_0026;
		}
	}
	{
		return;
	}

IL_0026:
	{
		uint64_t L_10 = ___3_mantissa;
		if (L_10)
		{
			goto IL_0068;
		}
	}
	{
		bool L_11 = ___4_isNegative;
		if (!L_11)
		{
			goto IL_0041;
		}
	}
	{
		int32_t* L_12 = ___1_destIndex;
		int32_t L_13 = *((int32_t*)L_12);
		int32_t L_14 = ___2_destLength;
		if ((((int32_t)L_13) < ((int32_t)L_14)))
		{
			goto IL_0033;
		}
	}
	{
		return;
	}

IL_0033:
	{
		uint8_t* L_15 = ___0_dest;
		int32_t* L_16 = ___1_destIndex;
		int32_t* L_17 = ___1_destIndex;
		int32_t L_18 = *((int32_t*)L_17);
		V_2 = L_18;
		int32_t L_19 = V_2;
		*((int32_t*)L_16) = (int32_t)((int32_t)il2cpp_codegen_add(L_19, 1));
		int32_t L_20 = V_2;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_15, L_20))) = (int8_t)((int32_t)45);
	}

IL_0041:
	{
		V_3 = 0;
		goto IL_0062;
	}

IL_0045:
	{
		int32_t* L_21 = ___1_destIndex;
		int32_t L_22 = *((int32_t*)L_21);
		int32_t L_23 = ___2_destLength;
		if ((((int32_t)L_22) < ((int32_t)L_23)))
		{
			goto IL_004b;
		}
	}
	{
		return;
	}

IL_004b:
	{
		uint8_t* L_24 = ___0_dest;
		int32_t* L_25 = ___1_destIndex;
		int32_t* L_26 = ___1_destIndex;
		int32_t L_27 = *((int32_t*)L_26);
		V_2 = L_27;
		int32_t L_28 = V_2;
		*((int32_t*)L_25) = (int32_t)((int32_t)il2cpp_codegen_add(L_28, 1));
		int32_t L_29 = V_2;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_30 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___InfinityString;
		int32_t L_31 = V_3;
		NullCheck(L_30);
		int32_t L_32 = L_31;
		uint8_t L_33 = (L_30)->GetAt(static_cast<il2cpp_array_size_t>(L_32));
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_24, L_29))) = (int8_t)L_33;
		int32_t L_34 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_34, 1));
	}

IL_0062:
	{
		int32_t L_35 = V_3;
		if ((((int32_t)L_35) < ((int32_t)8)))
		{
			goto IL_0045;
		}
	}
	{
		goto IL_0092;
	}

IL_0068:
	{
		V_4 = 0;
		goto IL_008d;
	}

IL_006d:
	{
		int32_t* L_36 = ___1_destIndex;
		int32_t L_37 = *((int32_t*)L_36);
		int32_t L_38 = ___2_destLength;
		if ((((int32_t)L_37) < ((int32_t)L_38)))
		{
			goto IL_0073;
		}
	}
	{
		return;
	}

IL_0073:
	{
		uint8_t* L_39 = ___0_dest;
		int32_t* L_40 = ___1_destIndex;
		int32_t* L_41 = ___1_destIndex;
		int32_t L_42 = *((int32_t*)L_41);
		V_2 = L_42;
		int32_t L_43 = V_2;
		*((int32_t*)L_40) = (int32_t)((int32_t)il2cpp_codegen_add(L_43, 1));
		int32_t L_44 = V_2;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_45 = ((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___NanString;
		int32_t L_46 = V_4;
		NullCheck(L_45);
		int32_t L_47 = L_46;
		uint8_t L_48 = (L_45)->GetAt(static_cast<il2cpp_array_size_t>(L_47));
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_39, L_44))) = (int8_t)L_48;
		int32_t L_49 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_49, 1));
	}

IL_008d:
	{
		int32_t L_50 = V_4;
		if ((((int32_t)L_50) < ((int32_t)3)))
		{
			goto IL_006d;
		}
	}

IL_0092:
	{
		uint8_t* L_51 = ___0_dest;
		int32_t* L_52 = ___1_destIndex;
		int32_t L_53 = ___2_destLength;
		int32_t L_54 = V_1;
		int32_t L_55 = V_0;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		bool L_56;
		L_56 = BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37(L_51, L_52, L_53, L_54, L_55, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, float ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_formatOptions, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA V_0;
	memset((&V_0), 0, sizeof(V_0));
	uint32_t V_1 = 0;
	uint32_t V_2 = 0;
	uint32_t V_3 = 0;
	int32_t V_4 = 0;
	uint32_t V_5 = 0;
	bool V_6 = false;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	uint8_t* V_9 = NULL;
	int32_t V_10 = 0;
	uint32_t V_11 = 0;
	bool V_12 = false;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4 V_13;
	memset((&V_13), 0, sizeof(V_13));
	int32_t G_B6_0 = 0;
	int32_t G_B11_0 = 0;
	{
		il2cpp_codegen_initobj((&V_0), sizeof(tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA));
		float L_0 = ___3_value;
		(&V_0)->___m_floatingPoint = L_0;
		uint32_t L_1;
		L_1 = tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6((&V_0), NULL);
		V_1 = L_1;
		uint32_t L_2;
		L_2 = tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF((&V_0), NULL);
		V_2 = L_2;
		uint32_t L_3 = V_1;
		if ((!(((uint32_t)L_3) == ((uint32_t)((int32_t)255)))))
		{
			goto IL_003c;
		}
	}
	{
		uint8_t* L_4 = ___0_dest;
		int32_t* L_5 = ___1_destIndex;
		int32_t L_6 = ___2_destLength;
		uint32_t L_7 = V_2;
		bool L_8;
		L_8 = tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388((&V_0), NULL);
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_9 = ___4_formatOptions;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979(L_4, L_5, L_6, ((int64_t)(uint64_t)L_7), L_8, L_9, NULL);
		return;
	}

IL_003c:
	{
		uint32_t L_10 = V_1;
		if (!L_10)
		{
			goto IL_0066;
		}
	}
	{
		uint32_t L_11 = V_2;
		V_3 = ((int32_t)(uint32_t)((int64_t)(((int64_t)((int32_t)8388608))|((int64_t)(uint64_t)L_11))));
		uint32_t L_12 = V_1;
		V_4 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract((int32_t)L_12, ((int32_t)127))), ((int32_t)23)));
		V_5 = ((int32_t)23);
		uint32_t L_13 = V_1;
		if ((((int32_t)L_13) == ((int32_t)1)))
		{
			goto IL_0061;
		}
	}
	{
		uint32_t L_14 = V_2;
		G_B6_0 = ((((int32_t)L_14) == ((int32_t)0))? 1 : 0);
		goto IL_0062;
	}

IL_0061:
	{
		G_B6_0 = 0;
	}

IL_0062:
	{
		V_6 = (bool)G_B6_0;
		goto IL_007a;
	}

IL_0066:
	{
		uint32_t L_15 = V_2;
		V_3 = L_15;
		V_4 = ((int32_t)-149);
		uint32_t L_16 = V_3;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_17;
		L_17 = BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54(L_16, NULL);
		V_5 = L_17;
		V_6 = (bool)0;
	}

IL_007a:
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_18 = ___4_formatOptions;
		uint8_t L_19 = L_18.___Specifier;
		if (!L_19)
		{
			goto IL_008c;
		}
	}
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_20 = ___4_formatOptions;
		uint8_t L_21 = L_20.___Specifier;
		G_B11_0 = ((int32_t)(L_21));
		goto IL_008d;
	}

IL_008c:
	{
		G_B11_0 = (-1);
	}

IL_008d:
	{
		V_7 = G_B11_0;
		int32_t L_22 = V_7;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		int32_t L_23;
		L_23 = Math_Max_m530EBA549AFD98CFC2BD29FE86C6376E67DF11CF(((int32_t)10), ((int32_t)il2cpp_codegen_add(L_22, 1)), NULL);
		V_8 = L_23;
		int32_t L_24 = V_8;
		uintptr_t L_25 = ((uintptr_t)L_24);
		int8_t* L_26 = (int8_t*) (L_25 ? alloca(L_25) : NULL);
		memset(L_26, 0, L_25);
		V_9 = (uint8_t*)(L_26);
		int32_t L_27 = V_7;
		if ((((int32_t)L_27) >= ((int32_t)0)))
		{
			goto IL_00ab;
		}
	}
	{
		V_7 = 7;
	}

IL_00ab:
	{
		uint32_t L_28 = V_3;
		int32_t L_29 = V_4;
		uint32_t L_30 = V_5;
		bool L_31 = V_6;
		int32_t L_32 = V_7;
		uint8_t* L_33 = V_9;
		int32_t L_34 = V_8;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_35;
		L_35 = BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE(((int64_t)(uint64_t)L_28), L_29, L_30, L_31, 1, L_32, L_33, ((int32_t)il2cpp_codegen_subtract(L_34, 1)), (&V_10), NULL);
		V_11 = L_35;
		uint8_t* L_36 = V_9;
		uint32_t L_37 = V_11;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_36, (intptr_t)((uintptr_t)L_37)))) = (int8_t)0;
		bool L_38;
		L_38 = tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388((&V_0), NULL);
		V_12 = L_38;
		tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA L_39 = V_0;
		uint32_t L_40 = L_39.___m_integer;
		if ((!(((uint32_t)L_40) == ((uint32_t)((int32_t)-2147483648LL)))))
		{
			goto IL_00e6;
		}
	}
	{
		V_12 = (bool)0;
	}

IL_00e6:
	{
		uint8_t* L_41 = V_9;
		uint32_t L_42 = V_11;
		int32_t L_43 = V_10;
		bool L_44 = V_12;
		NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141((&V_13), 1, L_41, L_42, ((int32_t)il2cpp_codegen_add(L_43, 1)), L_44, NULL);
		uint8_t* L_45 = ___0_dest;
		int32_t* L_46 = ___1_destIndex;
		int32_t L_47 = ___2_destLength;
		int32_t L_48 = V_7;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_49 = ___4_formatOptions;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673(L_45, L_46, L_47, (&V_13), L_48, L_49, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0 (uint8_t* ___0_dest, int32_t* ___1_destIndex, int32_t ___2_destLength, double ___3_value, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 ___4_formatOptions, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC V_0;
	memset((&V_0), 0, sizeof(V_0));
	uint32_t V_1 = 0;
	uint64_t V_2 = 0;
	uint64_t V_3 = 0;
	int32_t V_4 = 0;
	uint32_t V_5 = 0;
	bool V_6 = false;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	uint8_t* V_9 = NULL;
	int32_t V_10 = 0;
	uint32_t V_11 = 0;
	bool V_12 = false;
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4 V_13;
	memset((&V_13), 0, sizeof(V_13));
	int32_t G_B6_0 = 0;
	int32_t G_B11_0 = 0;
	{
		il2cpp_codegen_initobj((&V_0), sizeof(tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC));
		double L_0 = ___3_value;
		(&V_0)->___m_floatingPoint = L_0;
		uint32_t L_1;
		L_1 = tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60((&V_0), NULL);
		V_1 = L_1;
		uint64_t L_2;
		L_2 = tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C((&V_0), NULL);
		V_2 = L_2;
		uint32_t L_3 = V_1;
		if ((!(((uint32_t)L_3) == ((uint32_t)((int32_t)2047)))))
		{
			goto IL_003b;
		}
	}
	{
		uint8_t* L_4 = ___0_dest;
		int32_t* L_5 = ___1_destIndex;
		int32_t L_6 = ___2_destLength;
		uint64_t L_7 = V_2;
		bool L_8;
		L_8 = tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B((&V_0), NULL);
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_9 = ___4_formatOptions;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979(L_4, L_5, L_6, L_7, L_8, L_9, NULL);
		return;
	}

IL_003b:
	{
		uint32_t L_10 = V_1;
		if (!L_10)
		{
			goto IL_006a;
		}
	}
	{
		uint64_t L_11 = V_2;
		V_3 = ((int64_t)(((int64_t)4503599627370496LL)|(int64_t)L_11));
		uint32_t L_12 = V_1;
		V_4 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract((int32_t)L_12, ((int32_t)1023))), ((int32_t)52)));
		V_5 = ((int32_t)52);
		uint32_t L_13 = V_1;
		if ((((int32_t)L_13) == ((int32_t)1)))
		{
			goto IL_0065;
		}
	}
	{
		uint64_t L_14 = V_2;
		G_B6_0 = ((((int64_t)L_14) == ((int64_t)((int64_t)0)))? 1 : 0);
		goto IL_0066;
	}

IL_0065:
	{
		G_B6_0 = 0;
	}

IL_0066:
	{
		V_6 = (bool)G_B6_0;
		goto IL_007f;
	}

IL_006a:
	{
		uint64_t L_15 = V_2;
		V_3 = L_15;
		V_4 = ((int32_t)-1074);
		uint64_t L_16 = V_3;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_17;
		L_17 = BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54(((int32_t)(uint32_t)L_16), NULL);
		V_5 = L_17;
		V_6 = (bool)0;
	}

IL_007f:
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_18 = ___4_formatOptions;
		uint8_t L_19 = L_18.___Specifier;
		if (!L_19)
		{
			goto IL_0091;
		}
	}
	{
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_20 = ___4_formatOptions;
		uint8_t L_21 = L_20.___Specifier;
		G_B11_0 = ((int32_t)(L_21));
		goto IL_0092;
	}

IL_0091:
	{
		G_B11_0 = (-1);
	}

IL_0092:
	{
		V_7 = G_B11_0;
		int32_t L_22 = V_7;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		int32_t L_23;
		L_23 = Math_Max_m530EBA549AFD98CFC2BD29FE86C6376E67DF11CF(((int32_t)18), ((int32_t)il2cpp_codegen_add(L_22, 1)), NULL);
		V_8 = L_23;
		int32_t L_24 = V_8;
		uintptr_t L_25 = ((uintptr_t)L_24);
		int8_t* L_26 = (int8_t*) (L_25 ? alloca(L_25) : NULL);
		memset(L_26, 0, L_25);
		V_9 = (uint8_t*)(L_26);
		int32_t L_27 = V_7;
		if ((((int32_t)L_27) >= ((int32_t)0)))
		{
			goto IL_00b1;
		}
	}
	{
		V_7 = ((int32_t)15);
	}

IL_00b1:
	{
		uint64_t L_28 = V_3;
		int32_t L_29 = V_4;
		uint32_t L_30 = V_5;
		bool L_31 = V_6;
		int32_t L_32 = V_7;
		uint8_t* L_33 = V_9;
		int32_t L_34 = V_8;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		uint32_t L_35;
		L_35 = BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE(L_28, L_29, L_30, L_31, 1, L_32, L_33, ((int32_t)il2cpp_codegen_subtract(L_34, 1)), (&V_10), NULL);
		V_11 = L_35;
		uint8_t* L_36 = V_9;
		uint32_t L_37 = V_11;
		*((int8_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_36, (intptr_t)((uintptr_t)L_37)))) = (int8_t)0;
		bool L_38;
		L_38 = tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B((&V_0), NULL);
		V_12 = L_38;
		tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC L_39 = V_0;
		uint64_t L_40 = L_39.___m_integer;
		if ((!(((uint64_t)L_40) == ((uint64_t)((int64_t)(std::numeric_limits<int64_t>::min)())))))
		{
			goto IL_00ef;
		}
	}
	{
		V_12 = (bool)0;
	}

IL_00ef:
	{
		uint8_t* L_41 = V_9;
		uint32_t L_42 = V_11;
		int32_t L_43 = V_10;
		bool L_44 = V_12;
		NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141((&V_13), 1, L_41, L_42, ((int32_t)il2cpp_codegen_add(L_43, 1)), L_44, NULL);
		uint8_t* L_45 = ___0_dest;
		int32_t* L_46 = ___1_destIndex;
		int32_t L_47 = ___2_destLength;
		int32_t L_48 = V_7;
		FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84 L_49 = ___4_formatOptions;
		il2cpp_codegen_runtime_class_init_inline(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673(L_45, L_46, L_47, (&V_13), L_48, L_49, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____07DB995E8ED2CFB0AB71EBA69F3A3EC07D5C6AC10C0C64F33E94ED2949B348AA_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____C69994AC61B52FBCEA582D6CCCD595C12E00BDB18F0C6F593FB6B393CAEDB08C_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D0067CAD9A63E0813759A2BB841051CA73570C0DA2E08E840A8EB45DB6A7A010_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D5B592C05DC25B5032553F1B27F4139BE95E881F73DB33B02B05AB20C3F9981E_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* L_0 = (CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB*)(CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB*)SZArrayNew(CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB_il2cpp_TypeInfo_var, (uint32_t)1);
		CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Il2CppChar)((int32_t)58));
		((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___SplitByColon = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___SplitByColon), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)256));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = L_2;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_4 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____07DB995E8ED2CFB0AB71EBA69F3A3EC07D5C6AC10C0C64F33E94ED2949B348AA_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_3, L_4, NULL);
		((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___logTable), (void*)L_3);
		UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* L_5 = (UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA*)(UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA*)SZArrayNew(UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA_il2cpp_TypeInfo_var, (uint32_t)8);
		UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* L_6 = L_5;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_7 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____C69994AC61B52FBCEA582D6CCCD595C12E00BDB18F0C6F593FB6B393CAEDB08C_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_6, L_7, NULL);
		((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___g_PowerOf10_U32 = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___g_PowerOf10_U32), (void*)L_6);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)8);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_9 = L_8;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_10 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D0067CAD9A63E0813759A2BB841051CA73570C0DA2E08E840A8EB45DB6A7A010_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_9, L_10, NULL);
		((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___InfinityString = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___InfinityString), (void*)L_9);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_11 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)3);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_12 = L_11;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_13 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2CADAF0D55AC9D0785A6F7B80D4772CF1220C48F____D5B592C05DC25B5032553F1B27F4139BE95E881F73DB33B02B05AB20C3F9981E_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_12, L_13, NULL);
		((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___NanString = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_StaticFields*)il2cpp_codegen_static_fields_for(BurstString_tD6AF700FD5AF48728FC90C6CA2AA2E48C6472AF1_il2cpp_TypeInfo_var))->___NanString), (void*)L_12);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1 (PreserveAttribute_t54BBA699FC0C1DD99BED77D21CADC33A352E1999* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_pinvoke(const NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4& unmarshaled, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_pinvoke& marshaled)
{
	marshaled.____buffer = unmarshaled.____buffer;
	marshaled.___Kind = unmarshaled.___Kind;
	marshaled.___DigitsCount = unmarshaled.___DigitsCount;
	marshaled.___Scale = unmarshaled.___Scale;
	marshaled.___IsNegative = static_cast<int32_t>(unmarshaled.___IsNegative);
}
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_pinvoke_back(const NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_pinvoke& marshaled, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4& unmarshaled)
{
	unmarshaled.____buffer = marshaled.____buffer;
	int32_t unmarshaledKind_temp_1 = 0;
	unmarshaledKind_temp_1 = marshaled.___Kind;
	unmarshaled.___Kind = unmarshaledKind_temp_1;
	int32_t unmarshaledDigitsCount_temp_2 = 0;
	unmarshaledDigitsCount_temp_2 = marshaled.___DigitsCount;
	unmarshaled.___DigitsCount = unmarshaledDigitsCount_temp_2;
	int32_t unmarshaledScale_temp_3 = 0;
	unmarshaledScale_temp_3 = marshaled.___Scale;
	unmarshaled.___Scale = unmarshaledScale_temp_3;
	bool unmarshaledIsNegative_temp_4 = false;
	unmarshaledIsNegative_temp_4 = static_cast<bool>(marshaled.___IsNegative);
	unmarshaled.___IsNegative = unmarshaledIsNegative_temp_4;
}
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_pinvoke_cleanup(NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_com(const NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4& unmarshaled, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_com& marshaled)
{
	marshaled.____buffer = unmarshaled.____buffer;
	marshaled.___Kind = unmarshaled.___Kind;
	marshaled.___DigitsCount = unmarshaled.___DigitsCount;
	marshaled.___Scale = unmarshaled.___Scale;
	marshaled.___IsNegative = static_cast<int32_t>(unmarshaled.___IsNegative);
}
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_com_back(const NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_com& marshaled, NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4& unmarshaled)
{
	unmarshaled.____buffer = marshaled.____buffer;
	int32_t unmarshaledKind_temp_1 = 0;
	unmarshaledKind_temp_1 = marshaled.___Kind;
	unmarshaled.___Kind = unmarshaledKind_temp_1;
	int32_t unmarshaledDigitsCount_temp_2 = 0;
	unmarshaledDigitsCount_temp_2 = marshaled.___DigitsCount;
	unmarshaled.___DigitsCount = unmarshaledDigitsCount_temp_2;
	int32_t unmarshaledScale_temp_3 = 0;
	unmarshaledScale_temp_3 = marshaled.___Scale;
	unmarshaled.___Scale = unmarshaledScale_temp_3;
	bool unmarshaledIsNegative_temp_4 = false;
	unmarshaledIsNegative_temp_4 = static_cast<bool>(marshaled.___IsNegative);
	unmarshaled.___IsNegative = unmarshaledIsNegative_temp_4;
}
IL2CPP_EXTERN_C void NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshal_com_cleanup(NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* __this, int32_t ___0_kind, uint8_t* ___1_buffer, int32_t ___2_digitsCount, int32_t ___3_scale, bool ___4_isNegative, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_kind;
		__this->___Kind = L_0;
		uint8_t* L_1 = ___1_buffer;
		__this->____buffer = L_1;
		int32_t L_2 = ___2_digitsCount;
		__this->___DigitsCount = L_2;
		int32_t L_3 = ___3_scale;
		__this->___Scale = L_3;
		bool L_4 = ___4_isNegative;
		__this->___IsNegative = L_4;
		return;
	}
}
IL2CPP_EXTERN_C  void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk (RuntimeObject* __this, int32_t ___0_kind, uint8_t* ___1_buffer, int32_t ___2_digitsCount, int32_t ___3_scale, bool ___4_isNegative, const RuntimeMethod* method)
{
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4*>(__this + _offset);
	NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141(_thisAdjusted, ___0_kind, ___1_buffer, ___2_digitsCount, ___3_scale, ___4_isNegative, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t* NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846 (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* __this, const RuntimeMethod* method) 
{
	{
		uint8_t* L_0 = __this->____buffer;
		return L_0;
	}
}
IL2CPP_EXTERN_C  uint8_t* NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4*>(__this + _offset);
	uint8_t* _returnValue;
	_returnValue = NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_pinvoke(const FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84& unmarshaled, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_pinvoke& marshaled)
{
	marshaled.___Kind = unmarshaled.___Kind;
	marshaled.___AlignAndSize = unmarshaled.___AlignAndSize;
	marshaled.___Specifier = unmarshaled.___Specifier;
	marshaled.___Lowercase = static_cast<int32_t>(unmarshaled.___Lowercase);
}
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_pinvoke_back(const FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_pinvoke& marshaled, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84& unmarshaled)
{
	uint8_t unmarshaledKind_temp_0 = 0;
	unmarshaledKind_temp_0 = marshaled.___Kind;
	unmarshaled.___Kind = unmarshaledKind_temp_0;
	int8_t unmarshaledAlignAndSize_temp_1 = 0x0;
	unmarshaledAlignAndSize_temp_1 = marshaled.___AlignAndSize;
	unmarshaled.___AlignAndSize = unmarshaledAlignAndSize_temp_1;
	uint8_t unmarshaledSpecifier_temp_2 = 0x0;
	unmarshaledSpecifier_temp_2 = marshaled.___Specifier;
	unmarshaled.___Specifier = unmarshaledSpecifier_temp_2;
	bool unmarshaledLowercase_temp_3 = false;
	unmarshaledLowercase_temp_3 = static_cast<bool>(marshaled.___Lowercase);
	unmarshaled.___Lowercase = unmarshaledLowercase_temp_3;
}
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_pinvoke_cleanup(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_com(const FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84& unmarshaled, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_com& marshaled)
{
	marshaled.___Kind = unmarshaled.___Kind;
	marshaled.___AlignAndSize = unmarshaled.___AlignAndSize;
	marshaled.___Specifier = unmarshaled.___Specifier;
	marshaled.___Lowercase = static_cast<int32_t>(unmarshaled.___Lowercase);
}
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_com_back(const FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_com& marshaled, FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84& unmarshaled)
{
	uint8_t unmarshaledKind_temp_0 = 0;
	unmarshaledKind_temp_0 = marshaled.___Kind;
	unmarshaled.___Kind = unmarshaledKind_temp_0;
	int8_t unmarshaledAlignAndSize_temp_1 = 0x0;
	unmarshaledAlignAndSize_temp_1 = marshaled.___AlignAndSize;
	unmarshaled.___AlignAndSize = unmarshaledAlignAndSize_temp_1;
	uint8_t unmarshaledSpecifier_temp_2 = 0x0;
	unmarshaledSpecifier_temp_2 = marshaled.___Specifier;
	unmarshaled.___Specifier = unmarshaledSpecifier_temp_2;
	bool unmarshaledLowercase_temp_3 = false;
	unmarshaledLowercase_temp_3 = static_cast<bool>(marshaled.___Lowercase);
	unmarshaled.___Lowercase = unmarshaledLowercase_temp_3;
}
IL2CPP_EXTERN_C void FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshal_com_cleanup(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, uint8_t ___0_kind, int8_t ___1_alignAndSize, uint8_t ___2_specifier, bool ___3_lowercase, const RuntimeMethod* method) 
{
	{
		il2cpp_codegen_initobj(__this, sizeof(FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84));
		uint8_t L_0 = ___0_kind;
		__this->___Kind = L_0;
		int8_t L_1 = ___1_alignAndSize;
		__this->___AlignAndSize = L_1;
		uint8_t L_2 = ___2_specifier;
		__this->___Specifier = L_2;
		bool L_3 = ___3_lowercase;
		__this->___Lowercase = L_3;
		return;
	}
}
IL2CPP_EXTERN_C  void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk (RuntimeObject* __this, uint8_t ___0_kind, int8_t ___1_alignAndSize, uint8_t ___2_specifier, bool ___3_lowercase, const RuntimeMethod* method)
{
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*>(__this + _offset);
	FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055(_thisAdjusted, ___0_kind, ___1_alignAndSize, ___2_specifier, ___3_lowercase, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___Lowercase;
		return (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*>(__this + _offset);
	bool _returnValue;
	_returnValue = FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) 
{
	{
		uint8_t L_0 = __this->___Kind;
		if ((!(((uint32_t)L_0) == ((uint32_t)3))))
		{
			goto IL_000c;
		}
	}
	{
		return ((int32_t)16);
	}

IL_000c:
	{
		return ((int32_t)10);
	}
}
IL2CPP_EXTERN_C  int32_t FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488 (FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NumberFormatKind_t0CCF7872121CBA35A7D6296565B4A7554FB275E4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6CE6C7F7F72B90957BFCD4BAD12273C41A1C3421);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral733F2C0F892979C2C29E7E7599E36E7BC6DA158B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC00E97A4D6DA0A1E727CA6FCAC517CF439F3A016);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEEA647B69ECF2FB3DD083E36418FF930832E0BEF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFFEAABBBE67A35DBB7CF309C3EC21780633775FD);
		s_Il2CppMethodInitialized = true;
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)8);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, _stringLiteral6CE6C7F7F72B90957BFCD4BAD12273C41A1C3421);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)_stringLiteral6CE6C7F7F72B90957BFCD4BAD12273C41A1C3421);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_2 = L_1;
		uint8_t L_3 = __this->___Kind;
		uint8_t L_4 = L_3;
		RuntimeObject* L_5 = Box(NumberFormatKind_t0CCF7872121CBA35A7D6296565B4A7554FB275E4_il2cpp_TypeInfo_var, &L_4);
		NullCheck(L_2);
		ArrayElementTypeCheck (L_2, L_5);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_5);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = L_2;
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, _stringLiteralFFEAABBBE67A35DBB7CF309C3EC21780633775FD);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)_stringLiteralFFEAABBBE67A35DBB7CF309C3EC21780633775FD);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_7 = L_6;
		int8_t L_8 = __this->___AlignAndSize;
		int8_t L_9 = L_8;
		RuntimeObject* L_10 = Box(il2cpp_defaults.sbyte_class, &L_9);
		NullCheck(L_7);
		ArrayElementTypeCheck (L_7, L_10);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_10);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = L_7;
		NullCheck(L_11);
		ArrayElementTypeCheck (L_11, _stringLiteral733F2C0F892979C2C29E7E7599E36E7BC6DA158B);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)_stringLiteral733F2C0F892979C2C29E7E7599E36E7BC6DA158B);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_12 = L_11;
		uint8_t L_13 = __this->___Specifier;
		uint8_t L_14 = L_13;
		RuntimeObject* L_15 = Box(il2cpp_defaults.byte_class, &L_14);
		NullCheck(L_12);
		ArrayElementTypeCheck (L_12, L_15);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)L_15);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_16 = L_12;
		NullCheck(L_16);
		ArrayElementTypeCheck (L_16, _stringLiteralEEA647B69ECF2FB3DD083E36418FF930832E0BEF);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)_stringLiteralEEA647B69ECF2FB3DD083E36418FF930832E0BEF);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_17 = L_16;
		bool L_18;
		L_18 = FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3(__this, NULL);
		bool L_19 = L_18;
		RuntimeObject* L_20 = Box(il2cpp_defaults.boolean_class, &L_19);
		NullCheck(L_17);
		ArrayElementTypeCheck (L_17, L_20);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(7), (RuntimeObject*)L_20);
		String_t* L_21;
		L_21 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralC00E97A4D6DA0A1E727CA6FCAC517CF439F3A016, L_17, NULL);
		return L_21;
	}
}
IL2CPP_EXTERN_C  String_t* FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FormatOptions_tBD49C0C9CC14282D1249620565FC537D4D4AFB84*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_length;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tBigInt_t6A436AD3913A2950571338A5018B48B299987358*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, int32_t ___0_idx, const RuntimeMethod* method) 
{
	{
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_0 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&__this->___m_blocks);
		uint32_t* L_1 = (uint32_t*)(&L_0->___FixedElementField);
		int32_t L_2 = ___0_idx;
		int32_t L_3 = *((uint32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_1, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_2), 4)))));
		return L_3;
	}
}
IL2CPP_EXTERN_C  uint32_t tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk (RuntimeObject* __this, int32_t ___0_idx, const RuntimeMethod* method)
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tBigInt_t6A436AD3913A2950571338A5018B48B299987358*>(__this + _offset);
	uint32_t _returnValue;
	_returnValue = tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2(_thisAdjusted, ___0_idx, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_length;
		return (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tBigInt_t6A436AD3913A2950571338A5018B48B299987358*>(__this + _offset);
	bool _returnValue;
	_returnValue = tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, uint64_t ___0_val, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = ___0_val;
		if ((!(((uint64_t)L_0) > ((uint64_t)((int64_t)(uint64_t)((uint32_t)(-1)))))))
		{
			goto IL_0034;
		}
	}
	{
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&__this->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		uint64_t L_3 = ___0_val;
		*((int32_t*)L_2) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_3&((int64_t)(uint64_t)((uint32_t)(-1))))));
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_4 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&__this->___m_blocks);
		uint32_t* L_5 = (uint32_t*)(&L_4->___FixedElementField);
		uint64_t L_6 = ___0_val;
		*((int32_t*)((uint32_t*)il2cpp_codegen_add((intptr_t)L_5, 4))) = (int32_t)((int32_t)(uint32_t)((int64_t)(((int64_t)((uint64_t)L_6>>((int32_t)32)))&((int64_t)(uint64_t)((uint32_t)(-1))))));
		__this->___m_length = 2;
		return;
	}

IL_0034:
	{
		uint64_t L_7 = ___0_val;
		if (!L_7)
		{
			goto IL_0050;
		}
	}
	{
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_8 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&__this->___m_blocks);
		uint32_t* L_9 = (uint32_t*)(&L_8->___FixedElementField);
		uint64_t L_10 = ___0_val;
		*((int32_t*)L_9) = (int32_t)((int32_t)(uint32_t)((int64_t)((int64_t)L_10&((int64_t)(uint64_t)((uint32_t)(-1))))));
		__this->___m_length = 1;
		return;
	}

IL_0050:
	{
		__this->___m_length = 0;
		return;
	}
}
IL2CPP_EXTERN_C  void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk (RuntimeObject* __this, uint64_t ___0_val, const RuntimeMethod* method)
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tBigInt_t6A436AD3913A2950571338A5018B48B299987358*>(__this + _offset);
	tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14(_thisAdjusted, ___0_val, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233 (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, uint32_t ___0_val, const RuntimeMethod* method) 
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* G_B3_0 = NULL;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* G_B2_0 = NULL;
	int32_t G_B4_0 = 0;
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* G_B4_1 = NULL;
	{
		uint32_t L_0 = ___0_val;
		if (!L_0)
		{
			goto IL_001e;
		}
	}
	{
		U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C* L_1 = (U3Cm_blocksU3Ee__FixedBuffer_tBBE20C4EF7009465021F0375E2128D5DCFF59F7C*)(&__this->___m_blocks);
		uint32_t* L_2 = (uint32_t*)(&L_1->___FixedElementField);
		uint32_t L_3 = ___0_val;
		*((int32_t*)L_2) = (int32_t)L_3;
		uint32_t L_4 = ___0_val;
		if (L_4)
		{
			G_B3_0 = __this;
			goto IL_0017;
		}
		G_B2_0 = __this;
	}
	{
		G_B4_0 = 0;
		G_B4_1 = G_B2_0;
		goto IL_0018;
	}

IL_0017:
	{
		G_B4_0 = 1;
		G_B4_1 = G_B3_0;
	}

IL_0018:
	{
		G_B4_1->___m_length = G_B4_0;
		return;
	}

IL_001e:
	{
		__this->___m_length = 0;
		return;
	}
}
IL2CPP_EXTERN_C  void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk (RuntimeObject* __this, uint32_t ___0_val, const RuntimeMethod* method)
{
	tBigInt_t6A436AD3913A2950571338A5018B48B299987358* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tBigInt_t6A436AD3913A2950571338A5018B48B299987358*>(__this + _offset);
	tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233(_thisAdjusted, ___0_val, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388 (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = __this->___m_integer;
		return (bool)((!(((uint32_t)((int32_t)((uint32_t)L_0>>((int32_t)31)))) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA*>(__this + _offset);
	bool _returnValue;
	_returnValue = tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6 (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = __this->___m_integer;
		return ((int32_t)(((int32_t)((uint32_t)L_0>>((int32_t)23)))&((int32_t)255)));
	}
}
IL2CPP_EXTERN_C  uint32_t tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA*>(__this + _offset);
	uint32_t _returnValue;
	_returnValue = tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF (tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* __this, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = __this->___m_integer;
		return ((int32_t)((int32_t)L_0&((int32_t)8388607)));
	}
}
IL2CPP_EXTERN_C  uint32_t tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion32_t1140001CA96F869F598FBC16C082BC2BA85AB2CA*>(__this + _offset);
	uint32_t _returnValue;
	_returnValue = tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = __this->___m_integer;
		return (bool)((!(((uint64_t)((int64_t)((uint64_t)L_0>>((int32_t)63)))) <= ((uint64_t)((int64_t)0))))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC*>(__this + _offset);
	bool _returnValue;
	_returnValue = tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60 (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = __this->___m_integer;
		return ((int32_t)(uint32_t)((int64_t)(((int64_t)((uint64_t)L_0>>((int32_t)52)))&((int64_t)((int32_t)2047)))));
	}
}
IL2CPP_EXTERN_C  uint32_t tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC*>(__this + _offset);
	uint32_t _returnValue;
	_returnValue = tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C (tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* __this, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = __this->___m_integer;
		return ((int64_t)((int64_t)L_0&((int64_t)4503599627370495LL)));
	}
}
IL2CPP_EXTERN_C  uint64_t tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<tFloatUnion64_t737111FBE1FD2D4509E72C45FE6389106B60B2FC*>(__this + _offset);
	uint64_t _returnValue;
	_returnValue = tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void* SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD (int64_t ___0_getHashCode64, int64_t ___1_getSubHashCode64, uint32_t ___2_sizeOf, uint32_t ___3_alignment, const RuntimeMethod* method) 
{
	Hash128_t93367F504B687578F893CDBCD13FB95AC8A87A40 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int64_t L_0 = ___0_getHashCode64;
		int64_t L_1 = ___1_getSubHashCode64;
		Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3((&V_0), L_0, L_1, NULL);
		uint32_t L_2 = ___2_sizeOf;
		uint32_t L_3 = ___3_alignment;
		void* L_4;
		L_4 = BurstCompilerService_GetOrCreateSharedMemory_m1293EB3119CBEE41DBCC0E3B2235601BD927BFE6((&V_0), L_2, L_3, NULL);
		return L_4;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F (PreserveAttribute_tDEA15EF9DCAB8AC4428ED72A2A1377384FE7C27B* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7 (AssumeRangeAttribute_t06ACC8EEDB5AB2CE78FDE39A4F36B674A1CE06CA* __this, int64_t ___0_min, int64_t ___1_max, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4_inline (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsGlobalU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69_inline (BurstCompilerOptions_t5F93118F305E1B0C950C6F9AF8BCA74033DA01C9* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___U3COptionsChangedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_inline (NumberBuffer_tF09E8463D840202ECA50F50BE6D57729C18213B4* __this, const RuntimeMethod* method) 
{
	{
		uint8_t* L_0 = __this->____buffer;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_inline (tBigInt_t6A436AD3913A2950571338A5018B48B299987358* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_length;
		return L_0;
	}
}
