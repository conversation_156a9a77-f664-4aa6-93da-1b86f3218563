<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname=".BikeAnimation/IKPointsClass" preserve="nothing" serialized="true"/>
		<type fullname=".BikeCamera/BikeUIClass" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/BikeLights" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/BikeParticles" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/BikeSetting" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/BikeSounds" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/BikeWheels" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/ConnectWheel" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/HitGround" preserve="nothing" serialized="true"/>
		<type fullname=".BikeControl/WheelSetting" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Camera/CameraTarget" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_CarControllerV4/Gear" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_ChangableWheels/ChangableWheels" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_CustomizationData" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Customizer_Loadout" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Damage" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_DashboardObjects/FuelDial" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_DashboardObjects/HeatDial" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_DashboardObjects/RPMDial" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_DashboardObjects/SpeedoMeterDial" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Emission" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_GroundMaterials/GroundMaterialFrictions" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_GroundMaterials/TerrainFrictions" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Inputs" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_Settings/BehaviorType" preserve="nothing" serialized="true"/>
		<type fullname=".RCC_TruckTrailer/TrailerWheel" preserve="nothing" serialized="true"/>
		<type fullname="AdvancedHelicopterControllerwithShooting.RadarTypeInfo" preserve="nothing" serialized="true"/>
		<type fullname="BoatControllerwithShooting.RadarTypeInfo" preserve="nothing" serialized="true"/>
		<type fullname="Invector.vCharacterController.vThirdPersonMotor/vMovementSpeed" preserve="nothing" serialized="true"/>
		<type fullname="WSMGameStudio.RailroadSystem.Route" preserve="nothing" serialized="true"/>
		<type fullname="WSMGameStudio.RailroadSystem.Sensors" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="DOTween">
		<type fullname="DG.Tweening.Core.DOTweenSettings/ModulesSetup" preserve="nothing" serialized="true"/>
		<type fullname="DG.Tweening.Core.DOTweenSettings/SafeModeOptions" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.Cinemachine">
		<type fullname="Unity.Cinemachine.CameraTarget" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineBrain/LensModeOverrideSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineDeoccluder/ObstacleAvoidance" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineDeoccluder/ObstacleAvoidance/FollowTargetSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineDeoccluder/QualityEvaluation" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineInputAxisController/Reader" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.CinemachineThirdPersonFollow/ObstacleSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.InputAxisControllerManager`1" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.LensSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.LensSettings/PhysicalSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.LookaheadSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.PrioritySettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.ScreenComposerSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.ScreenComposerSettings/DeadZoneSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.ScreenComposerSettings/HardLimitSettings" preserve="nothing" serialized="true"/>
		<type fullname="Unity.Cinemachine.TargetTracking.TrackerSettings" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputAction" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputActionMap" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputBinding" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme/DeviceRequirement" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.LensFlareDataElementSRP" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.SRPLensFlareBlendMode" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.SRPLensFlareColorType" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.SRPLensFlareDistribution" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.SRPLensFlareType" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.TextureGradient" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIPrefabBundle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.KerningTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Sprite" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Style" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.Timeline">
		<type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule">
		<type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.IMGUIModule">
		<type fullname="UnityEngine.GUISettings" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.GUIStyle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.TextCoreFontEngineModule">
		<type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/DropdownEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionDataList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true"/>
	</assembly>
</linker>
