﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void AccessibilityAction_Dispose_m7C7E356F1EF00E9B23CAEFA9291D5FBBE78A8907 (void);
extern void AccessibilityAction_Dispose_m47E2DCB64AF8193F2A225433524603DC0A729254 (void);
extern void AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94 (void);
extern void AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281 (void);
extern void AccessibilityAction_Internal_InvokeActivated_mA861809EB68DD92BD8A00831D8CA83A5CDA4888A (void);
extern void AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696 (void);
extern void AccessibilityManager_remove_screenReaderStatusChanged_m2D03E603E74E4418CBA1C91C107AAB82ACABB010 (void);
extern void AccessibilityManager_add_nodeFocusChanged_m5BFCE49D472600D1544C25A28931D8FBCD032CFA (void);
extern void AccessibilityManager_remove_nodeFocusChanged_mE608913E1DAAD6F32EA974A9B26E66E6EE0C2203 (void);
extern void AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2 (void);
extern void AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0 (void);
extern void AccessibilityManager_Internal_Initialize_mBCC892503DA42AF7A8AD91918E0C5BB04DF62B84 (void);
extern void AccessibilityManager_Internal_Update_m14966B56AEA0950BC940AB264999BCC172EE1DCE (void);
extern void AccessibilityManager_Internal_GetRootNodeIds_mCB3855E8EDB937C5D097FD7CCD84BE50A6ADB6A5 (void);
extern void AccessibilityManager_Internal_GetNode_mA5DBEEE6012BF17E04200FCC028B0729E3F67CA1 (void);
extern void AccessibilityManager_Internal_GetNodeIdAt_m55E20752B5F8C3ED7910ED649F0BD70627D974B3 (void);
extern void AccessibilityManager_Internal_OnAccessibilityNotificationReceived_m1C65ECB977E8A5A7F6C0AD8F97D0B39A49832C86 (void);
extern void AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5 (void);
extern void AccessibilityManager_GetExclusiveLock_m81EB3F3F7018C08A22AE25AB3554AA1EF02EC2C7 (void);
extern void AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C (void);
extern void AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110 (void);
extern void AccessibilityManager__cctor_m04A7449F4F8891545FBA74BF400873CF71C928CC (void);
extern void NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0 (void);
extern void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0 (void);
extern void NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C (void);
extern void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174 (void);
extern void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639 (void);
extern void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE (void);
extern void NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57 (void);
extern void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E (void);
extern void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B (void);
extern void NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC (void);
extern void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611 (void);
extern void NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11 (void);
extern void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39 (void);
extern void NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2 (void);
extern void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF (void);
extern void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA (void);
extern void NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024 (void);
extern void ExclusiveLock__ctor_m875188F2E41DC8113A37A71CE4DC8B6707D265F5 (void);
extern void ExclusiveLock_Finalize_m5FAA7693E76CD44F15160418E97D384FC354CB68 (void);
extern void ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3 (void);
extern void ExclusiveLock_Dispose_mD5D75B88E21B82A4F3FB65338FD81B813F524D4F (void);
extern void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E (void);
extern void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD (void);
extern void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714 (void);
extern void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F (void);
extern void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C (void);
extern void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3 (void);
extern void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2 (void);
extern void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1 (void);
extern void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78 (void);
extern void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3 (void);
extern void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036 (void);
extern void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A (void);
extern void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F (void);
extern void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD (void);
extern void AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43 (void);
extern void AccessibilityNodeManager_SetFrame_mE47923B10A1592690CEF29D975C2C22654AA6E0A (void);
extern void AccessibilityNodeManager_SetChildren_mE43C7A8C5CDC9D8049088AC1A89E0BDC4015C7C9 (void);
extern void AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B (void);
extern void AccessibilityNodeManager_Internal_InvokeFocusChanged_mF52C86D20B4F7CEF594D028EB8A0D073CDC86F1C (void);
extern void AccessibilityNodeManager_Internal_InvokeSelected_m3361DDF76F20E2F5B6240718B79A08B87BD1CC55 (void);
extern void AccessibilityNodeManager_Internal_InvokeIncremented_m43911AF44D7577BFD428E7CAB48DBB1F7EE2EF02 (void);
extern void AccessibilityNodeManager_Internal_InvokeDecremented_mEFAAE7277E2856511A4FFB730338D59DB0E5227E (void);
extern void AccessibilityNodeManager_Internal_InvokeDismissed_m68A53CADDE358234374B04D231D071D0816DF974 (void);
extern void AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0 (void);
extern void AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8 (void);
extern void AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1 (void);
extern void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B (void);
extern void AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379 (void);
extern void AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71 (void);
extern void AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0 (void);
extern void AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB (void);
extern void AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514 (void);
extern void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED (void);
extern void AccessibilitySettings_Internal_OnFontScaleChanged_m188BDB50617266AD9B80720FB3D70011DF06C58A (void);
extern void AccessibilitySettings_Internal_OnBoldTextStatusChanged_mB80B283779D5416E1162FFB9BB831F2B37AC1C0D (void);
extern void AccessibilitySettings_Internal_OnClosedCaptioningStatusChanged_m7B522D6FC70776E4D1A5A8B184578F64EE4D11E5 (void);
extern void AccessibilitySettings_InvokeFontScaleChanged_m01DE529AC72A0FBE344F62C7E03FF028E7C196D0 (void);
extern void AccessibilitySettings_InvokeBoldTextStatusChanged_mE3FAB90ED74180EF706A8AA160628EB14C202E7D (void);
extern void AccessibilitySettings_InvokeClosedCaptionStatusChanged_m501523ED4BB118A6FE6370D1C617ADDB007EE8A4 (void);
extern void AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80 (void);
extern void AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340 (void);
extern void AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC (void);
extern void AssistiveSupport_Initialize_mDB3A5CE4DA477AF876D3A1DAB0577B559CEA1063 (void);
extern void AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D (void);
extern void AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF (void);
extern void AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE (void);
extern void AssistiveSupport__cctor_m4088D8A848615B8528F864DBF58A6E628179FAB2 (void);
extern void NotificationDispatcher_Send_m44FE72134E006FF63A7E6A0B57629775C5ABE1FE (void);
extern void NotificationDispatcher_SendScreenChanged_mC11676B524097EB3CC3FE09B4952C9385393F5D6 (void);
extern void NotificationDispatcher__ctor_m4C2107C7F72DA9205175A3D3F5832F328E548F68 (void);
extern void AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1 (void);
extern void AccessibilityHierarchy_FreeNative_m335828EAF3E5F150C16550316584F71CCAFB2758 (void);
extern void AccessibilityHierarchy_TryGetNodeAt_m22C5D534645D8EFC73811373C2050E7E775F1990 (void);
extern void AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368 (void);
extern void AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E (void);
extern void AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7 (void);
extern void AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2 (void);
extern void AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657 (void);
extern void AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A (void);
extern void AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D (void);
extern void AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F (void);
extern void AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1 (void);
extern void AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F (void);
extern void AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11 (void);
extern void AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA (void);
extern void AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59 (void);
extern void AccessibilityNode_SetFrame_mF216E2796C4A53212FD410C58C9734293FB5396B (void);
extern void AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB (void);
extern void AccessibilityNode_CalculateFrame_m0CD6C0D1DB2539DC74CDE281FAC23058D191E054 (void);
extern void AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F (void);
extern void AccessibilityNode_GetNodeData_m6C7765891167AB6E820D68F4AF89F891F03ACE2C (void);
extern void AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF (void);
extern void AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2 (void);
extern void AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C (void);
extern void AccessibilityNode_NotifyFocusChanged_mEA14C3D3534CB0920ECD5AFF32350855BC6FD5E8 (void);
extern void AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A (void);
extern void AccessibilityNode_InvokeSelected_mBB094C8957C71FBCA0C033A4F9EE7127B8D1A7BA (void);
extern void AccessibilityNode_InvokeIncremented_mC2B5AF750A1BC3177E92787DAA385360234B59AF (void);
extern void AccessibilityNode_InvokeDecremented_m3BF11CFA927B9303367B46C9EA7E0E99A5C422D5 (void);
extern void AccessibilityNode_Dismissed_m7492D9E72948C6718A13A2FDB628BDC4E81091EF (void);
extern void AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947 (void);
extern void AccessibilityHierarchyService_Start_mEC19D7908745553B772EC14E4AF0CE4492135B67 (void);
extern void AccessibilityHierarchyService_Stop_mEA1D729479D0BC1458D851F1B1706EF420351794 (void);
extern void AccessibilityHierarchyService_RemoveActiveHierarchy_m61D66D2B65B8E17B08948962B227203C35DF5A3A (void);
extern void AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8 (void);
extern void AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2 (void);
extern void AccessibilityHierarchyService_TryGetNodeAt_mBEDA9E8FBF12970339369F80650637BE17E2280F (void);
extern void AccessibilityHierarchyService__ctor_mE2F69255846F000B58E412306376C09C02DB276B (void);
extern void ServiceManager__ctor_m06C1D1E60CF676E1E526A3E281757604BA5C9F3A (void);
extern void ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582 (void);
extern void ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2 (void);
static Il2CppMethodPointer s_methodPointers[144] = 
{
	AccessibilityAction_Dispose_m7C7E356F1EF00E9B23CAEFA9291D5FBBE78A8907,
	AccessibilityAction_Dispose_m47E2DCB64AF8193F2A225433524603DC0A729254,
	AccessibilityAction_Internal_Destroy_m8ED004D8C1FCA8DA973C963F42237F8CF7906C94,
	AccessibilityAction_get_activated_m0553742FF5C346016429F9EDEF0C4BA47A97C281,
	AccessibilityAction_Internal_InvokeActivated_mA861809EB68DD92BD8A00831D8CA83A5CDA4888A,
	AccessibilityManager_add_screenReaderStatusChanged_m3F58FA8BC33AA1A47825CE70167A3CCB2B71B696,
	AccessibilityManager_remove_screenReaderStatusChanged_m2D03E603E74E4418CBA1C91C107AAB82ACABB010,
	AccessibilityManager_add_nodeFocusChanged_m5BFCE49D472600D1544C25A28931D8FBCD032CFA,
	AccessibilityManager_remove_nodeFocusChanged_mE608913E1DAAD6F32EA974A9B26E66E6EE0C2203,
	AccessibilityManager_IsScreenReaderEnabled_m23258F035C878DE1E60F45AADDAB82701592B2F2,
	AccessibilityManager_SendAccessibilityNotification_mD1622940B50F840DFDB5CD9B860918559A7A7CD0,
	AccessibilityManager_Internal_Initialize_mBCC892503DA42AF7A8AD91918E0C5BB04DF62B84,
	AccessibilityManager_Internal_Update_m14966B56AEA0950BC940AB264999BCC172EE1DCE,
	AccessibilityManager_Internal_GetRootNodeIds_mCB3855E8EDB937C5D097FD7CCD84BE50A6ADB6A5,
	AccessibilityManager_Internal_GetNode_mA5DBEEE6012BF17E04200FCC028B0729E3F67CA1,
	AccessibilityManager_Internal_GetNodeIdAt_m55E20752B5F8C3ED7910ED649F0BD70627D974B3,
	AccessibilityManager_Internal_OnAccessibilityNotificationReceived_m1C65ECB977E8A5A7F6C0AD8F97D0B39A49832C86,
	AccessibilityManager_QueueNotification_m78C7248860B35C51F688B0BE377A54155D6CC9C5,
	AccessibilityManager_GetExclusiveLock_m81EB3F3F7018C08A22AE25AB3554AA1EF02EC2C7,
	AccessibilityManager_Lock_m8B3C3A53D1D61AA599B54EF84C945C85E052816C,
	AccessibilityManager_Unlock_mFD453A428800D2A59908EF9AA62791E497028110,
	AccessibilityManager__cctor_m04A7449F4F8891545FBA74BF400873CF71C928CC,
	NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0,
	NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0,
	NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C,
	NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174,
	NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639,
	NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE,
	NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57,
	NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E,
	NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B,
	NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC,
	NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611,
	NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11,
	NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39,
	NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2,
	NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF,
	NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA,
	NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024,
	ExclusiveLock__ctor_m875188F2E41DC8113A37A71CE4DC8B6707D265F5,
	ExclusiveLock_Finalize_m5FAA7693E76CD44F15160418E97D384FC354CB68,
	ExclusiveLock_InternalDispose_mE29AF9E4A0FDD5CBB3D5E0D0D9BD5B9DD2602BD3,
	ExclusiveLock_Dispose_mD5D75B88E21B82A4F3FB65338FD81B813F524D4F,
	AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E,
	AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD,
	AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714,
	AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F,
	AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C,
	AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3,
	AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2,
	AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1,
	AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78,
	AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3,
	AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036,
	AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A,
	AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F,
	AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD,
	AccessibilityNodeManager_DestroyNativeNode_m02AD32BA9B71834975261D2EF921AD1DCE469E43,
	AccessibilityNodeManager_SetFrame_mE47923B10A1592690CEF29D975C2C22654AA6E0A,
	AccessibilityNodeManager_SetChildren_mE43C7A8C5CDC9D8049088AC1A89E0BDC4015C7C9,
	AccessibilityNodeManager_SetActions_m851B8FCD73B118DBA653073F7269412DA7A4DD5B,
	AccessibilityNodeManager_Internal_InvokeFocusChanged_mF52C86D20B4F7CEF594D028EB8A0D073CDC86F1C,
	AccessibilityNodeManager_Internal_InvokeSelected_m3361DDF76F20E2F5B6240718B79A08B87BD1CC55,
	AccessibilityNodeManager_Internal_InvokeIncremented_m43911AF44D7577BFD428E7CAB48DBB1F7EE2EF02,
	AccessibilityNodeManager_Internal_InvokeDecremented_mEFAAE7277E2856511A4FFB730338D59DB0E5227E,
	AccessibilityNodeManager_Internal_InvokeDismissed_m68A53CADDE358234374B04D231D071D0816DF974,
	AccessibilityNodeManager_SetFrame_Injected_mE6A1B2BE1CB65747C9A702A529AB1FC60D6CCFA0,
	AccessibilityNodeManager_SetChildren_Injected_m19BD3971BB15ABA6A0AC667E6F05C5E692C095A8,
	AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1,
	AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B,
	AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379,
	AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71,
	AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0,
	AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB,
	AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514,
	AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED,
	AccessibilitySettings_Internal_OnFontScaleChanged_m188BDB50617266AD9B80720FB3D70011DF06C58A,
	AccessibilitySettings_Internal_OnBoldTextStatusChanged_mB80B283779D5416E1162FFB9BB831F2B37AC1C0D,
	AccessibilitySettings_Internal_OnClosedCaptioningStatusChanged_m7B522D6FC70776E4D1A5A8B184578F64EE4D11E5,
	AccessibilitySettings_InvokeFontScaleChanged_m01DE529AC72A0FBE344F62C7E03FF028E7C196D0,
	AccessibilitySettings_InvokeBoldTextStatusChanged_mE3FAB90ED74180EF706A8AA160628EB14C202E7D,
	AccessibilitySettings_InvokeClosedCaptionStatusChanged_m501523ED4BB118A6FE6370D1C617ADDB007EE8A4,
	AssistiveSupport_get_isScreenReaderEnabled_m9769DC0E61E158E2E11AC637C4122B29D1239B80,
	AssistiveSupport_set_isScreenReaderEnabled_m0226DFBEC36B21C15BEF8EBB3289076D14CEF340,
	AssistiveSupport_get_notificationDispatcher_m5F6A698706FC3CE7A105ED9505B4B223630A74FC,
	AssistiveSupport_Initialize_mDB3A5CE4DA477AF876D3A1DAB0577B559CEA1063,
	NULL,
	AssistiveSupport_ScreenReaderStatusChanged_mF3DF822627A448D7DD95294E673244A3CECF813D,
	AssistiveSupport_NodeFocusChanged_m6BD85DF234861058E6F0DEA12F458314689237CF,
	AssistiveSupport_get_activeHierarchy_m6D7A736CDA7CF9F386474EF3FCE76AEAF6429FCE,
	AssistiveSupport__cctor_m4088D8A848615B8528F864DBF58A6E628179FAB2,
	NotificationDispatcher_Send_m44FE72134E006FF63A7E6A0B57629775C5ABE1FE,
	NotificationDispatcher_SendScreenChanged_mC11676B524097EB3CC3FE09B4952C9385393F5D6,
	NotificationDispatcher__ctor_m4C2107C7F72DA9205175A3D3F5832F328E548F68,
	AccessibilityHierarchy_TryGetNode_m98DF2D49C160054D599F9895E32F2D8877962EB1,
	AccessibilityHierarchy_FreeNative_m335828EAF3E5F150C16550316584F71CCAFB2758,
	AccessibilityHierarchy_TryGetNodeAt_m22C5D534645D8EFC73811373C2050E7E775F1990,
	AccessibilityHierarchy_U3CTryGetNodeAtU3Eg__FindNodeContainingPointU7C27_0_m972A78385EB1C8838C3B150B38EF66ADF4DC0368,
	AccessibilityNode_FreeNative_m5AA2FF6DAD9ACB892651A2E5D7ECCBA4FBC09C5E,
	AccessibilityNode_get_id_m81143255DEA24BD056EA778DCCDBC2E83B7A10F7,
	AccessibilityNode_get_label_m80F6E9460938846F7849B4C9D493C60FC88281B2,
	AccessibilityNode_get_value_m3A4869063576AD5BE456D9FD79AB8B89B6B9E657,
	AccessibilityNode_get_hint_m147DAEAD18359CB6D226A60299634F7A2D0D056A,
	AccessibilityNode_get_isActive_mC25CBD54BA64A903D2876F7E14892FA7E88C272D,
	AccessibilityNode_get_role_mA3324691B787FB85DD4837CCB8BF6DEC471D806F,
	AccessibilityNode_get_allowsDirectInteraction_mA4018A0411797DC5E7DCDD7EF38F8F353BAE9AD1,
	AccessibilityNode_get_state_mB27D6E233B11A28AAEDD615D8D33A55BCF1A768F,
	AccessibilityNode_get_parent_m8C4EC21867FE72694688B45EAEE16051B2007E11,
	AccessibilityNode_get_childList_m8A5E69FFF1D54BE9750169100AFA6008BA93EDFA,
	AccessibilityNode_get_frame_m20553D15C2ACFD776F44C9BDA3EE0FDB5CBB1C59,
	AccessibilityNode_SetFrame_mF216E2796C4A53212FD410C58C9734293FB5396B,
	AccessibilityNode_get_frameGetter_mF34AC4C142A58E66A2D86C1F9947E99B2948CEFB,
	AccessibilityNode_CalculateFrame_m0CD6C0D1DB2539DC74CDE281FAC23058D191E054,
	AccessibilityNode_get_language_m153F88B4F9BC3FE8FA069EF58271ADF5FE28CA6F,
	AccessibilityNode_GetNodeData_m6C7765891167AB6E820D68F4AF89F891F03ACE2C,
	AccessibilityNode_ChildrenChanged_m3677FA30E0314C5A641C44696DFE9A3936EB2EFF,
	AccessibilityNode_ActionsChanged_m52031FF685A82117789DB62F2185C24FDCE707B2,
	AccessibilityNode_IsInActiveHierarchy_m81A3BC3EEC7156C54AC4BC4D9238037E3DFE4E2C,
	AccessibilityNode_NotifyFocusChanged_mEA14C3D3534CB0920ECD5AFF32350855BC6FD5E8,
	AccessibilityNode_InvokeFocusChanged_m79E2A5C2D663F133B2633467738C18F117E6BE8A,
	AccessibilityNode_InvokeSelected_mBB094C8957C71FBCA0C033A4F9EE7127B8D1A7BA,
	AccessibilityNode_InvokeIncremented_mC2B5AF750A1BC3177E92787DAA385360234B59AF,
	AccessibilityNode_InvokeDecremented_m3BF11CFA927B9303367B46C9EA7E0E99A5C422D5,
	AccessibilityNode_Dismissed_m7492D9E72948C6718A13A2FDB628BDC4E81091EF,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AccessibilityHierarchyService_get_hierarchy_mD97C09144AA068B7983DEB882CF7030204090947,
	AccessibilityHierarchyService_Start_mEC19D7908745553B772EC14E4AF0CE4492135B67,
	AccessibilityHierarchyService_Stop_mEA1D729479D0BC1458D851F1B1706EF420351794,
	AccessibilityHierarchyService_RemoveActiveHierarchy_m61D66D2B65B8E17B08948962B227203C35DF5A3A,
	AccessibilityHierarchyService_TryGetNode_mFC8404990B7EC5418649A229004C35F3C4BDC7B8,
	AccessibilityHierarchyService_GetRootNodes_mD28B03E9482635AC5DA42A059FF30C6165AC46D2,
	AccessibilityHierarchyService_TryGetNodeAt_mBEDA9E8FBF12970339369F80650637BE17E2280F,
	AccessibilityHierarchyService__ctor_mE2F69255846F000B58E412306376C09C02DB276B,
	NULL,
	ServiceManager__ctor_m06C1D1E60CF676E1E526A3E281757604BA5C9F3A,
	NULL,
	NULL,
	ServiceManager_UpdateServices_mD104C87C15292CDF135A6D96A161620F15B29582,
	ServiceManager_ScreenReaderStatusChanged_mF8C9F82CBE80964C87F68D06786504164F969CD2,
};
extern void NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_AdjustorThunk (void);
extern void NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_AdjustorThunk (void);
extern void NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_AdjustorThunk (void);
extern void NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_AdjustorThunk (void);
extern void NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_AdjustorThunk (void);
extern void NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_AdjustorThunk (void);
extern void NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_AdjustorThunk (void);
extern void NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_AdjustorThunk (void);
extern void NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_AdjustorThunk (void);
extern void NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_AdjustorThunk (void);
extern void NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_AdjustorThunk (void);
extern void NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_AdjustorThunk (void);
extern void NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_AdjustorThunk (void);
extern void NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_AdjustorThunk (void);
extern void NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_AdjustorThunk (void);
extern void NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_AdjustorThunk (void);
extern void NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024_AdjustorThunk (void);
extern void AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_AdjustorThunk (void);
extern void AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_AdjustorThunk (void);
extern void AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_AdjustorThunk (void);
extern void AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_AdjustorThunk (void);
extern void AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_AdjustorThunk (void);
extern void AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_AdjustorThunk (void);
extern void AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_AdjustorThunk (void);
extern void AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_AdjustorThunk (void);
extern void AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_AdjustorThunk (void);
extern void AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_AdjustorThunk (void);
extern void AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_AdjustorThunk (void);
extern void AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_AdjustorThunk (void);
extern void AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_AdjustorThunk (void);
extern void AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_AdjustorThunk (void);
extern void AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_AdjustorThunk (void);
extern void AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_AdjustorThunk (void);
extern void AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[39] = 
{
	{ 0x06000017, NotificationContext_get_notification_m43EC0F66FFD242C0FB1904CE40316D09CBE73EC0_AdjustorThunk },
	{ 0x06000018, NotificationContext_set_notification_m789E1626EC459FB7AC6EA643EEA5DDEBF212A7E0_AdjustorThunk },
	{ 0x06000019, NotificationContext_get_isScreenReaderEnabled_mCA89CB7D5277069EA548AB04D450D1A3FA43251C_AdjustorThunk },
	{ 0x0600001A, NotificationContext_set_isScreenReaderEnabled_m3EE5C6A100E4C21E0B1E160A379BE5CF493E8174_AdjustorThunk },
	{ 0x0600001B, NotificationContext_set_announcement_mDA692D804B7B561F6FD44421E42074DBC0E74639_AdjustorThunk },
	{ 0x0600001C, NotificationContext_set_wasAnnouncementSuccessful_mD8F69B143EF4815BC7050FC9185E74095C9787BE_AdjustorThunk },
	{ 0x0600001D, NotificationContext_get_currentNode_mB1116EC445A271556E44D2A21963B306A87DDD57_AdjustorThunk },
	{ 0x0600001E, NotificationContext_set_currentNode_mDF9C200B9476BD1CFC7A7A364F1DA3681859C12E_AdjustorThunk },
	{ 0x0600001F, NotificationContext_set_nextNode_mAC398C81DC48BD89B85D932BD7959C55985B6E2B_AdjustorThunk },
	{ 0x06000020, NotificationContext_get_fontScale_mF7F3CCC0FA60799DA2F89923A247B6AB0CB82CEC_AdjustorThunk },
	{ 0x06000021, NotificationContext_set_fontScale_m896C4DD01D56C9589F4B1D0C36E601D228E18611_AdjustorThunk },
	{ 0x06000022, NotificationContext_get_isBoldTextEnabled_mFC0C3B09FC0FE5BF822D07DFC0961402F5084E11_AdjustorThunk },
	{ 0x06000023, NotificationContext_set_isBoldTextEnabled_m3A37BD8251B1174EE2814AD7301429338B611E39_AdjustorThunk },
	{ 0x06000024, NotificationContext_get_isClosedCaptioningEnabled_mDEE8525771D32877ECF4A722E21C7A8EEF4477F2_AdjustorThunk },
	{ 0x06000025, NotificationContext_set_isClosedCaptioningEnabled_mCAC73C4CB87325895FEAF8BDD027AB37FFABEBDF_AdjustorThunk },
	{ 0x06000026, NotificationContext_set_nativeContext_mA27427ADC47003C7E5FA9B1F89EC2F892C6CD0FA_AdjustorThunk },
	{ 0x06000027, NotificationContext__ctor_mC39620E6AB64695255F481303439B84C42552024_AdjustorThunk },
	{ 0x0600002C, AccessibilityNodeData_set_id_m010670C4263AE105E7221B6837DB3B3B8C39100E_AdjustorThunk },
	{ 0x0600002D, AccessibilityNodeData_set_isActive_mE75907C1ADFCF6526A00AB5B3C9601C0C69698AD_AdjustorThunk },
	{ 0x0600002E, AccessibilityNodeData_set_label_mE1C2F563B22C49F0D6D5F4C6CB504D0D0ED89714_AdjustorThunk },
	{ 0x0600002F, AccessibilityNodeData_set_value_mE50F323A25771EDC248C28C56677EB3C1F166B2F_AdjustorThunk },
	{ 0x06000030, AccessibilityNodeData_set_hint_m06DFA8DE490A72C91BDD9A2CE5C8160E02FF617C_AdjustorThunk },
	{ 0x06000031, AccessibilityNodeData_set_role_m3DCC606228DB09F8B56B4B89E99D8DE4A24648C3_AdjustorThunk },
	{ 0x06000032, AccessibilityNodeData_set_allowsDirectInteraction_m62C76A306BD2E6D07BFE16BAF71F80B12A8E70F2_AdjustorThunk },
	{ 0x06000033, AccessibilityNodeData_set_state_m6C38957BE9C08063A104D6EEFACC0206581F29D1_AdjustorThunk },
	{ 0x06000034, AccessibilityNodeData_set_frame_m6FF1A016E9D607D767C76F3BD3182824EFB4CF78_AdjustorThunk },
	{ 0x06000035, AccessibilityNodeData_set_parentId_mCD60B5E43B15577CE8DFBDE445F2F154FE5858A3_AdjustorThunk },
	{ 0x06000036, AccessibilityNodeData_set_childIds_m8C2EBFEC9C66F77099AB7A4344C82445D436E036_AdjustorThunk },
	{ 0x06000037, AccessibilityNodeData_set_language_m8FF04B6043B11841CC5F93B7A54C1FE0FDFA0F1A_AdjustorThunk },
	{ 0x06000038, AccessibilityNodeData_set_implementsSelected_m9CE2D7764BD44B684C39238FAA6DEE63FF06DA7F_AdjustorThunk },
	{ 0x06000039, AccessibilityNodeData_set_implementsDismissed_m9737F36ADF034BB82CFC756370949F79724542CD_AdjustorThunk },
	{ 0x06000045, AccessibilityNotificationContext_get_notification_m68BFDD01B2BC766255E7B50A96DD6F96B8EA30D1_AdjustorThunk },
	{ 0x06000046, AccessibilityNotificationContext_set_notification_m3DD36A6B5C772A6A7CDC044FB61D2E6ABCF1071B_AdjustorThunk },
	{ 0x06000047, AccessibilityNotificationContext_get_isScreenReaderEnabled_m83C39B91CA41D4CE14E0F9F149D82A8B07A2C379_AdjustorThunk },
	{ 0x06000048, AccessibilityNotificationContext_get_announcement_m590A8451DD0BEB7DEF23B0D0CB1F9560ECF35A71_AdjustorThunk },
	{ 0x06000049, AccessibilityNotificationContext_get_wasAnnouncementSuccessful_m586FF2A87CB135D1EE51F8F4BDBBD20298006EE0_AdjustorThunk },
	{ 0x0600004A, AccessibilityNotificationContext_get_currentNodeId_mEA271DE9F787C8B25EB1231ED0FA54FF599458BB_AdjustorThunk },
	{ 0x0600004B, AccessibilityNotificationContext_get_nextNodeId_m1B5F3FA60A3A94C030A4F990D8F820467E3E6514_AdjustorThunk },
	{ 0x0600004C, AccessibilityNotificationContext_set_nextNodeId_m7902300291066B5DB6F0A31155E42255C21257ED_AdjustorThunk },
};
static const int32_t s_InvokerIndices[144] = 
{
	10870,
	8468,
	15981,
	10698,
	10537,
	15983,
	15983,
	15983,
	15983,
	16291,
	15971,
	16420,
	16420,
	16341,
	13963,
	14215,
	15971,
	15996,
	16341,
	16420,
	16420,
	16420,
	10637,
	8568,
	10537,
	8468,
	8627,
	8468,
	10698,
	8627,
	8627,
	10781,
	8700,
	10537,
	8468,
	10537,
	8468,
	8447,
	8445,
	10870,
	10870,
	10870,
	10870,
	8568,
	8468,
	8627,
	8627,
	8627,
	8775,
	8468,
	8775,
	8662,
	8568,
	8627,
	8568,
	8468,
	8468,
	14701,
	14704,
	14703,
	14703,
	14700,
	15447,
	15979,
	15979,
	15447,
	14699,
	14699,
	10637,
	8568,
	10537,
	10698,
	10537,
	10637,
	10637,
	8568,
	15991,
	15972,
	15972,
	15991,
	15972,
	15972,
	16291,
	15972,
	16341,
	16420,
	-1,
	15972,
	15983,
	16341,
	16420,
	15971,
	8627,
	10870,
	2740,
	10870,
	1717,
	14326,
	8468,
	10637,
	10698,
	10698,
	10698,
	10537,
	10854,
	10537,
	10854,
	10698,
	10698,
	10738,
	8662,
	10698,
	10870,
	10637,
	8445,
	10870,
	10870,
	10537,
	8468,
	8468,
	10537,
	10870,
	10870,
	10537,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10698,
	10870,
	10870,
	8468,
	2740,
	10698,
	1717,
	10870,
	-1,
	10870,
	-1,
	-1,
	8468,
	8468,
};
static const Il2CppTokenRangePair s_rgctxIndices[4] = 
{
	{ 0x02000011, { 2, 8 } },
	{ 0x06000057, { 0, 2 } },
	{ 0x0600008D, { 10, 2 } },
	{ 0x0600008E, { 12, 4 } },
};
extern const uint32_t g_rgctx_T_tD5F3F41A7F8057A8B53DDCF4662BD926A404EB4D;
extern const uint32_t g_rgctx_ServiceManager_GetService_TisT_tD5F3F41A7F8057A8B53DDCF4662BD926A404EB4D_m37EF4D438603D50B174415DF67D3A6494D8EC10F;
extern const uint32_t g_rgctx_ObservableList_1_t8A21132CEA9DD922C8E2AD37490F3BC972C7F8B1;
extern const uint32_t g_rgctx_List_1_t00F794A6C179F9CFA5DB09FE43C5AC4DFE7F0F6F;
extern const uint32_t g_rgctx_List_1_get_Count_m7CE4F02149487F512B200CA6746259F57956209A;
extern const uint32_t g_rgctx_List_1_get_Item_m3B2D334C2778090DDCD646A439D8E85B3E13127F;
extern const uint32_t g_rgctx_T_tB8AB41CDED15F0F2E3213746AF9DB5E61DF5E48C;
extern const uint32_t g_rgctx_List_1_GetEnumerator_mC988CB6C33BA013FDD17D95D4339E5D39490320F;
extern const uint32_t g_rgctx_Enumerator_t3D8D6B86CB8A6DABEC3C85A2A01E812EA9373B17;
extern const uint32_t g_rgctx_IEnumerator_1_t215A441780E31164A94873C3B2C5E8FF55A04241;
extern const uint32_t g_rgctx_T_tE82034840514F307E21B6FFC415C4940FBD29CF9;
extern const uint32_t g_rgctx_T_tE82034840514F307E21B6FFC415C4940FBD29CF9;
extern const uint32_t g_rgctx_ServiceManager_GetService_TisT_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264_mA1715829327AD4A87F4E4308B82A72B27806E6EA;
extern const uint32_t g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264_IService_Stop_mC0D01910A2138A56C4F73C28C088A789D4E05812;
extern const uint32_t g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264;
static const Il2CppRGCTXDefinition s_rgctxValues[16] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD5F3F41A7F8057A8B53DDCF4662BD926A404EB4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ServiceManager_GetService_TisT_tD5F3F41A7F8057A8B53DDCF4662BD926A404EB4D_m37EF4D438603D50B174415DF67D3A6494D8EC10F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObservableList_1_t8A21132CEA9DD922C8E2AD37490F3BC972C7F8B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t00F794A6C179F9CFA5DB09FE43C5AC4DFE7F0F6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m7CE4F02149487F512B200CA6746259F57956209A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m3B2D334C2778090DDCD646A439D8E85B3E13127F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB8AB41CDED15F0F2E3213746AF9DB5E61DF5E48C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_mC988CB6C33BA013FDD17D95D4339E5D39490320F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t3D8D6B86CB8A6DABEC3C85A2A01E812EA9373B17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t215A441780E31164A94873C3B2C5E8FF55A04241 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tE82034840514F307E21B6FFC415C4940FBD29CF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE82034840514F307E21B6FFC415C4940FBD29CF9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ServiceManager_GetService_TisT_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264_mA1715829327AD4A87F4E4308B82A72B27806E6EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264_IService_Stop_mC0D01910A2138A56C4F73C28C088A789D4E05812 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t7DF365D486713C1FE9D7BB7EAEE5A01BB2E0D264 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AccessibilityModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AccessibilityModule_CodeGenModule = 
{
	"UnityEngine.AccessibilityModule.dll",
	144,
	s_methodPointers,
	39,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	4,
	s_rgctxIndices,
	16,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
