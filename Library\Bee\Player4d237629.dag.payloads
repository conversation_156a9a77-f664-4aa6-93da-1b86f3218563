Payload for "WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info"

{"System.Object":null,"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.PlayerBuildProgramBase","methodName":"WriteBootConfigAction","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/boot.config"],"inputs":["Library/PlayerDataCache/Android/Data/boot.config","Library/PlayerDataCache/Android/Data/boot.config","Library/PlayerDataCache/Android/Data/data.unity3d","Library/PlayerDataCache/Android/Data/resources.resource","Library/PlayerDataCache/Android/Data/RuntimeInitializeOnLoads.json","Library/PlayerDataCache/Android/Data/ScriptingAssemblies.json","Library/PlayerDataCache/Android/Data/sharedassets0.resource","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Splines.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll","D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll","D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll","D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTween/DOTween.dll","D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll","D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.burst/Unity.Burst.Unsafe.dll","D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll","D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/AOT/Newtonsoft.Json.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll","Library/Bee/Player4d237629-inputdata.json"],"targetDirectories":[]}}    

Payload for "WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp"

--allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Postprocessing.Runtime.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Splines.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTween/DOTween.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.burst/Unity.Burst.Unsafe.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll" --allowed-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/AOT/Newtonsoft.Json.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll" --allowed-assembly="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll" --out="Library/Bee/artifacts/Android/ManagedStripped" --include-link-xml="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/UnityLinkerInputs/MethodsToPreserve.xml" --include-link-xml="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml" --include-link-xml="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml" --include-link-xml="D:/My Project/Driving Simulator Game Z TEC/Library/InputSystem/AndroidLink.xml" --include-link-xml="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/AndroidNativeLink.xml" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTween" --include-directory="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DemiLib/Core" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.burst" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTweenPro" --include-directory="D:/My Project/Driving Simulator Game Z TEC/Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/AOT" --include-directory="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux" --include-directory="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades" --rule-set=Conservative --profiler-report --profiler-output-file="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" --dotnetprofile=unityaot-linux --dotnetruntime=Il2Cpp --platform=Android --use-editor-options --engine-stripping-flag=EnableUnityConnect --engine-stripping-flag=EnablePerformanceReporting --engine-stripping-flag=EnableAnalytics --engine-modules-asset-file="C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/modules.asset" --editor-data-file="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Cinemachine.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --include-unity-root-assembly="D:/My Project/Driving Simulator Game Z TEC/Assets/Plugins/Demigiant/DOTween/DOTween.dll" --print-command-line --enable-analytics    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-firstpass-FeaturesChecked.txt_ww6f.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp-firstpass.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/DOTween-FeaturesChecked.txt_esvd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/DOTween.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/DOTweenPro-FeaturesChecked.txt_dxgf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/DOTweenPro.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_pcel.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_vfp9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Configuration.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Core.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Xml.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.Unsafe.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Cinemachine-FeaturesChecked.txt_blar.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Cinemachine.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Postprocessing.Runtime-FeaturesChecked.txt_6gzn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Postprocessing.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Splines-FeaturesChecked.txt_jdw1.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Splines.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.TextMeshPro-FeaturesChecked.txt_c7od.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_in83.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Timeline.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AccessibilityModule-FeaturesChecked.txt_ttts.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AccessibilityModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AndroidJNIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_3sdh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AnimationModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AssetBundleModule-FeaturesChecked.txt_1gjh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AssetBundleModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.DirectorModule-FeaturesChecked.txt_ruda.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.DirectorModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_5p9t.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.HierarchyCoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputForUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_vjsc.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.JSONSerializeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ParticleSystemModule-FeaturesChecked.txt_kapj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.ParticleSystemModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.Physics2DModule-FeaturesChecked.txt_45hf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.Physics2DModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2l1t.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PhysicsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PropertiesModule-FeaturesChecked.txt_m9z5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SharedInternalsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_zrrh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SubsystemsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wybo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_15vy.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainPhysicsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextRenderingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TilemapModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_v0bu.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_aabo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityWebRequestModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VehiclesModule-FeaturesChecked.txt_a1wc.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VehiclesModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_r8mm.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VFXModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_2ixs.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VideoModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VRModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.XRModule.dll"],"targetDirectories":[]}}    

Payload for "GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker"

{"PlayerBuildProgramLibrary.Data.GenerateNativePluginsForAssembliesArgs":{"PluginOutputFolder":"Library/Bee/artifacts/Android/AsyncPluginsFromLinker","SymbolOutputFolder":"Library/Bee/artifacts/Android/AsyncPluginsFromLinkerSymbols","Assemblies":["Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp-firstpass.dll","Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp.dll","Library/Bee/artifacts/Android/ManagedStripped/DOTween.dll","Library/Bee/artifacts/Android/ManagedStripped/DOTweenPro.dll","Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll","Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Configuration.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Core.dll","Library/Bee/artifacts/Android/ManagedStripped/System.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Xml.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.Unsafe.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Cinemachine.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Postprocessing.Runtime.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Splines.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Timeline.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AccessibilityModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AndroidJNIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AnimationModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AssetBundleModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.DirectorModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.HierarchyCoreModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputForUIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.JSONSerializeModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.ParticleSystemModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.Physics2DModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PhysicsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SharedInternalsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SubsystemsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainPhysicsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextRenderingModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TilemapModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityWebRequestModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VehiclesModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VFXModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VideoModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VRModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.XRModule.dll"]}}    

Payload for "WriteText Library/Bee/artifacts/Android/dqdht/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteText Library/Bee/artifacts/Android/zz99l/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/ej6vm62pypgm0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/UnityAdsStubs.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/char-conversions.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/il2cpp-api.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/il2cpp-benchmark-support.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/il2cpp-mono-api.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/il2cpp-runtime-stats.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/9em6ozbr74n90.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/BoehmGC.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/GCHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/GarbageCollector.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/NullGC.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/WriteBarrier.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/gc/WriteBarrierValidation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/jwjr6qym3dey0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/Mono/Runtime.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeClassHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeGPtrArrayHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeMarshal.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/Mono/SafeStringMarshal.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/lu8yj5jrdyca0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/Debugger.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/StackFrame.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/ygdt04tzkjvu0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CalendarData.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CompareInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CultureData.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CultureInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/RegionInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tj6cj0cdvelj0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.IO/BrokeredFileSystem.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.IO/DriveInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.IO/MonoIO.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.IO/Path.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/qh0raogri2o30.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/Assembly.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/CustomAttributeData.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/EventInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/ParameterInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeMethodInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/qh0raogri2o31.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/AssemblyName.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/FieldInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/Module.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MonoMethodInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeAssembly.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeEventInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeModule.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeParameterInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/qh0raogri2o32.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MethodBase.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MonoMethod.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeConstructorInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeFieldInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimePropertyInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/6fd85pedjyw80.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/GCHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/Marshal.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/RuntimeInformation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/mjsmeljc12io0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Messaging/AsyncResult.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Messaging/MonoMethodMessage.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/vhl7eyw9dbu40.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsIdentity.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsImpersonationContext.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsPrincipal.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/s54mlex3ruhv0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Text/EncodingHelper.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Text/Normalization.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hybtsv4bzoua0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/InternalThread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Monitor.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Mutex.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/NativeEventCalls.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/OSSpecificSynchronizationContext.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Thread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Timer.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/WaitHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/r5dixifkuj0e0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/ArgIterator.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeFieldHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Type.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/r5dixifkuj0e1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/AppDomain.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Array.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/CurrentSystemTimeZone.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/DateTime.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/NumberFormatter.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Object.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeMethodHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/r5dixifkuj0e2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Buffer.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/GC.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/MonoCustomAttrs.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeType.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeTypeHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/r5dixifkuj0e3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/CLRConfig.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/ConsoleDriver.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Delegate.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Enum.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Exception.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/Number.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/String.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/TypedReference.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/mscorlib/System/ValueType.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/bmk5ykkz9j290.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Diagnostics/DefaultTraceListener.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Diagnostics/FileVersionInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Diagnostics/Process.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Diagnostics/Stopwatch.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/o9hgtmz9g4l10.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Net.NetworkInformation/LinuxNetworkInterface.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Net.NetworkInformation/MacOsIPInterfaceProperties.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/uus2xk5s8x7s0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Net.Sockets/Socket.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/icalls/System/System.Net.Sockets/SocketException.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/kpc5e4nw0y120.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/CustomAttributeCreator.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/CustomAttributeDataReader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/FieldLayout.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/GenericMetadata.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/GenericSharing.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericContextCompare.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericMethodHash.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppSignature.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/kpc5e4nw0y121.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/ArrayMetadata.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/GenericMethod.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericContextHash.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericMethodCompare.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppTypeCompare.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/kpc5e4nw0y122.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericClassCompare.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericClassHash.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericInstCompare.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppGenericInstHash.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/metadata/Il2CppTypeHash.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/lyh14odvw7xe0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/mono/ThreadPool/ThreadPoolMonitorThread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/mono/ThreadPool/ThreadPoolWorkerThread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms-io-poll.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms-io.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/5h6pnaj7da710.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Android/ConsoleExtension.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Android/Initialize.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Android/Locale.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Android/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/yjquc21s1nci0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Error.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/File.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Locale.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/yjquc21s1nci1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Allocator.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Directory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Path.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Socket.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/c-api/Time.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/i1gye3ln0em50.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_errno.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_io.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_networking.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_random.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_sizecheck.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_time.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_uid.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_unused.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/pte6jpc4p7430.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/CrashHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Error.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Event.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/FastReaderReaderWriterLock.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/LibraryLoader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Messages.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Semaphore.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/pte6jpc4p7431.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ConditionVariable.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Image.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Mutex.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Path.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ReaderWriterLock.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Socket.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hdho9qhrwlmp0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/BrokeredFileSystem.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Initialize.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/WaitObject.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hdho9qhrwlmp1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Assert.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/CpuInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Debug.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Handle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/LibraryLoader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/MemoryMappedFile.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/SystemCertificates.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Thread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hdho9qhrwlmp2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/COM.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/CrashHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/File.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/MarshalStringAlloc.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/SocketBridge.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Generic/SocketImpl.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/mvw66oqvf1e90.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/OSX/Image.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/OSX/Process.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/OSX/SystemCertificates.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/OSX/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hwm9b1b7n81d0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/CrashHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Error.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Locale.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/PosixHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Thread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/ThreadImpl.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hwm9b1b7n81d1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Directory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Encoding.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/FileSystemWatcher.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/MarshalAlloc.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/SocketImpl.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/SystemCertificates.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hwm9b1b7n81d2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/CpuInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Cryptography.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/File.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/LastError.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/LibraryLoader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/MemoryMappedFile.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/hwm9b1b7n81d3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/ConditionVariableImpl.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Console.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Image.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Memory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/NativeMethods.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Path.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Process.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/StackTrace.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/Time.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Posix/TimeZone.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tzsowq483dsj0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Assert.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/CrashHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/File.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/LibraryLoader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/MarshalStringAlloc.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tzsowq483dsj1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Console.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Encoding.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Image.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Locale.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Path.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tzsowq483dsj2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/COM.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Cryptography.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Debug.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Directory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/DllMain.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/FileSystemWatcher.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Initialize.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/MemoryMappedFile.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/NativeMethods.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Process.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/SystemCertificates.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Thread.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/WindowsHelpers.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tzsowq483dsj3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/MarshalAlloc.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Memory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/SocketImpl.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/StackTrace.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/ThreadImpl.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/tzsowq483dsj4.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/ConditionVariableImpl.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/CpuInfo.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/LastError.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/SynchronizationContext.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/Time.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/Win32/TimeZone.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/ss7czia2x7nc0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/BrokeredFileSystem.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/File.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Initialize.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Locale.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Process.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Win32ApiSharedEmulation.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/WinRT/Win32ApiWinRTEmulation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/8k5nozsaloy80.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Output.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Runtime.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/mono-structs.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/sha1.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/8k5nozsaloy81.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Exception.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Il2CppError.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Logging.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Memory.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/MemoryMappedFile.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/MemoryPool.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/MemoryRead.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/8k5nozsaloy82.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/DirectoryUtils.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/Environment.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/MarshalingUtils.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/MemoryPoolAddressSanitizer.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/PathUtils.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/utils/StringUtils.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/4vka8qh8fy4f0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/BlobReader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/DebugSymbolReader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/Debugger.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/Finally.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/NativeSymbol.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/VmStringUtils.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/VmThreadUtils.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/5z15y8m0kqva0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/System/Math.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/System/MathF.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/ClassInlines.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/ComObjectBase.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Module.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Path.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/String.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/VisualizerHelpers.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Array.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Assembly.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/MarshalAlloc.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Object.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/PlatformInvoke.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Reflection.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/WaitHandle.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/WeakReference.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/COM.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/COMEntryPoints.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Enum.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Exception.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/InternalCalls.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Liveness.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/MetadataAlloc.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Profiler.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/RCW.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/AndroidRuntime.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/CCW.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/ClassLibraryPAL.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Domain.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/GlobalMetadata.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/LastError.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/MemoryInformation.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Method.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Monitor.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Random.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/ScopedThreadAttacher.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w4.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/AssemblyName.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/CCWBase.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/GenericClass.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/GenericContainer.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Image.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Parameter.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Property.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Type.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w5.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Field.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Il2CppHStringReference.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/MetadataLoader.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Runtime.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/StackTrace.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/ThreadPoolMs.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0frsleixj10w6.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Class.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/Event.cpp"
#include "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/vm/MetadataCache.cpp"
    

Payload for "WriteText Library/Bee/artifacts/Android/87lik/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteText Library/Bee/artifacts/Android/d8kzr/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp"

-march=armv8-a -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fstack-protector -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fmessage-length=0 -pipe -o "Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" -fcolor-diagnostics -fdiagnostics-absolute-paths -target aarch64-linux-android22 -fstrict-aliasing -fdiagnostics-format=msvc "Library/Bee/artifacts/Android/d8kzr/9kll8spbuk4q.o" "Library/Bee/artifacts/Android/d8kzr/hu59tggcxxy3.o" "Library/Bee/artifacts/Android/d8kzr/i54a6c2ohxdh.o" "Library/Bee/artifacts/Android/d8kzr/jdprqsinlzuw.o" "Library/Bee/artifacts/Android/d8kzr/4ssf61wkon6f.o" "Library/Bee/artifacts/Android/d8kzr/p8v3lbbirbiz.o" "Library/Bee/artifacts/Android/d8kzr/qggk6qrgvdze.o" "Library/Bee/artifacts/Android/d8kzr/bwj8l05dy1ay.o" "Library/Bee/artifacts/Android/d8kzr/wbnw19jb1pmh.o" "Library/Bee/artifacts/Android/d8kzr/xj7cmp094r3w.o" "Library/Bee/artifacts/Android/d8kzr/iza11ye77ffg.o" "Library/Bee/artifacts/Android/d8kzr/zals4mmypley.o" "Library/Bee/artifacts/Android/d8kzr/075gzxgtt4dn.o" "Library/Bee/artifacts/Android/d8kzr/8ayg1b2oww8v.o" "Library/Bee/artifacts/Android/d8kzr/tq14glgmzkkf.o" "Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" "Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" "Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" "Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" "Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" "Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" "Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" "Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" "Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" "Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" "Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" "Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" "Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" "Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" "Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" "Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" "Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" "Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" "Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" "Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" "Library/Bee/artifacts/Android/d8kzr/nzaqginwsw4s.o" "Library/Bee/artifacts/Android/d8kzr/8eeewr1tvkgc.o" "Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" "Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" "Library/Bee/artifacts/Android/d8kzr/fn868ses76d8.o" "Library/Bee/artifacts/Android/d8kzr/gvsnt8uqb9un.o" "Library/Bee/artifacts/Android/d8kzr/1awb9h9nex66.o" "Library/Bee/artifacts/Android/d8kzr/mqzzornlhliq.o" "Library/Bee/artifacts/Android/d8kzr/nyjg963jlnz5.o" "Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" "Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" "Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" "Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" "Library/Bee/artifacts/Android/d8kzr/8zg0e63qyl8i.o" "Library/Bee/artifacts/Android/d8kzr/tfkoufio19j2.o" "Library/Bee/artifacts/Android/d8kzr/vomfw8rdeo41.o" "Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" "Library/Bee/artifacts/Android/d8kzr/u401qaj1n1mo.o" "Library/Bee/artifacts/Android/d8kzr/g85u144pif6u.o" "Library/Bee/artifacts/Android/d8kzr/hgqbmklomhna.o" "Library/Bee/artifacts/Android/d8kzr/2vtz1tzlp5yt.o" "Library/Bee/artifacts/Android/d8kzr/nbxnh3djstad.o" "Library/Bee/artifacts/Android/d8kzr/ojh41iuhwvrs.o" "Library/Bee/artifacts/Android/d8kzr/9ykshs8ezj3b.o" "Library/Bee/artifacts/Android/d8kzr/a74817od3lkr.o" "Library/Bee/artifacts/Android/d8kzr/vm8whh3a69va.o" "Library/Bee/artifacts/Android/d8kzr/g2blxqh88x7t.o" "Library/Bee/artifacts/Android/d8kzr/haw1h6x6c0o9.o" "Library/Bee/artifacts/Android/d8kzr/vclibqzzr333.o" "Library/Bee/artifacts/Android/d8kzr/32m6eyw7lyam.o" "Library/Bee/artifacts/Android/d8kzr/oiquu7a5omm5.o" "Library/Bee/artifacts/Android/d8kzr/gso6qzdxurfn.o" "Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" "Library/Bee/artifacts/Android/d8kzr/2fcbro8s1h8l.o" "Library/Bee/artifacts/Android/d8kzr/nvfz6xmq45j5.o" "Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" "Library/Bee/artifacts/Android/d8kzr/9i336mhmbwc4.o" "Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" "Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" "Library/Bee/artifacts/Android/d8kzr/lz9rfs69d1h2.o" "Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" "Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" "Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" "Library/Bee/artifacts/Android/d8kzr/o285j2jc1yvx.o" "Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" "Library/Bee/artifacts/Android/d8kzr/uxfiflc76aj0.o" "Library/Bee/artifacts/Android/d8kzr/v5zyz0s5ad0f.o" "Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" "Library/Bee/artifacts/Android/d8kzr/106avjl0gpni.o" "Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" "Library/Bee/artifacts/Android/d8kzr/6edgv1k7gptl.o" "Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" "Library/Bee/artifacts/Android/d8kzr/9glks0exwd2t.o" "Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" "Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" "Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" "Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" "Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" "Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" "Library/Bee/artifacts/Android/d8kzr/ovnm8cdik8r9.o" "Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" "Library/Bee/artifacts/Android/d8kzr/7mxwfh15kra1.o" "Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" "Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" "Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" "Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" "Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" "Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" "Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" "Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" "Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" "Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" "Library/Bee/artifacts/Android/d8kzr/s20kvqf3nfmk.o" "Library/Bee/artifacts/Android/d8kzr/25hyt1fxyzo0.o" "Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" "Library/Bee/artifacts/Android/d8kzr/ot52tqat5phz.o" "Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" "Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" "Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" "Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" "Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" "Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" "Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" "Library/Bee/artifacts/Android/d8kzr/tal1f6v1rh3z.o" "Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" "Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" "Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" "Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" "Library/Bee/artifacts/Android/d8kzr/hjtq22u5el9s.o" "Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" "Library/Bee/artifacts/Android/d8kzr/36gv2rp1lb2r.o" "Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" "Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" "Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" "Library/Bee/artifacts/Android/d8kzr/eqopvfazu5ej.o" "Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" "Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" "Library/Bee/artifacts/Android/d8kzr/iqmxf7rrbell.o" "Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" "Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" "Library/Bee/artifacts/Android/d8kzr/ptdqv50klsp3.o" "Library/Bee/artifacts/Android/d8kzr/a9gebffiog1m.o" "Library/Bee/artifacts/Android/d8kzr/bh1uvuvgsii2.o" "Library/Bee/artifacts/Android/d8kzr/ww4ib49dv6tl.o" "Library/Bee/artifacts/Android/d8kzr/hc86qdobyu54.o" "Library/Bee/artifacts/Android/d8kzr/z5rdbpowxtq2.o" "Library/Bee/artifacts/Android/d8kzr/34ybo5mc7srh.o" "Library/Bee/artifacts/Android/d8kzr/4cjs8k3abv8w.o" "Library/Bee/artifacts/Android/d8kzr/psmgouh8djkg.o" "Library/Bee/artifacts/Android/d8kzr/a8p433v5g7vz.o" "Library/Bee/artifacts/Android/d8kzr/bgalojc4k9cf.o" "Library/Bee/artifacts/Android/d8kzr/wvd93sq1nxoy.o" "Library/Bee/artifacts/Android/d8kzr/hbhxj24yql0h.o" "Library/Bee/artifacts/Android/d8kzr/ij1d4hlxunhx.o" "Library/Bee/artifacts/Android/d8kzr/3y41jrzuxbtg.o" "Library/Bee/artifacts/Android/d8kzr/46oi46ft1daw.o" "Library/Bee/artifacts/Android/d8kzr/0dcuv44u1v7h.o" "Library/Bee/artifacts/Android/d8kzr/a7yuwsct9xqc.o" "Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" "Library/Bee/artifacts/Android/d8kzr/wumzwh7ogojb.o" "Library/Bee/artifacts/Android/d8kzr/x26fhxnnkq0q.o" "Library/Bee/artifacts/Android/d8kzr/iia4w61kneca.o" "Library/Bee/artifacts/Android/d8kzr/3xdscggiq2nt.o" "Library/Bee/artifacts/Android/d8kzr/45x8wvwgu448.o" "Library/Bee/artifacts/Android/d8kzr/pl1wc5aexsgs.o" "Library/Bee/artifacts/Android/d8kzr/a14ksepb0gsb.o" "Library/Bee/artifacts/Android/d8kzr/b9o1cu593i9q.o" "Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" "Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" "Library/Bee/artifacts/Android/d8kzr/gomkx7xyft0v.o" "Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" "Library/Bee/artifacts/Android/d8kzr/l5t0o7id251b.o" "Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" "Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" "Library/Bee/artifacts/Android/d8kzr/55wal3sioh2n.o" "Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" "Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" "Library/Bee/artifacts/Android/d8kzr/01zek1xq1lza.o" "Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" "Library/Bee/artifacts/Android/d8kzr/mcvqv3cujwyv.o" "Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" "Library/Bee/artifacts/Android/d8kzr/34e4vqh3e4xi.o" "Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" "Library/Bee/artifacts/Android/d8kzr/weayk7q21k6f.o" "Library/Bee/artifacts/Android/d8kzr/xmue5m705mnv.o" "Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" "Library/Bee/artifacts/Android/d8kzr/3h1q05zvbzay.o" "Library/Bee/artifacts/Android/d8kzr/4pl7klguf1rd.o" "Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" "Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" "Library/Bee/artifacts/Android/d8kzr/btc00jpnpfwv.o" "Library/Bee/artifacts/Android/d8kzr/4czkg6x1i6ey.o" "Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" "Library/Bee/artifacts/Android/d8kzr/a85wbpqwoi20.o" "Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" "Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" "Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" "Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" "Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" "Library/Bee/artifacts/Android/d8kzr/608olej0nq3l.o" "Library/Bee/artifacts/Android/d8kzr/lh8sv41vny8b.o" "Library/Bee/artifacts/Android/d8kzr/2jrgs6r4v1zy.o" "Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" "Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" "Library/Bee/artifacts/Android/d8kzr/qxqd40q4j6kr.o" "Library/Bee/artifacts/Android/d8kzr/bdu1ja51muwa.o" "Library/Bee/artifacts/Android/d8kzr/mrt9ubex4zyy.o" "Library/Bee/artifacts/Android/d8kzr/8dae7wztktyl.o" "Library/Bee/artifacts/Android/d8kzr/cczik646utbr.o" "Library/Bee/artifacts/Android/d8kzr/3rk5hkcdfkff.o" "Library/Bee/artifacts/Android/d8kzr/4z4l2zsbjmwu.o" "Library/Bee/artifacts/Android/d8kzr/pf89h968la8d.o" "Library/Bee/artifacts/Android/d8kzr/qnsq2on7pcpt.o" "Library/Bee/artifacts/Android/d8kzr/b3veiy14s00c.o" "Library/Bee/artifacts/Android/d8kzr/wiz2x7f2vocw.o" "Library/Bee/artifacts/Android/d8kzr/3cc7s5m7xd51.o" "Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" "Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" "Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" "Library/Bee/artifacts/Android/d8kzr/ac7tbdjsuouj.o" "Library/Bee/artifacts/Android/d8kzr/vrairmxpxc62.o" "Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" "Library/Bee/artifacts/Android/d8kzr/mckl3g7t98d1.o" "Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" "Library/Bee/artifacts/Android/d8kzr/8z7p351pgz6z.o" "Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" "Library/Bee/artifacts/Android/d8kzr/unvu3uwlnpzy.o" "Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" "Library/Bee/artifacts/Android/d8kzr/gbjz3jrhufsx.o" "Library/Bee/artifacts/Android/d8kzr/83b95fuxpl22.o" "Library/Bee/artifacts/Android/d8kzr/ldqhcp8dvt5c.o" "Library/Bee/artifacts/Android/d8kzr/uq6vwcfgi5ds.o" "Library/Bee/artifacts/Android/d8kzr/f69jcmtdltpb.o" "Library/Bee/artifacts/Android/d8kzr/gy0yzb544nxj.o" "Library/Bee/artifacts/Android/d8kzr/v2ltsjojhjw6.o" "Library/Bee/artifacts/Android/d8kzr/giph8s2gk77p.o" "Library/Bee/artifacts/Android/d8kzr/hq9xs8ifo9o5.o" "Library/Bee/artifacts/Android/d8kzr/25cm8hxcrx0o.o" "Library/Bee/artifacts/Android/d8kzr/nlgaorbaulc8.o" "Library/Bee/artifacts/Android/d8kzr/ot0q86r8xntn.o" "Library/Bee/artifacts/Android/d8kzr/983eog650b56.o" "Library/Bee/artifacts/Android/d8kzr/uo723pk33zgq.o" "Library/Bee/artifacts/Android/d8kzr/k4kibxippfaw.o" "Library/Bee/artifacts/Android/d8kzr/tqqyoib0g7mb.o" "Library/Bee/artifacts/Android/d8kzr/mlw3ex27neg0.o" "Library/Bee/artifacts/Android/d8kzr/70zru6g5q2rj.o" "Library/Bee/artifacts/Android/d8kzr/88j8emx3u48y.o" "Library/Bee/artifacts/Android/d8kzr/tonwuvb1xski.o" "Library/Bee/artifacts/Android/d8kzr/uw7cfbsz1u1x.o" "Library/Bee/artifacts/Android/d8kzr/fcb1uk6w4idh.o" "Library/Bee/artifacts/Android/d8kzr/0repauku76o0.o" "Library/Bee/artifacts/Android/d8kzr/1zy5u90sb95f.o" "Library/Bee/artifacts/Android/d8kzr/tg0fx1n1w1ut.o" "Library/Bee/artifacts/Android/d8kzr/2n3g8s5g95on.o" "Library/Bee/artifacts/Android/d8kzr/a5qnuk1zwuff.o" "Library/Bee/artifacts/Android/d8kzr/yzxvdadx1wuw.o" "Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" "Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" "Library/Bee/artifacts/Android/d8kzr/ig5tdiuec55o.o" "Library/Bee/artifacts/Android/d8kzr/2wexl85vs87e.o" "Library/Bee/artifacts/Android/d8kzr/hl1iyi40oidl.o" "Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" "Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" "Library/Bee/artifacts/Android/d8kzr/ohmn0a84o3bk.o" "Library/Bee/artifacts/Android/d8kzr/9wpbgkm2rrn3.o" "Library/Bee/artifacts/Android/d8kzr/ulhaqys58uf2.o" "Library/Bee/artifacts/Android/d8kzr/m7tsc9fssqjw.o" "Library/Bee/artifacts/Android/d8kzr/coeqiwfgefg2.o" "Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" "Library/Bee/artifacts/Android/d8kzr/jxnazyrk9929.o" "Library/Bee/artifacts/Android/d8kzr/kdkmybhldeiu.o" "Library/Bee/artifacts/Android/d8kzr/xqgs9zqegqf7.o" "Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" "Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" "Library/Bee/artifacts/Android/d8kzr/c7i60zrowo1v.o" "Library/Bee/artifacts/Android/d8kzr/7sqf1sldz1ha.o" "Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" "Library/Bee/artifacts/Android/d8kzr/rjj1gat48zbs.o" "Library/Bee/artifacts/Android/d8kzr/pgj75bd160pu.o" "Library/Bee/artifacts/Android/d8kzr/x88dxcrw5he9.o" "Library/Bee/artifacts/Android/d8kzr/06e9mmocdc22.o" "Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" "Library/Bee/artifacts/Android/d8kzr/pn5tr6jvsup6.o" "Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" "Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" "Library/Bee/artifacts/Android/d8kzr/91501rpe8hhy.o" "Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" "Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" "Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" "Library/Bee/artifacts/Android/d8kzr/lj6ht2s4c6zd.o" "Library/Bee/artifacts/Android/d8kzr/nqp3qp5ad6sw.o" "Library/Bee/artifacts/Android/d8kzr/m20qes3fkulp.o" "Library/Bee/artifacts/Android/d8kzr/ibt2f73hq3tq.o" "Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" "Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" "Library/Bee/artifacts/Android/d8kzr/stosql5gpuhq.o" "Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" "Library/Bee/artifacts/Android/d8kzr/ehcxqa0cwkap.o" "Library/Bee/artifacts/Android/d8kzr/zwfl6jeaz8l8.o" "Library/Bee/artifacts/Android/d8kzr/kcj9lts71wxr.o" "Library/Bee/artifacts/Android/d8kzr/lk3p68855ye7.o" "Library/Bee/artifacts/Android/d8kzr/6z6dmin38mqq.o" "Library/Bee/artifacts/Android/d8kzr/77qu6x31cp75.o" "Library/Bee/artifacts/Android/d8kzr/snuim7hzfdjp.o" "Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" "Library/Bee/artifacts/Android/d8kzr/jbm9bjl27jgz.o" "Library/Bee/artifacts/Android/d8kzr/4qpxrszza8ri.o" "Library/Bee/artifacts/Android/d8kzr/p6sl71dxdw31.o" "Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" "Library/Bee/artifacts/Android/d8kzr/bugq7q8skmw0.o" "Library/Bee/artifacts/Android/d8kzr/w9jen0mqna8j.o" "Library/Bee/artifacts/Android/d8kzr/xh4u7f3orcpz.o" "Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" "Library/Bee/artifacts/Android/d8kzr/i6hutl07x9cu.o" "Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" "Library/Bee/artifacts/Android/d8kzr/yo7r87bsn5oh.o" "Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" "Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" "Library/Bee/artifacts/Android/d8kzr/jxs8b7zbmatr.o" "Library/Bee/artifacts/Android/d8kzr/kzwq165kpxqi.o" "Library/Bee/artifacts/Android/d8kzr/x76fzfo8l23p.o" "Library/Bee/artifacts/Android/d8kzr/8zadaggpr090.o" "Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" "Library/Bee/artifacts/Android/d8kzr/mri45r890jp3.o" "Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" "Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" "Library/Bee/artifacts/Android/d8kzr/lzrbp09zxf4t.o" "Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" "Library/Bee/artifacts/Android/d8kzr/009ns7gybvdd.o" "Library/Bee/artifacts/Android/d8kzr/vge3azf3u12w.o" "Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" "Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" "Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" "Library/Bee/artifacts/Android/d8kzr/n6qmfnbbo4ko.o" "Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" "Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" "Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" "Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" "Library/Bee/artifacts/Android/d8kzr/jov4p39u0xgj.o" "Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" "Library/Bee/artifacts/Android/d8kzr/e7m8oxn5tfdg.o" "Library/Bee/artifacts/Android/d8kzr/c4dabwex14fm.o" "Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" "Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" "Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" "Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" "Library/Bee/artifacts/Android/d8kzr/s2bwaz6dxjth.o" "Library/Bee/artifacts/Android/d8kzr/uuardhox5g5u.o" "Library/Bee/artifacts/Android/d8kzr/sli71m7v0fnl.o" "Library/Bee/artifacts/Android/d8kzr/bcwljkgbs91u.o" "Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" "Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" "Library/Bee/artifacts/Android/d8kzr/sk3psbif85dy.o" "Library/Bee/artifacts/Android/d8kzr/hzzs0hwl5jko.o" "Library/Bee/artifacts/Android/d8kzr/cc6dokwk1bcr.o" "Library/Bee/artifacts/Android/d8kzr/o44r908tjxtl.o" "Library/Bee/artifacts/Android/d8kzr/9nmkg3j9skao.o" "Library/Bee/artifacts/Android/d8kzr/56ben38xdc5p.o" "Library/Bee/artifacts/Android/d8kzr/u9oigfpdat6n.o" "Library/Bee/artifacts/Android/d8kzr/upsfj2k4spt9.o" "Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" "Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" "Library/Bee/artifacts/Android/d8kzr/6386mtmlure2.o" "Library/Bee/artifacts/Android/d8kzr/aggn294o7ldd.o" "Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" "Library/Bee/artifacts/Android/d8kzr/6b8lrklf1hrp.o" "Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" "Library/Bee/artifacts/Android/d8kzr/4g4fb246gu9o.o" "Library/Bee/artifacts/Android/d8kzr/n96lhotmyyf2.o" "Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" "Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" "Library/Bee/artifacts/Android/d8kzr/a951q8orlkl2.o" "Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" "Library/Bee/artifacts/Android/d8kzr/jf3wjb0lbtlr.o" "Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" "Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" "Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" "Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" "Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" "Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" "Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" "Library/Bee/artifacts/Android/d8kzr/s8wzl21r6wdv.o" "Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" "Library/Bee/artifacts/Android/d8kzr/tyrz3lym5pkq.o" "Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" "Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" "Library/Bee/artifacts/Android/d8kzr/9up4h199he7v.o" "Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" "Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" "Library/Bee/artifacts/Android/d8kzr/a7ezf8z3szds.o" "Library/Bee/artifacts/Android/d8kzr/b82i0fmwhe88.o" "Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" "Library/Bee/artifacts/Android/d8kzr/hcfnlxn37dpc.o" "Library/Bee/artifacts/Android/d8kzr/sszjk6y5r3mc.o" "Library/Bee/artifacts/Android/d8kzr/r1w5t531a50x.o" "Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" "Library/Bee/artifacts/Android/d8kzr/8ijiwlknrkr7.o" "Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" "Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" -Wl,--wrap,sigaction -Wl,--wrap,sigaction -fuse-ld=lld.exe -Wl,--gc-sections -Wl,--fatal-warnings -Wl,-soname=libil2cpp.so -Wl,--no-undefined -Wl,--build-id=sha1 -Wl,-z,relro -Wl,-z,noexecstack -shared -static-libstdc++  -Wl,--icf=safe "Library/Bee/artifacts/Android/87lik/il2cpp.a" "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/StaticLibs/arm64-v8a/baselib.a" -l"log"    

Payload for "WriteText Library/Bee/artifacts/csharpactions/IntermediateFiles.txt_ri9j.info"

{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments":{"ProjectPath":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle","Architectures":"ARM64","BuildSystem":"Gradle","GradleProjectCreateInfo":{"ArtifactsPath":"Library/Bee/artifacts/Android","EnvironmentVariableInputs":["UNITY_THISISABUILDMACHINE:"],"HostPlatform":"Windows","ApplicationType":"APK","BuildType":"Release","AndroidSDKPath":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK","AndroidNDKPath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK","AndroidJavaPath":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\OpenJDK","PreferredHeapSizeForJVM":4096,"GradleVersion":"8.4","ProjectFiles":{"UnityLibraryBuildGradle":{"RelativeDestinationPath":"unityLibrary/build.gradle","CanBeModifiedByUser":true},"LauncherBuildGradle":{"RelativeDestinationPath":"launcher/build.gradle","CanBeModifiedByUser":true},"LauncherSetupUnitySymbolsGradle":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\launcher/setupSymbols.gradle","RelativeDestinationPath":"launcher/setupSymbols.gradle","CanBeModifiedByUser":false},"SharedKeepUnitySymbolsGradle":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/keepUnitySymbols.gradle","RelativeDestinationPath":"shared/keepUnitySymbols.gradle","CanBeModifiedByUser":false},"ProjectLevelBuildGradle":{"RelativeDestinationPath":"build.gradle","CanBeModifiedByUser":true},"GradleProperties":{"RelativeDestinationPath":"gradle.properties","CanBeModifiedByUser":true},"UnityProguard":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\UnityProGuardTemplate.txt","RelativeDestinationPath":"unityLibrary/proguard-unity.txt","CanBeModifiedByUser":true},"ProguardUser":{"RelativeDestinationPath":"unityLibrary/proguard-user.txt","CanBeModifiedByUser":true},"GradleSettings":{"RelativeDestinationPath":"settings.gradle","CanBeModifiedByUser":true},"LocalProperties":{"RelativeDestinationPath":"local.properties","CanBeModifiedByUser":true}},"AdditionalLibrariesRelativePaths":[],"AdditionalUserInputs":[],"AdditionalUserOutputs":{"AdditionalManifests":[],"AdditionalBuildGradleFiles":[],"AdditionalGradleSettings":[],"AdditionalGradleProperties":[],"AdditionalFilesWithContents":[]},"UserCopyData":{"FilesToCopy":[],"DirectoriesToCopy":[]},"AdditionalUserData":[],"BuildTools":"34.0.0","TargetSDKVersion":36,"MinSDKVersion":23,"PackageName":"com.atbfig.firecopter","Architectures":"ARM64","BuildApkPerCpuArchitecture":false,"DebugSymbols":{"Level":"None","Format":"5"},"VersionCode":3,"VersionName":"1.5","Minify":1,"NoCompressOptions":{"RelativeFilePaths":[],"FileExtensions":[]},"UseCustomKeystore":false,"KeystorePath":"C:/Users/<USER>/user.keystore","KeystoreName":"user.keystore","KeystorePassword":"","KeystoreAliasName":"gazi","KeystoreAliasPassword":"","ScriptingImplementation":"IL2CPP","AndroidLibraries":[],"AARFiles":[],"BuiltinJavaSourcePaths":["com/unity3d/player/UnityPlayerGameActivity.java"],"JavaSourcePaths":[],"KotlinSourcePaths":[],"PlayerPackage":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer","PlayerPackageTools":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools","SymlinkSources":false,"InstallIntoBuildsFolder":false,"UnityPath":"","UnityProjectPath":"D:/My Project/Driving Simulator Game Z TEC","OverrideCMakeIntermdiateDirectory":true,"Dependencies":[],"ApplicationEntry":"GameActivity","JarFiles":["classes.jar"],"UseOptimizedFramePacing":false,"ReportGooglePlayAppDependencies":false,"UnityVersion":"6000.0.30f1"}},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe","targets":["Library/Bee/artifacts/Android/IntermediateFiles.txt","Library/Bee/artifacts/Android/Gradle/build.gradle","Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle","Library/Bee/artifacts/Android/Gradle/launcher/build.gradle","Library/Bee/artifacts/Android/Gradle/gradle.properties","Library/Bee/artifacts/Android/Gradle/local.properties","Library/Bee/artifacts/Android/Gradle/settings.gradle","Library/Bee/artifacts/Android/Gradle/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/launcher/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/gradle.properties.xml","Library/Bee/artifacts/Android/Gradle/local.properties.xml","Library/Bee/artifacts/Android/Gradle/settings.gradle.xml","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/setupSymbols.gradle","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/keepUnitySymbols.gradle","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/GAToUnityCallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroEnd.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroHeaderBegin.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroSourceBegin.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEvents.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboardCallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGATypes.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAVersion.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGACallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAConfigurationCallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAKeyEventCallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAMotionEventCallbacks.h","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt","Library/Bee/artifacts/Android/Gradle/unityLibrary/proguard-unity.txt","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"],"inputs":["C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/CMakeLists.txt","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/GAToUnityCallbacks.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroEnd.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroHeaderBegin.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroSourceBegin.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/ReadMe.txt","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEntry.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEvents.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputKeyEvent.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputMotionEvent.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.cpp","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboardCallbacks.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGATypes.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAVersion.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGACallbacks.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAConfigurationCallbacks.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAKeyEventCallbacks.h","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAMotionEventCallbacks.h"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/IntermediateLauncherManifestDiag.txt_4oyd.info"

{"AndroidPlayerBuildProgram.Actions.GenerateManifests+Arguments":{"Configuration":{"TargetSDKVersion":36,"LauncherManifestTemplatePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\LauncherManifest.xml","LauncherManifestTemplateUsed":false,"LibraryManifestTemplatePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\UnityManifest.xml","LibraryManifestCustomTemplateUsed":false,"LauncherManifestPath":"launcher\\src\\main\\AndroidManifest.xml","LibraryManifestPath":"unityLibrary\\src\\main\\AndroidManifest.xml","TVCompatibility":false,"BannerEnabled":true,"IsGame":true,"PreferredInstallLocation":"PreferExternal","TextureSupport":"Generic","GamepadSupportLevel":"SupportsDPad","SupportedAspectRatioMode":1,"MaxAspectRatio":2.4,"MinAspectRatio":1,"ForceInternetPermission":false,"UseLowAccuracyLocation":false,"ForceSDCardPermission":false,"PreserveFramebufferAlpha":false,"DefaultInterfaceOrientation":"AutoRotation","AllowedAutorotateToPortrait":false,"AllowedAutorotateToPortraitUpsideDown":false,"AllowedAutorotateToLandscapeLeft":true,"AllowedAutorotateToLandscapeRight":true,"SplashScreenScale":"ScaleToFit","RenderOutsideSafeArea":false,"GraphicsDevices":["OpenGLES3"],"OpenGLRequireES31":false,"OpenGLRequireES31AEP":false,"OpenGLRequireES32":false,"StartInFullscreen":true,"DefaultWindowWidth":1920,"DefaultWindowHeight":1080,"MinimumWindowWidth":400,"MinimumWindowHeight":300,"ResizeableActivity":true,"FullScreenMode":"FullScreenWindow","AutoRotationBehavior":"User","StripEngineCode":false,"ApplicationEntry":"GameActivity","JavaFileNames":["UnityPlayerGameActivity.java"],"EnableOnBackInvokedCallback":false},"Services":{"EnableUnityConnect":true,"EnablePerformanceReporting":true,"EnableAnalytics":true,"EnableCrashReporting":false},"ProjectPath":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle","ArtifactsPath":"Library/Bee/artifacts/Android/Manifest","FeatureChecklist":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"],"Development":false,"UsingObb":false,"UsingMTE":false,"GradleResourcesInformation":{"TargetSDKVersion":36,"RoundIconsAvailable":false,"RoundIconsSupported":true,"AdaptiveIconsSupported":true,"AdaptiveIconsAvailable":false},"LauncherManifestDiagnosticsPath":"Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt","LibraryManifestDiagnosticsPath":"Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt","APIRequiringInternetPermission":["UnityEngine.Networking","System.Net.Sockets","UnityEngine.Ping","UnityEngine.Networking.UnityWebRequest"]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.GenerateManifests","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe","targets":["Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt","Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt","Library/Bee/artifacts/Android/Manifest/launcher/src/main/AndroidManifest.xml","Library/Bee/artifacts/Android/Manifest/unityLibrary/src/main/AndroidManifest.xml"],"inputs":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml"],"targetDirectories":[]}}    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
  </style>
</resources>    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
    <item name="android:windowLayoutInDisplayCutoutMode">default</item>
  </style>
</resources>    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
  </style>
  <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
  </style>
</resources>    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="UnityThemeSelector" parent="BaseUnityTheme">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
  <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen" />
  <style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
  <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
</resources>    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <color name="staticSplashScreenBackgroundColor">#231F20</color>
</resources>    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <string name="app_name">Driving Simulator Game Z TEC</string>
</resources>    

Payload for "WriteText Library/Bee/artifacts/csharpactions/unity_app_guid_2cqv.info"

{"System.Object":null,"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.GuidGenerator","methodName":"Run","assemblyLocation":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe","targets":["D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid"],"inputs":["Library/PlayerDataCache/Android/Data/data.unity3d","Library/PlayerDataCache/Android/Data/resources.resource","Library/PlayerDataCache/Android/Data/RuntimeInitializeOnLoads.json","Library/PlayerDataCache/Android/Data/ScriptingAssemblies.json","Library/PlayerDataCache/Android/Data/sharedassets0.resource","Library/Bee/artifacts/Android/boot.config","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/data/Metadata/global-metadata.dat","D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/data/Resources/mscorlib.dll-resources.dat","C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Data/Resources/unity default resources"],"targetDirectories":[]}}    

Payload for "WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint"

6000.0.30f1;IL2CPP;Release;StripEngineCode:0;OptimizedFramePacing:0;AppEntry:2    

Payload for "ModifyAndroidProjectCallback D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)"

{"AndroidPlayerBuildProgram.Data.AndroidProjectFileArgs":{"ProjectPath":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle","ArtifactsPath":"Library/Bee/artifacts/Android","LauncherManifestPath":"launcher\\src\\main\\AndroidManifest.xml","LibraryManifestPath":"unityLibrary\\src\\main\\AndroidManifest.xml","ProjectFiles":{"UnityLibraryBuildGradle":{"RelativeDestinationPath":"unityLibrary/build.gradle","CanBeModifiedByUser":true},"LauncherBuildGradle":{"RelativeDestinationPath":"launcher/build.gradle","CanBeModifiedByUser":true},"LauncherSetupUnitySymbolsGradle":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\launcher/setupSymbols.gradle","RelativeDestinationPath":"launcher/setupSymbols.gradle","CanBeModifiedByUser":false},"SharedKeepUnitySymbolsGradle":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/keepUnitySymbols.gradle","RelativeDestinationPath":"shared/keepUnitySymbols.gradle","CanBeModifiedByUser":false},"ProjectLevelBuildGradle":{"RelativeDestinationPath":"build.gradle","CanBeModifiedByUser":true},"GradleProperties":{"RelativeDestinationPath":"gradle.properties","CanBeModifiedByUser":true},"UnityProguard":{"SourcePath":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\UnityProGuardTemplate.txt","RelativeDestinationPath":"unityLibrary/proguard-unity.txt","CanBeModifiedByUser":true},"ProguardUser":{"RelativeDestinationPath":"unityLibrary/proguard-user.txt","CanBeModifiedByUser":true},"GradleSettings":{"RelativeDestinationPath":"settings.gradle","CanBeModifiedByUser":true},"LocalProperties":{"RelativeDestinationPath":"local.properties","CanBeModifiedByUser":true}},"AdditionalLibrariesBuildGradlePaths":[],"AdditionalUserOutputs":{"AdditionalManifests":[],"AdditionalBuildGradleFiles":[],"AdditionalGradleSettings":[],"AdditionalGradleProperties":[],"AdditionalFilesWithContents":[]},"AdditionalUserData":[]}}    

